{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/app/users/system/page.tsx"], "sourcesContent": ["/**\n * System Users List Page\n * Management page for SystemUser accounts (Admin/Editor/Moderator)\n */\n\n'use client';\n\nimport React, { useState } from 'react';\nimport {\n  Card,\n  Button,\n  Space,\n  Input,\n  Select,\n  Tag,\n  Avatar,\n  Dropdown,\n  Modal,\n  Typography,\n  Alert,\n  Table,\n  Row,\n  Col,\n  Statistic,\n  Breadcrumb,\n} from 'antd';\nimport {\n  UserOutlined,\n  PlusOutlined,\n  SearchOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  MoreOutlined,\n  TeamOutlined,\n  UserAddOutlined,\n  UserDeleteOutlined,\n  ExclamationCircleOutlined,\n  HomeOutlined,\n} from '@ant-design/icons';\nimport { useUsers, useUserStatistics, useDeleteUser } from '@/hooks/api';\nimport {\n  SystemUser,\n  UserListParams,\n  SystemUserRole,\n  UserStatus,\n  ROLE_LABELS,\n  STATUS_LABELS,\n  ROLE_COLORS,\n  STATUS_COLORS,\n  userHelpers,\n  DEFAULT_USER_PARAMS,\n} from '@/types/user';\nimport { useRouter } from 'next/navigation';\n\nconst { Text } = Typography;\nconst { confirm } = Modal;\n\nexport default function SystemUsersPage() {\n  const router = useRouter();\n  const [params, setParams] = useState<UserListParams>(DEFAULT_USER_PARAMS);\n\n  // API hooks\n  const { data: usersData, isLoading, error } = useUsers(params);\n  const { data: statistics } = useUserStatistics();\n  const deleteUserMutation = useDeleteUser();\n\n  // Handle search\n  const handleSearch = (value: string) => {\n    setParams(prev => ({ ...prev, search: value, page: 1 }));\n  };\n\n  // Handle filter change\n  const handleFilterChange = (key: keyof UserListParams, value: any) => {\n    setParams(prev => ({ ...prev, [key]: value, page: 1 }));\n  };\n\n  // Handle pagination\n  const handlePageChange = (page: number, pageSize: number) => {\n    setParams(prev => ({ ...prev, page, limit: pageSize }));\n  };\n\n  // Handle table change (pagination, filters, sorter)\n  const handleTableChange = (pagination: any, filters: any, sorter: any) => {\n    // Handle pagination\n    if (pagination) {\n      setParams(prev => ({\n        ...prev,\n        page: pagination.current,\n        limit: pagination.pageSize\n      }));\n    }\n\n    // Handle sorting\n    if (sorter && sorter.field) {\n      setParams(prev => ({\n        ...prev,\n        sortBy: sorter.field as any,\n        sortOrder: sorter.order === 'ascend' ? 'asc' : 'desc'\n      }));\n    }\n  };\n\n  // Handle delete user\n  const handleDeleteUser = (user: SystemUser) => {\n    confirm({\n      title: 'Delete User',\n      icon: <ExclamationCircleOutlined />,\n      content: (\n        <div>\n          <p>Are you sure you want to delete user <strong>{userHelpers.getDisplayName(user)}</strong>?</p>\n          <p>This action cannot be undone.</p>\n        </div>\n      ),\n      okText: 'Delete',\n      okType: 'danger',\n      cancelText: 'Cancel',\n      onOk: () => deleteUserMutation.mutate(user.id),\n    });\n  };\n\n  // Table columns\n  const columns = [\n    {\n      title: 'User',\n      dataIndex: 'username',\n      key: 'username',\n      sorter: true,\n      render: (username: string, user: SystemUser) => {\n        const avatar = userHelpers.getAvatarDisplay(user);\n        return (\n          <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>\n            <Avatar\n              size=\"default\"\n              src={avatar.type === 'url' ? avatar.value : undefined}\n              style={avatar.type === 'initials' ? { backgroundColor: ROLE_COLORS[user.role] } : undefined}\n            >\n              {avatar.type === 'initials' ? avatar.value : undefined}\n            </Avatar>\n            <div>\n              <div style={{ fontWeight: 'bold' }}>{userHelpers.getFullName(user)}</div>\n              <div style={{ fontSize: '12px', color: '#666' }}>@{username}</div>\n            </div>\n          </div>\n        );\n      },\n    },\n    {\n      title: 'Email',\n      dataIndex: 'email',\n      key: 'email',\n      sorter: true,\n    },\n    {\n      title: 'Role',\n      dataIndex: 'role',\n      key: 'role',\n      sorter: true,\n      render: (role: SystemUserRole) => (\n        <Tag color={ROLE_COLORS[role]}>\n          {ROLE_LABELS[role]}\n        </Tag>\n      ),\n    },\n    {\n      title: 'Status',\n      dataIndex: 'status',\n      key: 'status',\n      sorter: true,\n      render: (status: UserStatus) => (\n        <Tag color={STATUS_COLORS[status]}>\n          {STATUS_LABELS[status]}\n        </Tag>\n      ),\n    },\n    {\n      title: 'Last Login',\n      dataIndex: 'lastLogin',\n      key: 'lastLogin',\n      sorter: true,\n      render: (lastLogin: string) => (\n        <Text type=\"secondary\">{userHelpers.formatLastLogin(lastLogin)}</Text>\n      ),\n    },\n    {\n      title: 'Created',\n      dataIndex: 'createdAt',\n      key: 'createdAt',\n      sorter: true,\n      render: (createdAt: string) => (\n        <Text type=\"secondary\">{new Date(createdAt).toLocaleDateString()}</Text>\n      ),\n    },\n    {\n      title: 'Actions',\n      key: 'actions',\n      width: 120,\n      render: (_, user: SystemUser) => {\n        const menuItems = [\n          {\n            key: 'edit',\n            icon: <EditOutlined />,\n            label: 'Edit User',\n            onClick: () => router.push(`/users/system/${user.id}/edit`),\n          },\n          {\n            key: 'profile',\n            icon: <UserOutlined />,\n            label: 'View Profile',\n            onClick: () => router.push(`/users/system/${user.id}`),\n          },\n          {\n            type: 'divider' as const,\n          },\n          {\n            key: 'delete',\n            icon: <DeleteOutlined />,\n            label: 'Delete User',\n            danger: true,\n            onClick: () => handleDeleteUser(user),\n          },\n        ];\n\n        return (\n          <Dropdown menu={{ items: menuItems }} trigger={['click']}>\n            <Button type=\"text\" icon={<MoreOutlined />} />\n          </Dropdown>\n        );\n      },\n    },\n  ];\n\n  return (\n    <div>\n      {/* Breadcrumb Navigation */}\n      <Breadcrumb\n        className=\"mb-4\"\n        items={[\n          {\n            href: '/',\n            title: <HomeOutlined />\n          },\n          {\n            href: '/users',\n            title: 'User System'\n          },\n          {\n            title: 'System Users'\n          }\n        ]}\n      />\n\n      {/* Page Header */}\n      <div className=\"mb-6 flex justify-between items-start\">\n        <div>\n          <Typography.Title level={2}>\n            <UserOutlined className=\"mr-2\" />\n            System Users\n          </Typography.Title>\n          <Typography.Text type=\"secondary\">\n            Manage administrator, editor, and moderator accounts\n          </Typography.Text>\n        </div>\n        <Space>\n          <Button\n            type=\"primary\"\n            icon={<PlusOutlined />}\n            onClick={() => router.push('/users/system/create')}\n          >\n            Create User\n          </Button>\n        </Space>\n      </div>\n      {/* Statistics Cards */}\n      {statistics && (\n        <Row gutter={16} className=\"mb-6\">\n          <Col xs={12} sm={6}>\n            <Card>\n              <Statistic\n                title=\"Total Users\"\n                value={statistics.total}\n                prefix={<TeamOutlined />}\n              />\n            </Card>\n          </Col>\n          <Col xs={12} sm={6}>\n            <Card>\n              <Statistic\n                title=\"Active Users\"\n                value={statistics.active}\n                prefix={<UserOutlined />}\n                valueStyle={{ color: '#3f8600' }}\n              />\n            </Card>\n          </Col>\n          <Col xs={12} sm={6}>\n            <Card>\n              <Statistic\n                title=\"Recent Logins\"\n                value={statistics.recentLogins}\n                prefix={<UserAddOutlined />}\n              />\n            </Card>\n          </Col>\n          <Col xs={12} sm={6}>\n            <Card>\n              <Statistic\n                title=\"New This Month\"\n                value={statistics.newThisMonth}\n                prefix={<UserAddOutlined />}\n              />\n            </Card>\n          </Col>\n        </Row>\n      )}\n\n      {/* Filters and Search */}\n      <Card style={{ marginBottom: '24px' }}>\n        <Space size=\"middle\" wrap>\n          <Input\n            placeholder=\"Search users...\"\n            prefix={<SearchOutlined />}\n            value={params.search}\n            onChange={(e) => handleSearch(e.target.value)}\n            style={{ width: '300px' }}\n            allowClear\n          />\n\n          <Select\n            placeholder=\"Filter by role\"\n            value={params.role}\n            onChange={(value) => handleFilterChange('role', value)}\n            style={{ width: '150px' }}\n            allowClear\n          >\n            <Select.Option value=\"admin\">Administrator</Select.Option>\n            <Select.Option value=\"editor\">Editor</Select.Option>\n            <Select.Option value=\"moderator\">Moderator</Select.Option>\n          </Select>\n\n          <Select\n            placeholder=\"Filter by status\"\n            value={params.status}\n            onChange={(value) => handleFilterChange('status', value)}\n            style={{ width: '150px' }}\n            allowClear\n          >\n            <Select.Option value=\"active\">Active</Select.Option>\n            <Select.Option value=\"inactive\">Inactive</Select.Option>\n            <Select.Option value=\"suspended\">Suspended</Select.Option>\n          </Select>\n        </Space>\n      </Card>\n\n      {/* Error Alert */}\n      {error && (\n        <Alert\n          message=\"Error Loading Users\"\n          description=\"Failed to load user data. Please try again.\"\n          type=\"error\"\n          showIcon\n          style={{ marginBottom: '24px' }}\n        />\n      )}\n\n      {/* Users Table */}\n      <Card>\n        <Table\n          columns={columns}\n          dataSource={usersData?.users || []}\n          loading={isLoading}\n          pagination={{\n            current: params.page,\n            pageSize: params.limit,\n            total: usersData?.total || 0,\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total, range) =>\n              `${range[0]}-${range[1]} of ${total} users`,\n          }}\n          onChange={handleTableChange}\n          rowKey=\"id\"\n          scroll={{ x: 800 }}\n        />\n      </Card>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAID;AAgCA;AACA;AAYA;AA5CA;AAAA;AA+BA;AAbA;AAlBA;AAAA;AAkBA;AAAA;AAAA;AAlBA;AAAA;AAkBA;AAlBA;AAkBA;AAlBA;AAkBA;AAlBA;AAAA;AAAA;AAAA;AAkBA;AAAA;AAlBA;AAkBA;AAlBA;AAAA;AAAA;;;AAHA;;;;;;;AAiDA,MAAM,EAAE,IAAI,EAAE,GAAG,6LAAA,CAAA,aAAU;AAC3B,MAAM,EAAE,OAAO,EAAE,GAAG,mLAAA,CAAA,QAAK;AAEV,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,uHAAA,CAAA,sBAAmB;IAExE,YAAY;IACZ,MAAM,EAAE,MAAM,SAAS,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,EAAE,MAAM,UAAU,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,oBAAiB,AAAD;IAC7C,MAAM,qBAAqB,CAAA,GAAA,+HAAA,CAAA,gBAAa,AAAD;IAEvC,gBAAgB;IAChB,MAAM,eAAe,CAAC;QACpB,UAAU,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,QAAQ;gBAAO,MAAM;YAAE,CAAC;IACxD;IAEA,uBAAuB;IACvB,MAAM,qBAAqB,CAAC,KAA2B;QACrD,UAAU,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,IAAI,EAAE;gBAAO,MAAM;YAAE,CAAC;IACvD;IAEA,oBAAoB;IACpB,MAAM,mBAAmB,CAAC,MAAc;QACtC,UAAU,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE;gBAAM,OAAO;YAAS,CAAC;IACvD;IAEA,oDAAoD;IACpD,MAAM,oBAAoB,CAAC,YAAiB,SAAc;QACxD,oBAAoB;QACpB,IAAI,YAAY;YACd,UAAU,CAAA,OAAQ,CAAC;oBACjB,GAAG,IAAI;oBACP,MAAM,WAAW,OAAO;oBACxB,OAAO,WAAW,QAAQ;gBAC5B,CAAC;QACH;QAEA,iBAAiB;QACjB,IAAI,UAAU,OAAO,KAAK,EAAE;YAC1B,UAAU,CAAA,OAAQ,CAAC;oBACjB,GAAG,IAAI;oBACP,QAAQ,OAAO,KAAK;oBACpB,WAAW,OAAO,KAAK,KAAK,WAAW,QAAQ;gBACjD,CAAC;QACH;IACF;IAEA,qBAAqB;IACrB,MAAM,mBAAmB,CAAC;QACxB,QAAQ;YACN,OAAO;YACP,oBAAM,6LAAC,+OAAA,CAAA,4BAAyB;;;;;YAChC,uBACE,6LAAC;;kCACC,6LAAC;;4BAAE;0CAAqC,6LAAC;0CAAQ,uHAAA,CAAA,cAAW,CAAC,cAAc,CAAC;;;;;;4BAAe;;;;;;;kCAC3F,6LAAC;kCAAE;;;;;;;;;;;;YAGP,QAAQ;YACR,QAAQ;YACR,YAAY;YACZ,MAAM,IAAM,mBAAmB,MAAM,CAAC,KAAK,EAAE;QAC/C;IACF;IAEA,gBAAgB;IAChB,MAAM,UAAU;QACd;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,QAAQ;YACR,QAAQ,CAAC,UAAkB;gBACzB,MAAM,SAAS,uHAAA,CAAA,cAAW,CAAC,gBAAgB,CAAC;gBAC5C,qBACE,6LAAC;oBAAI,OAAO;wBAAE,SAAS;wBAAQ,YAAY;wBAAU,KAAK;oBAAO;;sCAC/D,6LAAC,qLAAA,CAAA,SAAM;4BACL,MAAK;4BACL,KAAK,OAAO,IAAI,KAAK,QAAQ,OAAO,KAAK,GAAG;4BAC5C,OAAO,OAAO,IAAI,KAAK,aAAa;gCAAE,iBAAiB,uHAAA,CAAA,cAAW,CAAC,KAAK,IAAI,CAAC;4BAAC,IAAI;sCAEjF,OAAO,IAAI,KAAK,aAAa,OAAO,KAAK,GAAG;;;;;;sCAE/C,6LAAC;;8CACC,6LAAC;oCAAI,OAAO;wCAAE,YAAY;oCAAO;8CAAI,uHAAA,CAAA,cAAW,CAAC,WAAW,CAAC;;;;;;8CAC7D,6LAAC;oCAAI,OAAO;wCAAE,UAAU;wCAAQ,OAAO;oCAAO;;wCAAG;wCAAE;;;;;;;;;;;;;;;;;;;YAI3D;QACF;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,QAAQ;QACV;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,QAAQ;YACR,QAAQ,CAAC,qBACP,6LAAC,+KAAA,CAAA,MAAG;oBAAC,OAAO,uHAAA,CAAA,cAAW,CAAC,KAAK;8BAC1B,uHAAA,CAAA,cAAW,CAAC,KAAK;;;;;;QAGxB;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,QAAQ;YACR,QAAQ,CAAC,uBACP,6LAAC,+KAAA,CAAA,MAAG;oBAAC,OAAO,uHAAA,CAAA,gBAAa,CAAC,OAAO;8BAC9B,uHAAA,CAAA,gBAAa,CAAC,OAAO;;;;;;QAG5B;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,QAAQ;YACR,QAAQ,CAAC,0BACP,6LAAC;oBAAK,MAAK;8BAAa,uHAAA,CAAA,cAAW,CAAC,eAAe,CAAC;;;;;;QAExD;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,QAAQ;YACR,QAAQ,CAAC,0BACP,6LAAC;oBAAK,MAAK;8BAAa,IAAI,KAAK,WAAW,kBAAkB;;;;;;QAElE;QACA;YACE,OAAO;YACP,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,GAAG;gBACV,MAAM,YAAY;oBAChB;wBACE,KAAK;wBACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;wBACnB,OAAO;wBACP,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,cAAc,EAAE,KAAK,EAAE,CAAC,KAAK,CAAC;oBAC5D;oBACA;wBACE,KAAK;wBACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;wBACnB,OAAO;wBACP,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,cAAc,EAAE,KAAK,EAAE,EAAE;oBACvD;oBACA;wBACE,MAAM;oBACR;oBACA;wBACE,KAAK;wBACL,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;wBACrB,OAAO;wBACP,QAAQ;wBACR,SAAS,IAAM,iBAAiB;oBAClC;iBACD;gBAED,qBACE,6LAAC,yLAAA,CAAA,WAAQ;oBAAC,MAAM;wBAAE,OAAO;oBAAU;oBAAG,SAAS;wBAAC;qBAAQ;8BACtD,cAAA,6LAAC,qMAAA,CAAA,SAAM;wBAAC,MAAK;wBAAO,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;;;;;;;;;;;YAG7C;QACF;KACD;IAED,qBACE,6LAAC;;0BAEC,6LAAC,6LAAA,CAAA,aAAU;gBACT,WAAU;gBACV,OAAO;oBACL;wBACE,MAAM;wBACN,qBAAO,6LAAC,qNAAA,CAAA,eAAY;;;;;oBACtB;oBACA;wBACE,MAAM;wBACN,OAAO;oBACT;oBACA;wBACE,OAAO;oBACT;iBACD;;;;;;0BAIH,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC,6LAAA,CAAA,aAAU,CAAC,KAAK;gCAAC,OAAO;;kDACvB,6LAAC,qNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;oCAAS;;;;;;;0CAGnC,6LAAC,6LAAA,CAAA,aAAU,CAAC,IAAI;gCAAC,MAAK;0CAAY;;;;;;;;;;;;kCAIpC,6LAAC,mMAAA,CAAA,QAAK;kCACJ,cAAA,6LAAC,qMAAA,CAAA,SAAM;4BACL,MAAK;4BACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;4BACnB,SAAS,IAAM,OAAO,IAAI,CAAC;sCAC5B;;;;;;;;;;;;;;;;;YAMJ,4BACC,6LAAC,+KAAA,CAAA,MAAG;gBAAC,QAAQ;gBAAI,WAAU;;kCACzB,6LAAC,+KAAA,CAAA,MAAG;wBAAC,IAAI;wBAAI,IAAI;kCACf,cAAA,6LAAC,iLAAA,CAAA,OAAI;sCACH,cAAA,6LAAC,2LAAA,CAAA,YAAS;gCACR,OAAM;gCACN,OAAO,WAAW,KAAK;gCACvB,sBAAQ,6LAAC,qNAAA,CAAA,eAAY;;;;;;;;;;;;;;;;;;;;kCAI3B,6LAAC,+KAAA,CAAA,MAAG;wBAAC,IAAI;wBAAI,IAAI;kCACf,cAAA,6LAAC,iLAAA,CAAA,OAAI;sCACH,cAAA,6LAAC,2LAAA,CAAA,YAAS;gCACR,OAAM;gCACN,OAAO,WAAW,MAAM;gCACxB,sBAAQ,6LAAC,qNAAA,CAAA,eAAY;;;;;gCACrB,YAAY;oCAAE,OAAO;gCAAU;;;;;;;;;;;;;;;;kCAIrC,6LAAC,+KAAA,CAAA,MAAG;wBAAC,IAAI;wBAAI,IAAI;kCACf,cAAA,6LAAC,iLAAA,CAAA,OAAI;sCACH,cAAA,6LAAC,2LAAA,CAAA,YAAS;gCACR,OAAM;gCACN,OAAO,WAAW,YAAY;gCAC9B,sBAAQ,6LAAC,2NAAA,CAAA,kBAAe;;;;;;;;;;;;;;;;;;;;kCAI9B,6LAAC,+KAAA,CAAA,MAAG;wBAAC,IAAI;wBAAI,IAAI;kCACf,cAAA,6LAAC,iLAAA,CAAA,OAAI;sCACH,cAAA,6LAAC,2LAAA,CAAA,YAAS;gCACR,OAAM;gCACN,OAAO,WAAW,YAAY;gCAC9B,sBAAQ,6LAAC,2NAAA,CAAA,kBAAe;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQlC,6LAAC,iLAAA,CAAA,OAAI;gBAAC,OAAO;oBAAE,cAAc;gBAAO;0BAClC,cAAA,6LAAC,mMAAA,CAAA,QAAK;oBAAC,MAAK;oBAAS,IAAI;;sCACvB,6LAAC,mLAAA,CAAA,QAAK;4BACJ,aAAY;4BACZ,sBAAQ,6LAAC,yNAAA,CAAA,iBAAc;;;;;4BACvB,OAAO,OAAO,MAAM;4BACpB,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;4BAC5C,OAAO;gCAAE,OAAO;4BAAQ;4BACxB,UAAU;;;;;;sCAGZ,6LAAC,qLAAA,CAAA,SAAM;4BACL,aAAY;4BACZ,OAAO,OAAO,IAAI;4BAClB,UAAU,CAAC,QAAU,mBAAmB,QAAQ;4BAChD,OAAO;gCAAE,OAAO;4BAAQ;4BACxB,UAAU;;8CAEV,6LAAC,qLAAA,CAAA,SAAM,CAAC,MAAM;oCAAC,OAAM;8CAAQ;;;;;;8CAC7B,6LAAC,qLAAA,CAAA,SAAM,CAAC,MAAM;oCAAC,OAAM;8CAAS;;;;;;8CAC9B,6LAAC,qLAAA,CAAA,SAAM,CAAC,MAAM;oCAAC,OAAM;8CAAY;;;;;;;;;;;;sCAGnC,6LAAC,qLAAA,CAAA,SAAM;4BACL,aAAY;4BACZ,OAAO,OAAO,MAAM;4BACpB,UAAU,CAAC,QAAU,mBAAmB,UAAU;4BAClD,OAAO;gCAAE,OAAO;4BAAQ;4BACxB,UAAU;;8CAEV,6LAAC,qLAAA,CAAA,SAAM,CAAC,MAAM;oCAAC,OAAM;8CAAS;;;;;;8CAC9B,6LAAC,qLAAA,CAAA,SAAM,CAAC,MAAM;oCAAC,OAAM;8CAAW;;;;;;8CAChC,6LAAC,qLAAA,CAAA,SAAM,CAAC,MAAM;oCAAC,OAAM;8CAAY;;;;;;;;;;;;;;;;;;;;;;;YAMtC,uBACC,6LAAC,mLAAA,CAAA,QAAK;gBACJ,SAAQ;gBACR,aAAY;gBACZ,MAAK;gBACL,QAAQ;gBACR,OAAO;oBAAE,cAAc;gBAAO;;;;;;0BAKlC,6LAAC,iLAAA,CAAA,OAAI;0BACH,cAAA,6LAAC,mLAAA,CAAA,QAAK;oBACJ,SAAS;oBACT,YAAY,WAAW,SAAS,EAAE;oBAClC,SAAS;oBACT,YAAY;wBACV,SAAS,OAAO,IAAI;wBACpB,UAAU,OAAO,KAAK;wBACtB,OAAO,WAAW,SAAS;wBAC3B,iBAAiB;wBACjB,iBAAiB;wBACjB,WAAW,CAAC,OAAO,QACjB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC,IAAI,EAAE,MAAM,MAAM,CAAC;oBAC/C;oBACA,UAAU;oBACV,QAAO;oBACP,QAAQ;wBAAE,GAAG;oBAAI;;;;;;;;;;;;;;;;;AAK3B;GAzUwB;;QACP,qIAAA,CAAA,YAAS;QAIsB,+HAAA,CAAA,WAAQ;QACzB,+HAAA,CAAA,oBAAiB;QACnB,+HAAA,CAAA,gBAAa;;;KAPlB"}}, {"offset": {"line": 738, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 759, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/node_modules/%40ant-design/icons-svg/es/asn/ExclamationCircleOutlined.js"], "sourcesContent": ["// This icon file is generated automatically.\nvar ExclamationCircleOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M512 64C264.6 64 64 264.6 64 512s200.6 *********** 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-*********** 372 166.6 ***********.6 372-372 372z\" } }, { \"tag\": \"path\", \"attrs\": { \"d\": \"M464 688a48 48 0 1096 0 48 48 0 10-96 0zm24-112h48c4.4 0 8-3.6 8-8V296c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v272c0 4.4 3.6 8 8 8z\" } }] }, \"name\": \"exclamation-circle\", \"theme\": \"outlined\" };\nexport default ExclamationCircleOutlined;\n"], "names": [], "mappings": "AAAA,6CAA6C;;;;AAC7C,IAAI,4BAA4B;IAAE,QAAQ;QAAE,OAAO;QAAO,SAAS;YAAE,WAAW;YAAiB,aAAa;QAAQ;QAAG,YAAY;YAAC;gBAAE,OAAO;gBAAQ,SAAS;oBAAE,KAAK;gBAAgL;YAAE;YAAG;gBAAE,OAAO;gBAAQ,SAAS;oBAAE,KAAK;gBAAmI;YAAE;SAAE;IAAC;IAAG,QAAQ;IAAsB,SAAS;AAAW;uCAC3iB", "ignoreList": [0]}}, {"offset": {"line": 789, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 795, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/node_modules/%40ant-design/icons/es/icons/ExclamationCircleOutlined.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport ExclamationCircleOutlinedSvg from \"@ant-design/icons-svg/es/asn/ExclamationCircleOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar ExclamationCircleOutlined = function ExclamationCircleOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: ExclamationCircleOutlinedSvg\n  }));\n};\n\n/**![exclamation-circle](data:image/svg+xml;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(ExclamationCircleOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'ExclamationCircleOutlined';\n}\nexport default RefIcon;"], "names": [], "mappings": ";;;AAAA;AACA,oCAAoC;AACpC,2BAA2B;AAE3B;AAEA;AADA;AAWI;;;;;AATJ,IAAI,4BAA4B,SAAS,0BAA0B,KAAK,EAAE,GAAG;IAC3E,OAAO,WAAW,GAAE,8JAAM,aAAa,CAAC,2KAAA,CAAA,UAAQ,EAAE,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,OAAO;QACpE,KAAK;QACL,MAAM,4LAAA,CAAA,UAA4B;IACpC;AACF;AAEA,ipBAAipB,GACjpB,IAAI,UAAU,WAAW,GAAE,8JAAM,UAAU,CAAC;AAC5C,wCAA2C;IACzC,QAAQ,WAAW,GAAG;AACxB;uCACe", "ignoreList": [0]}}, {"offset": {"line": 820, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 866, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/node_modules/%40ant-design/icons-svg/es/asn/MoreOutlined.js"], "sourcesContent": ["// This icon file is generated automatically.\nvar MoreOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M456 231a56 56 0 10112 0 56 56 0 10-112 0zm0 280a56 56 0 10112 0 56 56 0 10-112 0zm0 280a56 56 0 10112 0 56 56 0 10-112 0z\" } }] }, \"name\": \"more\", \"theme\": \"outlined\" };\nexport default MoreOutlined;\n"], "names": [], "mappings": "AAAA,6CAA6C;;;;AAC7C,IAAI,eAAe;IAAE,QAAQ;QAAE,OAAO;QAAO,SAAS;YAAE,WAAW;YAAiB,aAAa;QAAQ;QAAG,YAAY;YAAC;gBAAE,OAAO;gBAAQ,SAAS;oBAAE,KAAK;gBAA6H;YAAE;SAAE;IAAC;IAAG,QAAQ;IAAQ,SAAS;AAAW;uCACpT", "ignoreList": [0]}}, {"offset": {"line": 890, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 896, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/node_modules/%40ant-design/icons/es/icons/MoreOutlined.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport MoreOutlinedSvg from \"@ant-design/icons-svg/es/asn/MoreOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar MoreOutlined = function MoreOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: MoreOutlinedSvg\n  }));\n};\n\n/**![more](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTQ1NiAyMzFhNTYgNTYgMCAxMDExMiAwIDU2IDU2IDAgMTAtMTEyIDB6bTAgMjgwYTU2IDU2IDAgMTAxMTIgMCA1NiA1NiAwIDEwLTExMiAwem0wIDI4MGE1NiA1NiAwIDEwMTEyIDAgNTYgNTYgMCAxMC0xMTIgMHoiIC8+PC9zdmc+) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(MoreOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'MoreOutlined';\n}\nexport default RefIcon;"], "names": [], "mappings": ";;;AAAA;AACA,oCAAoC;AACpC,2BAA2B;AAE3B;AAEA;AADA;AAWI;;;;;AATJ,IAAI,eAAe,SAAS,aAAa,KAAK,EAAE,GAAG;IACjD,OAAO,WAAW,GAAE,8JAAM,aAAa,CAAC,2KAAA,CAAA,UAAQ,EAAE,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,OAAO;QACpE,KAAK;QACL,MAAM,+KAAA,CAAA,UAAe;IACvB;AACF;AAEA,mYAAmY,GACnY,IAAI,UAAU,WAAW,GAAE,8JAAM,UAAU,CAAC;AAC5C,wCAA2C;IACzC,QAAQ,WAAW,GAAG;AACxB;uCACe", "ignoreList": [0]}}, {"offset": {"line": 921, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 947, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/node_modules/%40ant-design/icons-svg/es/asn/UserAddOutlined.js"], "sourcesContent": ["// This icon file is generated automatically.\nvar UserAddOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M678.3 642.4c24.2-13 51.9-20.4 81.4-20.4h.1c3 0 4.4-3.6 2.2-5.6a371.67 371.67 0 00-103.7-65.8c-.4-.2-.8-.3-1.2-.5C719.2 505 759.6 431.7 759.6 349c0-137-110.8-248-247.5-248S264.7 212 264.7 349c0 82.7 40.4 156 102.6 201.1-.4.2-.8.3-1.2.5-44.7 18.9-84.8 46-119.3 80.6a373.42 373.42 0 00-80.4 119.5A373.6 373.6 0 00137 888.8a8 8 0 008 8.2h59.9c4.3 0 7.9-3.5 8-7.8 2-77.2 32.9-149.5 87.6-204.3C357 628.2 432.2 597 512.2 597c56.7 0 111.1 15.7 158 45.1a8.1 8.1 0 008.1.3zM512.2 521c-45.8 0-88.9-17.9-121.4-50.4A171.2 171.2 0 01340.5 349c0-45.9 17.9-89.1 50.3-121.6S466.3 177 512.2 177s88.9 17.9 121.4 50.4A171.2 171.2 0 01683.9 349c0 45.9-17.9 89.1-50.3 121.6C601.1 503.1 558 521 512.2 521zM880 759h-84v-84c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v84h-84c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h84v84c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-84h84c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8z\" } }] }, \"name\": \"user-add\", \"theme\": \"outlined\" };\nexport default UserAddOutlined;\n"], "names": [], "mappings": "AAAA,6CAA6C;;;;AAC7C,IAAI,kBAAkB;IAAE,QAAQ;QAAE,OAAO;QAAO,SAAS;YAAE,WAAW;YAAiB,aAAa;QAAQ;QAAG,YAAY;YAAC;gBAAE,OAAO;gBAAQ,SAAS;oBAAE,KAAK;gBAAm2B;YAAE;SAAE;IAAC;IAAG,QAAQ;IAAY,SAAS;AAAW;uCACjiC", "ignoreList": [0]}}, {"offset": {"line": 971, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 977, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/node_modules/%40ant-design/icons/es/icons/UserAddOutlined.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport UserAddOutlinedSvg from \"@ant-design/icons-svg/es/asn/UserAddOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar UserAddOutlined = function UserAddOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: UserAddOutlinedSvg\n  }));\n};\n\n/**![user-add](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTY3OC4zIDY0Mi40YzI0LjItMTMgNTEuOS0yMC40IDgxLjQtMjAuNGguMWMzIDAgNC40LTMuNiAyLjItNS42YTM3MS42NyAzNzEuNjcgMCAwMC0xMDMuNy02NS44Yy0uNC0uMi0uOC0uMy0xLjItLjVDNzE5LjIgNTA1IDc1OS42IDQzMS43IDc1OS42IDM0OWMwLTEzNy0xMTAuOC0yNDgtMjQ3LjUtMjQ4UzI2NC43IDIxMiAyNjQuNyAzNDljMCA4Mi43IDQwLjQgMTU2IDEwMi42IDIwMS4xLS40LjItLjguMy0xLjIuNS00NC43IDE4LjktODQuOCA0Ni0xMTkuMyA4MC42YTM3My40MiAzNzMuNDIgMCAwMC04MC40IDExOS41QTM3My42IDM3My42IDAgMDAxMzcgODg4LjhhOCA4IDAgMDA4IDguMmg1OS45YzQuMyAwIDcuOS0zLjUgOC03LjggMi03Ny4yIDMyLjktMTQ5LjUgODcuNi0yMDQuM0MzNTcgNjI4LjIgNDMyLjIgNTk3IDUxMi4yIDU5N2M1Ni43IDAgMTExLjEgMTUuNyAxNTggNDUuMWE4LjEgOC4xIDAgMDA4LjEuM3pNNTEyLjIgNTIxYy00NS44IDAtODguOS0xNy45LTEyMS40LTUwLjRBMTcxLjIgMTcxLjIgMCAwMTM0MC41IDM0OWMwLTQ1LjkgMTcuOS04OS4xIDUwLjMtMTIxLjZTNDY2LjMgMTc3IDUxMi4yIDE3N3M4OC45IDE3LjkgMTIxLjQgNTAuNEExNzEuMiAxNzEuMiAwIDAxNjgzLjkgMzQ5YzAgNDUuOS0xNy45IDg5LjEtNTAuMyAxMjEuNkM2MDEuMSA1MDMuMSA1NTggNTIxIDUxMi4yIDUyMXpNODgwIDc1OWgtODR2LTg0YzAtNC40LTMuNi04LTgtOGgtNTZjLTQuNCAwLTggMy42LTggOHY4NGgtODRjLTQuNCAwLTggMy42LTggOHY1NmMwIDQuNCAzLjYgOCA4IDhoODR2ODRjMCA0LjQgMy42IDggOCA4aDU2YzQuNCAwIDgtMy42IDgtOHYtODRoODRjNC40IDAgOC0zLjYgOC04di01NmMwLTQuNC0zLjYtOC04LTh6IiAvPjwvc3ZnPg==) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(UserAddOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'UserAddOutlined';\n}\nexport default RefIcon;"], "names": [], "mappings": ";;;AAAA;AACA,oCAAoC;AACpC,2BAA2B;AAE3B;AAEA;AADA;AAWI;;;;;AATJ,IAAI,kBAAkB,SAAS,gBAAgB,KAAK,EAAE,GAAG;IACvD,OAAO,WAAW,GAAE,8JAAM,aAAa,CAAC,2KAAA,CAAA,UAAQ,EAAE,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,OAAO;QACpE,KAAK;QACL,MAAM,kLAAA,CAAA,UAAkB;IAC1B;AACF;AAEA,u2CAAu2C,GACv2C,IAAI,UAAU,WAAW,GAAE,8JAAM,UAAU,CAAC;AAC5C,wCAA2C;IACzC,QAAQ,WAAW,GAAG;AACxB;uCACe", "ignoreList": [0]}}, {"offset": {"line": 1002, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}