(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["static/chunks/src_adb861._.js", {

"[project]/src/lib/query-error-handler.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Query Error Handler
 * Centralized error handling for TanStack Query
 */ __turbopack_esm__({
    "ErrorType": (()=>ErrorType),
    "createApiError": (()=>createApiError),
    "createGlobalErrorHandler": (()=>createGlobalErrorHandler),
    "errorUtils": (()=>errorUtils),
    "getErrorType": (()=>getErrorType),
    "handleApiError": (()=>handleApiError),
    "isApiError": (()=>isApiError),
    "parseErrorResponse": (()=>parseErrorResponse),
    "setupQueryErrorHandling": (()=>setupQueryErrorHandling)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var ErrorType = /*#__PURE__*/ function(ErrorType) {
    ErrorType["NETWORK"] = "NETWORK";
    ErrorType["AUTHENTICATION"] = "AUTHENTICATION";
    ErrorType["AUTHORIZATION"] = "AUTHORIZATION";
    ErrorType["VALIDATION"] = "VALIDATION";
    ErrorType["SERVER"] = "SERVER";
    ErrorType["UNKNOWN"] = "UNKNOWN";
    return ErrorType;
}({});
function getErrorType(status) {
    if (status === 401) return "AUTHENTICATION";
    if (status === 403) return "AUTHORIZATION";
    if (status >= 400 && status < 500) return "VALIDATION";
    if (status >= 500) return "SERVER";
    if (status === 0) return "NETWORK";
    return "UNKNOWN";
}
function createApiError(status, statusText, message, details) {
    return {
        status,
        statusText,
        message,
        details,
        timestamp: new Date().toISOString()
    };
}
async function parseErrorResponse(response) {
    let message = response.statusText || 'An error occurred';
    let details = null;
    try {
        const errorData = await response.json();
        message = errorData.message || errorData.error || message;
        details = errorData.details || errorData;
    } catch  {
    // If response is not JSON, use status text
    }
    return createApiError(response.status, response.statusText, message, details);
}
function createGlobalErrorHandler() {
    return (error)=>{
        console.error('[Query Error]', error);
        // Handle different types of errors
        if (error instanceof Error) {
            // Network errors, parsing errors, etc.
            console.error('Error details:', {
                name: error.name,
                message: error.message,
                stack: error.stack
            });
        }
        // Handle API errors
        if (isApiError(error)) {
            handleApiError(error);
        }
    };
}
function isApiError(error) {
    return typeof error === 'object' && error !== null && 'status' in error && 'message' in error;
}
function handleApiError(error) {
    const errorType = getErrorType(error.status);
    switch(errorType){
        case "AUTHENTICATION":
            handleAuthenticationError(error);
            break;
        case "AUTHORIZATION":
            handleAuthorizationError(error);
            break;
        case "VALIDATION":
            handleValidationError(error);
            break;
        case "SERVER":
            handleServerError(error);
            break;
        case "NETWORK":
            handleNetworkError(error);
            break;
        default:
            handleUnknownError(error);
    }
}
/**
 * Handle authentication errors (401)
 */ function handleAuthenticationError(error) {
    console.warn('[Auth Error]', error.message);
    // In development mode, authentication is disabled
    if ("TURBOPACK compile-time truthy", 1) {
        console.log('[Dev Mode] Authentication error ignored');
        return;
    }
// In production, redirect to login or refresh token
// This will be implemented when auth system is ready
}
/**
 * Handle authorization errors (403)
 */ function handleAuthorizationError(error) {
    console.warn('[Authorization Error]', error.message);
// Show user-friendly message about insufficient permissions
// This will be integrated with notification system
}
/**
 * Handle validation errors (400-499)
 */ function handleValidationError(error) {
    console.warn('[Validation Error]', error.message);
// These are usually handled by individual components
// Global handler just logs for debugging
}
/**
 * Handle server errors (500+)
 */ function handleServerError(error) {
    console.error('[Server Error]', error.message);
// Show generic error message to user
// Log detailed error for debugging
}
/**
 * Handle network errors
 */ function handleNetworkError(error) {
    console.error('[Network Error]', error.message);
// Show network connectivity message
// Suggest retry or check connection
}
/**
 * Handle unknown errors
 */ function handleUnknownError(error) {
    console.error('[Unknown Error]', error);
// Show generic error message
// Log for investigation
}
function setupQueryErrorHandling(queryClient) {
    // Set up global error handler
    queryClient.setDefaultOptions({
        queries: {
            ...queryClient.getDefaultOptions().queries,
            throwOnError: false
        },
        mutations: {
            ...queryClient.getDefaultOptions().mutations,
            throwOnError: false
        }
    });
    // Set up global error handler
    queryClient.setMutationDefaults([
        'mutation'
    ], {
        onError: createGlobalErrorHandler()
    });
}
const errorUtils = {
    /**
   * Check if error should trigger retry
   */ shouldRetry: (error)=>{
        if (isApiError(error)) {
            const errorType = getErrorType(error.status);
            // Don't retry client errors (4xx)
            return errorType !== "VALIDATION" && errorType !== "AUTHENTICATION" && errorType !== "AUTHORIZATION";
        }
        return true; // Retry network and unknown errors
    },
    /**
   * Get user-friendly error message
   */ getUserMessage: (error)=>{
        if (isApiError(error)) {
            const errorType = getErrorType(error.status);
            switch(errorType){
                case "AUTHENTICATION":
                    return 'Please log in to continue';
                case "AUTHORIZATION":
                    return 'You do not have permission to perform this action';
                case "VALIDATION":
                    return error.message || 'Please check your input and try again';
                case "SERVER":
                    return 'Server error occurred. Please try again later';
                case "NETWORK":
                    return 'Network error. Please check your connection';
                default:
                    return 'An unexpected error occurred';
            }
        }
        return 'An unexpected error occurred';
    },
    /**
   * Check if error is retryable
   */ isRetryable: (error)=>{
        if (isApiError(error)) {
            return error.status >= 500 || error.status === 0;
        }
        return true;
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/query-utils.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Query Utilities and Helpers
 * Common utilities for working with TanStack Query
 */ __turbopack_esm__({
    "cacheUtils": (()=>cacheUtils),
    "devUtils": (()=>devUtils),
    "mutationOptionsBuilder": (()=>mutationOptionsBuilder),
    "queryErrorUtils": (()=>queryErrorUtils),
    "queryOptionsBuilder": (()=>queryOptionsBuilder),
    "queryStateUtils": (()=>queryStateUtils),
    "typeGuards": (()=>typeGuards)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/lib/query-client.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$error$2d$handler$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/lib/query-error-handler.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
;
;
const queryOptionsBuilder = {
    /**
   * Build options for real-time data (short cache)
   */ realTime: (options)=>({
            staleTime: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUERY_CONFIG"].STALE_TIME.SHORT,
            gcTime: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUERY_CONFIG"].STALE_TIME.MEDIUM,
            refetchInterval: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUERY_CONFIG"].REFETCH_INTERVAL.FAST,
            ...options
        }),
    /**
   * Build options for static data (long cache)
   */ static: (options)=>({
            staleTime: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUERY_CONFIG"].STALE_TIME.VERY_LONG,
            gcTime: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUERY_CONFIG"].STALE_TIME.VERY_LONG,
            refetchOnWindowFocus: false,
            refetchOnReconnect: false,
            ...options
        }),
    /**
   * Build options for user-specific data
   */ userSpecific: (options)=>({
            staleTime: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUERY_CONFIG"].STALE_TIME.MEDIUM,
            gcTime: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUERY_CONFIG"].STALE_TIME.LONG,
            refetchOnWindowFocus: true,
            ...options
        }),
    /**
   * Build options for background sync data
   */ backgroundSync: (options)=>({
            staleTime: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUERY_CONFIG"].STALE_TIME.LONG,
            gcTime: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUERY_CONFIG"].STALE_TIME.VERY_LONG,
            refetchInterval: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUERY_CONFIG"].REFETCH_INTERVAL.SLOW,
            refetchIntervalInBackground: true,
            ...options
        })
};
const mutationOptionsBuilder = {
    /**
   * Build options for optimistic updates
   */ optimistic: (options)=>({
            retry: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUERY_CONFIG"].RETRY.ONCE,
            ...options
        }),
    /**
   * Build options for critical operations
   */ critical: (options)=>({
            retry: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUERY_CONFIG"].RETRY.DEFAULT,
            retryDelay: (attemptIndex)=>Math.min(1000 * 2 ** attemptIndex, 30000),
            ...options
        }),
    /**
   * Build options for background operations
   */ background: (options)=>({
            retry: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUERY_CONFIG"].RETRY.TWICE,
            ...options
        })
};
const cacheUtils = {
    /**
   * Invalidate queries by pattern
   */ invalidateByPattern: async (queryClient, pattern)=>{
        await queryClient.invalidateQueries({
            queryKey: pattern
        });
    },
    /**
   * Remove queries by pattern
   */ removeByPattern: (queryClient, pattern)=>{
        queryClient.removeQueries({
            queryKey: pattern
        });
    },
    /**
   * Update query data
   */ updateQueryData: (queryClient, queryKey, updater)=>{
        queryClient.setQueryData(queryKey, updater);
    },
    /**
   * Optimistically update list data
   */ optimisticListUpdate: (queryClient, queryKey, item, operation)=>{
        queryClient.setQueryData(queryKey, (oldData)=>{
            if (!oldData) return operation === 'add' ? [
                item
            ] : [];
            switch(operation){
                case 'add':
                    return [
                        ...oldData,
                        item
                    ];
                case 'update':
                    return oldData.map((existing)=>existing.id === item.id ? {
                            ...existing,
                            ...item
                        } : existing);
                case 'remove':
                    return oldData.filter((existing)=>existing.id !== item.id);
                default:
                    return oldData;
            }
        });
    },
    /**
   * Optimistically update paginated data
   */ optimisticPaginatedUpdate: (queryClient, queryKey, item, operation)=>{
        queryClient.setQueryData(queryKey, (oldData)=>{
            if (!oldData) return oldData;
            const updatedData = cacheUtils.optimisticListUpdate(queryClient, [
                'temp'
            ], item, operation);
            return {
                ...oldData,
                data: updatedData || oldData.data
            };
        });
    }
};
const queryStateUtils = {
    /**
   * Check if any queries are loading
   */ isAnyLoading: (queryClient, queryKeys)=>{
        return queryKeys.some((key)=>{
            const query = queryClient.getQueryState(key);
            return query?.fetchStatus === 'fetching';
        });
    },
    /**
   * Check if any queries have errors
   */ hasAnyErrors: (queryClient, queryKeys)=>{
        return queryKeys.some((key)=>{
            const query = queryClient.getQueryState(key);
            return query?.status === 'error';
        });
    },
    /**
   * Get all errors from queries
   */ getAllErrors: (queryClient, queryKeys)=>{
        return queryKeys.map((key)=>{
            const query = queryClient.getQueryState(key);
            return query?.error;
        }).filter((error)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$error$2d$handler$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isApiError"])(error));
    },
    /**
   * Check if data is stale
   */ isStale: (queryClient, queryKey)=>{
        const query = queryClient.getQueryState(queryKey);
        return query ? query.isStale : true;
    }
};
const devUtils = {
    /**
   * Log query cache state
   */ logCacheState: (queryClient)=>{
        if ("TURBOPACK compile-time truthy", 1) {
            const cache = queryClient.getQueryCache();
            console.log('[Query Cache]', {
                queries: cache.getAll().length,
                state: cache.getAll().map((query)=>({
                        key: query.queryKey,
                        status: query.state.status,
                        dataUpdatedAt: query.state.dataUpdatedAt,
                        error: query.state.error
                    }))
            });
        }
    },
    /**
   * Clear all cache (development only)
   */ clearAllCache: (queryClient)=>{
        if ("TURBOPACK compile-time truthy", 1) {
            queryClient.clear();
            console.log('[Dev] Query cache cleared');
        }
    },
    /**
   * Force refetch all queries (development only)
   */ refetchAll: async (queryClient)=>{
        if ("TURBOPACK compile-time truthy", 1) {
            await queryClient.refetchQueries();
            console.log('[Dev] All queries refetched');
        }
    }
};
const queryErrorUtils = {
    /**
   * Handle query error with user feedback
   */ handleQueryError: (error, context)=>{
        const message = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$error$2d$handler$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["errorUtils"].getUserMessage(error);
        console.error(`[Query Error${context ? ` - ${context}` : ''}]`, error);
        // This will be integrated with notification system
        // For now, just log the user-friendly message
        console.log('[User Message]', message);
        return message;
    },
    /**
   * Handle mutation error with user feedback
   */ handleMutationError: (error, context)=>{
        const message = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$error$2d$handler$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["errorUtils"].getUserMessage(error);
        console.error(`[Mutation Error${context ? ` - ${context}` : ''}]`, error);
        // This will be integrated with notification system
        // For now, just log the user-friendly message
        console.log('[User Message]', message);
        return message;
    }
};
const typeGuards = {
    /**
   * Check if response is a valid API response
   */ isApiResponse: (data)=>{
        return typeof data === 'object' && data !== null && 'data' in data && 'success' in data && 'timestamp' in data;
    },
    /**
   * Check if response is a paginated response
   */ isPaginatedResponse: (data)=>{
        return typeGuards.isApiResponse(data) && 'pagination' in data && typeof data.pagination === 'object';
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/hooks/api/base-hooks.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Base API Hooks
 * Foundation hooks for API operations with TanStack Query
 */ __turbopack_esm__({
    "useApiHookUtils": (()=>useApiHookUtils),
    "useApiStatus": (()=>useApiStatus),
    "useBackgroundMutation": (()=>useBackgroundMutation),
    "useBackgroundSyncQuery": (()=>useBackgroundSyncQuery),
    "useBaseMutation": (()=>useBaseMutation),
    "useBaseQuery": (()=>useBaseQuery),
    "useCriticalMutation": (()=>useCriticalMutation),
    "useOptimisticMutation": (()=>useOptimisticMutation),
    "usePaginatedQuery": (()=>usePaginatedQuery),
    "useRealTimeQuery": (()=>useRealTimeQuery),
    "useStaticQuery": (()=>useStaticQuery)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/lib/query-utils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$error$2d$handler$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/lib/query-error-handler.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/@tanstack/react-query/build/modern/useQuery.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/@tanstack/react-query/build/modern/useMutation.js [app-client] (ecmascript)");
var _s = __turbopack_refresh__.signature(), _s1 = __turbopack_refresh__.signature(), _s2 = __turbopack_refresh__.signature(), _s3 = __turbopack_refresh__.signature(), _s4 = __turbopack_refresh__.signature(), _s5 = __turbopack_refresh__.signature(), _s6 = __turbopack_refresh__.signature(), _s7 = __turbopack_refresh__.signature(), _s8 = __turbopack_refresh__.signature(), _s9 = __turbopack_refresh__.signature(), _s10 = __turbopack_refresh__.signature();
'use client';
;
;
;
function useBaseQuery(queryKey, queryFn, options) {
    _s();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey,
        queryFn,
        ...options,
        onError: {
            "useBaseQuery.useQuery": (error)=>{
                console.error(`[Query Error] ${queryKey.join(' → ')}:`, error);
                if (options?.onError) {
                    options.onError(error);
                }
            }
        }["useBaseQuery.useQuery"]
    });
}
_s(useBaseQuery, "4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"]
    ];
});
function useBaseMutation(mutationFn, options) {
    _s1();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn,
        ...options,
        onError: {
            "useBaseMutation.useMutation": (error, variables, context)=>{
                console.error('[Mutation Error]:', error);
                if (options?.onError) {
                    options.onError(error, variables, context);
                }
            }
        }["useBaseMutation.useMutation"],
        onSuccess: {
            "useBaseMutation.useMutation": (data, variables, context)=>{
                if (options?.onSuccess) {
                    options.onSuccess(data, variables, context);
                }
            }
        }["useBaseMutation.useMutation"]
    });
}
_s1(useBaseMutation, "YK0wzM21ECnncaq5SECwU+/SVdQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
function usePaginatedQuery(queryKey, queryFn, options) {
    _s2();
    return useBaseQuery(queryKey, queryFn, {
        ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryOptionsBuilder"].userSpecific(),
        ...options
    });
}
_s2(usePaginatedQuery, "KQLwJ9Hz54v54vEsUujw5Lh1nqo=", false, function() {
    return [
        useBaseQuery
    ];
});
function useRealTimeQuery(queryKey, queryFn, options) {
    _s3();
    return useBaseQuery(queryKey, queryFn, {
        ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryOptionsBuilder"].realTime(),
        ...options
    });
}
_s3(useRealTimeQuery, "KQLwJ9Hz54v54vEsUujw5Lh1nqo=", false, function() {
    return [
        useBaseQuery
    ];
});
function useStaticQuery(queryKey, queryFn, options) {
    _s4();
    return useBaseQuery(queryKey, queryFn, {
        ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryOptionsBuilder"].static(),
        ...options
    });
}
_s4(useStaticQuery, "KQLwJ9Hz54v54vEsUujw5Lh1nqo=", false, function() {
    return [
        useBaseQuery
    ];
});
function useBackgroundSyncQuery(queryKey, queryFn, options) {
    _s5();
    return useBaseQuery(queryKey, queryFn, {
        ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryOptionsBuilder"].backgroundSync(),
        ...options
    });
}
_s5(useBackgroundSyncQuery, "KQLwJ9Hz54v54vEsUujw5Lh1nqo=", false, function() {
    return [
        useBaseQuery
    ];
});
function useOptimisticMutation(mutationFn, options) {
    _s6();
    return useBaseMutation(mutationFn, {
        ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mutationOptionsBuilder"].optimistic(),
        ...options
    });
}
_s6(useOptimisticMutation, "cbsa5fN2Kd00W6TK3HZASQia1Kg=", false, function() {
    return [
        useBaseMutation
    ];
});
function useCriticalMutation(mutationFn, options) {
    _s7();
    return useBaseMutation(mutationFn, {
        ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mutationOptionsBuilder"].critical(),
        ...options
    });
}
_s7(useCriticalMutation, "cbsa5fN2Kd00W6TK3HZASQia1Kg=", false, function() {
    return [
        useBaseMutation
    ];
});
function useBackgroundMutation(mutationFn, options) {
    _s8();
    return useBaseMutation(mutationFn, {
        ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mutationOptionsBuilder"].background(),
        ...options
    });
}
_s8(useBackgroundMutation, "cbsa5fN2Kd00W6TK3HZASQia1Kg=", false, function() {
    return [
        useBaseMutation
    ];
});
const useApiHookUtils = ()=>{
    _s9();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return {
        /**
     * Invalidate queries by pattern
     */ invalidateQueries: (queryKey)=>{
            return queryClient.invalidateQueries({
                queryKey
            });
        },
        /**
     * Remove queries from cache
     */ removeQueries: (queryKey)=>{
            return queryClient.removeQueries({
                queryKey
            });
        },
        /**
     * Update query data optimistically
     */ updateQueryData: (queryKey, updater)=>{
            queryClient.setQueryData(queryKey, updater);
        },
        /**
     * Get cached query data
     */ getQueryData: (queryKey)=>{
            return queryClient.getQueryData(queryKey);
        },
        /**
     * Prefetch query data
     */ prefetchQuery: (queryKey, queryFn)=>{
            return queryClient.prefetchQuery({
                queryKey,
                queryFn
            });
        },
        /**
     * Check if query is loading
     */ isQueryLoading: (queryKey)=>{
            const query = queryClient.getQueryState(queryKey);
            return query?.fetchStatus === 'fetching';
        },
        /**
     * Get query error
     */ getQueryError: (queryKey)=>{
            const query = queryClient.getQueryState(queryKey);
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$error$2d$handler$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isApiError"])(query?.error) ? query.error : null;
        },
        /**
     * Handle API error with user feedback
     */ handleApiError: (error, context)=>{
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$error$2d$handler$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["errorUtils"].getUserMessage(error);
        }
    };
};
_s9(useApiHookUtils, "4R+oYVB2Uc11P7bp1KcuhpkfaTw=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"]
    ];
});
const useApiStatus = ()=>{
    _s10();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return {
        /**
     * Get overall API status
     */ getApiStatus: ()=>{
            const queries = queryClient.getQueryCache().getAll();
            const totalQueries = queries.length;
            const loadingQueries = queries.filter((q)=>q.state.fetchStatus === 'fetching').length;
            const errorQueries = queries.filter((q)=>q.state.status === 'error').length;
            const successQueries = queries.filter((q)=>q.state.status === 'success').length;
            return {
                total: totalQueries,
                loading: loadingQueries,
                error: errorQueries,
                success: successQueries,
                isLoading: loadingQueries > 0,
                hasErrors: errorQueries > 0,
                healthScore: totalQueries > 0 ? successQueries / totalQueries * 100 : 100
            };
        },
        /**
     * Get queries by status
     */ getQueriesByStatus: (status)=>{
            const queries = queryClient.getQueryCache().getAll();
            switch(status){
                case 'loading':
                    return queries.filter((q)=>q.state.fetchStatus === 'fetching');
                case 'error':
                    return queries.filter((q)=>q.state.status === 'error');
                case 'success':
                    return queries.filter((q)=>q.state.status === 'success');
                case 'idle':
                    return queries.filter((q)=>q.state.fetchStatus === 'idle');
                default:
                    return [];
            }
        },
        /**
     * Clear all errors
     */ clearAllErrors: ()=>{
            const errorQueries = queryClient.getQueryCache().getAll().filter((q)=>q.state.status === 'error');
            errorQueries.forEach((query)=>{
                queryClient.resetQueries({
                    queryKey: query.queryKey
                });
            });
        }
    };
};
_s10(useApiStatus, "4R+oYVB2Uc11P7bp1KcuhpkfaTw=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"]
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/hooks/api/auth-hooks.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Authentication API Hooks
 * Hooks for system authentication operations
 */ __turbopack_esm__({
    "useAuth": (()=>useAuth),
    "useChangePassword": (()=>useChangePassword),
    "useCreateUser": (()=>useCreateUser),
    "useLogin": (()=>useLogin),
    "useLogout": (()=>useLogout),
    "useLogoutAll": (()=>useLogoutAll),
    "useProfile": (()=>useProfile),
    "useSystemUser": (()=>useSystemUser),
    "useSystemUsers": (()=>useSystemUsers),
    "useUpdateProfile": (()=>useUpdateProfile)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/lib/query-client.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/hooks/api/base-hooks.ts [app-client] (ecmascript)");
var _s = __turbopack_refresh__.signature(), _s1 = __turbopack_refresh__.signature(), _s2 = __turbopack_refresh__.signature(), _s3 = __turbopack_refresh__.signature(), _s4 = __turbopack_refresh__.signature(), _s5 = __turbopack_refresh__.signature(), _s6 = __turbopack_refresh__.signature(), _s7 = __turbopack_refresh__.signature(), _s8 = __turbopack_refresh__.signature(), _s9 = __turbopack_refresh__.signature();
'use client';
;
;
function useLogin() {
    _s();
    const { invalidateQueries } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiHookUtils"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBaseMutation"])({
        "useLogin.useBaseMutation": async (credentials)=>{
            const response = await fetch('/api/system-auth/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(credentials)
            });
            if (!response.ok) {
                throw new Error(`Login failed: ${response.statusText}`);
            }
            return response.json();
        }
    }["useLogin.useBaseMutation"], {
        onSuccess: {
            "useLogin.useBaseMutation": (data)=>{
                // Invalidate auth queries on successful login
                invalidateQueries(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryKeys"].auth.all);
                console.log('✅ Login successful:', data.user.username);
            }
        }["useLogin.useBaseMutation"],
        onError: {
            "useLogin.useBaseMutation": (error)=>{
                console.error('❌ Login failed:', error);
            }
        }["useLogin.useBaseMutation"]
    });
}
_s(useLogin, "u64JDqrckp6d8UXRWRy4SgQqcdg=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiHookUtils"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBaseMutation"]
    ];
});
function useLogout() {
    _s1();
    const { invalidateQueries } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiHookUtils"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBaseMutation"])({
        "useLogout.useBaseMutation": async ()=>{
            const response = await fetch('/api/system-auth/logout', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            if (!response.ok) {
                throw new Error(`Logout failed: ${response.statusText}`);
            }
            return response.json();
        }
    }["useLogout.useBaseMutation"], {
        onSuccess: {
            "useLogout.useBaseMutation": ()=>{
                // Clear all auth-related queries on logout
                invalidateQueries(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryKeys"].auth.all);
                console.log('✅ Logout successful');
            }
        }["useLogout.useBaseMutation"],
        onError: {
            "useLogout.useBaseMutation": (error)=>{
                console.error('❌ Logout failed:', error);
            }
        }["useLogout.useBaseMutation"]
    });
}
_s1(useLogout, "u64JDqrckp6d8UXRWRy4SgQqcdg=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiHookUtils"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBaseMutation"]
    ];
});
function useLogoutAll() {
    _s2();
    const { invalidateQueries } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiHookUtils"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBaseMutation"])({
        "useLogoutAll.useBaseMutation": async ()=>{
            const response = await fetch('/api/system-auth/logout-all', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            if (!response.ok) {
                throw new Error(`Logout all failed: ${response.statusText}`);
            }
            return response.json();
        }
    }["useLogoutAll.useBaseMutation"], {
        onSuccess: {
            "useLogoutAll.useBaseMutation": ()=>{
                // Clear all auth-related queries on logout all
                invalidateQueries(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryKeys"].auth.all);
                console.log('✅ Logout all successful');
            }
        }["useLogoutAll.useBaseMutation"],
        onError: {
            "useLogoutAll.useBaseMutation": (error)=>{
                console.error('❌ Logout all failed:', error);
            }
        }["useLogoutAll.useBaseMutation"]
    });
}
_s2(useLogoutAll, "u64JDqrckp6d8UXRWRy4SgQqcdg=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiHookUtils"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBaseMutation"]
    ];
});
function useProfile() {
    _s3();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBaseQuery"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryKeys"].auth.profile(), {
        "useProfile.useBaseQuery": async ()=>{
            const response = await fetch('/api/system-auth/profile');
            if (!response.ok) {
                throw new Error(`Failed to fetch profile: ${response.statusText}`);
            }
            return response.json();
        }
    }["useProfile.useBaseQuery"], {
        enabled: false,
        staleTime: 5 * 60 * 1000,
        retry: {
            "useProfile.useBaseQuery": (failureCount, error)=>{
                // Don't retry on 401 (unauthorized)
                if (error?.status === 401) return false;
                return failureCount < 2;
            }
        }["useProfile.useBaseQuery"]
    });
}
_s3(useProfile, "KQLwJ9Hz54v54vEsUujw5Lh1nqo=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBaseQuery"]
    ];
});
function useUpdateProfile() {
    _s4();
    const { invalidateQueries } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiHookUtils"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBaseMutation"])({
        "useUpdateProfile.useBaseMutation": async (data)=>{
            const response = await fetch('/api/system-auth/profile', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            });
            if (!response.ok) {
                throw new Error(`Failed to update profile: ${response.statusText}`);
            }
            return response.json();
        }
    }["useUpdateProfile.useBaseMutation"], {
        onSuccess: {
            "useUpdateProfile.useBaseMutation": ()=>{
                // Invalidate profile query to refetch updated data
                invalidateQueries(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryKeys"].auth.profile());
                console.log('✅ Profile updated successfully');
            }
        }["useUpdateProfile.useBaseMutation"],
        onError: {
            "useUpdateProfile.useBaseMutation": (error)=>{
                console.error('❌ Profile update failed:', error);
            }
        }["useUpdateProfile.useBaseMutation"]
    });
}
_s4(useUpdateProfile, "u64JDqrckp6d8UXRWRy4SgQqcdg=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiHookUtils"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBaseMutation"]
    ];
});
function useChangePassword() {
    _s5();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBaseMutation"])({
        "useChangePassword.useBaseMutation": async (data)=>{
            const response = await fetch('/api/system-auth/change-password', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            });
            if (!response.ok) {
                throw new Error(`Failed to change password: ${response.statusText}`);
            }
            return response.json();
        }
    }["useChangePassword.useBaseMutation"], {
        onSuccess: {
            "useChangePassword.useBaseMutation": ()=>{
                console.log('✅ Password changed successfully');
            }
        }["useChangePassword.useBaseMutation"],
        onError: {
            "useChangePassword.useBaseMutation": (error)=>{
                console.error('❌ Password change failed:', error);
            }
        }["useChangePassword.useBaseMutation"]
    });
}
_s5(useChangePassword, "cbsa5fN2Kd00W6TK3HZASQia1Kg=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBaseMutation"]
    ];
});
function useCreateUser() {
    _s6();
    const { invalidateQueries } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiHookUtils"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBaseMutation"])({
        "useCreateUser.useBaseMutation": async (data)=>{
            const response = await fetch('/api/system-auth/users', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            });
            if (!response.ok) {
                throw new Error(`Failed to create user: ${response.statusText}`);
            }
            return response.json();
        }
    }["useCreateUser.useBaseMutation"], {
        onSuccess: {
            "useCreateUser.useBaseMutation": ()=>{
                // Invalidate users list to show new user
                invalidateQueries(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryKeys"].auth.users());
                console.log('✅ User created successfully');
            }
        }["useCreateUser.useBaseMutation"],
        onError: {
            "useCreateUser.useBaseMutation": (error)=>{
                console.error('❌ User creation failed:', error);
            }
        }["useCreateUser.useBaseMutation"]
    });
}
_s6(useCreateUser, "u64JDqrckp6d8UXRWRy4SgQqcdg=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiHookUtils"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBaseMutation"]
    ];
});
function useSystemUsers() {
    _s7();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBaseQuery"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryKeys"].auth.users(), {
        "useSystemUsers.useBaseQuery": async ()=>{
            const response = await fetch('/api/system-auth/users');
            if (!response.ok) {
                throw new Error(`Failed to fetch users: ${response.statusText}`);
            }
            return response.json();
        }
    }["useSystemUsers.useBaseQuery"], {
        enabled: false,
        staleTime: 2 * 60 * 1000
    });
}
_s7(useSystemUsers, "KQLwJ9Hz54v54vEsUujw5Lh1nqo=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBaseQuery"]
    ];
});
function useSystemUser(userId) {
    _s8();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBaseQuery"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryKeys"].auth.user(userId), {
        "useSystemUser.useBaseQuery": async ()=>{
            const response = await fetch(`/api/system-auth/users/${userId}`);
            if (!response.ok) {
                throw new Error(`Failed to fetch user: ${response.statusText}`);
            }
            return response.json();
        }
    }["useSystemUser.useBaseQuery"], {
        enabled: !!userId,
        staleTime: 5 * 60 * 1000
    });
}
_s8(useSystemUser, "KQLwJ9Hz54v54vEsUujw5Lh1nqo=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBaseQuery"]
    ];
});
function useAuth() {
    _s9();
    const login = useLogin();
    const logout = useLogout();
    const logoutAll = useLogoutAll();
    const profile = useProfile();
    const updateProfile = useUpdateProfile();
    const changePassword = useChangePassword();
    const createUser = useCreateUser();
    return {
        // Queries
        profile,
        // Mutations
        login,
        logout,
        logoutAll,
        updateProfile,
        changePassword,
        createUser,
        // Computed state
        isAuthenticated: !!profile.data,
        user: profile.data,
        isLoading: profile.isLoading || login.isPending || logout.isPending,
        error: profile.error || login.error || logout.error,
        // Actions
        loginUser: login.mutate,
        logoutUser: logout.mutate,
        logoutAllDevices: logoutAll.mutate,
        updateUserProfile: updateProfile.mutate,
        changeUserPassword: changePassword.mutate,
        createNewUser: createUser.mutate
    };
}
_s9(useAuth, "Vh8Wc9jVPdrmNcsWGzFAy7ZTC2E=", false, function() {
    return [
        useLogin,
        useLogout,
        useLogoutAll,
        useProfile,
        useUpdateProfile,
        useChangePassword,
        useCreateUser
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/hooks/api/football-hooks.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Football Data API Hooks
 * Hooks for football leagues, teams, and fixtures operations
 */ __turbopack_esm__({
    "useDailySync": (()=>useDailySync),
    "useFixture": (()=>useFixture),
    "useFixtures": (()=>useFixtures),
    "useFootball": (()=>useFootball),
    "useLeague": (()=>useLeague),
    "useLeagues": (()=>useLeagues),
    "useLiveFixtures": (()=>useLiveFixtures),
    "useSyncFixtures": (()=>useSyncFixtures),
    "useSyncStatus": (()=>useSyncStatus),
    "useTeam": (()=>useTeam),
    "useTeams": (()=>useTeams),
    "useTodayFixtures": (()=>useTodayFixtures),
    "useUpcomingFixtures": (()=>useUpcomingFixtures)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/lib/query-client.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/hooks/api/base-hooks.ts [app-client] (ecmascript)");
var _s = __turbopack_refresh__.signature(), _s1 = __turbopack_refresh__.signature(), _s2 = __turbopack_refresh__.signature(), _s3 = __turbopack_refresh__.signature(), _s4 = __turbopack_refresh__.signature(), _s5 = __turbopack_refresh__.signature(), _s6 = __turbopack_refresh__.signature(), _s7 = __turbopack_refresh__.signature(), _s8 = __turbopack_refresh__.signature(), _s9 = __turbopack_refresh__.signature(), _s10 = __turbopack_refresh__.signature(), _s11 = __turbopack_refresh__.signature(), _s12 = __turbopack_refresh__.signature();
'use client';
;
;
function useLeagues(params) {
    _s();
    const queryParams = new URLSearchParams();
    if (params?.page) queryParams.set('page', params.page.toString());
    if (params?.limit) queryParams.set('limit', params.limit.toString());
    if (params?.country) queryParams.set('country', params.country);
    if (params?.isActive !== undefined) queryParams.set('isActive', params.isActive.toString());
    if (params?.query) queryParams.set('query', params.query);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePaginatedQuery"])([
        ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryKeys"].football.leagues(),
        params
    ], {
        "useLeagues.usePaginatedQuery": async ()=>{
            const response = await fetch(`/api/football/leagues?${queryParams.toString()}`);
            if (!response.ok) {
                throw new Error(`Failed to fetch leagues: ${response.statusText}`);
            }
            return response.json();
        }
    }["useLeagues.usePaginatedQuery"], {
        staleTime: 10 * 60 * 1000
    });
}
_s(useLeagues, "8dRrW1kK8lRPrshcMFzQabx/L7w=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePaginatedQuery"]
    ];
});
function useLeague(leagueId) {
    _s1();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBaseQuery"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryKeys"].football.league(leagueId), {
        "useLeague.useBaseQuery": async ()=>{
            const response = await fetch(`/api/football/leagues/${leagueId}`);
            if (!response.ok) {
                throw new Error(`Failed to fetch league: ${response.statusText}`);
            }
            return response.json();
        }
    }["useLeague.useBaseQuery"], {
        enabled: !!leagueId,
        staleTime: 10 * 60 * 1000
    });
}
_s1(useLeague, "KQLwJ9Hz54v54vEsUujw5Lh1nqo=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBaseQuery"]
    ];
});
function useTeams(params) {
    _s2();
    const queryParams = new URLSearchParams();
    if (params?.page) queryParams.set('page', params.page.toString());
    if (params?.limit) queryParams.set('limit', params.limit.toString());
    if (params?.leagueId) queryParams.set('leagueId', params.leagueId);
    if (params?.country) queryParams.set('country', params.country);
    if (params?.query) queryParams.set('query', params.query);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePaginatedQuery"])([
        ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryKeys"].football.teams(),
        params
    ], {
        "useTeams.usePaginatedQuery": async ()=>{
            const response = await fetch(`/api/football/teams?${queryParams.toString()}`);
            if (!response.ok) {
                throw new Error(`Failed to fetch teams: ${response.statusText}`);
            }
            return response.json();
        }
    }["useTeams.usePaginatedQuery"], {
        staleTime: 5 * 60 * 1000
    });
}
_s2(useTeams, "8dRrW1kK8lRPrshcMFzQabx/L7w=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePaginatedQuery"]
    ];
});
function useTeam(teamId) {
    _s3();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBaseQuery"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryKeys"].football.team(teamId), {
        "useTeam.useBaseQuery": async ()=>{
            const response = await fetch(`/api/football/teams/${teamId}`);
            if (!response.ok) {
                throw new Error(`Failed to fetch team: ${response.statusText}`);
            }
            return response.json();
        }
    }["useTeam.useBaseQuery"], {
        enabled: !!teamId,
        staleTime: 5 * 60 * 1000
    });
}
_s3(useTeam, "KQLwJ9Hz54v54vEsUujw5Lh1nqo=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBaseQuery"]
    ];
});
function useFixtures(params) {
    _s4();
    const queryParams = new URLSearchParams();
    if (params?.page) queryParams.set('page', params.page.toString());
    if (params?.limit) queryParams.set('limit', params.limit.toString());
    if (params?.leagueId) queryParams.set('leagueId', params.leagueId);
    if (params?.teamId) queryParams.set('teamId', params.teamId);
    if (params?.status) queryParams.set('status', params.status);
    if (params?.dateFrom) queryParams.set('dateFrom', params.dateFrom);
    if (params?.dateTo) queryParams.set('dateTo', params.dateTo);
    if (params?.query) queryParams.set('query', params.query);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePaginatedQuery"])([
        ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryKeys"].football.fixtures(),
        params
    ], {
        "useFixtures.usePaginatedQuery": async ()=>{
            const response = await fetch(`/api/football/fixtures?${queryParams.toString()}`);
            if (!response.ok) {
                throw new Error(`Failed to fetch fixtures: ${response.statusText}`);
            }
            return response.json();
        }
    }["useFixtures.usePaginatedQuery"], {
        staleTime: 1 * 60 * 1000
    });
}
_s4(useFixtures, "8dRrW1kK8lRPrshcMFzQabx/L7w=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePaginatedQuery"]
    ];
});
function useFixture(fixtureId) {
    _s5();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBaseQuery"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryKeys"].football.fixture(fixtureId), {
        "useFixture.useBaseQuery": async ()=>{
            const response = await fetch(`/api/football/fixtures/${fixtureId}`);
            if (!response.ok) {
                throw new Error(`Failed to fetch fixture: ${response.statusText}`);
            }
            return response.json();
        }
    }["useFixture.useBaseQuery"], {
        enabled: !!fixtureId,
        staleTime: 30 * 1000
    });
}
_s5(useFixture, "KQLwJ9Hz54v54vEsUujw5Lh1nqo=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBaseQuery"]
    ];
});
function useSyncStatus() {
    _s6();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBackgroundSyncQuery"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryKeys"].football.syncStatus(), {
        "useSyncStatus.useBackgroundSyncQuery": async ()=>{
            const response = await fetch('/api/football/fixtures/sync/status');
            if (!response.ok) {
                throw new Error(`Failed to fetch sync status: ${response.statusText}`);
            }
            return response.json();
        }
    }["useSyncStatus.useBackgroundSyncQuery"], {
        staleTime: 30 * 1000,
        refetchInterval: 60 * 1000
    });
}
_s6(useSyncStatus, "Xisi00twd9E8WbSd/RCl4O/WVzY=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBackgroundSyncQuery"]
    ];
});
function useSyncFixtures() {
    _s7();
    const { invalidateQueries } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiHookUtils"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBaseMutation"])({
        "useSyncFixtures.useBaseMutation": async ()=>{
            const response = await fetch('/api/football/fixtures/sync', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            if (!response.ok) {
                throw new Error(`Failed to start sync: ${response.statusText}`);
            }
            return response.json();
        }
    }["useSyncFixtures.useBaseMutation"], {
        onSuccess: {
            "useSyncFixtures.useBaseMutation": ()=>{
                // Invalidate sync status and fixtures to show updated data
                invalidateQueries(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryKeys"].football.syncStatus());
                invalidateQueries(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryKeys"].football.fixtures());
                console.log('✅ Fixtures sync started');
            }
        }["useSyncFixtures.useBaseMutation"],
        onError: {
            "useSyncFixtures.useBaseMutation": (error)=>{
                console.error('❌ Fixtures sync failed:', error);
            }
        }["useSyncFixtures.useBaseMutation"]
    });
}
_s7(useSyncFixtures, "u64JDqrckp6d8UXRWRy4SgQqcdg=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiHookUtils"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBaseMutation"]
    ];
});
function useDailySync() {
    _s8();
    const { invalidateQueries } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiHookUtils"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBaseMutation"])({
        "useDailySync.useBaseMutation": async ()=>{
            const response = await fetch('/api/football/fixtures/sync/daily', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            if (!response.ok) {
                throw new Error(`Failed to start daily sync: ${response.statusText}`);
            }
            return response.json();
        }
    }["useDailySync.useBaseMutation"], {
        onSuccess: {
            "useDailySync.useBaseMutation": ()=>{
                // Invalidate sync status and fixtures
                invalidateQueries(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryKeys"].football.syncStatus());
                invalidateQueries(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryKeys"].football.fixtures());
                console.log('✅ Daily sync started');
            }
        }["useDailySync.useBaseMutation"],
        onError: {
            "useDailySync.useBaseMutation": (error)=>{
                console.error('❌ Daily sync failed:', error);
            }
        }["useDailySync.useBaseMutation"]
    });
}
_s8(useDailySync, "u64JDqrckp6d8UXRWRy4SgQqcdg=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiHookUtils"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBaseMutation"]
    ];
});
function useFootball() {
    _s9();
    const syncFixtures = useSyncFixtures();
    const dailySync = useDailySync();
    const syncStatus = useSyncStatus();
    return {
        // Sync operations
        syncFixtures,
        dailySync,
        syncStatus,
        // Sync actions
        startSync: syncFixtures.mutate,
        startDailySync: dailySync.mutate,
        // Sync state
        isSyncing: syncFixtures.isPending || dailySync.isPending,
        syncError: syncFixtures.error || dailySync.error,
        lastSyncStatus: syncStatus.data
    };
}
_s9(useFootball, "0psCbtzM30SpLKt5GSKOpeeHq2k=", false, function() {
    return [
        useSyncFixtures,
        useDailySync,
        useSyncStatus
    ];
});
function useLiveFixtures() {
    _s10();
    return useFixtures({
        status: 'live',
        limit: 50
    });
}
_s10(useLiveFixtures, "kNJmKRNnT0VZ7/QeoHQabLuDNrM=", false, function() {
    return [
        useFixtures
    ];
});
function useTodayFixtures() {
    _s11();
    const today = new Date().toISOString().split('T')[0];
    return useFixtures({
        dateFrom: today,
        dateTo: today,
        limit: 100
    });
}
_s11(useTodayFixtures, "kNJmKRNnT0VZ7/QeoHQabLuDNrM=", false, function() {
    return [
        useFixtures
    ];
});
function useUpcomingFixtures(days = 7) {
    _s12();
    const today = new Date();
    const futureDate = new Date(today.getTime() + days * 24 * 60 * 60 * 1000);
    return useFixtures({
        dateFrom: today.toISOString().split('T')[0],
        dateTo: futureDate.toISOString().split('T')[0],
        status: 'scheduled',
        limit: 100
    });
}
_s12(useUpcomingFixtures, "kNJmKRNnT0VZ7/QeoHQabLuDNrM=", false, function() {
    return [
        useFixtures
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/types/user.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * User Types & Interfaces
 * SystemUser management types for APISportsGame CMS
 */ /**
 * System user roles
 */ __turbopack_esm__({
    "DEFAULT_USER_PARAMS": (()=>DEFAULT_USER_PARAMS),
    "ROLE_COLORS": (()=>ROLE_COLORS),
    "ROLE_LABELS": (()=>ROLE_LABELS),
    "ROLE_PERMISSIONS": (()=>ROLE_PERMISSIONS),
    "STATUS_COLORS": (()=>STATUS_COLORS),
    "STATUS_LABELS": (()=>STATUS_LABELS),
    "USER_VALIDATION": (()=>USER_VALIDATION),
    "userHelpers": (()=>userHelpers)
});
const ROLE_PERMISSIONS = {
    admin: [
        'users.create',
        'users.read',
        'users.update',
        'users.delete',
        'users.manage_roles',
        'football.create',
        'football.read',
        'football.update',
        'football.delete',
        'football.sync',
        'broadcast.create',
        'broadcast.read',
        'broadcast.update',
        'broadcast.delete',
        'system.settings',
        'system.logs',
        'system.health'
    ],
    editor: [
        'users.read',
        'football.create',
        'football.read',
        'football.update',
        'football.sync',
        'broadcast.create',
        'broadcast.read',
        'broadcast.update',
        'broadcast.delete'
    ],
    moderator: [
        'users.read',
        'football.read',
        'broadcast.read',
        'broadcast.update'
    ]
};
const ROLE_LABELS = {
    admin: 'Administrator',
    editor: 'Editor',
    moderator: 'Moderator'
};
const STATUS_LABELS = {
    active: 'Active',
    inactive: 'Inactive',
    suspended: 'Suspended'
};
const ROLE_COLORS = {
    admin: '#ff4d4f',
    editor: '#1890ff',
    moderator: '#52c41a'
};
const STATUS_COLORS = {
    active: '#52c41a',
    inactive: '#d9d9d9',
    suspended: '#ff4d4f'
};
const USER_VALIDATION = {
    username: {
        min: 3,
        max: 50,
        pattern: /^[a-zA-Z0-9_-]+$/,
        message: 'Username must be 3-50 characters and contain only letters, numbers, hyphens, and underscores'
    },
    email: {
        pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
        message: 'Please enter a valid email address'
    },
    password: {
        min: 8,
        pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
        message: 'Password must be at least 8 characters with uppercase, lowercase, number, and special character'
    },
    firstName: {
        max: 50,
        message: 'First name must not exceed 50 characters'
    },
    lastName: {
        max: 50,
        message: 'Last name must not exceed 50 characters'
    }
};
const DEFAULT_USER_PARAMS = {
    page: 1,
    limit: 20,
    sortBy: 'createdAt',
    sortOrder: 'desc'
};
const userHelpers = {
    /**
   * Get user full name
   */ getFullName: (user)=>{
        if (user.firstName && user.lastName) {
            return `${user.firstName} ${user.lastName}`;
        }
        if (user.firstName) {
            return user.firstName;
        }
        if (user.lastName) {
            return user.lastName;
        }
        return user.username;
    },
    /**
   * Get user display name
   */ getDisplayName: (user)=>{
        const fullName = userHelpers.getFullName(user);
        return fullName !== user.username ? `${fullName} (${user.username})` : user.username;
    },
    /**
   * Check if user has permission
   */ hasPermission: (user, permission)=>{
        const rolePermissions = ROLE_PERMISSIONS[user.role] || [];
        return rolePermissions.includes(permission);
    },
    /**
   * Check if user is active
   */ isActive: (user)=>{
        return user.status === 'active';
    },
    /**
   * Get user avatar URL or initials
   */ getAvatarDisplay: (user)=>{
        if (user.avatar) {
            return {
                type: 'url',
                value: user.avatar
            };
        }
        const fullName = userHelpers.getFullName(user);
        const initials = fullName.split(' ').map((name)=>name.charAt(0).toUpperCase()).slice(0, 2).join('');
        return {
            type: 'initials',
            value: initials || user.username.charAt(0).toUpperCase()
        };
    },
    /**
   * Format last login time
   */ formatLastLogin: (lastLogin)=>{
        if (!lastLogin) return 'Never';
        const date = new Date(lastLogin);
        const now = new Date();
        const diffMs = now.getTime() - date.getTime();
        const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
        if (diffDays === 0) return 'Today';
        if (diffDays === 1) return 'Yesterday';
        if (diffDays < 7) return `${diffDays} days ago`;
        if (diffDays < 30) return `${Math.floor(diffDays / 7)} weeks ago`;
        if (diffDays < 365) return `${Math.floor(diffDays / 30)} months ago`;
        return `${Math.floor(diffDays / 365)} years ago`;
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/hooks/api/users.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * User API Hooks
 * TanStack Query hooks for SystemUser management
 */ __turbopack_esm__({
    "useCreateUser": (()=>useCreateUser),
    "useDeleteUser": (()=>useDeleteUser),
    "useUpdateUser": (()=>useUpdateUser),
    "useUser": (()=>useUser),
    "useUserStatistics": (()=>useUserStatistics),
    "useUsers": (()=>useUsers),
    "userQueryKeys": (()=>userQueryKeys)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$user$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/types/user.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/@tanstack/react-query/build/modern/useMutation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/@tanstack/react-query/build/modern/useQuery.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$message$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__message$3e$__ = __turbopack_import__("[project]/node_modules/antd/es/message/index.js [app-client] (ecmascript) <export default as message>");
var _s = __turbopack_refresh__.signature(), _s1 = __turbopack_refresh__.signature(), _s2 = __turbopack_refresh__.signature(), _s3 = __turbopack_refresh__.signature(), _s4 = __turbopack_refresh__.signature(), _s5 = __turbopack_refresh__.signature();
;
;
;
/**
 * API endpoints
 */ const API_ENDPOINTS = {
    users: '/api/system-auth/users',
    userById: (id)=>`/api/system-auth/users/${id}`,
    userStats: '/api/system-auth/users/statistics',
    userActivity: (id)=>`/api/system-auth/users/${id}/activity`,
    userSessions: (id)=>`/api/system-auth/users/${id}/sessions`,
    changePassword: (id)=>`/api/system-auth/users/${id}/change-password`,
    resetPassword: (id)=>`/api/system-auth/users/${id}/reset-password`
};
const userQueryKeys = {
    all: [
        'users'
    ],
    lists: ()=>[
            ...userQueryKeys.all,
            'list'
        ],
    list: (params)=>[
            ...userQueryKeys.lists(),
            params
        ],
    details: ()=>[
            ...userQueryKeys.all,
            'detail'
        ],
    detail: (id)=>[
            ...userQueryKeys.details(),
            id
        ],
    statistics: ()=>[
            ...userQueryKeys.all,
            'statistics'
        ],
    activity: (id)=>[
            ...userQueryKeys.all,
            'activity',
            id
        ],
    sessions: (id)=>[
            ...userQueryKeys.all,
            'sessions',
            id
        ]
};
/**
 * Mock data for development
 */ const mockUsers = [
    {
        id: '1',
        username: 'admin',
        email: '<EMAIL>',
        firstName: 'System',
        lastName: 'Administrator',
        role: 'admin',
        status: 'active',
        lastLogin: new Date(Date.now() - 1000 * 60 * 30).toISOString(),
        createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 30).toISOString(),
        updatedAt: new Date().toISOString(),
        createdBy: 'system'
    },
    {
        id: '2',
        username: 'editor1',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Editor',
        role: 'editor',
        status: 'active',
        lastLogin: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(),
        createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 15).toISOString(),
        updatedAt: new Date().toISOString(),
        createdBy: '1'
    },
    {
        id: '3',
        username: 'moderator1',
        email: '<EMAIL>',
        firstName: 'Jane',
        lastName: 'Moderator',
        role: 'moderator',
        status: 'active',
        lastLogin: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString(),
        createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 7).toISOString(),
        updatedAt: new Date().toISOString(),
        createdBy: '1'
    },
    {
        id: '4',
        username: 'inactive_user',
        email: '<EMAIL>',
        firstName: 'Inactive',
        lastName: 'User',
        role: 'editor',
        status: 'inactive',
        createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 60).toISOString(),
        updatedAt: new Date().toISOString(),
        createdBy: '1'
    }
];
/**
 * Mock API functions
 */ const mockAPI = {
    getUsers: async (params)=>{
        await new Promise((resolve)=>setTimeout(resolve, 500)); // Simulate network delay
        let filteredUsers = [
            ...mockUsers
        ];
        // Apply filters
        if (params.search) {
            const search = params.search.toLowerCase();
            filteredUsers = filteredUsers.filter((user)=>user.username.toLowerCase().includes(search) || user.email.toLowerCase().includes(search) || user.firstName?.toLowerCase().includes(search) || user.lastName?.toLowerCase().includes(search));
        }
        if (params.role) {
            filteredUsers = filteredUsers.filter((user)=>user.role === params.role);
        }
        if (params.status) {
            filteredUsers = filteredUsers.filter((user)=>user.status === params.status);
        }
        // Apply sorting
        if (params.sortBy) {
            filteredUsers.sort((a, b)=>{
                const aValue = a[params.sortBy] || '';
                const bValue = b[params.sortBy] || '';
                const comparison = aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
                return params.sortOrder === 'desc' ? -comparison : comparison;
            });
        }
        // Apply pagination
        const page = params.page || 1;
        const limit = params.limit || 20;
        const startIndex = (page - 1) * limit;
        const endIndex = startIndex + limit;
        const paginatedUsers = filteredUsers.slice(startIndex, endIndex);
        return {
            users: paginatedUsers,
            total: filteredUsers.length,
            page,
            limit,
            totalPages: Math.ceil(filteredUsers.length / limit)
        };
    },
    getUser: async (id)=>{
        await new Promise((resolve)=>setTimeout(resolve, 300));
        const user = mockUsers.find((u)=>u.id === id);
        if (!user) throw new Error('User not found');
        return user;
    },
    createUser: async (data)=>{
        await new Promise((resolve)=>setTimeout(resolve, 1000));
        const newUser = {
            id: String(mockUsers.length + 1),
            ...data,
            status: data.status || 'active',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            createdBy: '1'
        };
        mockUsers.push(newUser);
        return newUser;
    },
    updateUser: async (id, data)=>{
        await new Promise((resolve)=>setTimeout(resolve, 800));
        const userIndex = mockUsers.findIndex((u)=>u.id === id);
        if (userIndex === -1) throw new Error('User not found');
        mockUsers[userIndex] = {
            ...mockUsers[userIndex],
            ...data,
            updatedAt: new Date().toISOString()
        };
        return mockUsers[userIndex];
    },
    deleteUser: async (id)=>{
        await new Promise((resolve)=>setTimeout(resolve, 500));
        const userIndex = mockUsers.findIndex((u)=>u.id === id);
        if (userIndex === -1) throw new Error('User not found');
        mockUsers.splice(userIndex, 1);
    },
    getStatistics: async ()=>{
        await new Promise((resolve)=>setTimeout(resolve, 400));
        const total = mockUsers.length;
        const active = mockUsers.filter((u)=>u.status === 'active').length;
        const inactive = mockUsers.filter((u)=>u.status === 'inactive').length;
        const suspended = mockUsers.filter((u)=>u.status === 'suspended').length;
        const byRole = {
            admin: mockUsers.filter((u)=>u.role === 'admin').length,
            editor: mockUsers.filter((u)=>u.role === 'editor').length,
            moderator: mockUsers.filter((u)=>u.role === 'moderator').length
        };
        const recentLogins = mockUsers.filter((u)=>{
            if (!u.lastLogin) return false;
            const lastLogin = new Date(u.lastLogin);
            const dayAgo = new Date(Date.now() - 1000 * 60 * 60 * 24);
            return lastLogin > dayAgo;
        }).length;
        const monthAgo = new Date(Date.now() - 1000 * 60 * 60 * 24 * 30);
        const newThisMonth = mockUsers.filter((u)=>{
            const created = new Date(u.createdAt);
            return created > monthAgo;
        }).length;
        return {
            total,
            active,
            inactive,
            suspended,
            byRole,
            recentLogins,
            newThisMonth
        };
    }
};
function useUsers(params = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$user$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DEFAULT_USER_PARAMS"]) {
    _s();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: userQueryKeys.list(params),
        queryFn: {
            "useUsers.useQuery": ()=>mockAPI.getUsers(params)
        }["useUsers.useQuery"],
        staleTime: 5 * 60 * 1000
    });
}
_s(useUsers, "4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"]
    ];
});
function useUser(id) {
    _s1();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: userQueryKeys.detail(id),
        queryFn: {
            "useUser.useQuery": ()=>mockAPI.getUser(id)
        }["useUser.useQuery"],
        enabled: !!id,
        staleTime: 5 * 60 * 1000
    });
}
_s1(useUser, "4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"]
    ];
});
function useUserStatistics() {
    _s2();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: userQueryKeys.statistics(),
        queryFn: {
            "useUserStatistics.useQuery": ()=>mockAPI.getStatistics()
        }["useUserStatistics.useQuery"],
        staleTime: 2 * 60 * 1000
    });
}
_s2(useUserStatistics, "4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"]
    ];
});
function useCreateUser() {
    _s3();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useCreateUser.useMutation": (data)=>mockAPI.createUser(data)
        }["useCreateUser.useMutation"],
        onSuccess: {
            "useCreateUser.useMutation": ()=>{
                queryClient.invalidateQueries({
                    queryKey: userQueryKeys.lists()
                });
                queryClient.invalidateQueries({
                    queryKey: userQueryKeys.statistics()
                });
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$message$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__message$3e$__["message"].success('User created successfully');
            }
        }["useCreateUser.useMutation"],
        onError: {
            "useCreateUser.useMutation": (error)=>{
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$message$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__message$3e$__["message"].error(`Failed to create user: ${error.message}`);
            }
        }["useCreateUser.useMutation"]
    });
}
_s3(useCreateUser, "YK0wzM21ECnncaq5SECwU+/SVdQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
function useUpdateUser() {
    _s4();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useUpdateUser.useMutation": ({ id, data })=>mockAPI.updateUser(id, data)
        }["useUpdateUser.useMutation"],
        onSuccess: {
            "useUpdateUser.useMutation": (updatedUser)=>{
                queryClient.invalidateQueries({
                    queryKey: userQueryKeys.lists()
                });
                queryClient.invalidateQueries({
                    queryKey: userQueryKeys.detail(updatedUser.id)
                });
                queryClient.invalidateQueries({
                    queryKey: userQueryKeys.statistics()
                });
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$message$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__message$3e$__["message"].success('User updated successfully');
            }
        }["useUpdateUser.useMutation"],
        onError: {
            "useUpdateUser.useMutation": (error)=>{
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$message$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__message$3e$__["message"].error(`Failed to update user: ${error.message}`);
            }
        }["useUpdateUser.useMutation"]
    });
}
_s4(useUpdateUser, "YK0wzM21ECnncaq5SECwU+/SVdQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
function useDeleteUser() {
    _s5();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useDeleteUser.useMutation": (id)=>mockAPI.deleteUser(id)
        }["useDeleteUser.useMutation"],
        onSuccess: {
            "useDeleteUser.useMutation": ()=>{
                queryClient.invalidateQueries({
                    queryKey: userQueryKeys.lists()
                });
                queryClient.invalidateQueries({
                    queryKey: userQueryKeys.statistics()
                });
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$message$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__message$3e$__["message"].success('User deleted successfully');
            }
        }["useDeleteUser.useMutation"],
        onError: {
            "useDeleteUser.useMutation": (error)=>{
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$message$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__message$3e$__["message"].error(`Failed to delete user: ${error.message}`);
            }
        }["useDeleteUser.useMutation"]
    });
}
_s5(useDeleteUser, "YK0wzM21ECnncaq5SECwU+/SVdQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/hooks/api/broadcast-hooks.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Broadcast Links API Hooks
 * Hooks for broadcast links management operations
 */ __turbopack_esm__({
    "useActiveBroadcastLinks": (()=>useActiveBroadcastLinks),
    "useBroadcastLink": (()=>useBroadcastLink),
    "useBroadcastLinks": (()=>useBroadcastLinks),
    "useBroadcastLinksByLanguage": (()=>useBroadcastLinksByLanguage),
    "useBroadcastLinksByQuality": (()=>useBroadcastLinksByQuality),
    "useBroadcastLinksManager": (()=>useBroadcastLinksManager),
    "useCreateBroadcastLink": (()=>useCreateBroadcastLink),
    "useDeleteBroadcastLink": (()=>useDeleteBroadcastLink),
    "useFixtureBroadcastLinks": (()=>useFixtureBroadcastLinks),
    "useToggleBroadcastLinkStatus": (()=>useToggleBroadcastLinkStatus),
    "useUpdateBroadcastLink": (()=>useUpdateBroadcastLink)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/lib/query-client.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/hooks/api/base-hooks.ts [app-client] (ecmascript)");
var _s = __turbopack_refresh__.signature(), _s1 = __turbopack_refresh__.signature(), _s2 = __turbopack_refresh__.signature(), _s3 = __turbopack_refresh__.signature(), _s4 = __turbopack_refresh__.signature(), _s5 = __turbopack_refresh__.signature(), _s6 = __turbopack_refresh__.signature(), _s7 = __turbopack_refresh__.signature(), _s8 = __turbopack_refresh__.signature(), _s9 = __turbopack_refresh__.signature(), _s10 = __turbopack_refresh__.signature();
'use client';
;
;
function useBroadcastLinks(params) {
    _s();
    const queryParams = new URLSearchParams();
    if (params?.page) queryParams.set('page', params.page.toString());
    if (params?.limit) queryParams.set('limit', params.limit.toString());
    if (params?.fixtureId) queryParams.set('fixtureId', params.fixtureId);
    if (params?.quality) queryParams.set('quality', params.quality);
    if (params?.language) queryParams.set('language', params.language);
    if (params?.isActive !== undefined) queryParams.set('isActive', params.isActive.toString());
    if (params?.query) queryParams.set('query', params.query);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePaginatedQuery"])([
        ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryKeys"].broadcast.links(),
        params
    ], {
        "useBroadcastLinks.usePaginatedQuery": async ()=>{
            const response = await fetch(`/api/broadcast-links?${queryParams.toString()}`);
            if (!response.ok) {
                throw new Error(`Failed to fetch broadcast links: ${response.statusText}`);
            }
            return response.json();
        }
    }["useBroadcastLinks.usePaginatedQuery"], {
        staleTime: 2 * 60 * 1000
    });
}
_s(useBroadcastLinks, "8dRrW1kK8lRPrshcMFzQabx/L7w=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePaginatedQuery"]
    ];
});
function useBroadcastLink(linkId) {
    _s1();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBaseQuery"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryKeys"].broadcast.link(linkId), {
        "useBroadcastLink.useBaseQuery": async ()=>{
            const response = await fetch(`/api/broadcast-links/${linkId}`);
            if (!response.ok) {
                throw new Error(`Failed to fetch broadcast link: ${response.statusText}`);
            }
            return response.json();
        }
    }["useBroadcastLink.useBaseQuery"], {
        enabled: !!linkId,
        staleTime: 2 * 60 * 1000
    });
}
_s1(useBroadcastLink, "KQLwJ9Hz54v54vEsUujw5Lh1nqo=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBaseQuery"]
    ];
});
function useFixtureBroadcastLinks(fixtureId) {
    _s2();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBaseQuery"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryKeys"].broadcast.fixture(fixtureId), {
        "useFixtureBroadcastLinks.useBaseQuery": async ()=>{
            const response = await fetch(`/api/broadcast-links/fixture/${fixtureId}`);
            if (!response.ok) {
                throw new Error(`Failed to fetch fixture broadcast links: ${response.statusText}`);
            }
            return response.json();
        }
    }["useFixtureBroadcastLinks.useBaseQuery"], {
        enabled: !!fixtureId,
        staleTime: 1 * 60 * 1000
    });
}
_s2(useFixtureBroadcastLinks, "KQLwJ9Hz54v54vEsUujw5Lh1nqo=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBaseQuery"]
    ];
});
function useCreateBroadcastLink() {
    _s3();
    const { invalidateQueries } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiHookUtils"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useOptimisticMutation"])({
        "useCreateBroadcastLink.useOptimisticMutation": async (data)=>{
            const response = await fetch('/api/broadcast-links', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            });
            if (!response.ok) {
                throw new Error(`Failed to create broadcast link: ${response.statusText}`);
            }
            return response.json();
        }
    }["useCreateBroadcastLink.useOptimisticMutation"], {
        onSuccess: {
            "useCreateBroadcastLink.useOptimisticMutation": (data)=>{
                // Invalidate broadcast links queries
                invalidateQueries(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryKeys"].broadcast.links());
                invalidateQueries(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryKeys"].broadcast.fixture(data.fixtureId));
                console.log('✅ Broadcast link created successfully');
            }
        }["useCreateBroadcastLink.useOptimisticMutation"],
        onError: {
            "useCreateBroadcastLink.useOptimisticMutation": (error)=>{
                console.error('❌ Failed to create broadcast link:', error);
            }
        }["useCreateBroadcastLink.useOptimisticMutation"]
    });
}
_s3(useCreateBroadcastLink, "wAIvSt9W/+5IvmTsbat1EBSQXss=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiHookUtils"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useOptimisticMutation"]
    ];
});
function useUpdateBroadcastLink() {
    _s4();
    const { invalidateQueries } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiHookUtils"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useOptimisticMutation"])({
        "useUpdateBroadcastLink.useOptimisticMutation": async ({ id, data })=>{
            const response = await fetch(`/api/broadcast-links/${id}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            });
            if (!response.ok) {
                throw new Error(`Failed to update broadcast link: ${response.statusText}`);
            }
            return response.json();
        }
    }["useUpdateBroadcastLink.useOptimisticMutation"], {
        onSuccess: {
            "useUpdateBroadcastLink.useOptimisticMutation": (data)=>{
                // Invalidate specific link and related queries
                invalidateQueries(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryKeys"].broadcast.link(data.id));
                invalidateQueries(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryKeys"].broadcast.links());
                invalidateQueries(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryKeys"].broadcast.fixture(data.fixtureId));
                console.log('✅ Broadcast link updated successfully');
            }
        }["useUpdateBroadcastLink.useOptimisticMutation"],
        onError: {
            "useUpdateBroadcastLink.useOptimisticMutation": (error)=>{
                console.error('❌ Failed to update broadcast link:', error);
            }
        }["useUpdateBroadcastLink.useOptimisticMutation"]
    });
}
_s4(useUpdateBroadcastLink, "wAIvSt9W/+5IvmTsbat1EBSQXss=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiHookUtils"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useOptimisticMutation"]
    ];
});
function useDeleteBroadcastLink() {
    _s5();
    const { invalidateQueries } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiHookUtils"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBaseMutation"])({
        "useDeleteBroadcastLink.useBaseMutation": async (linkId)=>{
            const response = await fetch(`/api/broadcast-links/${linkId}`, {
                method: 'DELETE'
            });
            if (!response.ok) {
                throw new Error(`Failed to delete broadcast link: ${response.statusText}`);
            }
            return response.json();
        }
    }["useDeleteBroadcastLink.useBaseMutation"], {
        onSuccess: {
            "useDeleteBroadcastLink.useBaseMutation": (_, linkId)=>{
                // Invalidate broadcast links queries
                invalidateQueries(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryKeys"].broadcast.links());
                invalidateQueries(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryKeys"].broadcast.link(linkId));
                console.log('✅ Broadcast link deleted successfully');
            }
        }["useDeleteBroadcastLink.useBaseMutation"],
        onError: {
            "useDeleteBroadcastLink.useBaseMutation": (error)=>{
                console.error('❌ Failed to delete broadcast link:', error);
            }
        }["useDeleteBroadcastLink.useBaseMutation"]
    });
}
_s5(useDeleteBroadcastLink, "u64JDqrckp6d8UXRWRy4SgQqcdg=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiHookUtils"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBaseMutation"]
    ];
});
function useToggleBroadcastLinkStatus() {
    _s6();
    const { invalidateQueries, updateQueryData } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiHookUtils"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useOptimisticMutation"])({
        "useToggleBroadcastLinkStatus.useOptimisticMutation": async ({ id, isActive })=>{
            const response = await fetch(`/api/broadcast-links/${id}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    isActive
                })
            });
            if (!response.ok) {
                throw new Error(`Failed to toggle broadcast link status: ${response.statusText}`);
            }
            return response.json();
        }
    }["useToggleBroadcastLinkStatus.useOptimisticMutation"], {
        onMutate: {
            "useToggleBroadcastLinkStatus.useOptimisticMutation": async ({ id, isActive })=>{
                // Optimistically update the link status
                const linkQueryKey = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryKeys"].broadcast.link(id);
                const previousLink = updateQueryData(linkQueryKey, {
                    "useToggleBroadcastLinkStatus.useOptimisticMutation.previousLink": (old)=>old ? {
                            ...old,
                            isActive
                        } : old
                }["useToggleBroadcastLinkStatus.useOptimisticMutation.previousLink"]);
                return {
                    previousLink,
                    linkQueryKey
                };
            }
        }["useToggleBroadcastLinkStatus.useOptimisticMutation"],
        onError: {
            "useToggleBroadcastLinkStatus.useOptimisticMutation": (error, variables, context)=>{
                // Revert optimistic update on error
                if (context?.previousLink && context?.linkQueryKey) {
                    updateQueryData(context.linkQueryKey, {
                        "useToggleBroadcastLinkStatus.useOptimisticMutation": ()=>context.previousLink
                    }["useToggleBroadcastLinkStatus.useOptimisticMutation"]);
                }
                console.error('❌ Failed to toggle broadcast link status:', error);
            }
        }["useToggleBroadcastLinkStatus.useOptimisticMutation"],
        onSuccess: {
            "useToggleBroadcastLinkStatus.useOptimisticMutation": (data)=>{
                // Invalidate related queries
                invalidateQueries(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryKeys"].broadcast.links());
                invalidateQueries(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryKeys"].broadcast.fixture(data.fixtureId));
                console.log('✅ Broadcast link status toggled successfully');
            }
        }["useToggleBroadcastLinkStatus.useOptimisticMutation"]
    });
}
_s6(useToggleBroadcastLinkStatus, "rW7iOrorG+isk5GV43B/XjTPOuY=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiHookUtils"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useOptimisticMutation"]
    ];
});
function useBroadcastLinksManager() {
    _s7();
    const createLink = useCreateBroadcastLink();
    const updateLink = useUpdateBroadcastLink();
    const deleteLink = useDeleteBroadcastLink();
    const toggleStatus = useToggleBroadcastLinkStatus();
    return {
        // Mutations
        createLink,
        updateLink,
        deleteLink,
        toggleStatus,
        // Actions
        createBroadcastLink: createLink.mutate,
        updateBroadcastLink: updateLink.mutate,
        deleteBroadcastLink: deleteLink.mutate,
        toggleLinkStatus: toggleStatus.mutate,
        // State
        isCreating: createLink.isPending,
        isUpdating: updateLink.isPending,
        isDeleting: deleteLink.isPending,
        isToggling: toggleStatus.isPending,
        isLoading: createLink.isPending || updateLink.isPending || deleteLink.isPending || toggleStatus.isPending,
        // Errors
        createError: createLink.error,
        updateError: updateLink.error,
        deleteError: deleteLink.error,
        toggleError: toggleStatus.error
    };
}
_s7(useBroadcastLinksManager, "XbY0YNeA/qFF9uR9FgYYUbUwpQw=", false, function() {
    return [
        useCreateBroadcastLink,
        useUpdateBroadcastLink,
        useDeleteBroadcastLink,
        useToggleBroadcastLinkStatus
    ];
});
function useBroadcastLinksByQuality(quality) {
    _s8();
    return useBroadcastLinks({
        quality,
        isActive: true
    });
}
_s8(useBroadcastLinksByQuality, "M+exTM7lv8qALVRH2RnjVdrHrnw=", false, function() {
    return [
        useBroadcastLinks
    ];
});
function useBroadcastLinksByLanguage(language) {
    _s9();
    return useBroadcastLinks({
        language,
        isActive: true
    });
}
_s9(useBroadcastLinksByLanguage, "M+exTM7lv8qALVRH2RnjVdrHrnw=", false, function() {
    return [
        useBroadcastLinks
    ];
});
function useActiveBroadcastLinks() {
    _s10();
    return useBroadcastLinks({
        isActive: true
    });
}
_s10(useActiveBroadcastLinks, "M+exTM7lv8qALVRH2RnjVdrHrnw=", false, function() {
    return [
        useBroadcastLinks
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/hooks/api/health-hooks.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Health Check API Hooks
 * Hooks for API health monitoring operations
 */ __turbopack_esm__({
    "useApiHealth": (()=>useApiHealth),
    "useApiPerformance": (()=>useApiPerformance),
    "useDatabaseHealth": (()=>useDatabaseHealth),
    "useExternalApiHealth": (()=>useExternalApiHealth),
    "useHealthDashboard": (()=>useHealthDashboard),
    "useSystemHealth": (()=>useSystemHealth)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/lib/query-client.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/hooks/api/base-hooks.ts [app-client] (ecmascript)");
var _s = __turbopack_refresh__.signature(), _s1 = __turbopack_refresh__.signature(), _s2 = __turbopack_refresh__.signature(), _s3 = __turbopack_refresh__.signature(), _s4 = __turbopack_refresh__.signature(), _s5 = __turbopack_refresh__.signature();
'use client';
;
;
function useApiHealth() {
    _s();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBackgroundSyncQuery"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryKeys"].health.api(), {
        "useApiHealth.useBackgroundSyncQuery": async ()=>{
            const response = await fetch('/api/health');
            if (!response.ok) {
                throw new Error(`Health check failed: ${response.statusText}`);
            }
            return response.json();
        }
    }["useApiHealth.useBackgroundSyncQuery"], {
        staleTime: 30 * 1000,
        refetchInterval: 60 * 1000,
        retry: 3,
        retryDelay: {
            "useApiHealth.useBackgroundSyncQuery": (attemptIndex)=>Math.min(1000 * 2 ** attemptIndex, 10000)
        }["useApiHealth.useBackgroundSyncQuery"]
    });
}
_s(useApiHealth, "Xisi00twd9E8WbSd/RCl4O/WVzY=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBackgroundSyncQuery"]
    ];
});
function useDatabaseHealth() {
    _s1();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBaseQuery"])([
        ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryKeys"].health.all,
        'database'
    ], {
        "useDatabaseHealth.useBaseQuery": async ()=>{
            const startTime = performance.now();
            const response = await fetch('/api/health/database');
            const endTime = performance.now();
            if (!response.ok) {
                throw new Error(`Database health check failed: ${response.statusText}`);
            }
            const data = await response.json();
            return {
                ...data,
                responseTime: endTime - startTime
            };
        }
    }["useDatabaseHealth.useBaseQuery"], {
        staleTime: 30 * 1000,
        retry: 2
    });
}
_s1(useDatabaseHealth, "KQLwJ9Hz54v54vEsUujw5Lh1nqo=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBaseQuery"]
    ];
});
function useExternalApiHealth() {
    _s2();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBaseQuery"])([
        ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryKeys"].health.all,
        'external-api'
    ], {
        "useExternalApiHealth.useBaseQuery": async ()=>{
            const startTime = performance.now();
            const response = await fetch('/api/health/external-api');
            const endTime = performance.now();
            if (!response.ok) {
                throw new Error(`External API health check failed: ${response.statusText}`);
            }
            const data = await response.json();
            return {
                ...data,
                responseTime: endTime - startTime
            };
        }
    }["useExternalApiHealth.useBaseQuery"], {
        staleTime: 60 * 1000,
        retry: 2
    });
}
_s2(useExternalApiHealth, "KQLwJ9Hz54v54vEsUujw5Lh1nqo=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBaseQuery"]
    ];
});
function useSystemHealth() {
    _s3();
    const apiHealth = useApiHealth();
    const dbHealth = useDatabaseHealth();
    const externalApiHealth = useExternalApiHealth();
    const isLoading = apiHealth.isLoading || dbHealth.isLoading || externalApiHealth.isLoading;
    const hasErrors = apiHealth.isError || dbHealth.isError || externalApiHealth.isError;
    // Calculate overall health status
    const getOverallStatus = ()=>{
        if (hasErrors) return 'unhealthy';
        const apiStatus = apiHealth.data?.status;
        const dbStatus = dbHealth.data?.status;
        const externalStatus = externalApiHealth.data?.status;
        if (apiStatus === 'healthy' && dbStatus === 'up' && externalStatus === 'up') {
            return 'healthy';
        }
        if (apiStatus === 'unhealthy' || dbStatus === 'down') {
            return 'unhealthy';
        }
        return 'degraded';
    };
    // Calculate average response time
    const getAverageResponseTime = ()=>{
        const times = [
            dbHealth.data?.responseTime,
            externalApiHealth.data?.responseTime
        ].filter((time)=>typeof time === 'number');
        if (times.length === 0) return 0;
        return times.reduce((sum, time)=>sum + time, 0) / times.length;
    };
    return {
        // Individual health checks
        api: apiHealth,
        database: dbHealth,
        externalApi: externalApiHealth,
        // Overall status
        isLoading,
        hasErrors,
        overallStatus: getOverallStatus(),
        averageResponseTime: getAverageResponseTime(),
        // Health data
        healthData: {
            api: apiHealth.data,
            database: dbHealth.data,
            externalApi: externalApiHealth.data
        },
        // Error information
        errors: {
            api: apiHealth.error,
            database: dbHealth.error,
            externalApi: externalApiHealth.error
        },
        // Refetch functions
        refetchAll: ()=>{
            apiHealth.refetch();
            dbHealth.refetch();
            externalApiHealth.refetch();
        }
    };
}
_s3(useSystemHealth, "tg4dhD4iEHLtytmDjXCAthYjQ+k=", false, function() {
    return [
        useApiHealth,
        useDatabaseHealth,
        useExternalApiHealth
    ];
});
function useApiPerformance() {
    _s4();
    const systemHealth = useSystemHealth();
    const getPerformanceMetrics = ()=>{
        const { healthData } = systemHealth;
        return {
            uptime: healthData.api?.uptime || 0,
            responseTime: systemHealth.averageResponseTime,
            status: systemHealth.overallStatus,
            services: {
                database: healthData.database?.status || 'unknown',
                externalApi: healthData.externalApi?.status || 'unknown'
            },
            lastCheck: new Date().toISOString()
        };
    };
    return {
        ...systemHealth,
        performanceMetrics: getPerformanceMetrics(),
        // Performance indicators
        isPerformanceGood: systemHealth.averageResponseTime < 1000,
        isPerformanceFair: systemHealth.averageResponseTime < 3000,
        isPerformancePoor: systemHealth.averageResponseTime >= 3000
    };
}
_s4(useApiPerformance, "2d31NriCCprLf+vAmUKMsUXssds=", false, function() {
    return [
        useSystemHealth
    ];
});
function useHealthDashboard() {
    _s5();
    const performance1 = useApiPerformance();
    const getDashboardData = ()=>{
        const { healthData, overallStatus, averageResponseTime } = performance1;
        return {
            status: overallStatus,
            uptime: healthData.api?.uptime || 0,
            version: healthData.api?.version || 'unknown',
            responseTime: averageResponseTime,
            services: [
                {
                    name: 'Database',
                    status: healthData.database?.status || 'unknown',
                    responseTime: healthData.database?.responseTime || 0
                },
                {
                    name: 'External API',
                    status: healthData.externalApi?.status || 'unknown',
                    responseTime: healthData.externalApi?.responseTime || 0
                }
            ],
            lastUpdated: new Date().toISOString()
        };
    };
    return {
        ...performance1,
        dashboardData: getDashboardData(),
        // Dashboard actions
        refreshDashboard: performance1.refetchAll,
        // Status indicators
        statusColor: ({
            healthy: '#10b981',
            degraded: '#f59e0b',
            unhealthy: '#ef4444'
        })[performance1.overallStatus],
        statusIcon: ({
            healthy: '✅',
            degraded: '⚠️',
            unhealthy: '❌'
        })[performance1.overallStatus]
    };
}
_s5(useHealthDashboard, "LCN2gN/M9dZLNWkhxZDpRHCM8Uc=", false, function() {
    return [
        useApiPerformance
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/hooks/api/index.ts [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * API Hooks Index
 * Central export for all API hooks
 */ // Base hooks and utilities
__turbopack_esm__({
    "API_HOOKS_NAME": (()=>API_HOOKS_NAME),
    "API_HOOKS_VERSION": (()=>API_HOOKS_VERSION),
    "setupApiHooks": (()=>setupApiHooks)
});
;
;
;
;
;
;
;
const API_HOOKS_VERSION = '1.0.0';
const API_HOOKS_NAME = 'APISportsGame API Hooks';
function setupApiHooks() {
    console.log(`${API_HOOKS_NAME} v${API_HOOKS_VERSION} initialized`);
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/hooks/api/index.ts [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_esm__({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$base$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/hooks/api/base-hooks.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$auth$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/hooks/api/auth-hooks.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$football$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/hooks/api/football-hooks.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$users$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/hooks/api/users.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$broadcast$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/hooks/api/broadcast-hooks.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$health$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/hooks/api/health-hooks.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_import__("[project]/src/hooks/api/index.ts [app-client] (ecmascript) <locals>");
}}),
"[project]/src/components/layout/app-header.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * App Header Component
 * Main header for the APISportsGame CMS
 */ __turbopack_esm__({
    "AppHeader": (()=>AppHeader),
    "HeaderBreadcrumb": (()=>HeaderBreadcrumb)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_import__("[project]/src/theme/index.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_import__("[project]/src/hooks/api/index.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/theme/hooks.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$layout$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Layout$3e$__ = __turbopack_import__("[project]/node_modules/antd/es/layout/index.js [app-client] (ecmascript) <export default as Layout>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$typography$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Typography$3e$__ = __turbopack_import__("[project]/node_modules/antd/es/typography/index.js [app-client] (ecmascript) <export default as Typography>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$auth$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/hooks/api/auth-hooks.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$UserOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__UserOutlined$3e$__ = __turbopack_import__("[project]/node_modules/@ant-design/icons/es/icons/UserOutlined.js [app-client] (ecmascript) <export default as UserOutlined>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$SettingOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__SettingOutlined$3e$__ = __turbopack_import__("[project]/node_modules/@ant-design/icons/es/icons/SettingOutlined.js [app-client] (ecmascript) <export default as SettingOutlined>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$LogoutOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__LogoutOutlined$3e$__ = __turbopack_import__("[project]/node_modules/@ant-design/icons/es/icons/LogoutOutlined.js [app-client] (ecmascript) <export default as LogoutOutlined>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$button$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__$3c$export__default__as__Button$3e$__ = __turbopack_import__("[project]/node_modules/antd/es/button/index.js [app-client] (ecmascript) <locals> <export default as Button>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$MenuFoldOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__MenuFoldOutlined$3e$__ = __turbopack_import__("[project]/node_modules/@ant-design/icons/es/icons/MenuFoldOutlined.js [app-client] (ecmascript) <export default as MenuFoldOutlined>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$MenuUnfoldOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__MenuUnfoldOutlined$3e$__ = __turbopack_import__("[project]/node_modules/@ant-design/icons/es/icons/MenuUnfoldOutlined.js [app-client] (ecmascript) <export default as MenuUnfoldOutlined>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$space$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__$3c$export__default__as__Space$3e$__ = __turbopack_import__("[project]/node_modules/antd/es/space/index.js [app-client] (ecmascript) <locals> <export default as Space>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$tooltip$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Tooltip$3e$__ = __turbopack_import__("[project]/node_modules/antd/es/tooltip/index.js [app-client] (ecmascript) <export default as Tooltip>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$MoonOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__MoonOutlined$3e$__ = __turbopack_import__("[project]/node_modules/@ant-design/icons/es/icons/MoonOutlined.js [app-client] (ecmascript) <export default as MoonOutlined>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$SunOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__SunOutlined$3e$__ = __turbopack_import__("[project]/node_modules/@ant-design/icons/es/icons/SunOutlined.js [app-client] (ecmascript) <export default as SunOutlined>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$GlobalOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__GlobalOutlined$3e$__ = __turbopack_import__("[project]/node_modules/@ant-design/icons/es/icons/GlobalOutlined.js [app-client] (ecmascript) <export default as GlobalOutlined>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$dropdown$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Dropdown$3e$__ = __turbopack_import__("[project]/node_modules/antd/es/dropdown/index.js [app-client] (ecmascript) <export default as Dropdown>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$badge$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Badge$3e$__ = __turbopack_import__("[project]/node_modules/antd/es/badge/index.js [app-client] (ecmascript) <export default as Badge>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$BellOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__BellOutlined$3e$__ = __turbopack_import__("[project]/node_modules/@ant-design/icons/es/icons/BellOutlined.js [app-client] (ecmascript) <export default as BellOutlined>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$avatar$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Avatar$3e$__ = __turbopack_import__("[project]/node_modules/antd/es/avatar/index.js [app-client] (ecmascript) <export default as Avatar>");
;
var _s = __turbopack_refresh__.signature(), _s1 = __turbopack_refresh__.signature();
'use client';
;
;
;
;
;
const { Header } = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$layout$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Layout$3e$__["Layout"];
const { Text } = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$typography$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Typography$3e$__["Typography"];
function AppHeader({ sidebarCollapsed, onSidebarToggle, isMobile, showSidebarToggle = true, className, style }) {
    _s();
    const { theme, toggleTheme, isDark } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useTheme"])();
    const themeStyles = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useThemeStyles"])();
    const auth = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$auth$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"])();
    // User menu items
    const userMenuItems = [
        {
            key: 'profile',
            icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$UserOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__UserOutlined$3e$__["UserOutlined"], {}, void 0, false, {
                fileName: "[project]/src/components/layout/app-header.tsx",
                lineNumber: 58,
                columnNumber: 13
            }, this),
            label: 'Profile',
            onClick: ()=>console.log('Profile clicked')
        },
        {
            key: 'settings',
            icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$SettingOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__SettingOutlined$3e$__["SettingOutlined"], {}, void 0, false, {
                fileName: "[project]/src/components/layout/app-header.tsx",
                lineNumber: 64,
                columnNumber: 13
            }, this),
            label: 'Settings',
            onClick: ()=>console.log('Settings clicked')
        },
        {
            type: 'divider'
        },
        {
            key: 'logout',
            icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$LogoutOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__LogoutOutlined$3e$__["LogoutOutlined"], {}, void 0, false, {
                fileName: "[project]/src/components/layout/app-header.tsx",
                lineNumber: 73,
                columnNumber: 13
            }, this),
            label: 'Logout',
            onClick: ()=>auth.logoutUser(),
            danger: true
        }
    ];
    // Notification menu items
    const notificationItems = [
        {
            key: '1',
            label: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                style: {
                    padding: '8px 0'
                },
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        style: {
                            fontWeight: 'bold',
                            marginBottom: '4px'
                        },
                        children: "New fixture sync completed"
                    }, void 0, false, {
                        fileName: "[project]/src/components/layout/app-header.tsx",
                        lineNumber: 86,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        style: {
                            fontSize: '12px',
                            color: themeStyles.getTextColor('secondary')
                        },
                        children: "2 minutes ago"
                    }, void 0, false, {
                        fileName: "[project]/src/components/layout/app-header.tsx",
                        lineNumber: 89,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/layout/app-header.tsx",
                lineNumber: 85,
                columnNumber: 9
            }, this)
        },
        {
            key: '2',
            label: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                style: {
                    padding: '8px 0'
                },
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        style: {
                            fontWeight: 'bold',
                            marginBottom: '4px'
                        },
                        children: "User John Doe registered"
                    }, void 0, false, {
                        fileName: "[project]/src/components/layout/app-header.tsx",
                        lineNumber: 99,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        style: {
                            fontSize: '12px',
                            color: themeStyles.getTextColor('secondary')
                        },
                        children: "5 minutes ago"
                    }, void 0, false, {
                        fileName: "[project]/src/components/layout/app-header.tsx",
                        lineNumber: 102,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/layout/app-header.tsx",
                lineNumber: 98,
                columnNumber: 9
            }, this)
        },
        {
            type: 'divider'
        },
        {
            key: 'view-all',
            label: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                style: {
                    textAlign: 'center',
                    padding: '8px 0'
                },
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$button$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__$3c$export__default__as__Button$3e$__["Button"], {
                    type: "link",
                    size: "small",
                    children: "View All Notifications"
                }, void 0, false, {
                    fileName: "[project]/src/components/layout/app-header.tsx",
                    lineNumber: 115,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/layout/app-header.tsx",
                lineNumber: 114,
                columnNumber: 9
            }, this)
        }
    ];
    const headerStyle = {
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        zIndex: 1000,
        height: '64px',
        padding: '0 24px',
        backgroundColor: themeStyles.getBackgroundColor('container'),
        borderBottom: `1px solid ${themeStyles.getBorderColor('primary')}`,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        ...style
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Header, {
        className: className,
        style: headerStyle,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                style: {
                    display: 'flex',
                    alignItems: 'center',
                    gap: '16px'
                },
                children: [
                    showSidebarToggle && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$button$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__$3c$export__default__as__Button$3e$__["Button"], {
                        type: "text",
                        icon: sidebarCollapsed ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$MenuUnfoldOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__MenuUnfoldOutlined$3e$__["MenuUnfoldOutlined"], {}, void 0, false, {
                            fileName: "[project]/src/components/layout/app-header.tsx",
                            lineNumber: 147,
                            columnNumber: 38
                        }, void 0) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$MenuFoldOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__MenuFoldOutlined$3e$__["MenuFoldOutlined"], {}, void 0, false, {
                            fileName: "[project]/src/components/layout/app-header.tsx",
                            lineNumber: 147,
                            columnNumber: 63
                        }, void 0),
                        onClick: onSidebarToggle,
                        style: {
                            fontSize: '16px',
                            width: '40px',
                            height: '40px',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center'
                        }
                    }, void 0, false, {
                        fileName: "[project]/src/components/layout/app-header.tsx",
                        lineNumber: 145,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        style: {
                            display: 'flex',
                            alignItems: 'center',
                            gap: '12px'
                        },
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                style: {
                                    width: '32px',
                                    height: '32px',
                                    backgroundColor: themeStyles.getColor('primary'),
                                    borderRadius: '6px',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    color: 'white',
                                    fontWeight: 'bold',
                                    fontSize: '16px'
                                },
                                children: "⚽"
                            }, void 0, false, {
                                fileName: "[project]/src/components/layout/app-header.tsx",
                                lineNumber: 162,
                                columnNumber: 11
                            }, this),
                            (!isMobile || sidebarCollapsed) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Text, {
                                    style: {
                                        fontSize: '18px',
                                        fontWeight: 'bold',
                                        color: themeStyles.getTextColor('primary')
                                    },
                                    children: "APISportsGame"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/layout/app-header.tsx",
                                    lineNumber: 180,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/layout/app-header.tsx",
                                lineNumber: 179,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/layout/app-header.tsx",
                        lineNumber: 161,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/layout/app-header.tsx",
                lineNumber: 142,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$space$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__$3c$export__default__as__Space$3e$__["Space"], {
                size: "middle",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$tooltip$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Tooltip$3e$__["Tooltip"], {
                        title: `Switch to ${isDark ? 'light' : 'dark'} mode`,
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$button$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__$3c$export__default__as__Button$3e$__["Button"], {
                            type: "text",
                            icon: isDark ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$SunOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__SunOutlined$3e$__["SunOutlined"], {}, void 0, false, {
                                fileName: "[project]/src/components/layout/app-header.tsx",
                                lineNumber: 200,
                                columnNumber: 28
                            }, void 0) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$MoonOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__MoonOutlined$3e$__["MoonOutlined"], {}, void 0, false, {
                                fileName: "[project]/src/components/layout/app-header.tsx",
                                lineNumber: 200,
                                columnNumber: 46
                            }, void 0),
                            onClick: toggleTheme,
                            style: {
                                fontSize: '16px',
                                width: '40px',
                                height: '40px',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center'
                            }
                        }, void 0, false, {
                            fileName: "[project]/src/components/layout/app-header.tsx",
                            lineNumber: 198,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/layout/app-header.tsx",
                        lineNumber: 197,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$tooltip$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Tooltip$3e$__["Tooltip"], {
                        title: "Language",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$button$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__$3c$export__default__as__Button$3e$__["Button"], {
                            type: "text",
                            icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$GlobalOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__GlobalOutlined$3e$__["GlobalOutlined"], {}, void 0, false, {
                                fileName: "[project]/src/components/layout/app-header.tsx",
                                lineNumber: 217,
                                columnNumber: 19
                            }, void 0),
                            style: {
                                fontSize: '16px',
                                width: '40px',
                                height: '40px',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center'
                            }
                        }, void 0, false, {
                            fileName: "[project]/src/components/layout/app-header.tsx",
                            lineNumber: 215,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/layout/app-header.tsx",
                        lineNumber: 214,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$dropdown$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Dropdown$3e$__["Dropdown"], {
                        menu: {
                            items: notificationItems
                        },
                        trigger: [
                            'click'
                        ],
                        placement: "bottomRight",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$badge$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Badge$3e$__["Badge"], {
                            count: 2,
                            size: "small",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$button$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__$3c$export__default__as__Button$3e$__["Button"], {
                                type: "text",
                                icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$BellOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__BellOutlined$3e$__["BellOutlined"], {}, void 0, false, {
                                    fileName: "[project]/src/components/layout/app-header.tsx",
                                    lineNumber: 238,
                                    columnNumber: 21
                                }, void 0),
                                style: {
                                    fontSize: '16px',
                                    width: '40px',
                                    height: '40px',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center'
                                }
                            }, void 0, false, {
                                fileName: "[project]/src/components/layout/app-header.tsx",
                                lineNumber: 236,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/layout/app-header.tsx",
                            lineNumber: 235,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/layout/app-header.tsx",
                        lineNumber: 230,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$dropdown$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Dropdown$3e$__["Dropdown"], {
                        menu: {
                            items: userMenuItems
                        },
                        trigger: [
                            'click'
                        ],
                        placement: "bottomRight",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            style: {
                                display: 'flex',
                                alignItems: 'center',
                                gap: '8px',
                                cursor: 'pointer',
                                padding: '4px 8px',
                                borderRadius: '6px',
                                transition: 'background-color 0.2s ease'
                            },
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$avatar$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Avatar$3e$__["Avatar"], {
                                    size: "small",
                                    icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$UserOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__UserOutlined$3e$__["UserOutlined"], {}, void 0, false, {
                                        fileName: "[project]/src/components/layout/app-header.tsx",
                                        lineNumber: 270,
                                        columnNumber: 21
                                    }, void 0),
                                    style: {
                                        backgroundColor: themeStyles.getColor('primary')
                                    }
                                }, void 0, false, {
                                    fileName: "[project]/src/components/layout/app-header.tsx",
                                    lineNumber: 268,
                                    columnNumber: 13
                                }, this),
                                !isMobile && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            style: {
                                                fontSize: '14px',
                                                fontWeight: 'bold',
                                                color: themeStyles.getTextColor('primary'),
                                                lineHeight: 1.2
                                            },
                                            children: auth.user?.username || 'Admin'
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/layout/app-header.tsx",
                                            lineNumber: 277,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            style: {
                                                fontSize: '12px',
                                                color: themeStyles.getTextColor('secondary'),
                                                lineHeight: 1
                                            },
                                            children: auth.user?.role || 'Administrator'
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/layout/app-header.tsx",
                                            lineNumber: 287,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/layout/app-header.tsx",
                                    lineNumber: 276,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/layout/app-header.tsx",
                            lineNumber: 257,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/layout/app-header.tsx",
                        lineNumber: 252,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/layout/app-header.tsx",
                lineNumber: 195,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/layout/app-header.tsx",
        lineNumber: 140,
        columnNumber: 5
    }, this);
}
_s(AppHeader, "OrfBllodjmgatyq0a0JurE41Dv4=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useTheme"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useThemeStyles"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$auth$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"]
    ];
});
_c = AppHeader;
function HeaderBreadcrumb({ items, className, style }) {
    _s1();
    const themeStyles = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useThemeStyles"])();
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: className,
        style: {
            padding: '8px 24px',
            backgroundColor: themeStyles.getBackgroundColor('elevated'),
            borderBottom: `1px solid ${themeStyles.getBorderColor('primary')}`,
            ...style
        },
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            style: {
                display: 'flex',
                alignItems: 'center',
                gap: '8px'
            },
            children: items.map((item, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Fragment, {
                    children: [
                        index > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            style: {
                                color: themeStyles.getTextColor('tertiary')
                            },
                            children: "/"
                        }, void 0, false, {
                            fileName: "[project]/src/components/layout/app-header.tsx",
                            lineNumber: 334,
                            columnNumber: 15
                        }, this),
                        item.href ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                            href: item.href,
                            style: {
                                color: themeStyles.getColor('primary'),
                                textDecoration: 'none',
                                fontSize: '14px'
                            },
                            children: item.title
                        }, void 0, false, {
                            fileName: "[project]/src/components/layout/app-header.tsx",
                            lineNumber: 339,
                            columnNumber: 15
                        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            style: {
                                color: themeStyles.getTextColor('primary'),
                                fontSize: '14px',
                                fontWeight: index === items.length - 1 ? 'bold' : 'normal'
                            },
                            children: item.title
                        }, void 0, false, {
                            fileName: "[project]/src/components/layout/app-header.tsx",
                            lineNumber: 350,
                            columnNumber: 15
                        }, this)
                    ]
                }, index, true, {
                    fileName: "[project]/src/components/layout/app-header.tsx",
                    lineNumber: 332,
                    columnNumber: 11
                }, this))
        }, void 0, false, {
            fileName: "[project]/src/components/layout/app-header.tsx",
            lineNumber: 330,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/layout/app-header.tsx",
        lineNumber: 321,
        columnNumber: 5
    }, this);
}
_s1(HeaderBreadcrumb, "Px+foAvjAxePbahVty9OoyFHaM0=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useThemeStyles"]
    ];
});
_c1 = HeaderBreadcrumb;
var _c, _c1;
__turbopack_refresh__.register(_c, "AppHeader");
__turbopack_refresh__.register(_c1, "HeaderBreadcrumb");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/layout/app-sidebar.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * App Sidebar Component
 * Navigation sidebar for the APISportsGame CMS
 */ __turbopack_esm__({
    "AppSidebar": (()=>AppSidebar),
    "SidebarMenuItem": (()=>SidebarMenuItem)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_import__("[project]/src/theme/index.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/theme/hooks.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$layout$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Layout$3e$__ = __turbopack_import__("[project]/node_modules/antd/es/layout/index.js [app-client] (ecmascript) <export default as Layout>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$DashboardOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__DashboardOutlined$3e$__ = __turbopack_import__("[project]/node_modules/@ant-design/icons/es/icons/DashboardOutlined.js [app-client] (ecmascript) <export default as DashboardOutlined>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$UserOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__UserOutlined$3e$__ = __turbopack_import__("[project]/node_modules/@ant-design/icons/es/icons/UserOutlined.js [app-client] (ecmascript) <export default as UserOutlined>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$TeamOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__TeamOutlined$3e$__ = __turbopack_import__("[project]/node_modules/@ant-design/icons/es/icons/TeamOutlined.js [app-client] (ecmascript) <export default as TeamOutlined>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$SettingOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__SettingOutlined$3e$__ = __turbopack_import__("[project]/node_modules/@ant-design/icons/es/icons/SettingOutlined.js [app-client] (ecmascript) <export default as SettingOutlined>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$TrophyOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__TrophyOutlined$3e$__ = __turbopack_import__("[project]/node_modules/@ant-design/icons/es/icons/TrophyOutlined.js [app-client] (ecmascript) <export default as TrophyOutlined>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$CalendarOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CalendarOutlined$3e$__ = __turbopack_import__("[project]/node_modules/@ant-design/icons/es/icons/CalendarOutlined.js [app-client] (ecmascript) <export default as CalendarOutlined>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$DatabaseOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__DatabaseOutlined$3e$__ = __turbopack_import__("[project]/node_modules/@ant-design/icons/es/icons/DatabaseOutlined.js [app-client] (ecmascript) <export default as DatabaseOutlined>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$PlayCircleOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__PlayCircleOutlined$3e$__ = __turbopack_import__("[project]/node_modules/@ant-design/icons/es/icons/PlayCircleOutlined.js [app-client] (ecmascript) <export default as PlayCircleOutlined>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$LinkOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__LinkOutlined$3e$__ = __turbopack_import__("[project]/node_modules/@ant-design/icons/es/icons/LinkOutlined.js [app-client] (ecmascript) <export default as LinkOutlined>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$PlusOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__PlusOutlined$3e$__ = __turbopack_import__("[project]/node_modules/@ant-design/icons/es/icons/PlusOutlined.js [app-client] (ecmascript) <export default as PlusOutlined>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$ExperimentOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ExperimentOutlined$3e$__ = __turbopack_import__("[project]/node_modules/@ant-design/icons/es/icons/ExperimentOutlined.js [app-client] (ecmascript) <export default as ExperimentOutlined>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$HeartOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__HeartOutlined$3e$__ = __turbopack_import__("[project]/node_modules/@ant-design/icons/es/icons/HeartOutlined.js [app-client] (ecmascript) <export default as HeartOutlined>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$ApiOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ApiOutlined$3e$__ = __turbopack_import__("[project]/node_modules/@ant-design/icons/es/icons/ApiOutlined.js [app-client] (ecmascript) <export default as ApiOutlined>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$FileTextOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__FileTextOutlined$3e$__ = __turbopack_import__("[project]/node_modules/@ant-design/icons/es/icons/FileTextOutlined.js [app-client] (ecmascript) <export default as FileTextOutlined>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$menu$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Menu$3e$__ = __turbopack_import__("[project]/node_modules/antd/es/menu/index.js [app-client] (ecmascript) <export default as Menu>");
;
var _s = __turbopack_refresh__.signature(), _s1 = __turbopack_refresh__.signature();
'use client';
;
;
;
;
;
const { Sider } = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$layout$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Layout$3e$__["Layout"];
function AppSidebar({ collapsed, isMobile, onCollapse, className, style }) {
    _s();
    const themeStyles = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useThemeStyles"])();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    const pathname = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePathname"])();
    const [selectedKeys, setSelectedKeys] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [openKeys, setOpenKeys] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    // Menu items configuration
    const menuItems = [
        {
            key: 'dashboard',
            icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$DashboardOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__DashboardOutlined$3e$__["DashboardOutlined"], {}, void 0, false, {
                fileName: "[project]/src/components/layout/app-sidebar.tsx",
                lineNumber: 76,
                columnNumber: 13
            }, this),
            label: 'Dashboard',
            path: '/'
        },
        {
            key: 'divider-1',
            icon: null,
            label: ''
        },
        {
            key: 'user-management',
            icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$UserOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__UserOutlined$3e$__["UserOutlined"], {}, void 0, false, {
                fileName: "[project]/src/components/layout/app-sidebar.tsx",
                lineNumber: 87,
                columnNumber: 13
            }, this),
            label: 'User System',
            children: [
                {
                    key: 'system-users',
                    icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$TeamOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__TeamOutlined$3e$__["TeamOutlined"], {}, void 0, false, {
                        fileName: "[project]/src/components/layout/app-sidebar.tsx",
                        lineNumber: 92,
                        columnNumber: 17
                    }, this),
                    label: 'System Users',
                    path: '/users/system'
                },
                {
                    key: 'user-roles',
                    icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$SettingOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__SettingOutlined$3e$__["SettingOutlined"], {}, void 0, false, {
                        fileName: "[project]/src/components/layout/app-sidebar.tsx",
                        lineNumber: 98,
                        columnNumber: 17
                    }, this),
                    label: 'Roles & Permissions',
                    path: '/users/roles'
                }
            ]
        },
        {
            key: 'football-management',
            icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$TrophyOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__TrophyOutlined$3e$__["TrophyOutlined"], {}, void 0, false, {
                fileName: "[project]/src/components/layout/app-sidebar.tsx",
                lineNumber: 106,
                columnNumber: 13
            }, this),
            label: 'Football Data',
            children: [
                {
                    key: 'leagues',
                    icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$TrophyOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__TrophyOutlined$3e$__["TrophyOutlined"], {}, void 0, false, {
                        fileName: "[project]/src/components/layout/app-sidebar.tsx",
                        lineNumber: 111,
                        columnNumber: 17
                    }, this),
                    label: 'Leagues',
                    path: '/football/leagues'
                },
                {
                    key: 'teams',
                    icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$TeamOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__TeamOutlined$3e$__["TeamOutlined"], {}, void 0, false, {
                        fileName: "[project]/src/components/layout/app-sidebar.tsx",
                        lineNumber: 117,
                        columnNumber: 17
                    }, this),
                    label: 'Teams',
                    path: '/football/teams'
                },
                {
                    key: 'fixtures',
                    icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$CalendarOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CalendarOutlined$3e$__["CalendarOutlined"], {}, void 0, false, {
                        fileName: "[project]/src/components/layout/app-sidebar.tsx",
                        lineNumber: 123,
                        columnNumber: 17
                    }, this),
                    label: 'Fixtures',
                    path: '/football/fixtures'
                },
                {
                    key: 'sync-status',
                    icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$DatabaseOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__DatabaseOutlined$3e$__["DatabaseOutlined"], {}, void 0, false, {
                        fileName: "[project]/src/components/layout/app-sidebar.tsx",
                        lineNumber: 129,
                        columnNumber: 17
                    }, this),
                    label: 'Sync Status',
                    path: '/football/sync'
                }
            ]
        },
        {
            key: 'broadcast-management',
            icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$PlayCircleOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__PlayCircleOutlined$3e$__["PlayCircleOutlined"], {}, void 0, false, {
                fileName: "[project]/src/components/layout/app-sidebar.tsx",
                lineNumber: 137,
                columnNumber: 13
            }, this),
            label: 'Broadcast Links',
            children: [
                {
                    key: 'broadcast-links',
                    icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$LinkOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__LinkOutlined$3e$__["LinkOutlined"], {}, void 0, false, {
                        fileName: "[project]/src/components/layout/app-sidebar.tsx",
                        lineNumber: 142,
                        columnNumber: 17
                    }, this),
                    label: 'Manage Links',
                    path: '/broadcast-links'
                },
                {
                    key: 'broadcast-create',
                    icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$PlusOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__PlusOutlined$3e$__["PlusOutlined"], {}, void 0, false, {
                        fileName: "[project]/src/components/layout/app-sidebar.tsx",
                        lineNumber: 148,
                        columnNumber: 17
                    }, this),
                    label: 'Create Link',
                    path: '/broadcast-links/create'
                },
                {
                    key: 'broadcast-demo',
                    icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$ExperimentOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ExperimentOutlined$3e$__["ExperimentOutlined"], {}, void 0, false, {
                        fileName: "[project]/src/components/layout/app-sidebar.tsx",
                        lineNumber: 154,
                        columnNumber: 17
                    }, this),
                    label: 'Demo & Testing',
                    path: '/broadcast-demo'
                }
            ]
        },
        {
            key: 'divider-2',
            icon: null,
            label: ''
        },
        {
            key: 'system',
            icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$SettingOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__SettingOutlined$3e$__["SettingOutlined"], {}, void 0, false, {
                fileName: "[project]/src/components/layout/app-sidebar.tsx",
                lineNumber: 167,
                columnNumber: 13
            }, this),
            label: 'System',
            children: [
                {
                    key: 'api-health',
                    icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$HeartOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__HeartOutlined$3e$__["HeartOutlined"], {}, void 0, false, {
                        fileName: "[project]/src/components/layout/app-sidebar.tsx",
                        lineNumber: 172,
                        columnNumber: 17
                    }, this),
                    label: 'API Health',
                    path: '/system/health'
                },
                {
                    key: 'api-docs',
                    icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$ApiOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ApiOutlined$3e$__["ApiOutlined"], {}, void 0, false, {
                        fileName: "[project]/src/components/layout/app-sidebar.tsx",
                        lineNumber: 178,
                        columnNumber: 17
                    }, this),
                    label: 'API Documentation',
                    path: '/system/api-docs'
                },
                {
                    key: 'logs',
                    icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$FileTextOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__FileTextOutlined$3e$__["FileTextOutlined"], {}, void 0, false, {
                        fileName: "[project]/src/components/layout/app-sidebar.tsx",
                        lineNumber: 184,
                        columnNumber: 17
                    }, this),
                    label: 'System Logs',
                    path: '/system/logs'
                },
                {
                    key: 'settings',
                    icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$SettingOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__SettingOutlined$3e$__["SettingOutlined"], {}, void 0, false, {
                        fileName: "[project]/src/components/layout/app-sidebar.tsx",
                        lineNumber: 190,
                        columnNumber: 17
                    }, this),
                    label: 'Settings',
                    path: '/system/settings'
                }
            ]
        }
    ];
    // Update selected keys based on current pathname
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AppSidebar.useEffect": ()=>{
            const findSelectedKey = {
                "AppSidebar.useEffect.findSelectedKey": (items, path)=>{
                    for (const item of items){
                        if (item.path === path) {
                            return item.key;
                        }
                        if (item.children) {
                            const childKey = findSelectedKey(item.children, path);
                            if (childKey) {
                                return childKey;
                            }
                        }
                    }
                    return null;
                }
            }["AppSidebar.useEffect.findSelectedKey"];
            const selectedKey = findSelectedKey(menuItems, pathname);
            if (selectedKey) {
                setSelectedKeys([
                    selectedKey
                ]);
                // Auto-expand parent menu
                const findParentKey = {
                    "AppSidebar.useEffect.findParentKey": (items, targetKey)=>{
                        for (const item of items){
                            if (item.children) {
                                const hasChild = item.children.some({
                                    "AppSidebar.useEffect.findParentKey.hasChild": (child)=>child.key === targetKey
                                }["AppSidebar.useEffect.findParentKey.hasChild"]);
                                if (hasChild) {
                                    return item.key;
                                }
                            }
                        }
                        return null;
                    }
                }["AppSidebar.useEffect.findParentKey"];
                const parentKey = findParentKey(menuItems, selectedKey);
                if (parentKey && !collapsed) {
                    setOpenKeys([
                        parentKey
                    ]);
                }
            }
        }
    }["AppSidebar.useEffect"], [
        pathname,
        collapsed
    ]);
    // Handle menu click
    const handleMenuClick = ({ key })=>{
        const findMenuItem = (items, targetKey)=>{
            for (const item of items){
                if (item.key === targetKey) {
                    return item;
                }
                if (item.children) {
                    const childItem = findMenuItem(item.children, targetKey);
                    if (childItem) {
                        return childItem;
                    }
                }
            }
            return null;
        };
        const menuItem = findMenuItem(menuItems, key);
        if (menuItem?.path) {
            router.push(menuItem.path);
            // Close sidebar on mobile after navigation
            if (isMobile) {
                onCollapse(true);
            }
        }
    };
    // Handle submenu open/close
    const handleOpenChange = (keys)=>{
        setOpenKeys(keys);
    };
    // Convert menu items to Ant Design menu format
    const convertToAntMenuItems = (items)=>{
        return items.map((item)=>{
            // Handle dividers
            if (item.key.startsWith('divider')) {
                return {
                    type: 'divider',
                    key: item.key
                };
            }
            // Handle regular items
            if (item.children) {
                return {
                    key: item.key,
                    icon: item.icon,
                    label: item.label,
                    children: convertToAntMenuItems(item.children),
                    disabled: item.disabled
                };
            }
            return {
                key: item.key,
                icon: item.icon,
                label: item.label,
                disabled: item.disabled
            };
        });
    };
    const siderStyle = {
        position: 'fixed',
        left: 0,
        top: '64px',
        bottom: 0,
        zIndex: isMobile ? 1000 : 100,
        backgroundColor: themeStyles.getBackgroundColor('container'),
        borderRight: `1px solid ${themeStyles.getBorderColor('primary')}`,
        overflow: 'auto',
        ...style
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Sider, {
        className: className,
        style: siderStyle,
        collapsed: collapsed,
        collapsible: false,
        width: 250,
        collapsedWidth: 80,
        theme: "light",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            style: {
                height: '100%',
                display: 'flex',
                flexDirection: 'column'
            },
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$menu$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Menu$3e$__["Menu"], {
                    mode: "inline",
                    selectedKeys: selectedKeys,
                    openKeys: collapsed ? [] : openKeys,
                    onOpenChange: handleOpenChange,
                    onClick: handleMenuClick,
                    items: convertToAntMenuItems(menuItems),
                    style: {
                        flex: 1,
                        border: 'none',
                        backgroundColor: 'transparent'
                    }
                }, void 0, false, {
                    fileName: "[project]/src/components/layout/app-sidebar.tsx",
                    lineNumber: 329,
                    columnNumber: 9
                }, this),
                !collapsed && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    style: {
                        padding: '16px',
                        borderTop: `1px solid ${themeStyles.getBorderColor('primary')}`,
                        textAlign: 'center'
                    },
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            style: {
                                fontSize: '12px',
                                color: themeStyles.getTextColor('tertiary'),
                                marginBottom: '4px'
                            },
                            children: "APISportsGame CMS"
                        }, void 0, false, {
                            fileName: "[project]/src/components/layout/app-sidebar.tsx",
                            lineNumber: 352,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            style: {
                                fontSize: '10px',
                                color: themeStyles.getTextColor('tertiary')
                            },
                            children: "v1.0.0"
                        }, void 0, false, {
                            fileName: "[project]/src/components/layout/app-sidebar.tsx",
                            lineNumber: 361,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/layout/app-sidebar.tsx",
                    lineNumber: 345,
                    columnNumber: 11
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/layout/app-sidebar.tsx",
            lineNumber: 327,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/layout/app-sidebar.tsx",
        lineNumber: 317,
        columnNumber: 5
    }, this);
}
_s(AppSidebar, "P2xcUsKFIGhyJIJ6UWdx+elFEvY=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useThemeStyles"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePathname"]
    ];
});
_c = AppSidebar;
function SidebarMenuItem({ icon, label, active = false, collapsed = false, onClick, className, style }) {
    _s1();
    const themeStyles = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useThemeStyles"])();
    const itemStyle = {
        display: 'flex',
        alignItems: 'center',
        gap: collapsed ? '0' : '12px',
        padding: '12px 16px',
        cursor: 'pointer',
        borderRadius: '6px',
        margin: '2px 8px',
        transition: 'all 0.2s ease',
        backgroundColor: active ? themeStyles.getColor('primary') + '10' : 'transparent',
        color: active ? themeStyles.getColor('primary') : themeStyles.getTextColor('primary'),
        ...style
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: className,
        style: itemStyle,
        onClick: onClick,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                style: {
                    fontSize: '16px',
                    minWidth: '16px'
                },
                children: icon
            }, void 0, false, {
                fileName: "[project]/src/components/layout/app-sidebar.tsx",
                lineNumber: 420,
                columnNumber: 7
            }, this),
            !collapsed && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                style: {
                    fontSize: '14px',
                    fontWeight: active ? 'bold' : 'normal'
                },
                children: label
            }, void 0, false, {
                fileName: "[project]/src/components/layout/app-sidebar.tsx",
                lineNumber: 424,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/layout/app-sidebar.tsx",
        lineNumber: 415,
        columnNumber: 5
    }, this);
}
_s1(SidebarMenuItem, "Px+foAvjAxePbahVty9OoyFHaM0=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useThemeStyles"]
    ];
});
_c1 = SidebarMenuItem;
var _c, _c1;
__turbopack_refresh__.register(_c, "AppSidebar");
__turbopack_refresh__.register(_c1, "SidebarMenuItem");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/layout/app-footer.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * App Footer Component
 * Footer for the APISportsGame CMS
 */ __turbopack_esm__({
    "AppFooter": (()=>AppFooter),
    "SimpleFooter": (()=>SimpleFooter)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_import__("[project]/src/theme/index.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/theme/hooks.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$layout$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Layout$3e$__ = __turbopack_import__("[project]/node_modules/antd/es/layout/index.js [app-client] (ecmascript) <export default as Layout>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$typography$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Typography$3e$__ = __turbopack_import__("[project]/node_modules/antd/es/typography/index.js [app-client] (ecmascript) <export default as Typography>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$ApiOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ApiOutlined$3e$__ = __turbopack_import__("[project]/node_modules/@ant-design/icons/es/icons/ApiOutlined.js [app-client] (ecmascript) <export default as ApiOutlined>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$GithubOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__GithubOutlined$3e$__ = __turbopack_import__("[project]/node_modules/@ant-design/icons/es/icons/GithubOutlined.js [app-client] (ecmascript) <export default as GithubOutlined>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$TwitterOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__TwitterOutlined$3e$__ = __turbopack_import__("[project]/node_modules/@ant-design/icons/es/icons/TwitterOutlined.js [app-client] (ecmascript) <export default as TwitterOutlined>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$LinkedinOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__LinkedinOutlined$3e$__ = __turbopack_import__("[project]/node_modules/@ant-design/icons/es/icons/LinkedinOutlined.js [app-client] (ecmascript) <export default as LinkedinOutlined>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$GlobalOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__GlobalOutlined$3e$__ = __turbopack_import__("[project]/node_modules/@ant-design/icons/es/icons/GlobalOutlined.js [app-client] (ecmascript) <export default as GlobalOutlined>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$divider$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Divider$3e$__ = __turbopack_import__("[project]/node_modules/antd/es/divider/index.js [app-client] (ecmascript) <export default as Divider>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$HeartFilled$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__HeartFilled$3e$__ = __turbopack_import__("[project]/node_modules/@ant-design/icons/es/icons/HeartFilled.js [app-client] (ecmascript) <export default as HeartFilled>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$space$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__$3c$export__default__as__Space$3e$__ = __turbopack_import__("[project]/node_modules/antd/es/space/index.js [app-client] (ecmascript) <locals> <export default as Space>");
;
var _s = __turbopack_refresh__.signature(), _s1 = __turbopack_refresh__.signature();
'use client';
;
;
;
const { Footer } = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$layout$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Layout$3e$__["Layout"];
const { Text, Link } = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$typography$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Typography$3e$__["Typography"];
function AppFooter({ className, style, compact = false }) {
    _s();
    const themeStyles = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useThemeStyles"])();
    const footerStyle = {
        backgroundColor: themeStyles.getBackgroundColor('container'),
        borderTop: `1px solid ${themeStyles.getBorderColor('primary')}`,
        padding: compact ? '12px 24px' : '24px',
        textAlign: 'center',
        ...style
    };
    const currentYear = new Date().getFullYear();
    if (compact) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Footer, {
            className: className,
            style: footerStyle,
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Text, {
                style: {
                    fontSize: '12px',
                    color: themeStyles.getTextColor('tertiary')
                },
                children: [
                    "© ",
                    currentYear,
                    " APISportsGame CMS. Built with",
                    ' ',
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$HeartFilled$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__HeartFilled$3e$__["HeartFilled"], {
                        style: {
                            color: themeStyles.getColor('error')
                        }
                    }, void 0, false, {
                        fileName: "[project]/src/components/layout/app-footer.tsx",
                        lineNumber: 58,
                        columnNumber: 11
                    }, this),
                    " by Augment Code"
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/layout/app-footer.tsx",
                lineNumber: 51,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/layout/app-footer.tsx",
            lineNumber: 50,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Footer, {
        className: className,
        style: footerStyle,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            style: {
                maxWidth: '1200px',
                margin: '0 auto'
            },
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    style: {
                        display: 'grid',
                        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
                        gap: '32px',
                        marginBottom: '24px',
                        textAlign: 'left'
                    },
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    style: {
                                        display: 'flex',
                                        alignItems: 'center',
                                        gap: '8px',
                                        marginBottom: '12px'
                                    },
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            style: {
                                                width: '24px',
                                                height: '24px',
                                                backgroundColor: themeStyles.getColor('primary'),
                                                borderRadius: '4px',
                                                display: 'flex',
                                                alignItems: 'center',
                                                justifyContent: 'center',
                                                color: 'white',
                                                fontSize: '14px',
                                                fontWeight: 'bold'
                                            },
                                            children: "⚽"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/layout/app-footer.tsx",
                                            lineNumber: 87,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Text, {
                                            style: {
                                                fontSize: '16px',
                                                fontWeight: 'bold',
                                                color: themeStyles.getTextColor('primary')
                                            },
                                            children: "APISportsGame"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/layout/app-footer.tsx",
                                            lineNumber: 103,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/layout/app-footer.tsx",
                                    lineNumber: 79,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Text, {
                                    style: {
                                        fontSize: '14px',
                                        color: themeStyles.getTextColor('secondary'),
                                        lineHeight: 1.6
                                    },
                                    children: "A comprehensive CMS for managing football data, broadcast links, and user systems. Built with modern technologies for optimal performance."
                                }, void 0, false, {
                                    fileName: "[project]/src/components/layout/app-footer.tsx",
                                    lineNumber: 113,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/layout/app-footer.tsx",
                            lineNumber: 78,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Text, {
                                    style: {
                                        fontSize: '14px',
                                        fontWeight: 'bold',
                                        color: themeStyles.getTextColor('primary'),
                                        marginBottom: '12px',
                                        display: 'block'
                                    },
                                    children: "Quick Links"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/layout/app-footer.tsx",
                                    lineNumber: 127,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    style: {
                                        display: 'flex',
                                        flexDirection: 'column',
                                        gap: '8px'
                                    },
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Link, {
                                            href: "/",
                                            style: {
                                                fontSize: '13px',
                                                color: themeStyles.getTextColor('secondary')
                                            },
                                            children: "Dashboard"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/layout/app-footer.tsx",
                                            lineNumber: 139,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Link, {
                                            href: "/football/fixtures",
                                            style: {
                                                fontSize: '13px',
                                                color: themeStyles.getTextColor('secondary')
                                            },
                                            children: "Fixtures"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/layout/app-footer.tsx",
                                            lineNumber: 148,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Link, {
                                            href: "/broadcast/links",
                                            style: {
                                                fontSize: '13px',
                                                color: themeStyles.getTextColor('secondary')
                                            },
                                            children: "Broadcast Links"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/layout/app-footer.tsx",
                                            lineNumber: 157,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Link, {
                                            href: "/system/health",
                                            style: {
                                                fontSize: '13px',
                                                color: themeStyles.getTextColor('secondary')
                                            },
                                            children: "System Health"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/layout/app-footer.tsx",
                                            lineNumber: 166,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/layout/app-footer.tsx",
                                    lineNumber: 138,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/layout/app-footer.tsx",
                            lineNumber: 126,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Text, {
                                    style: {
                                        fontSize: '14px',
                                        fontWeight: 'bold',
                                        color: themeStyles.getTextColor('primary'),
                                        marginBottom: '12px',
                                        display: 'block'
                                    },
                                    children: "Resources"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/layout/app-footer.tsx",
                                    lineNumber: 180,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    style: {
                                        display: 'flex',
                                        flexDirection: 'column',
                                        gap: '8px'
                                    },
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Link, {
                                            href: "/system/api-docs",
                                            style: {
                                                fontSize: '13px',
                                                color: themeStyles.getTextColor('secondary')
                                            },
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$ApiOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ApiOutlined$3e$__["ApiOutlined"], {
                                                    style: {
                                                        marginRight: '4px'
                                                    }
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/layout/app-footer.tsx",
                                                    lineNumber: 199,
                                                    columnNumber: 17
                                                }, this),
                                                "API Documentation"
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/layout/app-footer.tsx",
                                            lineNumber: 192,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Link, {
                                            href: "/components-demo",
                                            style: {
                                                fontSize: '13px',
                                                color: themeStyles.getTextColor('secondary')
                                            },
                                            children: "Component Library"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/layout/app-footer.tsx",
                                            lineNumber: 202,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Link, {
                                            href: "/theme-demo",
                                            style: {
                                                fontSize: '13px',
                                                color: themeStyles.getTextColor('secondary')
                                            },
                                            children: "Theme System"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/layout/app-footer.tsx",
                                            lineNumber: 211,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Link, {
                                            href: "https://github.com/apisportsgame",
                                            target: "_blank",
                                            rel: "noopener noreferrer",
                                            style: {
                                                fontSize: '13px',
                                                color: themeStyles.getTextColor('secondary')
                                            },
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$GithubOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__GithubOutlined$3e$__["GithubOutlined"], {
                                                    style: {
                                                        marginRight: '4px'
                                                    }
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/layout/app-footer.tsx",
                                                    lineNumber: 229,
                                                    columnNumber: 17
                                                }, this),
                                                "GitHub Repository"
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/layout/app-footer.tsx",
                                            lineNumber: 220,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/layout/app-footer.tsx",
                                    lineNumber: 191,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/layout/app-footer.tsx",
                            lineNumber: 179,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Text, {
                                    style: {
                                        fontSize: '14px',
                                        fontWeight: 'bold',
                                        color: themeStyles.getTextColor('primary'),
                                        marginBottom: '12px',
                                        display: 'block'
                                    },
                                    children: "Connect"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/layout/app-footer.tsx",
                                    lineNumber: 237,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    style: {
                                        display: 'flex',
                                        flexDirection: 'column',
                                        gap: '8px'
                                    },
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Link, {
                                            href: "https://github.com/apisportsgame",
                                            target: "_blank",
                                            rel: "noopener noreferrer",
                                            style: {
                                                fontSize: '13px',
                                                color: themeStyles.getTextColor('secondary')
                                            },
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$GithubOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__GithubOutlined$3e$__["GithubOutlined"], {
                                                    style: {
                                                        marginRight: '4px'
                                                    }
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/layout/app-footer.tsx",
                                                    lineNumber: 258,
                                                    columnNumber: 17
                                                }, this),
                                                "GitHub"
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/layout/app-footer.tsx",
                                            lineNumber: 249,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Link, {
                                            href: "https://twitter.com/apisportsgame",
                                            target: "_blank",
                                            rel: "noopener noreferrer",
                                            style: {
                                                fontSize: '13px',
                                                color: themeStyles.getTextColor('secondary')
                                            },
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$TwitterOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__TwitterOutlined$3e$__["TwitterOutlined"], {
                                                    style: {
                                                        marginRight: '4px'
                                                    }
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/layout/app-footer.tsx",
                                                    lineNumber: 270,
                                                    columnNumber: 17
                                                }, this),
                                                "Twitter"
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/layout/app-footer.tsx",
                                            lineNumber: 261,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Link, {
                                            href: "https://linkedin.com/company/apisportsgame",
                                            target: "_blank",
                                            rel: "noopener noreferrer",
                                            style: {
                                                fontSize: '13px',
                                                color: themeStyles.getTextColor('secondary')
                                            },
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$LinkedinOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__LinkedinOutlined$3e$__["LinkedinOutlined"], {
                                                    style: {
                                                        marginRight: '4px'
                                                    }
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/layout/app-footer.tsx",
                                                    lineNumber: 282,
                                                    columnNumber: 17
                                                }, this),
                                                "LinkedIn"
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/layout/app-footer.tsx",
                                            lineNumber: 273,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Link, {
                                            href: "https://apisportsgame.com",
                                            target: "_blank",
                                            rel: "noopener noreferrer",
                                            style: {
                                                fontSize: '13px',
                                                color: themeStyles.getTextColor('secondary')
                                            },
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$GlobalOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__GlobalOutlined$3e$__["GlobalOutlined"], {
                                                    style: {
                                                        marginRight: '4px'
                                                    }
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/layout/app-footer.tsx",
                                                    lineNumber: 294,
                                                    columnNumber: 17
                                                }, this),
                                                "Website"
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/layout/app-footer.tsx",
                                            lineNumber: 285,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/layout/app-footer.tsx",
                                    lineNumber: 248,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/layout/app-footer.tsx",
                            lineNumber: 236,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/layout/app-footer.tsx",
                    lineNumber: 68,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$divider$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Divider$3e$__["Divider"], {
                    style: {
                        margin: '24px 0 16px 0'
                    }
                }, void 0, false, {
                    fileName: "[project]/src/components/layout/app-footer.tsx",
                    lineNumber: 301,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    style: {
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        flexWrap: 'wrap',
                        gap: '16px'
                    },
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Text, {
                            style: {
                                fontSize: '13px',
                                color: themeStyles.getTextColor('tertiary')
                            },
                            children: [
                                "© ",
                                currentYear,
                                " APISportsGame CMS. All rights reserved. Built with",
                                ' ',
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$HeartFilled$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__HeartFilled$3e$__["HeartFilled"], {
                                    style: {
                                        color: themeStyles.getColor('error')
                                    }
                                }, void 0, false, {
                                    fileName: "[project]/src/components/layout/app-footer.tsx",
                                    lineNumber: 320,
                                    columnNumber: 13
                                }, this),
                                " by Augment Code"
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/layout/app-footer.tsx",
                            lineNumber: 313,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$space$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__$3c$export__default__as__Space$3e$__["Space"], {
                            size: "middle",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Link, {
                                    href: "/privacy",
                                    style: {
                                        fontSize: '13px',
                                        color: themeStyles.getTextColor('tertiary')
                                    },
                                    children: "Privacy Policy"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/layout/app-footer.tsx",
                                    lineNumber: 324,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Link, {
                                    href: "/terms",
                                    style: {
                                        fontSize: '13px',
                                        color: themeStyles.getTextColor('tertiary')
                                    },
                                    children: "Terms of Service"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/layout/app-footer.tsx",
                                    lineNumber: 333,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Text, {
                                    style: {
                                        fontSize: '13px',
                                        color: themeStyles.getTextColor('tertiary')
                                    },
                                    children: "v1.0.0"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/layout/app-footer.tsx",
                                    lineNumber: 342,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/layout/app-footer.tsx",
                            lineNumber: 323,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/layout/app-footer.tsx",
                    lineNumber: 304,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/layout/app-footer.tsx",
            lineNumber: 66,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/layout/app-footer.tsx",
        lineNumber: 65,
        columnNumber: 5
    }, this);
}
_s(AppFooter, "Px+foAvjAxePbahVty9OoyFHaM0=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useThemeStyles"]
    ];
});
_c = AppFooter;
function SimpleFooter({ className, style }) {
    _s1();
    const themeStyles = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useThemeStyles"])();
    const currentYear = new Date().getFullYear();
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: className,
        style: {
            padding: '16px 24px',
            textAlign: 'center',
            backgroundColor: themeStyles.getBackgroundColor('container'),
            borderTop: `1px solid ${themeStyles.getBorderColor('primary')}`,
            ...style
        },
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Text, {
            style: {
                fontSize: '12px',
                color: themeStyles.getTextColor('tertiary')
            },
            children: [
                "© ",
                currentYear,
                " APISportsGame CMS. Built with",
                ' ',
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$HeartFilled$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__HeartFilled$3e$__["HeartFilled"], {
                    style: {
                        color: themeStyles.getColor('error')
                    }
                }, void 0, false, {
                    fileName: "[project]/src/components/layout/app-footer.tsx",
                    lineNumber: 387,
                    columnNumber: 9
                }, this),
                " by Augment Code"
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/layout/app-footer.tsx",
            lineNumber: 380,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/layout/app-footer.tsx",
        lineNumber: 370,
        columnNumber: 5
    }, this);
}
_s1(SimpleFooter, "Px+foAvjAxePbahVty9OoyFHaM0=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useThemeStyles"]
    ];
});
_c1 = SimpleFooter;
var _c, _c1;
__turbopack_refresh__.register(_c, "AppFooter");
__turbopack_refresh__.register(_c1, "SimpleFooter");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/layout/app-layout.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Main App Layout Component
 * Primary layout structure for the APISportsGame CMS
 */ __turbopack_esm__({
    "AppLayout": (()=>AppLayout),
    "LayoutProvider": (()=>LayoutProvider),
    "SimpleLayout": (()=>SimpleLayout),
    "useLayout": (()=>useLayout)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_import__("[project]/src/theme/index.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_import__("[project]/src/stores/index.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$app$2d$header$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/layout/app-header.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$app$2d$sidebar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/layout/app-sidebar.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$app$2d$footer$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/layout/app-footer.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/theme/hooks.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$layout$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Layout$3e$__ = __turbopack_import__("[project]/node_modules/antd/es/layout/index.js [app-client] (ecmascript) <export default as Layout>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$provider$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/provider-hooks.ts [app-client] (ecmascript)");
;
var _s = __turbopack_refresh__.signature(), _s1 = __turbopack_refresh__.signature(), _s2 = __turbopack_refresh__.signature(), _s3 = __turbopack_refresh__.signature();
'use client';
;
;
;
;
;
;
;
const { Content } = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$layout$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Layout$3e$__["Layout"];
function AppLayout({ children, className, style }) {
    _s();
    const themeStyles = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useThemeStyles"])();
    const app = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$provider$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppProvider"])();
    // Layout state
    const [sidebarCollapsed, setSidebarCollapsed] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isMobile, setIsMobile] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    // Handle responsive behavior
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AppLayout.useEffect": ()=>{
            const handleResize = {
                "AppLayout.useEffect.handleResize": ()=>{
                    const mobile = window.innerWidth < 768;
                    setIsMobile(mobile);
                    // Auto-collapse sidebar on mobile
                    if (mobile && !sidebarCollapsed) {
                        setSidebarCollapsed(true);
                    }
                }
            }["AppLayout.useEffect.handleResize"];
            // Initial check
            handleResize();
            // Add event listener
            window.addEventListener('resize', handleResize);
            // Cleanup
            return ({
                "AppLayout.useEffect": ()=>window.removeEventListener('resize', handleResize)
            })["AppLayout.useEffect"];
        }
    }["AppLayout.useEffect"], [
        sidebarCollapsed
    ]);
    // Handle sidebar toggle
    const handleSidebarToggle = ()=>{
        setSidebarCollapsed(!sidebarCollapsed);
    };
    // Handle mobile sidebar overlay
    const handleMobileOverlayClick = ()=>{
        if (isMobile && !sidebarCollapsed) {
            setSidebarCollapsed(true);
        }
    };
    const layoutStyle = {
        minHeight: '100vh',
        backgroundColor: themeStyles.getBackgroundColor('layout'),
        ...style
    };
    const contentStyle = {
        marginLeft: isMobile ? 0 : sidebarCollapsed ? '80px' : '250px',
        transition: 'margin-left 0.2s ease',
        minHeight: 'calc(100vh - 64px)',
        backgroundColor: themeStyles.getBackgroundColor('layout')
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$layout$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Layout$3e$__["Layout"], {
        className: className,
        style: layoutStyle,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$app$2d$header$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AppHeader"], {
                sidebarCollapsed: sidebarCollapsed,
                onSidebarToggle: handleSidebarToggle,
                isMobile: isMobile
            }, void 0, false, {
                fileName: "[project]/src/components/layout/app-layout.tsx",
                lineNumber: 88,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$app$2d$sidebar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AppSidebar"], {
                collapsed: sidebarCollapsed,
                isMobile: isMobile,
                onCollapse: setSidebarCollapsed
            }, void 0, false, {
                fileName: "[project]/src/components/layout/app-layout.tsx",
                lineNumber: 95,
                columnNumber: 7
            }, this),
            isMobile && !sidebarCollapsed && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                style: {
                    position: 'fixed',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    backgroundColor: 'rgba(0, 0, 0, 0.5)',
                    zIndex: 999
                },
                onClick: handleMobileOverlayClick
            }, void 0, false, {
                fileName: "[project]/src/components/layout/app-layout.tsx",
                lineNumber: 103,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$layout$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Layout$3e$__["Layout"], {
                style: contentStyle,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Content, {
                        style: {
                            padding: '16px 24px',
                            backgroundColor: themeStyles.getBackgroundColor('layout'),
                            overflow: 'auto'
                        },
                        children: children
                    }, void 0, false, {
                        fileName: "[project]/src/components/layout/app-layout.tsx",
                        lineNumber: 119,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$app$2d$footer$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AppFooter"], {}, void 0, false, {
                        fileName: "[project]/src/components/layout/app-layout.tsx",
                        lineNumber: 130,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/layout/app-layout.tsx",
                lineNumber: 118,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/layout/app-layout.tsx",
        lineNumber: 86,
        columnNumber: 5
    }, this);
}
_s(AppLayout, "k1xWTv/0AgnfGgjGFR0bOaNIeQE=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useThemeStyles"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$provider$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppProvider"]
    ];
});
_c = AppLayout;
const LayoutContext = /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createContext(undefined);
function LayoutProvider({ children }) {
    _s1();
    const [sidebarCollapsed, setSidebarCollapsed] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isMobile, setIsMobile] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "LayoutProvider.useEffect": ()=>{
            const handleResize = {
                "LayoutProvider.useEffect.handleResize": ()=>{
                    const mobile = window.innerWidth < 768;
                    setIsMobile(mobile);
                }
            }["LayoutProvider.useEffect.handleResize"];
            handleResize();
            window.addEventListener('resize', handleResize);
            return ({
                "LayoutProvider.useEffect": ()=>window.removeEventListener('resize', handleResize)
            })["LayoutProvider.useEffect"];
        }
    }["LayoutProvider.useEffect"], []);
    const toggleSidebar = ()=>{
        setSidebarCollapsed(!sidebarCollapsed);
    };
    const value = {
        sidebarCollapsed,
        setSidebarCollapsed,
        isMobile,
        toggleSidebar
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(LayoutContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/components/layout/app-layout.tsx",
        lineNumber: 175,
        columnNumber: 5
    }, this);
}
_s1(LayoutProvider, "hbVz9H8x6m+0n8x9/01ZaH6vDI8=");
_c1 = LayoutProvider;
function useLayout() {
    _s2();
    const context = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useContext(LayoutContext);
    if (context === undefined) {
        throw new Error('useLayout must be used within a LayoutProvider');
    }
    return context;
}
_s2(useLayout, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
function SimpleLayout({ children, showHeader = true, showFooter = true, className, style }) {
    _s3();
    const themeStyles = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useThemeStyles"])();
    const layoutStyle = {
        minHeight: '100vh',
        backgroundColor: themeStyles.getBackgroundColor('layout'),
        display: 'flex',
        flexDirection: 'column',
        ...style
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$layout$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Layout$3e$__["Layout"], {
        className: className,
        style: layoutStyle,
        children: [
            showHeader && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$app$2d$header$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AppHeader"], {
                sidebarCollapsed: true,
                onSidebarToggle: ()=>{},
                isMobile: false,
                showSidebarToggle: false
            }, void 0, false, {
                fileName: "[project]/src/components/layout/app-layout.tsx",
                lineNumber: 223,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Content, {
                style: {
                    flex: 1,
                    padding: '24px',
                    backgroundColor: themeStyles.getBackgroundColor('layout')
                },
                children: children
            }, void 0, false, {
                fileName: "[project]/src/components/layout/app-layout.tsx",
                lineNumber: 231,
                columnNumber: 7
            }, this),
            showFooter && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$app$2d$footer$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AppFooter"], {}, void 0, false, {
                fileName: "[project]/src/components/layout/app-layout.tsx",
                lineNumber: 241,
                columnNumber: 22
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/layout/app-layout.tsx",
        lineNumber: 221,
        columnNumber: 5
    }, this);
}
_s3(SimpleLayout, "Px+foAvjAxePbahVty9OoyFHaM0=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useThemeStyles"]
    ];
});
_c2 = SimpleLayout;
var _c, _c1, _c2;
__turbopack_refresh__.register(_c, "AppLayout");
__turbopack_refresh__.register(_c1, "LayoutProvider");
__turbopack_refresh__.register(_c2, "SimpleLayout");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/layout/auth-layout.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Authentication Layout Component
 * Specialized layout for authentication pages
 */ __turbopack_esm__({
    "AuthCard": (()=>AuthCard),
    "AuthDivider": (()=>AuthDivider),
    "AuthForm": (()=>AuthForm),
    "AuthLayout": (()=>AuthLayout)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_import__("[project]/src/theme/index.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$app$2d$footer$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/layout/app-footer.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/theme/hooks.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$layout$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Layout$3e$__ = __turbopack_import__("[project]/node_modules/antd/es/layout/index.js [app-client] (ecmascript) <export default as Layout>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$typography$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Typography$3e$__ = __turbopack_import__("[project]/node_modules/antd/es/typography/index.js [app-client] (ecmascript) <export default as Typography>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$card$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Card$3e$__ = __turbopack_import__("[project]/node_modules/antd/es/card/index.js [app-client] (ecmascript) <export default as Card>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$space$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__$3c$export__default__as__Space$3e$__ = __turbopack_import__("[project]/node_modules/antd/es/space/index.js [app-client] (ecmascript) <locals> <export default as Space>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$divider$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Divider$3e$__ = __turbopack_import__("[project]/node_modules/antd/es/divider/index.js [app-client] (ecmascript) <export default as Divider>");
;
var _s = __turbopack_refresh__.signature(), _s1 = __turbopack_refresh__.signature(), _s2 = __turbopack_refresh__.signature();
'use client';
;
;
;
const { Content } = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$layout$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Layout$3e$__["Layout"];
const { Title, Text, Link } = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$typography$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Typography$3e$__["Typography"];
function AuthLayout({ children, title = 'APISportsGame CMS', subtitle = 'System Administration Portal', showFooter = true, className, style }) {
    _s();
    const themeStyles = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useThemeStyles"])();
    const layoutStyle = {
        minHeight: '100vh',
        backgroundColor: themeStyles.getBackgroundColor('layout'),
        display: 'flex',
        flexDirection: 'column',
        ...style
    };
    const contentStyle = {
        flex: 1,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '24px',
        backgroundImage: `linear-gradient(135deg, ${themeStyles.getColor('primary')}10 0%, ${themeStyles.getColor('primary')}05 100%)`
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$layout$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Layout$3e$__["Layout"], {
        className: className,
        style: layoutStyle,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Content, {
                style: contentStyle,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    style: {
                        width: '100%',
                        maxWidth: '400px',
                        margin: '0 auto'
                    },
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            style: {
                                textAlign: 'center',
                                marginBottom: '32px'
                            },
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    style: {
                                        display: 'flex',
                                        justifyContent: 'center',
                                        marginBottom: '16px'
                                    },
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        style: {
                                            width: '64px',
                                            height: '64px',
                                            backgroundColor: themeStyles.getColor('primary'),
                                            borderRadius: '12px',
                                            display: 'flex',
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                            color: 'white',
                                            fontSize: '32px',
                                            fontWeight: 'bold',
                                            boxShadow: `0 8px 24px ${themeStyles.getColor('primary')}30`
                                        },
                                        children: "⚽"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/layout/auth-layout.tsx",
                                        lineNumber: 83,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/layout/auth-layout.tsx",
                                    lineNumber: 76,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Title, {
                                    level: 2,
                                    style: {
                                        color: themeStyles.getTextColor('primary'),
                                        marginBottom: '8px',
                                        fontSize: '28px',
                                        fontWeight: 'bold'
                                    },
                                    children: title
                                }, void 0, false, {
                                    fileName: "[project]/src/components/layout/auth-layout.tsx",
                                    lineNumber: 103,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Text, {
                                    style: {
                                        color: themeStyles.getTextColor('secondary'),
                                        fontSize: '16px'
                                    },
                                    children: subtitle
                                }, void 0, false, {
                                    fileName: "[project]/src/components/layout/auth-layout.tsx",
                                    lineNumber: 116,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/layout/auth-layout.tsx",
                            lineNumber: 69,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$card$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Card$3e$__["Card"], {
                            style: {
                                backgroundColor: themeStyles.getBackgroundColor('container'),
                                border: `1px solid ${themeStyles.getBorderColor('primary')}`,
                                borderRadius: '12px',
                                boxShadow: `0 8px 32px ${themeStyles.getColor('primary')}08`
                            },
                            bodyStyle: {
                                padding: '32px'
                            },
                            children: children
                        }, void 0, false, {
                            fileName: "[project]/src/components/layout/auth-layout.tsx",
                            lineNumber: 127,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            style: {
                                textAlign: 'center',
                                marginTop: '24px'
                            },
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$space$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__$3c$export__default__as__Space$3e$__["Space"], {
                                split: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$divider$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Divider$3e$__["Divider"], {
                                    type: "vertical"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/layout/auth-layout.tsx",
                                    lineNumber: 148,
                                    columnNumber: 27
                                }, void 0),
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Link, {
                                        href: "/help",
                                        style: {
                                            color: themeStyles.getTextColor('secondary'),
                                            fontSize: '14px'
                                        },
                                        children: "Help & Support"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/layout/auth-layout.tsx",
                                        lineNumber: 149,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Link, {
                                        href: "/privacy",
                                        style: {
                                            color: themeStyles.getTextColor('secondary'),
                                            fontSize: '14px'
                                        },
                                        children: "Privacy Policy"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/layout/auth-layout.tsx",
                                        lineNumber: 158,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Link, {
                                        href: "/terms",
                                        style: {
                                            color: themeStyles.getTextColor('secondary'),
                                            fontSize: '14px'
                                        },
                                        children: "Terms of Service"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/layout/auth-layout.tsx",
                                        lineNumber: 167,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/layout/auth-layout.tsx",
                                lineNumber: 148,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/layout/auth-layout.tsx",
                            lineNumber: 142,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/layout/auth-layout.tsx",
                    lineNumber: 61,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/layout/auth-layout.tsx",
                lineNumber: 60,
                columnNumber: 7
            }, this),
            showFooter && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$app$2d$footer$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SimpleFooter"], {
                style: {
                    backgroundColor: themeStyles.getBackgroundColor('container'),
                    borderTop: `1px solid ${themeStyles.getBorderColor('primary')}`
                }
            }, void 0, false, {
                fileName: "[project]/src/components/layout/auth-layout.tsx",
                lineNumber: 183,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/layout/auth-layout.tsx",
        lineNumber: 59,
        columnNumber: 5
    }, this);
}
_s(AuthLayout, "Px+foAvjAxePbahVty9OoyFHaM0=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useThemeStyles"]
    ];
});
_c = AuthLayout;
function AuthCard({ children, title, description, className, style }) {
    _s1();
    const themeStyles = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useThemeStyles"])();
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: className,
        style: style,
        children: [
            (title || description) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                style: {
                    marginBottom: '24px',
                    textAlign: 'center'
                },
                children: [
                    title && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Title, {
                        level: 3,
                        style: {
                            color: themeStyles.getTextColor('primary'),
                            marginBottom: description ? '8px' : '0',
                            fontSize: '24px',
                            fontWeight: 'bold'
                        },
                        children: title
                    }, void 0, false, {
                        fileName: "[project]/src/components/layout/auth-layout.tsx",
                        lineNumber: 219,
                        columnNumber: 13
                    }, this),
                    description && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Text, {
                        style: {
                            color: themeStyles.getTextColor('secondary'),
                            fontSize: '14px',
                            lineHeight: 1.5
                        },
                        children: description
                    }, void 0, false, {
                        fileName: "[project]/src/components/layout/auth-layout.tsx",
                        lineNumber: 232,
                        columnNumber: 13
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/layout/auth-layout.tsx",
                lineNumber: 217,
                columnNumber: 9
            }, this),
            children
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/layout/auth-layout.tsx",
        lineNumber: 215,
        columnNumber: 5
    }, this);
}
_s1(AuthCard, "Px+foAvjAxePbahVty9OoyFHaM0=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useThemeStyles"]
    ];
});
_c1 = AuthCard;
function AuthForm({ children, onSubmit, className, style }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
        className: className,
        style: {
            width: '100%',
            ...style
        },
        onSubmit: onSubmit,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$space$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__$3c$export__default__as__Space$3e$__["Space"], {
            direction: "vertical",
            size: "large",
            style: {
                width: '100%'
            },
            children: children
        }, void 0, false, {
            fileName: "[project]/src/components/layout/auth-layout.tsx",
            lineNumber: 274,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/layout/auth-layout.tsx",
        lineNumber: 266,
        columnNumber: 5
    }, this);
}
_c2 = AuthForm;
function AuthDivider({ text = 'OR', className, style }) {
    _s2();
    const themeStyles = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useThemeStyles"])();
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: className,
        style: {
            position: 'relative',
            textAlign: 'center',
            margin: '24px 0',
            ...style
        },
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$divider$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Divider$3e$__["Divider"], {
                style: {
                    borderColor: themeStyles.getBorderColor('primary')
                }
            }, void 0, false, {
                fileName: "[project]/src/components/layout/auth-layout.tsx",
                lineNumber: 311,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                style: {
                    position: 'absolute',
                    top: '50%',
                    left: '50%',
                    transform: 'translate(-50%, -50%)',
                    backgroundColor: themeStyles.getBackgroundColor('container'),
                    padding: '0 16px',
                    color: themeStyles.getTextColor('tertiary'),
                    fontSize: '12px',
                    fontWeight: 'bold'
                },
                children: text
            }, void 0, false, {
                fileName: "[project]/src/components/layout/auth-layout.tsx",
                lineNumber: 316,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/layout/auth-layout.tsx",
        lineNumber: 302,
        columnNumber: 5
    }, this);
}
_s2(AuthDivider, "Px+foAvjAxePbahVty9OoyFHaM0=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useThemeStyles"]
    ];
});
_c3 = AuthDivider;
var _c, _c1, _c2, _c3;
__turbopack_refresh__.register(_c, "AuthLayout");
__turbopack_refresh__.register(_c1, "AuthCard");
__turbopack_refresh__.register(_c2, "AuthForm");
__turbopack_refresh__.register(_c3, "AuthDivider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/layout/page-header.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Page Header Component
 * Reusable page header with breadcrumbs, title, and actions
 */ __turbopack_esm__({
    "PageHeader": (()=>PageHeader),
    "SectionHeader": (()=>SectionHeader),
    "SimplePageHeader": (()=>SimplePageHeader)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_import__("[project]/src/theme/index.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/theme/hooks.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$HomeOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__HomeOutlined$3e$__ = __turbopack_import__("[project]/node_modules/@ant-design/icons/es/icons/HomeOutlined.js [app-client] (ecmascript) <export default as HomeOutlined>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$breadcrumb$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Breadcrumb$3e$__ = __turbopack_import__("[project]/node_modules/antd/es/breadcrumb/index.js [app-client] (ecmascript) <export default as Breadcrumb>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$space$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__$3c$export__default__as__Space$3e$__ = __turbopack_import__("[project]/node_modules/antd/es/space/index.js [app-client] (ecmascript) <locals> <export default as Space>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$divider$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Divider$3e$__ = __turbopack_import__("[project]/node_modules/antd/es/divider/index.js [app-client] (ecmascript) <export default as Divider>");
;
var _s = __turbopack_refresh__.signature(), _s1 = __turbopack_refresh__.signature(), _s2 = __turbopack_refresh__.signature();
'use client';
;
;
;
function PageHeader({ title, subtitle, breadcrumbs = [], actions = [], extra, children, showDivider = true, className, style }) {
    _s();
    const themeStyles = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useThemeStyles"])();
    // Add home breadcrumb if not present
    const allBreadcrumbs = breadcrumbs.length > 0 && breadcrumbs[0].href !== '/' ? [
        {
            title: 'Home',
            href: '/',
            icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$HomeOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__HomeOutlined$3e$__["HomeOutlined"], {}, void 0, false, {
                fileName: "[project]/src/components/layout/page-header.tsx",
                lineNumber: 55,
                columnNumber: 42
            }, this)
        },
        ...breadcrumbs
    ] : breadcrumbs;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: className,
        style: style,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            style: {
                padding: '16px 24px',
                backgroundColor: themeStyles.getBackgroundColor('container'),
                borderBottom: showDivider ? `1px solid ${themeStyles.getBorderColor('primary')}` : 'none'
            },
            children: [
                allBreadcrumbs.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$breadcrumb$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Breadcrumb$3e$__["Breadcrumb"], {
                    style: {
                        marginBottom: '8px'
                    },
                    children: allBreadcrumbs.map((item, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$breadcrumb$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Breadcrumb$3e$__["Breadcrumb"].Item, {
                            href: item.href,
                            children: [
                                item.icon && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    style: {
                                        marginRight: '4px'
                                    },
                                    children: item.icon
                                }, void 0, false, {
                                    fileName: "[project]/src/components/layout/page-header.tsx",
                                    lineNumber: 70,
                                    columnNumber: 31
                                }, this),
                                item.title
                            ]
                        }, index, true, {
                            fileName: "[project]/src/components/layout/page-header.tsx",
                            lineNumber: 69,
                            columnNumber: 15
                        }, this))
                }, void 0, false, {
                    fileName: "[project]/src/components/layout/page-header.tsx",
                    lineNumber: 67,
                    columnNumber: 11
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    style: {
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        flexWrap: 'wrap',
                        gap: '16px'
                    },
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            style: {
                                flex: 1,
                                minWidth: '200px'
                            },
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                    style: {
                                        margin: 0,
                                        fontSize: '24px',
                                        fontWeight: 'bold',
                                        color: themeStyles.getTextColor('primary'),
                                        lineHeight: 1.2
                                    },
                                    children: title
                                }, void 0, false, {
                                    fileName: "[project]/src/components/layout/page-header.tsx",
                                    lineNumber: 87,
                                    columnNumber: 13
                                }, this),
                                subtitle && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    style: {
                                        margin: '4px 0 0 0',
                                        fontSize: '14px',
                                        color: themeStyles.getTextColor('secondary'),
                                        lineHeight: 1.4
                                    },
                                    children: subtitle
                                }, void 0, false, {
                                    fileName: "[project]/src/components/layout/page-header.tsx",
                                    lineNumber: 97,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/layout/page-header.tsx",
                            lineNumber: 86,
                            columnNumber: 11
                        }, this),
                        (actions.length > 0 || extra) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            style: {
                                display: 'flex',
                                alignItems: 'center',
                                gap: '12px',
                                flexWrap: 'wrap'
                            },
                            children: [
                                actions.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$space$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__$3c$export__default__as__Space$3e$__["Space"], {
                                    size: "middle",
                                    children: actions
                                }, void 0, false, {
                                    fileName: "[project]/src/components/layout/page-header.tsx",
                                    lineNumber: 117,
                                    columnNumber: 17
                                }, this),
                                extra
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/layout/page-header.tsx",
                            lineNumber: 110,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/layout/page-header.tsx",
                    lineNumber: 78,
                    columnNumber: 9
                }, this),
                children && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    style: {
                        marginTop: '16px'
                    },
                    children: children
                }, void 0, false, {
                    fileName: "[project]/src/components/layout/page-header.tsx",
                    lineNumber: 128,
                    columnNumber: 11
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/layout/page-header.tsx",
            lineNumber: 60,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/layout/page-header.tsx",
        lineNumber: 59,
        columnNumber: 5
    }, this);
}
_s(PageHeader, "Px+foAvjAxePbahVty9OoyFHaM0=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useThemeStyles"]
    ];
});
_c = PageHeader;
function SimplePageHeader({ title, subtitle, backButton = false, onBack, actions = [], className, style }) {
    _s1();
    const themeStyles = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useThemeStyles"])();
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: className,
        style: {
            padding: '16px 24px',
            backgroundColor: themeStyles.getBackgroundColor('container'),
            borderBottom: `1px solid ${themeStyles.getBorderColor('primary')}`,
            ...style
        },
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            style: {
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                gap: '16px'
            },
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    style: {
                        display: 'flex',
                        alignItems: 'center',
                        gap: '12px',
                        flex: 1
                    },
                    children: [
                        backButton && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            onClick: onBack,
                            style: {
                                background: 'none',
                                border: 'none',
                                cursor: 'pointer',
                                fontSize: '16px',
                                color: themeStyles.getTextColor('secondary'),
                                padding: '4px'
                            },
                            children: "←"
                        }, void 0, false, {
                            fileName: "[project]/src/components/layout/page-header.tsx",
                            lineNumber: 179,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                    style: {
                                        margin: 0,
                                        fontSize: '20px',
                                        fontWeight: 'bold',
                                        color: themeStyles.getTextColor('primary')
                                    },
                                    children: title
                                }, void 0, false, {
                                    fileName: "[project]/src/components/layout/page-header.tsx",
                                    lineNumber: 194,
                                    columnNumber: 13
                                }, this),
                                subtitle && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    style: {
                                        margin: '2px 0 0 0',
                                        fontSize: '12px',
                                        color: themeStyles.getTextColor('secondary')
                                    },
                                    children: subtitle
                                }, void 0, false, {
                                    fileName: "[project]/src/components/layout/page-header.tsx",
                                    lineNumber: 203,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/layout/page-header.tsx",
                            lineNumber: 193,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/layout/page-header.tsx",
                    lineNumber: 177,
                    columnNumber: 9
                }, this),
                actions.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$space$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__$3c$export__default__as__Space$3e$__["Space"], {
                    size: "small",
                    children: actions
                }, void 0, false, {
                    fileName: "[project]/src/components/layout/page-header.tsx",
                    lineNumber: 215,
                    columnNumber: 11
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/layout/page-header.tsx",
            lineNumber: 171,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/layout/page-header.tsx",
        lineNumber: 162,
        columnNumber: 5
    }, this);
}
_s1(SimplePageHeader, "Px+foAvjAxePbahVty9OoyFHaM0=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useThemeStyles"]
    ];
});
_c1 = SimplePageHeader;
function SectionHeader({ title, subtitle, actions = [], divider = false, size = 'medium', className, style }) {
    _s2();
    const themeStyles = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useThemeStyles"])();
    const sizeMap = {
        small: {
            fontSize: '16px',
            marginBottom: '12px'
        },
        medium: {
            fontSize: '18px',
            marginBottom: '16px'
        },
        large: {
            fontSize: '20px',
            marginBottom: '20px'
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: className,
        style: style,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                style: {
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    marginBottom: sizeMap[size].marginBottom
                },
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                style: {
                                    margin: 0,
                                    fontSize: sizeMap[size].fontSize,
                                    fontWeight: 'bold',
                                    color: themeStyles.getTextColor('primary')
                                },
                                children: title
                            }, void 0, false, {
                                fileName: "[project]/src/components/layout/page-header.tsx",
                                lineNumber: 263,
                                columnNumber: 11
                            }, this),
                            subtitle && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                style: {
                                    margin: '4px 0 0 0',
                                    fontSize: '12px',
                                    color: themeStyles.getTextColor('secondary')
                                },
                                children: subtitle
                            }, void 0, false, {
                                fileName: "[project]/src/components/layout/page-header.tsx",
                                lineNumber: 272,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/layout/page-header.tsx",
                        lineNumber: 262,
                        columnNumber: 9
                    }, this),
                    actions.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$space$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__$3c$export__default__as__Space$3e$__["Space"], {
                        size: "small",
                        children: actions
                    }, void 0, false, {
                        fileName: "[project]/src/components/layout/page-header.tsx",
                        lineNumber: 283,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/layout/page-header.tsx",
                lineNumber: 256,
                columnNumber: 7
            }, this),
            divider && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$divider$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Divider$3e$__["Divider"], {
                style: {
                    margin: '0 0 16px 0'
                }
            }, void 0, false, {
                fileName: "[project]/src/components/layout/page-header.tsx",
                lineNumber: 289,
                columnNumber: 19
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/layout/page-header.tsx",
        lineNumber: 255,
        columnNumber: 5
    }, this);
}
_s2(SectionHeader, "Px+foAvjAxePbahVty9OoyFHaM0=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useThemeStyles"]
    ];
});
_c2 = SectionHeader;
var _c, _c1, _c2;
__turbopack_refresh__.register(_c, "PageHeader");
__turbopack_refresh__.register(_c1, "SimplePageHeader");
__turbopack_refresh__.register(_c2, "SectionHeader");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/layout/content-layout.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Content Layout Components
 * Reusable layout components for content organization
 */ __turbopack_esm__({
    "Container": (()=>Container),
    "ContentLayout": (()=>ContentLayout),
    "GridLayout": (()=>GridLayout),
    "SidebarLayout": (()=>SidebarLayout),
    "ThreeColumnLayout": (()=>ThreeColumnLayout),
    "TwoColumnLayout": (()=>TwoColumnLayout)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_import__("[project]/src/theme/index.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$layout$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Layout$3e$__ = __turbopack_import__("[project]/node_modules/antd/es/layout/index.js [app-client] (ecmascript) <export default as Layout>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/theme/hooks.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$row$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Row$3e$__ = __turbopack_import__("[project]/node_modules/antd/es/row/index.js [app-client] (ecmascript) <export default as Row>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$col$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Col$3e$__ = __turbopack_import__("[project]/node_modules/antd/es/col/index.js [app-client] (ecmascript) <export default as Col>");
;
var _s = __turbopack_refresh__.signature(), _s1 = __turbopack_refresh__.signature();
'use client';
;
;
;
const { Content } = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$layout$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Layout$3e$__["Layout"];
function ContentLayout({ children, maxWidth = '1200px', padding = 'medium', centered = true, className, style }) {
    _s();
    const themeStyles = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useThemeStyles"])();
    const paddingMap = {
        none: '0',
        small: '12px',
        medium: '24px',
        large: '32px'
    };
    const contentStyle = {
        maxWidth: typeof maxWidth === 'number' ? `${maxWidth}px` : maxWidth,
        margin: centered ? '0 auto' : '0',
        padding: paddingMap[padding],
        backgroundColor: themeStyles.getBackgroundColor('layout'),
        minHeight: 'calc(100vh - 64px)',
        ...style
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Content, {
        className: className,
        style: contentStyle,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/components/layout/content-layout.tsx",
        lineNumber: 56,
        columnNumber: 5
    }, this);
}
_s(ContentLayout, "Px+foAvjAxePbahVty9OoyFHaM0=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useThemeStyles"]
    ];
});
_c = ContentLayout;
function TwoColumnLayout({ leftContent, rightContent, leftSpan = 16, rightSpan = 8, gutter = 24, responsive = true, className, style }) {
    const responsiveProps = responsive ? {
        xs: 24,
        sm: 24,
        md: leftSpan,
        lg: leftSpan,
        xl: leftSpan
    } : {
        span: leftSpan
    };
    const rightResponsiveProps = responsive ? {
        xs: 24,
        sm: 24,
        md: rightSpan,
        lg: rightSpan,
        xl: rightSpan
    } : {
        span: rightSpan
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$row$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Row$3e$__["Row"], {
        gutter: gutter,
        className: className,
        style: style,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$col$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Col$3e$__["Col"], {
                ...responsiveProps,
                children: leftContent
            }, void 0, false, {
                fileName: "[project]/src/components/layout/content-layout.tsx",
                lineNumber: 107,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$col$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Col$3e$__["Col"], {
                ...rightResponsiveProps,
                children: rightContent
            }, void 0, false, {
                fileName: "[project]/src/components/layout/content-layout.tsx",
                lineNumber: 110,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/layout/content-layout.tsx",
        lineNumber: 106,
        columnNumber: 5
    }, this);
}
_c1 = TwoColumnLayout;
function ThreeColumnLayout({ leftContent, centerContent, rightContent, leftSpan = 6, centerSpan = 12, rightSpan = 6, gutter = 24, responsive = true, className, style }) {
    const getResponsiveProps = (span)=>responsive ? {
            xs: 24,
            sm: 24,
            md: span,
            lg: span,
            xl: span
        } : {
            span
        };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$row$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Row$3e$__["Row"], {
        gutter: gutter,
        className: className,
        style: style,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$col$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Col$3e$__["Col"], {
                ...getResponsiveProps(leftSpan),
                children: leftContent
            }, void 0, false, {
                fileName: "[project]/src/components/layout/content-layout.tsx",
                lineNumber: 158,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$col$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Col$3e$__["Col"], {
                ...getResponsiveProps(centerSpan),
                children: centerContent
            }, void 0, false, {
                fileName: "[project]/src/components/layout/content-layout.tsx",
                lineNumber: 161,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$col$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Col$3e$__["Col"], {
                ...getResponsiveProps(rightSpan),
                children: rightContent
            }, void 0, false, {
                fileName: "[project]/src/components/layout/content-layout.tsx",
                lineNumber: 164,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/layout/content-layout.tsx",
        lineNumber: 157,
        columnNumber: 5
    }, this);
}
_c2 = ThreeColumnLayout;
function GridLayout({ children, columns = 3, gutter = 24, responsive, className, style }) {
    const defaultResponsive = {
        xs: 1,
        sm: 1,
        md: 2,
        lg: columns,
        xl: columns,
        xxl: columns
    };
    const responsiveConfig = responsive || defaultResponsive;
    const span = 24 / columns;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$row$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Row$3e$__["Row"], {
        gutter: gutter,
        className: className,
        style: style,
        children: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Children.map(children, (child, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$col$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Col$3e$__["Col"], {
                xs: 24 / responsiveConfig.xs,
                sm: 24 / responsiveConfig.sm,
                md: 24 / responsiveConfig.md,
                lg: 24 / responsiveConfig.lg,
                xl: 24 / responsiveConfig.xl,
                xxl: 24 / responsiveConfig.xxl,
                children: child
            }, index, false, {
                fileName: "[project]/src/components/layout/content-layout.tsx",
                lineNumber: 216,
                columnNumber: 9
            }, this))
    }, void 0, false, {
        fileName: "[project]/src/components/layout/content-layout.tsx",
        lineNumber: 214,
        columnNumber: 5
    }, this);
}
_c3 = GridLayout;
function SidebarLayout({ sidebar, content, sidebarWidth = 250, sidebarPosition = 'left', collapsible = false, collapsed = false, onCollapse, className, style }) {
    _s1();
    const themeStyles = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useThemeStyles"])();
    const sidebarStyle = {
        width: collapsed ? '80px' : `${sidebarWidth}px`,
        backgroundColor: themeStyles.getBackgroundColor('container'),
        borderRight: sidebarPosition === 'left' ? `1px solid ${themeStyles.getBorderColor('primary')}` : 'none',
        borderLeft: sidebarPosition === 'right' ? `1px solid ${themeStyles.getBorderColor('primary')}` : 'none',
        transition: 'width 0.2s ease',
        overflow: 'hidden'
    };
    const contentStyle = {
        flex: 1,
        backgroundColor: themeStyles.getBackgroundColor('layout'),
        overflow: 'auto'
    };
    const layoutStyle = {
        display: 'flex',
        flexDirection: sidebarPosition === 'left' ? 'row' : 'row-reverse',
        height: '100%',
        ...style
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: className,
        style: layoutStyle,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                style: sidebarStyle,
                children: [
                    collapsible && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        style: {
                            padding: '8px',
                            borderBottom: `1px solid ${themeStyles.getBorderColor('primary')}`,
                            textAlign: 'center'
                        },
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            onClick: ()=>onCollapse?.(!collapsed),
                            style: {
                                background: 'none',
                                border: 'none',
                                cursor: 'pointer',
                                fontSize: '16px',
                                color: themeStyles.getTextColor('secondary')
                            },
                            children: collapsed ? '→' : '←'
                        }, void 0, false, {
                            fileName: "[project]/src/components/layout/content-layout.tsx",
                            lineNumber: 294,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/layout/content-layout.tsx",
                        lineNumber: 289,
                        columnNumber: 11
                    }, this),
                    sidebar
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/layout/content-layout.tsx",
                lineNumber: 287,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                style: contentStyle,
                children: content
            }, void 0, false, {
                fileName: "[project]/src/components/layout/content-layout.tsx",
                lineNumber: 310,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/layout/content-layout.tsx",
        lineNumber: 286,
        columnNumber: 5
    }, this);
}
_s1(SidebarLayout, "Px+foAvjAxePbahVty9OoyFHaM0=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$theme$2f$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useThemeStyles"]
    ];
});
_c4 = SidebarLayout;
function Container({ children, size = 'large', padding = true, centered = true, className, style }) {
    const sizeMap = {
        small: '600px',
        medium: '900px',
        large: '1200px',
        full: '100%'
    };
    const containerStyle = {
        maxWidth: sizeMap[size],
        margin: centered ? '0 auto' : '0',
        padding: padding ? '0 24px' : '0',
        width: '100%',
        ...style
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: className,
        style: containerStyle,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/components/layout/content-layout.tsx",
        lineNumber: 353,
        columnNumber: 5
    }, this);
}
_c5 = Container;
var _c, _c1, _c2, _c3, _c4, _c5;
__turbopack_refresh__.register(_c, "ContentLayout");
__turbopack_refresh__.register(_c1, "TwoColumnLayout");
__turbopack_refresh__.register(_c2, "ThreeColumnLayout");
__turbopack_refresh__.register(_c3, "GridLayout");
__turbopack_refresh__.register(_c4, "SidebarLayout");
__turbopack_refresh__.register(_c5, "Container");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/layout/index.ts [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Layout Components Index
 * Export all layout components
 */ // Main app layout components
__turbopack_esm__({
    "Content": (()=>Content),
    "Footer": (()=>Footer),
    "Header": (()=>Header),
    "LAYOUT_COMPONENTS_NAME": (()=>LAYOUT_COMPONENTS_NAME),
    "LAYOUT_COMPONENTS_VERSION": (()=>LAYOUT_COMPONENTS_VERSION),
    "Sider": (()=>Sider),
    "setupLayoutComponents": (()=>setupLayoutComponents)
});
// Export specific layout components
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$layout$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Layout$3e$__ = __turbopack_import__("[project]/node_modules/antd/es/layout/index.js [app-client] (ecmascript) <export default as Layout>");
;
;
;
;
;
;
;
;
;
const { Header, Footer, Sider, Content } = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$layout$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Layout$3e$__["Layout"];
;
const LAYOUT_COMPONENTS_VERSION = '1.0.0';
const LAYOUT_COMPONENTS_NAME = 'APISportsGame Layout Components';
function setupLayoutComponents() {
    console.log(`${LAYOUT_COMPONENTS_NAME} v${LAYOUT_COMPONENTS_VERSION} initialized`);
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/layout/index.ts [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_esm__({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$app$2d$layout$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/layout/app-layout.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$app$2d$header$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/layout/app-header.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$app$2d$sidebar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/layout/app-sidebar.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$app$2d$footer$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/layout/app-footer.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$auth$2d$layout$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/layout/auth-layout.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$page$2d$header$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/layout/page-header.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$content$2d$layout$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/layout/content-layout.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_import__("[project]/src/components/layout/index.ts [app-client] (ecmascript) <locals>");
}}),
"[project]/src/app/users/layout.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>UsersLayout)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_import__("[project]/src/components/layout/index.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$app$2d$layout$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/layout/app-layout.tsx [app-client] (ecmascript)");
'use client';
;
;
function UsersLayout({ children }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$app$2d$layout$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AppLayout"], {
        children: children
    }, void 0, false, {
        fileName: "[project]/src/app/users/layout.tsx",
        lineNumber: 10,
        columnNumber: 10
    }, this);
}
_c = UsersLayout;
var _c;
__turbopack_refresh__.register(_c, "UsersLayout");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/users/layout.tsx [app-rsc] (ecmascript, Next.js server component, client modules)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
}}),
}]);

//# sourceMappingURL=src_adb861._.js.map