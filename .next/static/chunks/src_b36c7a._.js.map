{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/football/league-form.tsx"], "sourcesContent": ["/**\n * League Form Component\n * Form for creating and editing football leagues\n */\n\n'use client';\n\nimport React from 'react';\nimport {\n  Form,\n  Input,\n  Select,\n  Switch,\n  Button,\n  Card,\n  Row,\n  Col,\n  Typography,\n  Space,\n  Upload,\n  message\n} from 'antd';\nimport {\n  TrophyOutlined,\n  SaveOutlined,\n  CloseOutlined,\n  UploadOutlined,\n  GlobalOutlined\n} from '@ant-design/icons';\nimport { FootballQueries } from '@/lib/query-types';\n\nconst { Title, Text } = Typography;\nconst { Option } = Select;\n\ninterface LeagueFormProps {\n  initialValues?: Partial<FootballQueries.League>;\n  onSubmit: (values: FootballQueries.CreateLeagueRequest | FootballQueries.UpdateLeagueRequest) => void;\n  onCancel: () => void;\n  loading?: boolean;\n  mode: 'create' | 'edit';\n}\n\n// Common countries for football leagues\nconst COUNTRIES = [\n  'England', 'Spain', 'Germany', 'Italy', 'France', 'Netherlands', 'Portugal',\n  'Brazil', 'Argentina', 'Mexico', 'United States', 'Turkey', 'Russia',\n  'Belgium', 'Scotland', 'Austria', 'Switzerland', 'Greece', 'Ukraine',\n  'Poland', 'Czech Republic', 'Croatia', 'Serbia', 'Denmark', 'Sweden',\n  'Norway', 'Romania', 'Bulgaria', 'Hungary', 'Slovakia', 'Slovenia'\n];\n\n// Current and recent seasons\nconst SEASONS = [\n  '2024/25', '2023/24', '2022/23', '2021/22', '2020/21', '2019/20'\n];\n\nexport default function LeagueForm({\n  initialValues,\n  onSubmit,\n  onCancel,\n  loading = false,\n  mode\n}: LeagueFormProps) {\n  const [form] = Form.useForm();\n\n  const handleSubmit = (values: any) => {\n    const formData = {\n      ...values,\n      isActive: values.isActive ?? true\n    };\n    onSubmit(formData);\n  };\n\n  const handleLogoUpload = (info: any) => {\n    if (info.file.status === 'done') {\n      message.success(`${info.file.name} file uploaded successfully`);\n      form.setFieldsValue({ logo: info.file.response?.url });\n    } else if (info.file.status === 'error') {\n      message.error(`${info.file.name} file upload failed.`);\n    }\n  };\n\n  return (\n    <Card>\n      <div className=\"mb-6\">\n        <Title level={3}>\n          <TrophyOutlined className=\"mr-2\" />\n          {mode === 'create' ? 'Create New League' : 'Edit League'}\n        </Title>\n        <Text type=\"secondary\">\n          {mode === 'create' \n            ? 'Add a new football league to the system'\n            : 'Update league information and settings'\n          }\n        </Text>\n      </div>\n\n      <Form\n        form={form}\n        layout=\"vertical\"\n        initialValues={initialValues}\n        onFinish={handleSubmit}\n        size=\"large\"\n      >\n        <Row gutter={24}>\n          <Col xs={24} md={12}>\n            <Form.Item\n              name=\"name\"\n              label=\"League Name\"\n              rules={[\n                { required: true, message: 'Please enter league name' },\n                { min: 2, message: 'League name must be at least 2 characters' },\n                { max: 100, message: 'League name must not exceed 100 characters' }\n              ]}\n            >\n              <Input\n                placeholder=\"e.g., Premier League, La Liga, Bundesliga\"\n                prefix={<TrophyOutlined />}\n              />\n            </Form.Item>\n          </Col>\n\n          <Col xs={24} md={12}>\n            <Form.Item\n              name=\"country\"\n              label=\"Country\"\n              rules={[\n                { required: true, message: 'Please select country' }\n              ]}\n            >\n              <Select\n                placeholder=\"Select country\"\n                showSearch\n                filterOption={(input, option) =>\n                  (option?.children as string)?.toLowerCase().includes(input.toLowerCase())\n                }\n                prefix={<GlobalOutlined />}\n              >\n                {COUNTRIES.map(country => (\n                  <Option key={country} value={country}>\n                    {country}\n                  </Option>\n                ))}\n              </Select>\n            </Form.Item>\n          </Col>\n        </Row>\n\n        <Row gutter={24}>\n          <Col xs={24} md={12}>\n            <Form.Item\n              name=\"season\"\n              label=\"Season\"\n              rules={[\n                { required: true, message: 'Please select season' }\n              ]}\n            >\n              <Select placeholder=\"Select season\">\n                {SEASONS.map(season => (\n                  <Option key={season} value={season}>\n                    {season}\n                  </Option>\n                ))}\n              </Select>\n            </Form.Item>\n          </Col>\n\n          <Col xs={24} md={12}>\n            <Form.Item\n              name=\"isActive\"\n              label=\"Status\"\n              valuePropName=\"checked\"\n            >\n              <Switch\n                checkedChildren=\"Active\"\n                unCheckedChildren=\"Inactive\"\n                defaultChecked\n              />\n            </Form.Item>\n          </Col>\n        </Row>\n\n        <Row gutter={24}>\n          <Col xs={24}>\n            <Form.Item\n              name=\"logo\"\n              label=\"League Logo URL\"\n              rules={[\n                { type: 'url', message: 'Please enter a valid URL' }\n              ]}\n            >\n              <Input\n                placeholder=\"https://example.com/logo.png\"\n                prefix={<UploadOutlined />}\n              />\n            </Form.Item>\n          </Col>\n        </Row>\n\n        <Form.Item className=\"mb-0\">\n          <Space>\n            <Button\n              type=\"primary\"\n              htmlType=\"submit\"\n              loading={loading}\n              icon={<SaveOutlined />}\n              size=\"large\"\n            >\n              {mode === 'create' ? 'Create League' : 'Update League'}\n            </Button>\n            <Button\n              onClick={onCancel}\n              icon={<CloseOutlined />}\n              size=\"large\"\n            >\n              Cancel\n            </Button>\n          </Space>\n        </Form.Item>\n      </Form>\n    </Card>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAKD;AAAA;AAAA;AAAA;AAAA;AAcA;AAdA;AAAA;AAAA;AAcA;AAdA;AAcA;AAdA;AAAA;AAcA;AAAA;;;AAjBA;;;AA0BA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,6LAAA,CAAA,aAAU;AAClC,MAAM,EAAE,MAAM,EAAE,GAAG,qLAAA,CAAA,SAAM;AAUzB,wCAAwC;AACxC,MAAM,YAAY;IAChB;IAAW;IAAS;IAAW;IAAS;IAAU;IAAe;IACjE;IAAU;IAAa;IAAU;IAAiB;IAAU;IAC5D;IAAW;IAAY;IAAW;IAAe;IAAU;IAC3D;IAAU;IAAkB;IAAW;IAAU;IAAW;IAC5D;IAAU;IAAW;IAAY;IAAW;IAAY;CACzD;AAED,6BAA6B;AAC7B,MAAM,UAAU;IACd;IAAW;IAAW;IAAW;IAAW;IAAW;CACxD;AAEc,SAAS,WAAW,EACjC,aAAa,EACb,QAAQ,EACR,QAAQ,EACR,UAAU,KAAK,EACf,IAAI,EACY;;IAChB,MAAM,CAAC,KAAK,GAAG,iLAAA,CAAA,OAAI,CAAC,OAAO;IAE3B,MAAM,eAAe,CAAC;QACpB,MAAM,WAAW;YACf,GAAG,MAAM;YACT,UAAU,OAAO,QAAQ,IAAI;QAC/B;QACA,SAAS;IACX;IAEA,MAAM,mBAAmB,CAAC;QACxB,IAAI,KAAK,IAAI,CAAC,MAAM,KAAK,QAAQ;YAC/B,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC,GAAG,KAAK,IAAI,CAAC,IAAI,CAAC,2BAA2B,CAAC;YAC9D,KAAK,cAAc,CAAC;gBAAE,MAAM,KAAK,IAAI,CAAC,QAAQ,EAAE;YAAI;QACtD,OAAO,IAAI,KAAK,IAAI,CAAC,MAAM,KAAK,SAAS;YACvC,uLAAA,CAAA,UAAO,CAAC,KAAK,CAAC,GAAG,KAAK,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC;QACvD;IACF;IAEA,qBACE,6LAAC,iLAAA,CAAA,OAAI;;0BACH,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAM,OAAO;;0CACZ,6LAAC,yNAAA,CAAA,iBAAc;gCAAC,WAAU;;;;;;4BACzB,SAAS,WAAW,sBAAsB;;;;;;;kCAE7C,6LAAC;wBAAK,MAAK;kCACR,SAAS,WACN,4CACA;;;;;;;;;;;;0BAKR,6LAAC,iLAAA,CAAA,OAAI;gBACH,MAAM;gBACN,QAAO;gBACP,eAAe;gBACf,UAAU;gBACV,MAAK;;kCAEL,6LAAC,+KAAA,CAAA,MAAG;wBAAC,QAAQ;;0CACX,6LAAC,+KAAA,CAAA,MAAG;gCAAC,IAAI;gCAAI,IAAI;0CACf,cAAA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCACL;4CAAE,UAAU;4CAAM,SAAS;wCAA2B;wCACtD;4CAAE,KAAK;4CAAG,SAAS;wCAA4C;wCAC/D;4CAAE,KAAK;4CAAK,SAAS;wCAA6C;qCACnE;8CAED,cAAA,6LAAC,mLAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,sBAAQ,6LAAC,yNAAA,CAAA,iBAAc;;;;;;;;;;;;;;;;;;;;0CAK7B,6LAAC,+KAAA,CAAA,MAAG;gCAAC,IAAI;gCAAI,IAAI;0CACf,cAAA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCACL;4CAAE,UAAU;4CAAM,SAAS;wCAAwB;qCACpD;8CAED,cAAA,6LAAC,qLAAA,CAAA,SAAM;wCACL,aAAY;wCACZ,UAAU;wCACV,cAAc,CAAC,OAAO,SACnB,QAAQ,UAAqB,cAAc,SAAS,MAAM,WAAW;wCAExE,sBAAQ,6LAAC,yNAAA,CAAA,iBAAc;;;;;kDAEtB,UAAU,GAAG,CAAC,CAAA,wBACb,6LAAC;gDAAqB,OAAO;0DAC1B;+CADU;;;;;;;;;;;;;;;;;;;;;;;;;;kCASvB,6LAAC,+KAAA,CAAA,MAAG;wBAAC,QAAQ;;0CACX,6LAAC,+KAAA,CAAA,MAAG;gCAAC,IAAI;gCAAI,IAAI;0CACf,cAAA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCACL;4CAAE,UAAU;4CAAM,SAAS;wCAAuB;qCACnD;8CAED,cAAA,6LAAC,qLAAA,CAAA,SAAM;wCAAC,aAAY;kDACjB,QAAQ,GAAG,CAAC,CAAA,uBACX,6LAAC;gDAAoB,OAAO;0DACzB;+CADU;;;;;;;;;;;;;;;;;;;;0CAQrB,6LAAC,+KAAA,CAAA,MAAG;gCAAC,IAAI;gCAAI,IAAI;0CACf,cAAA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,eAAc;8CAEd,cAAA,6LAAC,qLAAA,CAAA,SAAM;wCACL,iBAAgB;wCAChB,mBAAkB;wCAClB,cAAc;;;;;;;;;;;;;;;;;;;;;;kCAMtB,6LAAC,+KAAA,CAAA,MAAG;wBAAC,QAAQ;kCACX,cAAA,6LAAC,+KAAA,CAAA,MAAG;4BAAC,IAAI;sCACP,cAAA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;gCACR,MAAK;gCACL,OAAM;gCACN,OAAO;oCACL;wCAAE,MAAM;wCAAO,SAAS;oCAA2B;iCACpD;0CAED,cAAA,6LAAC,mLAAA,CAAA,QAAK;oCACJ,aAAY;oCACZ,sBAAQ,6LAAC,yNAAA,CAAA,iBAAc;;;;;;;;;;;;;;;;;;;;;;;;;kCAM/B,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;wBAAC,WAAU;kCACnB,cAAA,6LAAC,mMAAA,CAAA,QAAK;;8CACJ,6LAAC,qMAAA,CAAA,SAAM;oCACL,MAAK;oCACL,UAAS;oCACT,SAAS;oCACT,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;oCACnB,MAAK;8CAEJ,SAAS,WAAW,kBAAkB;;;;;;8CAEzC,6LAAC,qMAAA,CAAA,SAAM;oCACL,SAAS;oCACT,oBAAM,6LAAC,uNAAA,CAAA,gBAAa;;;;;oCACpB,MAAK;8CACN;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;GAtKwB;;QAOP,iLAAA,CAAA,OAAI,CAAC;;;KAPE"}}, {"offset": {"line": 419, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 425, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/football/team-form.tsx"], "sourcesContent": ["/**\n * Team Form Component\n * Form for creating and editing football teams\n */\n\n'use client';\n\nimport React from 'react';\nimport {\n  Form,\n  Input,\n  Select,\n  Switch,\n  Button,\n  Card,\n  Row,\n  Col,\n  Typography,\n  Space,\n  InputNumber,\n  message\n} from 'antd';\nimport {\n  TeamOutlined,\n  SaveOutlined,\n  CloseOutlined,\n  UploadOutlined,\n  GlobalOutlined,\n  TrophyOutlined,\n  HomeOutlined,\n  CalendarOutlined\n} from '@ant-design/icons';\nimport { FootballQueries } from '@/lib/query-types';\nimport { useLeagues } from '@/hooks/api/football-hooks';\n\nconst { Title, Text } = Typography;\nconst { Option } = Select;\n\ninterface TeamFormProps {\n  initialValues?: Partial<FootballQueries.Team>;\n  onSubmit: (values: FootballQueries.CreateTeamRequest | FootballQueries.UpdateTeamRequest) => void;\n  onCancel: () => void;\n  loading?: boolean;\n  mode: 'create' | 'edit';\n}\n\n// Common countries for football teams\nconst COUNTRIES = [\n  'England', 'Spain', 'Germany', 'Italy', 'France', 'Netherlands', 'Portugal',\n  'Brazil', 'Argentina', 'Mexico', 'United States', 'Turkey', 'Russia',\n  'Belgium', 'Scotland', 'Austria', 'Switzerland', 'Greece', 'Ukraine',\n  'Poland', 'Czech Republic', 'Croatia', 'Serbia', 'Denmark', 'Sweden',\n  'Norway', 'Romania', 'Bulgaria', 'Hungary', 'Slovakia', 'Slovenia'\n];\n\nexport default function TeamForm({\n  initialValues,\n  onSubmit,\n  onCancel,\n  loading = false,\n  mode\n}: TeamFormProps) {\n  const [form] = Form.useForm();\n  \n  // Fetch leagues for dropdown\n  const { data: leaguesData, isLoading: leaguesLoading } = useLeagues({ limit: 100 });\n\n  const handleSubmit = (values: any) => {\n    const formData = {\n      ...values,\n      isActive: values.isActive ?? true,\n      founded: values.founded ? parseInt(values.founded) : undefined\n    };\n    onSubmit(formData);\n  };\n\n  return (\n    <Card>\n      <div className=\"mb-6\">\n        <Title level={3}>\n          <TeamOutlined className=\"mr-2\" />\n          {mode === 'create' ? 'Create New Team' : 'Edit Team'}\n        </Title>\n        <Text type=\"secondary\">\n          {mode === 'create' \n            ? 'Add a new football team to the system'\n            : 'Update team information and settings'\n          }\n        </Text>\n      </div>\n\n      <Form\n        form={form}\n        layout=\"vertical\"\n        initialValues={initialValues}\n        onFinish={handleSubmit}\n        size=\"large\"\n      >\n        <Row gutter={24}>\n          <Col xs={24} md={12}>\n            <Form.Item\n              name=\"name\"\n              label=\"Team Name\"\n              rules={[\n                { required: true, message: 'Please enter team name' },\n                { min: 2, message: 'Team name must be at least 2 characters' },\n                { max: 100, message: 'Team name must not exceed 100 characters' }\n              ]}\n            >\n              <Input\n                placeholder=\"e.g., Manchester United, Real Madrid, Bayern Munich\"\n                prefix={<TeamOutlined />}\n              />\n            </Form.Item>\n          </Col>\n\n          <Col xs={24} md={12}>\n            <Form.Item\n              name=\"country\"\n              label=\"Country\"\n              rules={[\n                { required: true, message: 'Please select country' }\n              ]}\n            >\n              <Select\n                placeholder=\"Select country\"\n                showSearch\n                filterOption={(input, option) =>\n                  (option?.children as string)?.toLowerCase().includes(input.toLowerCase())\n                }\n              >\n                {COUNTRIES.map(country => (\n                  <Option key={country} value={country}>\n                    <GlobalOutlined className=\"mr-2\" />\n                    {country}\n                  </Option>\n                ))}\n              </Select>\n            </Form.Item>\n          </Col>\n        </Row>\n\n        <Row gutter={24}>\n          <Col xs={24} md={12}>\n            <Form.Item\n              name=\"leagueId\"\n              label=\"League\"\n              rules={[\n                { required: true, message: 'Please select league' }\n              ]}\n            >\n              <Select\n                placeholder=\"Select league\"\n                loading={leaguesLoading}\n                showSearch\n                filterOption={(input, option) =>\n                  (option?.children as string)?.toLowerCase().includes(input.toLowerCase())\n                }\n              >\n                {leaguesData?.data?.map(league => (\n                  <Option key={league.id} value={league.id}>\n                    <TrophyOutlined className=\"mr-2\" />\n                    {league.name} ({league.country})\n                  </Option>\n                ))}\n              </Select>\n            </Form.Item>\n          </Col>\n\n          <Col xs={24} md={12}>\n            <Form.Item\n              name=\"founded\"\n              label=\"Founded Year\"\n              rules={[\n                { type: 'number', min: 1800, max: new Date().getFullYear(), message: 'Please enter a valid year' }\n              ]}\n            >\n              <InputNumber\n                placeholder=\"e.g., 1878, 1902, 1900\"\n                prefix={<CalendarOutlined />}\n                style={{ width: '100%' }}\n                min={1800}\n                max={new Date().getFullYear()}\n              />\n            </Form.Item>\n          </Col>\n        </Row>\n\n        <Row gutter={24}>\n          <Col xs={24} md={12}>\n            <Form.Item\n              name=\"venue\"\n              label=\"Home Venue\"\n              rules={[\n                { max: 200, message: 'Venue name must not exceed 200 characters' }\n              ]}\n            >\n              <Input\n                placeholder=\"e.g., Old Trafford, Santiago Bernabéu, Allianz Arena\"\n                prefix={<HomeOutlined />}\n              />\n            </Form.Item>\n          </Col>\n\n          <Col xs={24} md={12}>\n            <Form.Item\n              name=\"isActive\"\n              label=\"Status\"\n              valuePropName=\"checked\"\n            >\n              <Switch\n                checkedChildren=\"Active\"\n                unCheckedChildren=\"Inactive\"\n                defaultChecked\n              />\n            </Form.Item>\n          </Col>\n        </Row>\n\n        <Row gutter={24}>\n          <Col xs={24}>\n            <Form.Item\n              name=\"logo\"\n              label=\"Team Logo URL\"\n              rules={[\n                { type: 'url', message: 'Please enter a valid URL' }\n              ]}\n            >\n              <Input\n                placeholder=\"https://example.com/team-logo.png\"\n                prefix={<UploadOutlined />}\n              />\n            </Form.Item>\n          </Col>\n        </Row>\n\n        <Form.Item className=\"mb-0\">\n          <Space>\n            <Button\n              type=\"primary\"\n              htmlType=\"submit\"\n              loading={loading}\n              icon={<SaveOutlined />}\n              size=\"large\"\n            >\n              {mode === 'create' ? 'Create Team' : 'Update Team'}\n            </Button>\n            <Button\n              onClick={onCancel}\n              icon={<CloseOutlined />}\n              size=\"large\"\n            >\n              Cancel\n            </Button>\n          </Space>\n        </Form.Item>\n      </Form>\n    </Card>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AA8BD;AAzBA;AAAA;AAAA;AAAA;AAcA;AAdA;AAAA;AAAA;AAcA;AAAA;AAdA;AAcA;AAAA;AAdA;AAcA;AAdA;AAAA;AAcA;AAAA;;;AAjBA;;;;AA8BA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,6LAAA,CAAA,aAAU;AAClC,MAAM,EAAE,MAAM,EAAE,GAAG,qLAAA,CAAA,SAAM;AAUzB,sCAAsC;AACtC,MAAM,YAAY;IAChB;IAAW;IAAS;IAAW;IAAS;IAAU;IAAe;IACjE;IAAU;IAAa;IAAU;IAAiB;IAAU;IAC5D;IAAW;IAAY;IAAW;IAAe;IAAU;IAC3D;IAAU;IAAkB;IAAW;IAAU;IAAW;IAC5D;IAAU;IAAW;IAAY;IAAW;IAAY;CACzD;AAEc,SAAS,SAAS,EAC/B,aAAa,EACb,QAAQ,EACR,QAAQ,EACR,UAAU,KAAK,EACf,IAAI,EACU;;IACd,MAAM,CAAC,KAAK,GAAG,iLAAA,CAAA,OAAI,CAAC,OAAO;IAE3B,6BAA6B;IAC7B,MAAM,EAAE,MAAM,WAAW,EAAE,WAAW,cAAc,EAAE,GAAG,CAAA,GAAA,2IAAA,CAAA,aAAU,AAAD,EAAE;QAAE,OAAO;IAAI;IAEjF,MAAM,eAAe,CAAC;QACpB,MAAM,WAAW;YACf,GAAG,MAAM;YACT,UAAU,OAAO,QAAQ,IAAI;YAC7B,SAAS,OAAO,OAAO,GAAG,SAAS,OAAO,OAAO,IAAI;QACvD;QACA,SAAS;IACX;IAEA,qBACE,6LAAC,iLAAA,CAAA,OAAI;;0BACH,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAM,OAAO;;0CACZ,6LAAC,qNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;4BACvB,SAAS,WAAW,oBAAoB;;;;;;;kCAE3C,6LAAC;wBAAK,MAAK;kCACR,SAAS,WACN,0CACA;;;;;;;;;;;;0BAKR,6LAAC,iLAAA,CAAA,OAAI;gBACH,MAAM;gBACN,QAAO;gBACP,eAAe;gBACf,UAAU;gBACV,MAAK;;kCAEL,6LAAC,+KAAA,CAAA,MAAG;wBAAC,QAAQ;;0CACX,6LAAC,+KAAA,CAAA,MAAG;gCAAC,IAAI;gCAAI,IAAI;0CACf,cAAA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCACL;4CAAE,UAAU;4CAAM,SAAS;wCAAyB;wCACpD;4CAAE,KAAK;4CAAG,SAAS;wCAA0C;wCAC7D;4CAAE,KAAK;4CAAK,SAAS;wCAA2C;qCACjE;8CAED,cAAA,6LAAC,mLAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,sBAAQ,6LAAC,qNAAA,CAAA,eAAY;;;;;;;;;;;;;;;;;;;;0CAK3B,6LAAC,+KAAA,CAAA,MAAG;gCAAC,IAAI;gCAAI,IAAI;0CACf,cAAA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCACL;4CAAE,UAAU;4CAAM,SAAS;wCAAwB;qCACpD;8CAED,cAAA,6LAAC,qLAAA,CAAA,SAAM;wCACL,aAAY;wCACZ,UAAU;wCACV,cAAc,CAAC,OAAO,SACnB,QAAQ,UAAqB,cAAc,SAAS,MAAM,WAAW;kDAGvE,UAAU,GAAG,CAAC,CAAA,wBACb,6LAAC;gDAAqB,OAAO;;kEAC3B,6LAAC,yNAAA,CAAA,iBAAc;wDAAC,WAAU;;;;;;oDACzB;;+CAFU;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUvB,6LAAC,+KAAA,CAAA,MAAG;wBAAC,QAAQ;;0CACX,6LAAC,+KAAA,CAAA,MAAG;gCAAC,IAAI;gCAAI,IAAI;0CACf,cAAA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCACL;4CAAE,UAAU;4CAAM,SAAS;wCAAuB;qCACnD;8CAED,cAAA,6LAAC,qLAAA,CAAA,SAAM;wCACL,aAAY;wCACZ,SAAS;wCACT,UAAU;wCACV,cAAc,CAAC,OAAO,SACnB,QAAQ,UAAqB,cAAc,SAAS,MAAM,WAAW;kDAGvE,aAAa,MAAM,IAAI,CAAA,uBACtB,6LAAC;gDAAuB,OAAO,OAAO,EAAE;;kEACtC,6LAAC,yNAAA,CAAA,iBAAc;wDAAC,WAAU;;;;;;oDACzB,OAAO,IAAI;oDAAC;oDAAG,OAAO,OAAO;oDAAC;;+CAFpB,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;0CAS9B,6LAAC,+KAAA,CAAA,MAAG;gCAAC,IAAI;gCAAI,IAAI;0CACf,cAAA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCACL;4CAAE,MAAM;4CAAU,KAAK;4CAAM,KAAK,IAAI,OAAO,WAAW;4CAAI,SAAS;wCAA4B;qCAClG;8CAED,cAAA,6LAAC,mMAAA,CAAA,cAAW;wCACV,aAAY;wCACZ,sBAAQ,6LAAC,6NAAA,CAAA,mBAAgB;;;;;wCACzB,OAAO;4CAAE,OAAO;wCAAO;wCACvB,KAAK;wCACL,KAAK,IAAI,OAAO,WAAW;;;;;;;;;;;;;;;;;;;;;;kCAMnC,6LAAC,+KAAA,CAAA,MAAG;wBAAC,QAAQ;;0CACX,6LAAC,+KAAA,CAAA,MAAG;gCAAC,IAAI;gCAAI,IAAI;0CACf,cAAA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCACL;4CAAE,KAAK;4CAAK,SAAS;wCAA4C;qCAClE;8CAED,cAAA,6LAAC,mLAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,sBAAQ,6LAAC,qNAAA,CAAA,eAAY;;;;;;;;;;;;;;;;;;;;0CAK3B,6LAAC,+KAAA,CAAA,MAAG;gCAAC,IAAI;gCAAI,IAAI;0CACf,cAAA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,eAAc;8CAEd,cAAA,6LAAC,qLAAA,CAAA,SAAM;wCACL,iBAAgB;wCAChB,mBAAkB;wCAClB,cAAc;;;;;;;;;;;;;;;;;;;;;;kCAMtB,6LAAC,+KAAA,CAAA,MAAG;wBAAC,QAAQ;kCACX,cAAA,6LAAC,+KAAA,CAAA,MAAG;4BAAC,IAAI;sCACP,cAAA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;gCACR,MAAK;gCACL,OAAM;gCACN,OAAO;oCACL;wCAAE,MAAM;wCAAO,SAAS;oCAA2B;iCACpD;0CAED,cAAA,6LAAC,mLAAA,CAAA,QAAK;oCACJ,aAAY;oCACZ,sBAAQ,6LAAC,yNAAA,CAAA,iBAAc;;;;;;;;;;;;;;;;;;;;;;;;;kCAM/B,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;wBAAC,WAAU;kCACnB,cAAA,6LAAC,mMAAA,CAAA,QAAK;;8CACJ,6LAAC,qMAAA,CAAA,SAAM;oCACL,MAAK;oCACL,UAAS;oCACT,SAAS;oCACT,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;oCACnB,MAAK;8CAEJ,SAAS,WAAW,gBAAgB;;;;;;8CAEvC,6LAAC,qMAAA,CAAA,SAAM;oCACL,SAAS;oCACT,oBAAM,6LAAC,uNAAA,CAAA,gBAAa;;;;;oCACpB,MAAK;8CACN;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;GA5MwB;;QAOP,iLAAA,CAAA,OAAI,CAAC;QAGqC,2IAAA,CAAA,aAAU;;;KAV7C"}}, {"offset": {"line": 932, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 938, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/football/index.ts"], "sourcesContent": ["/**\n * Football Components\n * Export all football-related components\n */\n\nexport { default as LeagueForm } from './league-form';\nexport { default as TeamForm } from './team-form';\n"], "names": [], "mappings": "AAAA;;;CAGC"}}, {"offset": {"line": 947, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 973, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/app/football/teams/page.tsx"], "sourcesContent": ["/**\n * Football Teams Management Page\n * Comprehensive teams management with CRUD operations\n */\n\n'use client';\n\nimport React, { useState } from 'react';\nimport {\n  Card,\n  Table,\n  Button,\n  Input,\n  Select,\n  Space,\n  Tag,\n  Tooltip,\n  Popconfirm,\n  Row,\n  Col,\n  Statistic,\n  Typography,\n  message,\n  Badge,\n  Avatar,\n  Dropdown,\n  MenuProps,\n  Modal,\n  Image,\n  Progress\n} from 'antd';\nimport {\n  PlusOutlined,\n  SearchOutlined,\n  FilterOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  EyeOutlined,\n  TeamOutlined,\n  GlobalOutlined,\n  MoreOutlined,\n  ReloadOutlined,\n  ExportOutlined,\n  TrophyOutlined,\n  HomeOutlined,\n  CalendarOutlined\n} from '@ant-design/icons';\nimport { useRouter } from 'next/navigation';\nimport { ColumnsType } from 'antd/es/table';\nimport { FootballQueries } from '@/lib/query-types';\nimport {\n  useTeams,\n  useLeagues,\n  useCreateTeam,\n  useUpdateTeam,\n  useDeleteTeam\n} from '@/hooks/api/football-hooks';\nimport { TeamForm } from '@/components/football';\n\nconst { Title, Text } = Typography;\nconst { Option } = Select;\n\n// Mock data for development\nconst MOCK_TEAMS: FootballQueries.Team[] = [\n  {\n    id: '1',\n    name: 'Manchester United',\n    logo: 'https://logos-world.net/wp-content/uploads/2020/06/Manchester-United-Logo.png',\n    country: 'England',\n    leagueId: '1',\n    league: { id: '1', name: 'Premier League', country: 'England', season: '2024/25', isActive: true, createdAt: '', updatedAt: '' },\n    statistics: {\n      played: 20,\n      wins: 12,\n      draws: 4,\n      losses: 4,\n      goalsFor: 35,\n      goalsAgainst: 22,\n      points: 40\n    },\n    createdAt: '2024-01-15T10:00:00Z',\n    updatedAt: '2024-01-15T10:00:00Z'\n  },\n  {\n    id: '2',\n    name: 'Real Madrid',\n    logo: 'https://logos-world.net/wp-content/uploads/2020/06/Real-Madrid-Logo.png',\n    country: 'Spain',\n    leagueId: '2',\n    league: { id: '2', name: 'La Liga', country: 'Spain', season: '2024/25', isActive: true, createdAt: '', updatedAt: '' },\n    statistics: {\n      played: 18,\n      wins: 14,\n      draws: 3,\n      losses: 1,\n      goalsFor: 42,\n      goalsAgainst: 15,\n      points: 45\n    },\n    createdAt: '2024-01-15T10:00:00Z',\n    updatedAt: '2024-01-15T10:00:00Z'\n  },\n  {\n    id: '3',\n    name: 'Bayern Munich',\n    logo: 'https://logos-world.net/wp-content/uploads/2020/06/Bayern-Munich-Logo.png',\n    country: 'Germany',\n    leagueId: '3',\n    league: { id: '3', name: 'Bundesliga', country: 'Germany', season: '2024/25', isActive: true, createdAt: '', updatedAt: '' },\n    statistics: {\n      played: 17,\n      wins: 13,\n      draws: 2,\n      losses: 2,\n      goalsFor: 48,\n      goalsAgainst: 18,\n      points: 41\n    },\n    createdAt: '2024-01-15T10:00:00Z',\n    updatedAt: '2024-01-15T10:00:00Z'\n  },\n  {\n    id: '4',\n    name: 'AC Milan',\n    country: 'Italy',\n    leagueId: '4',\n    league: { id: '4', name: 'Serie A', country: 'Italy', season: '2024/25', isActive: true, createdAt: '', updatedAt: '' },\n    statistics: {\n      played: 19,\n      wins: 10,\n      draws: 6,\n      losses: 3,\n      goalsFor: 28,\n      goalsAgainst: 20,\n      points: 36\n    },\n    createdAt: '2024-01-15T10:00:00Z',\n    updatedAt: '2024-01-15T10:00:00Z'\n  },\n  {\n    id: '5',\n    name: 'Paris Saint-Germain',\n    country: 'France',\n    leagueId: '5',\n    league: { id: '5', name: 'Ligue 1', country: 'France', season: '2023/24', isActive: false, createdAt: '', updatedAt: '' },\n    statistics: {\n      played: 22,\n      wins: 16,\n      draws: 4,\n      losses: 2,\n      goalsFor: 55,\n      goalsAgainst: 25,\n      points: 52\n    },\n    createdAt: '2024-01-15T10:00:00Z',\n    updatedAt: '2024-01-15T10:00:00Z'\n  }\n];\n\nexport default function TeamsPage() {\n  const router = useRouter();\n  const [queryParams, setQueryParams] = useState<FootballQueries.TeamQueryParams>({\n    page: 1,\n    limit: 10,\n    sortBy: 'name',\n    sortOrder: 'asc'\n  });\n  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);\n  const [editingTeam, setEditingTeam] = useState<FootballQueries.Team | null>(null);\n\n  // For development, use mock data\n  const teamsQuery = {\n    data: {\n      data: MOCK_TEAMS,\n      total: MOCK_TEAMS.length,\n      page: 1,\n      limit: 10,\n      totalPages: 1\n    },\n    isLoading: false,\n    error: null,\n    refetch: () => Promise.resolve()\n  };\n\n  const { data: leaguesData } = useLeagues({ limit: 100 });\n  const createTeam = useCreateTeam();\n  const updateTeam = useUpdateTeam();\n  const deleteTeam = useDeleteTeam();\n\n  // Statistics calculation\n  const statistics = React.useMemo(() => {\n    const teams = MOCK_TEAMS;\n    return {\n      total: teams.length,\n      withLogos: teams.filter(t => t.logo).length,\n      withStatistics: teams.filter(t => t.statistics).length,\n      countries: new Set(teams.map(t => t.country)).size,\n      averageWinRate: Math.round(\n        teams.reduce((sum, t) => {\n          if (!t.statistics || t.statistics.played === 0) return sum;\n          return sum + (t.statistics.wins / t.statistics.played) * 100;\n        }, 0) / teams.length\n      )\n    };\n  }, []);\n\n  // Handle search\n  const handleSearch = (value: string) => {\n    setQueryParams(prev => ({ ...prev, query: value, page: 1 }));\n  };\n\n  // Handle filter change\n  const handleFilterChange = (key: keyof FootballQueries.TeamQueryParams, value: any) => {\n    setQueryParams(prev => ({ ...prev, [key]: value, page: 1 }));\n  };\n\n  // Handle create team\n  const handleCreateTeam = async (values: FootballQueries.CreateTeamRequest) => {\n    try {\n      await createTeam.mutateAsync(values);\n      message.success('Team created successfully');\n      setIsCreateModalOpen(false);\n    } catch (error) {\n      message.error('Failed to create team');\n    }\n  };\n\n  // Handle update team\n  const handleUpdateTeam = async (values: FootballQueries.UpdateTeamRequest) => {\n    if (!editingTeam) return;\n\n    try {\n      await updateTeam.mutateAsync({ id: editingTeam.id, data: values });\n      message.success('Team updated successfully');\n      setEditingTeam(null);\n    } catch (error) {\n      message.error('Failed to update team');\n    }\n  };\n\n  // Handle delete\n  const handleDelete = async (id: string) => {\n    try {\n      await deleteTeam.mutateAsync(id);\n      message.success('Team deleted successfully');\n    } catch (error) {\n      message.error('Failed to delete team');\n    }\n  };\n\n  // Handle table change\n  const handleTableChange = (pagination: any, filters: any, sorter: any) => {\n    setQueryParams(prev => ({\n      ...prev,\n      page: pagination.current,\n      limit: pagination.pageSize,\n      sortBy: sorter.field || 'name',\n      sortOrder: sorter.order === 'ascend' ? 'asc' : 'desc'\n    }));\n  };\n\n  // Calculate win percentage\n  const getWinPercentage = (stats?: FootballQueries.TeamStatistics) => {\n    if (!stats || stats.played === 0) return 0;\n    return Math.round((stats.wins / stats.played) * 100);\n  };\n\n  // Table columns\n  const columns: ColumnsType<FootballQueries.Team> = [\n    {\n      title: 'Team',\n      key: 'team',\n      render: (_, record) => (\n        <div className=\"flex items-center gap-3\">\n          {record.logo ? (\n            <Avatar\n              src={record.logo}\n              size={40}\n              icon={<TeamOutlined />}\n            />\n          ) : (\n            <Avatar\n              size={40}\n              icon={<TeamOutlined />}\n              style={{ backgroundColor: '#1890ff' }}\n            />\n          )}\n          <div>\n            <Text strong className=\"block\">{record.name}</Text>\n            <Text type=\"secondary\" className=\"text-sm flex items-center gap-1\">\n              <GlobalOutlined />\n              {record.country}\n            </Text>\n          </div>\n        </div>\n      ),\n      width: 250,\n      sorter: true,\n    },\n    {\n      title: 'League',\n      key: 'league',\n      render: (_, record) => (\n        <div>\n          <Text strong className=\"block\">{record.league?.name || 'Unknown'}</Text>\n          <Text type=\"secondary\" className=\"text-sm\">\n            {record.league?.country}\n          </Text>\n        </div>\n      ),\n      width: 180,\n    },\n    {\n      title: 'Performance',\n      key: 'performance',\n      render: (_, record) => {\n        if (!record.statistics) {\n          return <Text type=\"secondary\">No data</Text>;\n        }\n\n        const winPercentage = getWinPercentage(record.statistics);\n        return (\n          <div className=\"min-w-[120px]\">\n            <div className=\"flex justify-between text-xs mb-1\">\n              <span>Win Rate</span>\n              <span>{winPercentage}%</span>\n            </div>\n            <Progress\n              percent={winPercentage}\n              size=\"small\"\n              strokeColor={winPercentage >= 60 ? '#52c41a' : winPercentage >= 40 ? '#faad14' : '#ff4d4f'}\n              showInfo={false}\n            />\n            <div className=\"text-xs text-gray-500 mt-1\">\n              {record.statistics.wins}W {record.statistics.draws}D {record.statistics.losses}L\n            </div>\n          </div>\n        );\n      },\n      width: 150,\n    },\n    {\n      title: 'Statistics',\n      key: 'statistics',\n      render: (_, record) => {\n        if (!record.statistics) {\n          return <Text type=\"secondary\">No data</Text>;\n        }\n\n        return (\n          <div className=\"text-sm\">\n            <div className=\"flex justify-between\">\n              <span>Played:</span>\n              <span>{record.statistics.played}</span>\n            </div>\n            <div className=\"flex justify-between\">\n              <span>Points:</span>\n              <span className=\"font-semibold\">{record.statistics.points}</span>\n            </div>\n            <div className=\"flex justify-between text-xs text-gray-500\">\n              <span>Goals:</span>\n              <span>{record.statistics.goalsFor}:{record.statistics.goalsAgainst}</span>\n            </div>\n          </div>\n        );\n      },\n      width: 120,\n    },\n    {\n      title: 'Actions',\n      key: 'actions',\n      render: (_, record) => {\n        const menuItems: MenuProps['items'] = [\n          {\n            key: 'view',\n            label: 'View Details',\n            icon: <EyeOutlined />,\n            onClick: () => router.push(`/football/teams/${record.id}`)\n          },\n          {\n            key: 'edit',\n            label: 'Edit',\n            icon: <EditOutlined />,\n            onClick: () => setEditingTeam(record)\n          },\n          {\n            key: 'fixtures',\n            label: 'View Fixtures',\n            icon: <CalendarOutlined />,\n            onClick: () => router.push(`/football/fixtures?teamId=${record.id}`)\n          },\n          {\n            type: 'divider'\n          },\n          {\n            key: 'delete',\n            label: 'Delete',\n            icon: <DeleteOutlined />,\n            danger: true,\n            onClick: () => handleDelete(record.id)\n          }\n        ];\n\n        return (\n          <Dropdown menu={{ items: menuItems }} trigger={['click']}>\n            <Button icon={<MoreOutlined />} />\n          </Dropdown>\n        );\n      },\n      width: 80,\n      fixed: 'right',\n    },\n  ];\n\n  return (\n    <div>\n      {/* Page Header */}\n      <div className=\"mb-6\">\n        <Title level={2}>\n          <TeamOutlined className=\"mr-2\" />\n          Football Teams Management\n        </Title>\n        <Text type=\"secondary\">\n          Manage football teams with comprehensive statistics and performance tracking\n        </Text>\n      </div>\n\n      {/* Statistics Cards */}\n      <Row gutter={16} className=\"mb-6\">\n        <Col xs={12} sm={6}>\n          <Card>\n            <Statistic\n              title=\"Total Teams\"\n              value={statistics.total}\n              prefix={<TeamOutlined />}\n            />\n          </Card>\n        </Col>\n        <Col xs={12} sm={6}>\n          <Card>\n            <Statistic\n              title=\"With Logos\"\n              value={statistics.withLogos}\n              suffix={`/ ${statistics.total}`}\n              prefix={<Avatar size=\"small\" icon={<TeamOutlined />} />}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={12} sm={6}>\n          <Card>\n            <Statistic\n              title=\"With Statistics\"\n              value={statistics.withStatistics}\n              suffix={`/ ${statistics.total}`}\n              prefix={<Badge status=\"success\" />}\n              valueStyle={{ color: '#3f8600' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={12} sm={6}>\n          <Card>\n            <Statistic\n              title=\"Avg Win Rate\"\n              value={statistics.averageWinRate}\n              suffix=\"%\"\n              prefix={<TrophyOutlined />}\n              valueStyle={{ color: '#faad14' }}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      {/* Filters and Actions */}\n      <Card className=\"mb-4\">\n        <Row gutter={16} align=\"middle\">\n          <Col xs={24} sm={8} md={6}>\n            <Input\n              placeholder=\"Search teams...\"\n              prefix={<SearchOutlined />}\n              onChange={(e) => handleSearch(e.target.value)}\n              allowClear\n            />\n          </Col>\n          <Col xs={12} sm={4} md={3}>\n            <Select\n              placeholder=\"League\"\n              allowClear\n              onChange={(value) => handleFilterChange('leagueId', value)}\n              className=\"w-full\"\n            >\n              {leaguesData?.data?.map(league => (\n                <Option key={league.id} value={league.id}>\n                  {league.name}\n                </Option>\n              ))}\n            </Select>\n          </Col>\n          <Col xs={12} sm={4} md={3}>\n            <Select\n              placeholder=\"Country\"\n              allowClear\n              onChange={(value) => handleFilterChange('country', value)}\n              className=\"w-full\"\n            >\n              {Array.from(new Set(MOCK_TEAMS.map(t => t.country))).map(country => (\n                <Option key={country} value={country}>\n                  {country}\n                </Option>\n              ))}\n            </Select>\n          </Col>\n          <Col xs={24} sm={8} md={12} className=\"text-right\">\n            <Space>\n              <Button\n                icon={<ReloadOutlined />}\n                onClick={() => teamsQuery.refetch()}\n                loading={teamsQuery.isLoading}\n              >\n                Refresh\n              </Button>\n              <Button\n                icon={<ExportOutlined />}\n                onClick={() => message.info('Export functionality coming soon')}\n              >\n                Export\n              </Button>\n              <Button\n                type=\"primary\"\n                icon={<PlusOutlined />}\n                onClick={() => setIsCreateModalOpen(true)}\n              >\n                Add Team\n              </Button>\n            </Space>\n          </Col>\n        </Row>\n      </Card>\n\n      {/* Teams Table */}\n      <Card>\n        <Table\n          columns={columns}\n          dataSource={teamsQuery.data?.data || []}\n          rowKey=\"id\"\n          loading={teamsQuery.isLoading}\n          pagination={{\n            current: queryParams.page,\n            pageSize: queryParams.limit,\n            total: teamsQuery.data?.total || 0,\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total, range) =>\n              `${range[0]}-${range[1]} of ${total} teams`,\n          }}\n          onChange={handleTableChange}\n          scroll={{ x: 1200 }}\n        />\n      </Card>\n\n      {/* Create Team Modal */}\n      <Modal\n        title=\"Create New Team\"\n        open={isCreateModalOpen}\n        onCancel={() => setIsCreateModalOpen(false)}\n        footer={null}\n        width={800}\n        destroyOnClose\n      >\n        <TeamForm\n          mode=\"create\"\n          onSubmit={handleCreateTeam}\n          onCancel={() => setIsCreateModalOpen(false)}\n          loading={createTeam.isPending}\n        />\n      </Modal>\n\n      {/* Edit Team Modal */}\n      <Modal\n        title=\"Edit Team\"\n        open={!!editingTeam}\n        onCancel={() => setEditingTeam(null)}\n        footer={null}\n        width={800}\n        destroyOnClose\n      >\n        {editingTeam && (\n          <TeamForm\n            mode=\"edit\"\n            initialValues={editingTeam}\n            onSubmit={handleUpdateTeam}\n            onCancel={() => setEditingTeam(null)}\n            loading={updateTeam.isPending}\n          />\n        )}\n      </Modal>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAID;AAwCA;AAGA;AAOA;AAjDA;AAAA;AAAA;AAAA;AAuBA;AAAA;AAvBA;AAuBA;AAAA;AAAA;AAAA;AAvBA;AAAA;AAuBA;AAvBA;AAAA;AAAA;AAAA;AAAA;AAuBA;AAvBA;AAuBA;AAvBA;AAuBA;AAAA;AAAA;AAvBA;AAAA;AAiDA;;;AApDA;;;;;;;AAsDA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,6LAAA,CAAA,aAAU;AAClC,MAAM,EAAE,MAAM,EAAE,GAAG,qLAAA,CAAA,SAAM;AAEzB,4BAA4B;AAC5B,MAAM,aAAqC;IACzC;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,UAAU;QACV,QAAQ;YAAE,IAAI;YAAK,MAAM;YAAkB,SAAS;YAAW,QAAQ;YAAW,UAAU;YAAM,WAAW;YAAI,WAAW;QAAG;QAC/H,YAAY;YACV,QAAQ;YACR,MAAM;YACN,OAAO;YACP,QAAQ;YACR,UAAU;YACV,cAAc;YACd,QAAQ;QACV;QACA,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,UAAU;QACV,QAAQ;YAAE,IAAI;YAAK,MAAM;YAAW,SAAS;YAAS,QAAQ;YAAW,UAAU;YAAM,WAAW;YAAI,WAAW;QAAG;QACtH,YAAY;YACV,QAAQ;YACR,MAAM;YACN,OAAO;YACP,QAAQ;YACR,UAAU;YACV,cAAc;YACd,QAAQ;QACV;QACA,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,UAAU;QACV,QAAQ;YAAE,IAAI;YAAK,MAAM;YAAc,SAAS;YAAW,QAAQ;YAAW,UAAU;YAAM,WAAW;YAAI,WAAW;QAAG;QAC3H,YAAY;YACV,QAAQ;YACR,MAAM;YACN,OAAO;YACP,QAAQ;YACR,UAAU;YACV,cAAc;YACd,QAAQ;QACV;QACA,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,SAAS;QACT,UAAU;QACV,QAAQ;YAAE,IAAI;YAAK,MAAM;YAAW,SAAS;YAAS,QAAQ;YAAW,UAAU;YAAM,WAAW;YAAI,WAAW;QAAG;QACtH,YAAY;YACV,QAAQ;YACR,MAAM;YACN,OAAO;YACP,QAAQ;YACR,UAAU;YACV,cAAc;YACd,QAAQ;QACV;QACA,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,SAAS;QACT,UAAU;QACV,QAAQ;YAAE,IAAI;YAAK,MAAM;YAAW,SAAS;YAAU,QAAQ;YAAW,UAAU;YAAO,WAAW;YAAI,WAAW;QAAG;QACxH,YAAY;YACV,QAAQ;YACR,MAAM;YACN,OAAO;YACP,QAAQ;YACR,UAAU;YACV,cAAc;YACd,QAAQ;QACV;QACA,WAAW;QACX,WAAW;IACb;CACD;AAEc,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmC;QAC9E,MAAM;QACN,OAAO;QACP,QAAQ;QACR,WAAW;IACb;IACA,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA+B;IAE5E,iCAAiC;IACjC,MAAM,aAAa;QACjB,MAAM;YACJ,MAAM;YACN,OAAO,WAAW,MAAM;YACxB,MAAM;YACN,OAAO;YACP,YAAY;QACd;QACA,WAAW;QACX,OAAO;QACP,SAAS,IAAM,QAAQ,OAAO;IAChC;IAEA,MAAM,EAAE,MAAM,WAAW,EAAE,GAAG,CAAA,GAAA,2IAAA,CAAA,aAAU,AAAD,EAAE;QAAE,OAAO;IAAI;IACtD,MAAM,aAAa,CAAA,GAAA,2IAAA,CAAA,gBAAa,AAAD;IAC/B,MAAM,aAAa,CAAA,GAAA,2IAAA,CAAA,gBAAa,AAAD;IAC/B,MAAM,aAAa,CAAA,GAAA,2IAAA,CAAA,gBAAa,AAAD;IAE/B,yBAAyB;IACzB,MAAM,aAAa,6JAAA,CAAA,UAAK,CAAC,OAAO;yCAAC;YAC/B,MAAM,QAAQ;YACd,OAAO;gBACL,OAAO,MAAM,MAAM;gBACnB,WAAW,MAAM,MAAM;qDAAC,CAAA,IAAK,EAAE,IAAI;oDAAE,MAAM;gBAC3C,gBAAgB,MAAM,MAAM;qDAAC,CAAA,IAAK,EAAE,UAAU;oDAAE,MAAM;gBACtD,WAAW,IAAI,IAAI,MAAM,GAAG;qDAAC,CAAA,IAAK,EAAE,OAAO;qDAAG,IAAI;gBAClD,gBAAgB,KAAK,KAAK,CACxB,MAAM,MAAM;qDAAC,CAAC,KAAK;wBACjB,IAAI,CAAC,EAAE,UAAU,IAAI,EAAE,UAAU,CAAC,MAAM,KAAK,GAAG,OAAO;wBACvD,OAAO,MAAM,AAAC,EAAE,UAAU,CAAC,IAAI,GAAG,EAAE,UAAU,CAAC,MAAM,GAAI;oBAC3D;oDAAG,KAAK,MAAM,MAAM;YAExB;QACF;wCAAG,EAAE;IAEL,gBAAgB;IAChB,MAAM,eAAe,CAAC;QACpB,eAAe,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,OAAO;gBAAO,MAAM;YAAE,CAAC;IAC5D;IAEA,uBAAuB;IACvB,MAAM,qBAAqB,CAAC,KAA4C;QACtE,eAAe,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,IAAI,EAAE;gBAAO,MAAM;YAAE,CAAC;IAC5D;IAEA,qBAAqB;IACrB,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,MAAM,WAAW,WAAW,CAAC;YAC7B,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;YAChB,qBAAqB;QACvB,EAAE,OAAO,OAAO;YACd,uLAAA,CAAA,UAAO,CAAC,KAAK,CAAC;QAChB;IACF;IAEA,qBAAqB;IACrB,MAAM,mBAAmB,OAAO;QAC9B,IAAI,CAAC,aAAa;QAElB,IAAI;YACF,MAAM,WAAW,WAAW,CAAC;gBAAE,IAAI,YAAY,EAAE;gBAAE,MAAM;YAAO;YAChE,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;YAChB,eAAe;QACjB,EAAE,OAAO,OAAO;YACd,uLAAA,CAAA,UAAO,CAAC,KAAK,CAAC;QAChB;IACF;IAEA,gBAAgB;IAChB,MAAM,eAAe,OAAO;QAC1B,IAAI;YACF,MAAM,WAAW,WAAW,CAAC;YAC7B,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;QAClB,EAAE,OAAO,OAAO;YACd,uLAAA,CAAA,UAAO,CAAC,KAAK,CAAC;QAChB;IACF;IAEA,sBAAsB;IACtB,MAAM,oBAAoB,CAAC,YAAiB,SAAc;QACxD,eAAe,CAAA,OAAQ,CAAC;gBACtB,GAAG,IAAI;gBACP,MAAM,WAAW,OAAO;gBACxB,OAAO,WAAW,QAAQ;gBAC1B,QAAQ,OAAO,KAAK,IAAI;gBACxB,WAAW,OAAO,KAAK,KAAK,WAAW,QAAQ;YACjD,CAAC;IACH;IAEA,2BAA2B;IAC3B,MAAM,mBAAmB,CAAC;QACxB,IAAI,CAAC,SAAS,MAAM,MAAM,KAAK,GAAG,OAAO;QACzC,OAAO,KAAK,KAAK,CAAC,AAAC,MAAM,IAAI,GAAG,MAAM,MAAM,GAAI;IAClD;IAEA,gBAAgB;IAChB,MAAM,UAA6C;QACjD;YACE,OAAO;YACP,KAAK;YACL,QAAQ,CAAC,GAAG,uBACV,6LAAC;oBAAI,WAAU;;wBACZ,OAAO,IAAI,iBACV,6LAAC,qLAAA,CAAA,SAAM;4BACL,KAAK,OAAO,IAAI;4BAChB,MAAM;4BACN,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;;;;;iDAGrB,6LAAC,qLAAA,CAAA,SAAM;4BACL,MAAM;4BACN,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;4BACnB,OAAO;gCAAE,iBAAiB;4BAAU;;;;;;sCAGxC,6LAAC;;8CACC,6LAAC;oCAAK,MAAM;oCAAC,WAAU;8CAAS,OAAO,IAAI;;;;;;8CAC3C,6LAAC;oCAAK,MAAK;oCAAY,WAAU;;sDAC/B,6LAAC,yNAAA,CAAA,iBAAc;;;;;wCACd,OAAO,OAAO;;;;;;;;;;;;;;;;;;;YAKvB,OAAO;YACP,QAAQ;QACV;QACA;YACE,OAAO;YACP,KAAK;YACL,QAAQ,CAAC,GAAG,uBACV,6LAAC;;sCACC,6LAAC;4BAAK,MAAM;4BAAC,WAAU;sCAAS,OAAO,MAAM,EAAE,QAAQ;;;;;;sCACvD,6LAAC;4BAAK,MAAK;4BAAY,WAAU;sCAC9B,OAAO,MAAM,EAAE;;;;;;;;;;;;YAItB,OAAO;QACT;QACA;YACE,OAAO;YACP,KAAK;YACL,QAAQ,CAAC,GAAG;gBACV,IAAI,CAAC,OAAO,UAAU,EAAE;oBACtB,qBAAO,6LAAC;wBAAK,MAAK;kCAAY;;;;;;gBAChC;gBAEA,MAAM,gBAAgB,iBAAiB,OAAO,UAAU;gBACxD,qBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;8CAAK;;;;;;8CACN,6LAAC;;wCAAM;wCAAc;;;;;;;;;;;;;sCAEvB,6LAAC,yLAAA,CAAA,WAAQ;4BACP,SAAS;4BACT,MAAK;4BACL,aAAa,iBAAiB,KAAK,YAAY,iBAAiB,KAAK,YAAY;4BACjF,UAAU;;;;;;sCAEZ,6LAAC;4BAAI,WAAU;;gCACZ,OAAO,UAAU,CAAC,IAAI;gCAAC;gCAAG,OAAO,UAAU,CAAC,KAAK;gCAAC;gCAAG,OAAO,UAAU,CAAC,MAAM;gCAAC;;;;;;;;;;;;;YAIvF;YACA,OAAO;QACT;QACA;YACE,OAAO;YACP,KAAK;YACL,QAAQ,CAAC,GAAG;gBACV,IAAI,CAAC,OAAO,UAAU,EAAE;oBACtB,qBAAO,6LAAC;wBAAK,MAAK;kCAAY;;;;;;gBAChC;gBAEA,qBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;8CAAK;;;;;;8CACN,6LAAC;8CAAM,OAAO,UAAU,CAAC,MAAM;;;;;;;;;;;;sCAEjC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;8CAAK;;;;;;8CACN,6LAAC;oCAAK,WAAU;8CAAiB,OAAO,UAAU,CAAC,MAAM;;;;;;;;;;;;sCAE3D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;8CAAK;;;;;;8CACN,6LAAC;;wCAAM,OAAO,UAAU,CAAC,QAAQ;wCAAC;wCAAE,OAAO,UAAU,CAAC,YAAY;;;;;;;;;;;;;;;;;;;YAI1E;YACA,OAAO;QACT;QACA;YACE,OAAO;YACP,KAAK;YACL,QAAQ,CAAC,GAAG;gBACV,MAAM,YAAgC;oBACpC;wBACE,KAAK;wBACL,OAAO;wBACP,oBAAM,6LAAC,mNAAA,CAAA,cAAW;;;;;wBAClB,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,gBAAgB,EAAE,OAAO,EAAE,EAAE;oBAC3D;oBACA;wBACE,KAAK;wBACL,OAAO;wBACP,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;wBACnB,SAAS,IAAM,eAAe;oBAChC;oBACA;wBACE,KAAK;wBACL,OAAO;wBACP,oBAAM,6LAAC,6NAAA,CAAA,mBAAgB;;;;;wBACvB,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,0BAA0B,EAAE,OAAO,EAAE,EAAE;oBACrE;oBACA;wBACE,MAAM;oBACR;oBACA;wBACE,KAAK;wBACL,OAAO;wBACP,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;wBACrB,QAAQ;wBACR,SAAS,IAAM,aAAa,OAAO,EAAE;oBACvC;iBACD;gBAED,qBACE,6LAAC,yLAAA,CAAA,WAAQ;oBAAC,MAAM;wBAAE,OAAO;oBAAU;oBAAG,SAAS;wBAAC;qBAAQ;8BACtD,cAAA,6LAAC,qMAAA,CAAA,SAAM;wBAAC,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;;;;;;;;;;;YAGjC;YACA,OAAO;YACP,OAAO;QACT;KACD;IAED,qBACE,6LAAC;;0BAEC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAM,OAAO;;0CACZ,6LAAC,qNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;4BAAS;;;;;;;kCAGnC,6LAAC;wBAAK,MAAK;kCAAY;;;;;;;;;;;;0BAMzB,6LAAC,+KAAA,CAAA,MAAG;gBAAC,QAAQ;gBAAI,WAAU;;kCACzB,6LAAC,+KAAA,CAAA,MAAG;wBAAC,IAAI;wBAAI,IAAI;kCACf,cAAA,6LAAC,iLAAA,CAAA,OAAI;sCACH,cAAA,6LAAC,2LAAA,CAAA,YAAS;gCACR,OAAM;gCACN,OAAO,WAAW,KAAK;gCACvB,sBAAQ,6LAAC,qNAAA,CAAA,eAAY;;;;;;;;;;;;;;;;;;;;kCAI3B,6LAAC,+KAAA,CAAA,MAAG;wBAAC,IAAI;wBAAI,IAAI;kCACf,cAAA,6LAAC,iLAAA,CAAA,OAAI;sCACH,cAAA,6LAAC,2LAAA,CAAA,YAAS;gCACR,OAAM;gCACN,OAAO,WAAW,SAAS;gCAC3B,QAAQ,CAAC,EAAE,EAAE,WAAW,KAAK,EAAE;gCAC/B,sBAAQ,6LAAC,qLAAA,CAAA,SAAM;oCAAC,MAAK;oCAAQ,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;;;;;;gCAChD,YAAY;oCAAE,OAAO;gCAAU;;;;;;;;;;;;;;;;kCAIrC,6LAAC,+KAAA,CAAA,MAAG;wBAAC,IAAI;wBAAI,IAAI;kCACf,cAAA,6LAAC,iLAAA,CAAA,OAAI;sCACH,cAAA,6LAAC,2LAAA,CAAA,YAAS;gCACR,OAAM;gCACN,OAAO,WAAW,cAAc;gCAChC,QAAQ,CAAC,EAAE,EAAE,WAAW,KAAK,EAAE;gCAC/B,sBAAQ,6LAAC,mLAAA,CAAA,QAAK;oCAAC,QAAO;;;;;;gCACtB,YAAY;oCAAE,OAAO;gCAAU;;;;;;;;;;;;;;;;kCAIrC,6LAAC,+KAAA,CAAA,MAAG;wBAAC,IAAI;wBAAI,IAAI;kCACf,cAAA,6LAAC,iLAAA,CAAA,OAAI;sCACH,cAAA,6LAAC,2LAAA,CAAA,YAAS;gCACR,OAAM;gCACN,OAAO,WAAW,cAAc;gCAChC,QAAO;gCACP,sBAAQ,6LAAC,yNAAA,CAAA,iBAAc;;;;;gCACvB,YAAY;oCAAE,OAAO;gCAAU;;;;;;;;;;;;;;;;;;;;;;0BAOvC,6LAAC,iLAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,MAAG;oBAAC,QAAQ;oBAAI,OAAM;;sCACrB,6LAAC,+KAAA,CAAA,MAAG;4BAAC,IAAI;4BAAI,IAAI;4BAAG,IAAI;sCACtB,cAAA,6LAAC,mLAAA,CAAA,QAAK;gCACJ,aAAY;gCACZ,sBAAQ,6LAAC,yNAAA,CAAA,iBAAc;;;;;gCACvB,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;gCAC5C,UAAU;;;;;;;;;;;sCAGd,6LAAC,+KAAA,CAAA,MAAG;4BAAC,IAAI;4BAAI,IAAI;4BAAG,IAAI;sCACtB,cAAA,6LAAC,qLAAA,CAAA,SAAM;gCACL,aAAY;gCACZ,UAAU;gCACV,UAAU,CAAC,QAAU,mBAAmB,YAAY;gCACpD,WAAU;0CAET,aAAa,MAAM,IAAI,CAAA,uBACtB,6LAAC;wCAAuB,OAAO,OAAO,EAAE;kDACrC,OAAO,IAAI;uCADD,OAAO,EAAE;;;;;;;;;;;;;;;sCAM5B,6LAAC,+KAAA,CAAA,MAAG;4BAAC,IAAI;4BAAI,IAAI;4BAAG,IAAI;sCACtB,cAAA,6LAAC,qLAAA,CAAA,SAAM;gCACL,aAAY;gCACZ,UAAU;gCACV,UAAU,CAAC,QAAU,mBAAmB,WAAW;gCACnD,WAAU;0CAET,MAAM,IAAI,CAAC,IAAI,IAAI,WAAW,GAAG,CAAC,CAAA,IAAK,EAAE,OAAO,IAAI,GAAG,CAAC,CAAA,wBACvD,6LAAC;wCAAqB,OAAO;kDAC1B;uCADU;;;;;;;;;;;;;;;sCAMnB,6LAAC,+KAAA,CAAA,MAAG;4BAAC,IAAI;4BAAI,IAAI;4BAAG,IAAI;4BAAI,WAAU;sCACpC,cAAA,6LAAC,mMAAA,CAAA,QAAK;;kDACJ,6LAAC,qMAAA,CAAA,SAAM;wCACL,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;wCACrB,SAAS,IAAM,WAAW,OAAO;wCACjC,SAAS,WAAW,SAAS;kDAC9B;;;;;;kDAGD,6LAAC,qMAAA,CAAA,SAAM;wCACL,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;wCACrB,SAAS,IAAM,uLAAA,CAAA,UAAO,CAAC,IAAI,CAAC;kDAC7B;;;;;;kDAGD,6LAAC,qMAAA,CAAA,SAAM;wCACL,MAAK;wCACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;wCACnB,SAAS,IAAM,qBAAqB;kDACrC;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAST,6LAAC,iLAAA,CAAA,OAAI;0BACH,cAAA,6LAAC,mLAAA,CAAA,QAAK;oBACJ,SAAS;oBACT,YAAY,WAAW,IAAI,EAAE,QAAQ,EAAE;oBACvC,QAAO;oBACP,SAAS,WAAW,SAAS;oBAC7B,YAAY;wBACV,SAAS,YAAY,IAAI;wBACzB,UAAU,YAAY,KAAK;wBAC3B,OAAO,WAAW,IAAI,EAAE,SAAS;wBACjC,iBAAiB;wBACjB,iBAAiB;wBACjB,WAAW,CAAC,OAAO,QACjB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC,IAAI,EAAE,MAAM,MAAM,CAAC;oBAC/C;oBACA,UAAU;oBACV,QAAQ;wBAAE,GAAG;oBAAK;;;;;;;;;;;0BAKtB,6LAAC,mLAAA,CAAA,QAAK;gBACJ,OAAM;gBACN,MAAM;gBACN,UAAU,IAAM,qBAAqB;gBACrC,QAAQ;gBACR,OAAO;gBACP,cAAc;0BAEd,cAAA,6LAAC,wLAAA,CAAA,WAAQ;oBACP,MAAK;oBACL,UAAU;oBACV,UAAU,IAAM,qBAAqB;oBACrC,SAAS,WAAW,SAAS;;;;;;;;;;;0BAKjC,6LAAC,mLAAA,CAAA,QAAK;gBACJ,OAAM;gBACN,MAAM,CAAC,CAAC;gBACR,UAAU,IAAM,eAAe;gBAC/B,QAAQ;gBACR,OAAO;gBACP,cAAc;0BAEb,6BACC,6LAAC,wLAAA,CAAA,WAAQ;oBACP,MAAK;oBACL,eAAe;oBACf,UAAU;oBACV,UAAU,IAAM,eAAe;oBAC/B,SAAS,WAAW,SAAS;;;;;;;;;;;;;;;;;AAMzC;GAvbwB;;QACP,qIAAA,CAAA,YAAS;QAwBM,2IAAA,CAAA,aAAU;QACrB,2IAAA,CAAA,gBAAa;QACb,2IAAA,CAAA,gBAAa;QACb,2IAAA,CAAA,gBAAa;;;KA5BV"}}, {"offset": {"line": 2065, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}