{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/app/football/sync/page.tsx"], "sourcesContent": ["/**\n * Football Data Sync Management Page (Legacy)\n * Placeholder page during refactoring\n */\n\n'use client';\n\nimport React from 'react';\nimport { Card, Typography, Button, Alert } from 'antd';\nimport { SyncOutlined, ToolOutlined } from '@ant-design/icons';\nimport { useRouter } from 'next/navigation';\n\nconst { Title, Text } = Typography;\n\nexport default function SyncPage() {\n  const router = useRouter();\n\n  return (\n    <div>\n      {/* Page Header */}\n      <div className=\"mb-6\">\n        <Title level={2}>\n          <SyncOutlined className=\"mr-2\" />\n          Football Data Sync (Legacy)\n        </Title>\n        <Text type=\"secondary\">\n          This page is being refactored. Please use the new Football Data Sync Management module.\n        </Text>\n      </div>\n\n      {/* Refactor Notice */}\n      <Alert\n        message=\"Page Under Refactoring\"\n        description=\"This legacy football data sync page is being refactored to use modern components and architecture. The new Football Data Sync Management module will be available soon with improved functionality.\"\n        type=\"warning\"\n        showIcon\n        className=\"mb-6\"\n      />\n\n      {/* Temporary Actions */}\n      <Card title=\"Available Actions\">\n        <div className=\"space-y-4\">\n          <div>\n            <Title level={4}>\n              <ToolOutlined className=\"mr-2\" />\n              Development Status\n            </Title>\n            <ul className=\"list-disc list-inside text-gray-600\">\n              <li>Legacy page temporarily disabled due to component conflicts</li>\n              <li>New Football Data Sync Management module in development</li>\n              <li>Will include real-time sync monitoring, advanced scheduling, and error handling</li>\n              <li>Expected completion: Next development cycle</li>\n            </ul>\n          </div>\n\n          <div className=\"pt-4\">\n            <Button\n              type=\"primary\"\n              onClick={() => router.push('/dashboard')}\n            >\n              Return to Dashboard\n            </Button>\n          </div>\n        </div>\n      </Card>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAOD;AAFA;AACA;AADA;AAAA;AACA;AADA;;;AAHA;;;;AAOA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,6LAAA,CAAA,aAAU;AAEnB,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,qBACE,6LAAC;;0BAEC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAM,OAAO;;0CACZ,6LAAC,qNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;4BAAS;;;;;;;kCAGnC,6LAAC;wBAAK,MAAK;kCAAY;;;;;;;;;;;;0BAMzB,6LAAC,mLAAA,CAAA,QAAK;gBACJ,SAAQ;gBACR,aAAY;gBACZ,MAAK;gBACL,QAAQ;gBACR,WAAU;;;;;;0BAIZ,6LAAC,iLAAA,CAAA,OAAI;gBAAC,OAAM;0BACV,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAM,OAAO;;sDACZ,6LAAC,qNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;wCAAS;;;;;;;8CAGnC,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;;;;;;;;;;;;;sCAIR,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,qMAAA,CAAA,SAAM;gCACL,MAAK;gCACL,SAAS,IAAM,OAAO,IAAI,CAAC;0CAC5B;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;GArDwB;;QACP,qIAAA,CAAA,YAAS;;;KADF"}}, {"offset": {"line": 190, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 201, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/node_modules/%40ant-design/icons-svg/es/asn/SyncOutlined.js"], "sourcesContent": ["// This icon file is generated automatically.\nvar SyncOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M168 504.2c1-43.7 10-86.1 26.9-126 17.3-41 42.1-77.7 73.7-109.4S337 212.3 378 195c42.4-17.9 87.4-27 133.9-27s91.5 9.1 133.8 27A341.5 341.5 0 01755 268.8c9.9 9.9 19.2 20.4 27.8 31.4l-60.2 47a8 8 0 003 14.1l175.7 43c5 1.2 9.9-2.6 9.9-7.7l.8-180.9c0-6.7-7.7-10.5-12.9-6.3l-56.4 44.1C765.8 155.1 646.2 92 511.8 92 282.7 92 96.3 275.6 92 503.8a8 8 0 008 8.2h60c4.4 0 7.9-3.5 8-7.8zm756 7.8h-60c-4.4 0-7.9 3.5-8 7.8-1 43.7-10 86.1-26.9 126-17.3 41-42.1 77.8-73.7 109.4A342.45 342.45 0 01512.1 856a342.24 342.24 0 01-243.2-100.8c-9.9-9.9-19.2-20.4-27.8-31.4l60.2-47a8 8 0 00-3-14.1l-175.7-43c-5-1.2-9.9 2.6-9.9 7.7l-.7 181c0 6.7 7.7 10.5 12.9 6.3l56.4-44.1C258.2 868.9 377.8 932 512.2 932c229.2 0 415.5-183.7 419.8-411.8a8 8 0 00-8-8.2z\" } }] }, \"name\": \"sync\", \"theme\": \"outlined\" };\nexport default SyncOutlined;\n"], "names": [], "mappings": "AAAA,6CAA6C;;;;AAC7C,IAAI,eAAe;IAAE,QAAQ;QAAE,OAAO;QAAO,SAAS;YAAE,WAAW;YAAiB,aAAa;QAAQ;QAAG,YAAY;YAAC;gBAAE,OAAO;gBAAQ,SAAS;oBAAE,KAAK;gBAA4tB;YAAE;SAAE;IAAC;IAAG,QAAQ;IAAQ,SAAS;AAAW;uCACn5B", "ignoreList": [0]}}, {"offset": {"line": 225, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 231, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/node_modules/%40ant-design/icons/es/icons/SyncOutlined.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport SyncOutlinedSvg from \"@ant-design/icons-svg/es/asn/SyncOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar SyncOutlined = function SyncOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: SyncOutlinedSvg\n  }));\n};\n\n/**![sync](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTE2OCA1MDQuMmMxLTQzLjcgMTAtODYuMSAyNi45LTEyNiAxNy4zLTQxIDQyLjEtNzcuNyA3My43LTEwOS40UzMzNyAyMTIuMyAzNzggMTk1YzQyLjQtMTcuOSA4Ny40LTI3IDEzMy45LTI3czkxLjUgOS4xIDEzMy44IDI3QTM0MS41IDM0MS41IDAgMDE3NTUgMjY4LjhjOS45IDkuOSAxOS4yIDIwLjQgMjcuOCAzMS40bC02MC4yIDQ3YTggOCAwIDAwMyAxNC4xbDE3NS43IDQzYzUgMS4yIDkuOS0yLjYgOS45LTcuN2wuOC0xODAuOWMwLTYuNy03LjctMTAuNS0xMi45LTYuM2wtNTYuNCA0NC4xQzc2NS44IDE1NS4xIDY0Ni4yIDkyIDUxMS44IDkyIDI4Mi43IDkyIDk2LjMgMjc1LjYgOTIgNTAzLjhhOCA4IDAgMDA4IDguMmg2MGM0LjQgMCA3LjktMy41IDgtNy44em03NTYgNy44aC02MGMtNC40IDAtNy45IDMuNS04IDcuOC0xIDQzLjctMTAgODYuMS0yNi45IDEyNi0xNy4zIDQxLTQyLjEgNzcuOC03My43IDEwOS40QTM0Mi40NSAzNDIuNDUgMCAwMTUxMi4xIDg1NmEzNDIuMjQgMzQyLjI0IDAgMDEtMjQzLjItMTAwLjhjLTkuOS05LjktMTkuMi0yMC40LTI3LjgtMzEuNGw2MC4yLTQ3YTggOCAwIDAwLTMtMTQuMWwtMTc1LjctNDNjLTUtMS4yLTkuOSAyLjYtOS45IDcuN2wtLjcgMTgxYzAgNi43IDcuNyAxMC41IDEyLjkgNi4zbDU2LjQtNDQuMUMyNTguMiA4NjguOSAzNzcuOCA5MzIgNTEyLjIgOTMyYzIyOS4yIDAgNDE1LjUtMTgzLjcgNDE5LjgtNDExLjhhOCA4IDAgMDAtOC04LjJ6IiAvPjwvc3ZnPg==) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(SyncOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'SyncOutlined';\n}\nexport default RefIcon;"], "names": [], "mappings": ";;;AAAA;AACA,oCAAoC;AACpC,2BAA2B;AAE3B;AAEA;AADA;AAWI;;;;;AATJ,IAAI,eAAe,SAAS,aAAa,KAAK,EAAE,GAAG;IACjD,OAAO,WAAW,GAAE,8JAAM,aAAa,CAAC,2KAAA,CAAA,UAAQ,EAAE,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,OAAO;QACpE,KAAK;QACL,MAAM,+KAAA,CAAA,UAAe;IACvB;AACF;AAEA,+qCAA+qC,GAC/qC,IAAI,UAAU,WAAW,GAAE,8JAAM,UAAU,CAAC;AAC5C,wCAA2C;IACzC,QAAQ,WAAW,GAAG;AACxB;uCACe", "ignoreList": [0]}}, {"offset": {"line": 256, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 282, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/node_modules/%40ant-design/icons-svg/es/asn/ToolOutlined.js"], "sourcesContent": ["// This icon file is generated automatically.\nvar ToolOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M876.6 239.5c-.5-.9-1.2-1.8-2-2.5-5-5-13.1-5-18.1 0L684.2 409.3l-67.9-67.9L788.7 169c.8-.8 1.4-1.6 2-2.5 3.6-6.1 1.6-13.9-4.5-17.5-98.2-58-226.8-44.7-311.3 39.7-67 67-89.2 162-66.5 247.4l-293 293c-3 3-2.8 7.9.3 11l169.7 169.7c3.1 3.1 8.1 3.3 11 .3l292.9-292.9c85.5 22.8 180.5.7 247.6-66.4 84.4-84.5 97.7-213.1 39.7-311.3zM786 499.8c-58.1 58.1-145.3 69.3-214.6 33.6l-8.8 8.8-.1-.1-274 274.1-79.2-79.2 230.1-230.1s0 .1.1.1l52.8-52.8c-35.7-69.3-24.5-156.5 33.6-214.6a184.2 184.2 0 01144-53.5L537 318.9a32.05 32.05 0 000 45.3l124.5 124.5a32.05 32.05 0 0045.3 0l132.8-132.8c3.7 51.8-14.4 104.8-53.6 143.9z\" } }] }, \"name\": \"tool\", \"theme\": \"outlined\" };\nexport default ToolOutlined;\n"], "names": [], "mappings": "AAAA,6CAA6C;;;;AAC7C,IAAI,eAAe;IAAE,QAAQ;QAAE,OAAO;QAAO,SAAS;YAAE,WAAW;YAAiB,aAAa;QAAQ;QAAG,YAAY;YAAC;gBAAE,OAAO;gBAAQ,SAAS;oBAAE,KAAK;gBAA2lB;YAAE;SAAE;IAAC;IAAG,QAAQ;IAAQ,SAAS;AAAW;uCAClxB", "ignoreList": [0]}}, {"offset": {"line": 306, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 312, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/node_modules/%40ant-design/icons/es/icons/ToolOutlined.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport ToolOutlinedSvg from \"@ant-design/icons-svg/es/asn/ToolOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar ToolOutlined = function ToolOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: ToolOutlinedSvg\n  }));\n};\n\n/**![tool](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg3Ni42IDIzOS41Yy0uNS0uOS0xLjItMS44LTItMi41LTUtNS0xMy4xLTUtMTguMSAwTDY4NC4yIDQwOS4zbC02Ny45LTY3LjlMNzg4LjcgMTY5Yy44LS44IDEuNC0xLjYgMi0yLjUgMy42LTYuMSAxLjYtMTMuOS00LjUtMTcuNS05OC4yLTU4LTIyNi44LTQ0LjctMzExLjMgMzkuNy02NyA2Ny04OS4yIDE2Mi02Ni41IDI0Ny40bC0yOTMgMjkzYy0zIDMtMi44IDcuOS4zIDExbDE2OS43IDE2OS43YzMuMSAzLjEgOC4xIDMuMyAxMSAuM2wyOTIuOS0yOTIuOWM4NS41IDIyLjggMTgwLjUuNyAyNDcuNi02Ni40IDg0LjQtODQuNSA5Ny43LTIxMy4xIDM5LjctMzExLjN6TTc4NiA0OTkuOGMtNTguMSA1OC4xLTE0NS4zIDY5LjMtMjE0LjYgMzMuNmwtOC44IDguOC0uMS0uMS0yNzQgMjc0LjEtNzkuMi03OS4yIDIzMC4xLTIzMC4xczAgLjEuMS4xbDUyLjgtNTIuOGMtMzUuNy02OS4zLTI0LjUtMTU2LjUgMzMuNi0yMTQuNmExODQuMiAxODQuMiAwIDAxMTQ0LTUzLjVMNTM3IDMxOC45YTMyLjA1IDMyLjA1IDAgMDAwIDQ1LjNsMTI0LjUgMTI0LjVhMzIuMDUgMzIuMDUgMCAwMDQ1LjMgMGwxMzIuOC0xMzIuOGMzLjcgNTEuOC0xNC40IDEwNC44LTUzLjYgMTQzLjl6IiAvPjwvc3ZnPg==) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(ToolOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'ToolOutlined';\n}\nexport default RefIcon;"], "names": [], "mappings": ";;;AAAA;AACA,oCAAoC;AACpC,2BAA2B;AAE3B;AAEA;AADA;AAWI;;;;;AATJ,IAAI,eAAe,SAAS,aAAa,KAAK,EAAE,GAAG;IACjD,OAAO,WAAW,GAAE,8JAAM,aAAa,CAAC,2KAAA,CAAA,UAAQ,EAAE,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,OAAO;QACpE,KAAK;QACL,MAAM,+KAAA,CAAA,UAAe;IACvB;AACF;AAEA,mgCAAmgC,GACngC,IAAI,UAAU,WAAW,GAAE,8JAAM,UAAU,CAAC;AAC5C,wCAA2C;IACzC,QAAQ,WAAW,GAAG;AACxB;uCACe", "ignoreList": [0]}}, {"offset": {"line": 337, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}