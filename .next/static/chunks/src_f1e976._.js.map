{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/stores/types.ts"], "sourcesContent": ["/**\n * Store Types and Interfaces\n * Defines TypeScript types for all store modules\n */\n\n// ============================================================================\n// Base Store Types\n// ============================================================================\n\n/**\n * Base store state interface\n * All stores should extend this interface\n */\nexport interface BaseStoreState {\n  // Hydration state for SSR compatibility\n  _hasHydrated: boolean;\n  setHasHydrated: (hasHydrated: boolean) => void;\n}\n\n/**\n * Store action interface\n * Defines the structure for store actions\n */\nexport interface StoreAction<T = any> {\n  type: string;\n  payload?: T;\n}\n\n/**\n * Store slice interface\n * For modular store composition\n */\nexport interface StoreSlice<T> {\n  (...args: any[]): T;\n}\n\n// ============================================================================\n// User and Authentication Types\n// ============================================================================\n\n/**\n * System user roles\n */\nexport type SystemUserRole = 'Admin' | 'Editor' | 'Moderator';\n\n/**\n * System user interface\n */\nexport interface SystemUser {\n  id: string;\n  email: string;\n  name: string;\n  role: SystemUserRole;\n  isActive: boolean;\n  createdAt: string;\n  updatedAt: string;\n  lastLoginAt?: string;\n}\n\n/**\n * Authentication tokens\n */\nexport interface AuthTokens {\n  accessToken: string;\n  refreshToken: string;\n  expiresAt: number; // Unix timestamp\n}\n\n/**\n * Authentication state\n */\nexport interface AuthState {\n  // User data\n  user: SystemUser | null;\n  tokens: AuthTokens | null;\n  \n  // Authentication status\n  isAuthenticated: boolean;\n  isLoading: boolean;\n  \n  // Error handling\n  error: string | null;\n  \n  // Session management\n  lastActivity: number; // Unix timestamp\n  sessionTimeout: number; // Minutes\n}\n\n/**\n * Authentication actions\n */\nexport interface AuthActions {\n  // Login/logout\n  login: (email: string, password: string) => Promise<void>;\n  logout: () => Promise<void>;\n  logoutAll: () => Promise<void>;\n  \n  // User management\n  updateProfile: (data: Partial<SystemUser>) => Promise<void>;\n  refreshTokens: () => Promise<void>;\n  \n  // State management\n  setUser: (user: SystemUser | null) => void;\n  setTokens: (tokens: AuthTokens | null) => void;\n  setLoading: (loading: boolean) => void;\n  setError: (error: string | null) => void;\n  clearError: () => void;\n  \n  // Session management\n  updateLastActivity: () => void;\n  checkSession: () => boolean;\n  \n  // Hydration\n  hydrate: () => void;\n}\n\n// ============================================================================\n// Application State Types\n// ============================================================================\n\n/**\n * Theme configuration\n */\nexport interface ThemeConfig {\n  mode: 'light' | 'dark';\n  primaryColor: string;\n  borderRadius: number;\n  compactMode: boolean;\n}\n\n/**\n * Navigation state\n */\nexport interface NavigationState {\n  currentPath: string;\n  breadcrumbs: Array<{\n    title: string;\n    path: string;\n  }>;\n  sidebarCollapsed: boolean;\n  activeMenuKey: string;\n}\n\n/**\n * Global UI state\n */\nexport interface UIState {\n  // Loading states\n  globalLoading: boolean;\n  loadingMessage: string;\n  \n  // Error states\n  globalError: string | null;\n  errorDetails: any;\n  \n  // Notification state\n  notifications: Array<{\n    id: string;\n    type: 'success' | 'error' | 'warning' | 'info';\n    title: string;\n    message: string;\n    duration?: number;\n    timestamp: number;\n  }>;\n  \n  // Modal state\n  modals: Record<string, {\n    visible: boolean;\n    data?: any;\n  }>;\n}\n\n/**\n * Application settings\n */\nexport interface AppSettings {\n  // Language and localization\n  language: 'en' | 'vi';\n  timezone: string;\n  dateFormat: string;\n  \n  // Data preferences\n  pageSize: number;\n  autoRefresh: boolean;\n  refreshInterval: number; // Seconds\n  \n  // Feature flags\n  features: {\n    darkMode: boolean;\n    notifications: boolean;\n    autoSave: boolean;\n    advancedFilters: boolean;\n  };\n}\n\n/**\n * Application state\n */\nexport interface AppState {\n  // Configuration\n  theme: ThemeConfig;\n  settings: AppSettings;\n  \n  // Navigation\n  navigation: NavigationState;\n  \n  // UI state\n  ui: UIState;\n  \n  // System info\n  version: string;\n  buildTime: string;\n  environment: 'development' | 'staging' | 'production';\n}\n\n/**\n * Application actions\n */\nexport interface AppActions {\n  // Theme management\n  setTheme: (theme: Partial<ThemeConfig>) => void;\n  toggleTheme: () => void;\n  \n  // Settings management\n  updateSettings: (settings: Partial<AppSettings>) => void;\n  resetSettings: () => void;\n  \n  // Navigation\n  setCurrentPath: (path: string) => void;\n  setBreadcrumbs: (breadcrumbs: NavigationState['breadcrumbs']) => void;\n  toggleSidebar: () => void;\n  setActiveMenu: (key: string) => void;\n  \n  // UI state management\n  setGlobalLoading: (loading: boolean, message?: string) => void;\n  setGlobalError: (error: string | null, details?: any) => void;\n  clearGlobalError: () => void;\n  \n  // Notifications\n  addNotification: (notification: Omit<UIState['notifications'][0], 'id' | 'timestamp'>) => void;\n  removeNotification: (id: string) => void;\n  clearNotifications: () => void;\n  \n  // Modals\n  showModal: (key: string, data?: any) => void;\n  hideModal: (key: string) => void;\n  hideAllModals: () => void;\n}\n\n// ============================================================================\n// Combined Store Types\n// ============================================================================\n\n/**\n * Complete authentication store\n */\nexport interface AuthStore extends BaseStoreState, AuthState, AuthActions {}\n\n/**\n * Complete application store\n */\nexport interface AppStore extends BaseStoreState, AppState, AppActions {}\n\n// ============================================================================\n// Store Configuration Types\n// ============================================================================\n\n/**\n * Store persistence configuration\n */\nexport interface StorePersistConfig {\n  name: string;\n  version: number;\n  partialize?: (state: any) => any;\n  skipHydration?: boolean;\n}\n\n/**\n * Store devtools configuration\n */\nexport interface StoreDevtoolsConfig {\n  name: string;\n  enabled: boolean;\n}\n\n/**\n * Store configuration\n */\nexport interface StoreConfig {\n  persist?: StorePersistConfig;\n  devtools?: StoreDevtoolsConfig;\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,+EAA+E;AAC/E,mBAAmB;AACnB,+EAA+E;AAE/E;;;CAGC"}}, {"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 27, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/stores/utils.ts"], "sourcesContent": ["/**\n * Store Utilities\n * Helper functions and utilities for store management\n */\n\nimport { StateCreator } from 'zustand';\nimport { persist, devtools, createJSONStorage } from 'zustand/middleware';\nimport type {\n  BaseStoreState,\n  StoreConfig,\n  StorePersistConfig,\n  StoreDevtoolsConfig\n} from './types';\n\n// ============================================================================\n// Store Creation Utilities\n// ============================================================================\n\n/**\n * Create a store with middleware support\n * Provides a consistent way to create stores with persistence and devtools\n */\nexport function createStoreWithMiddleware<T extends BaseStoreState>(\n  storeCreator: StateCreator<T>,\n  config: StoreConfig\n): StateCreator<T> {\n  let store: any = storeCreator;\n\n  // Apply persistence middleware if configured\n  if (config.persist) {\n    store = persist(\n      store,\n      {\n        name: config.persist.name,\n        version: config.persist.version,\n        storage: createJSONStorage(() => localStorage),\n        partialize: config.persist.partialize || ((state) => state),\n        skipHydration: config.persist.skipHydration || false,\n        onRehydrateStorage: () => (state: any) => {\n          if (state) {\n            state.setHasHydrated(true);\n          }\n        },\n      }\n    );\n  }\n\n  // Apply devtools middleware if configured\n  if (config.devtools) {\n    store = devtools(\n      store,\n      {\n        name: config.devtools.name,\n        enabled: config.devtools.enabled && process.env.NODE_ENV === 'development',\n      }\n    );\n  }\n\n  return store as StateCreator<T>;\n}\n\n// ============================================================================\n// Base Store State Utilities\n// ============================================================================\n\n/**\n * Create base store state\n * Provides common state properties for all stores\n */\nexport function createBaseStoreState(): BaseStoreState {\n  return {\n    _hasHydrated: false,\n    setHasHydrated: (hasHydrated: boolean) => {\n      // This will be implemented by the actual store\n    },\n  };\n}\n\n/**\n * Create base store actions\n * Provides common actions for all stores\n */\nexport function createBaseStoreActions<T extends BaseStoreState>(\n  set: (partial: Partial<T>) => void\n): Pick<BaseStoreState, 'setHasHydrated'> {\n  return {\n    setHasHydrated: (hasHydrated: boolean) => {\n      set({ _hasHydrated: hasHydrated } as Partial<T>);\n    },\n  };\n}\n\n// ============================================================================\n// Token Management Utilities\n// ============================================================================\n\n/**\n * Check if token is expired\n */\nexport function isTokenExpired(expiresAt: number): boolean {\n  return Date.now() >= expiresAt;\n}\n\n/**\n * Get token expiration time\n * Calculates expiration time from JWT token or sets default\n */\nexport function getTokenExpiration(token: string, defaultMinutes: number = 60): number {\n  try {\n    // Try to decode JWT token to get expiration\n    const payload = JSON.parse(atob(token.split('.')[1]));\n    if (payload.exp) {\n      return payload.exp * 1000; // Convert to milliseconds\n    }\n  } catch (error) {\n    // If JWT parsing fails, use default expiration\n  }\n\n  // Default expiration: current time + defaultMinutes\n  return Date.now() + (defaultMinutes * 60 * 1000);\n}\n\n/**\n * Clear all stored tokens\n */\nexport function clearStoredTokens(): void {\n  if (typeof window !== 'undefined') {\n    localStorage.removeItem('auth-storage');\n    sessionStorage.removeItem('auth-storage');\n  }\n}\n\n// ============================================================================\n// Session Management Utilities\n// ============================================================================\n\n/**\n * Check if session is valid\n */\nexport function isSessionValid(\n  lastActivity: number,\n  sessionTimeout: number\n): boolean {\n  const now = Date.now();\n  const timeoutMs = sessionTimeout * 60 * 1000; // Convert minutes to milliseconds\n  return (now - lastActivity) < timeoutMs;\n}\n\n/**\n * Get session remaining time in minutes\n */\nexport function getSessionRemainingTime(\n  lastActivity: number,\n  sessionTimeout: number\n): number {\n  const now = Date.now();\n  const timeoutMs = sessionTimeout * 60 * 1000;\n  const elapsed = now - lastActivity;\n  const remaining = timeoutMs - elapsed;\n  return Math.max(0, Math.floor(remaining / (60 * 1000)));\n}\n\n// ============================================================================\n// Error Handling Utilities\n// ============================================================================\n\n/**\n * Extract error message from various error types\n */\nexport function extractErrorMessage(error: any): string {\n  if (typeof error === 'string') {\n    return error;\n  }\n\n  if (error?.response?.data?.message) {\n    return error.response.data.message;\n  }\n\n  if (error?.message) {\n    return error.message;\n  }\n\n  if (error?.error) {\n    return error.error;\n  }\n\n  return 'An unexpected error occurred';\n}\n\n/**\n * Create error object with details\n */\nexport function createErrorObject(\n  message: string,\n  details?: any\n): { message: string; details: any } {\n  return {\n    message,\n    details: details || null,\n  };\n}\n\n// ============================================================================\n// Notification Utilities\n// ============================================================================\n\n/**\n * Generate unique notification ID\n */\nexport function generateNotificationId(): string {\n  return `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n}\n\n/**\n * Get default notification duration based on type\n */\nexport function getDefaultNotificationDuration(\n  type: 'success' | 'error' | 'warning' | 'info'\n): number {\n  switch (type) {\n    case 'success':\n      return 3000; // 3 seconds\n    case 'error':\n      return 5000; // 5 seconds\n    case 'warning':\n      return 4000; // 4 seconds\n    case 'info':\n      return 3000; // 3 seconds\n    default:\n      return 3000;\n  }\n}\n\n// ============================================================================\n// Local Storage Utilities\n// ============================================================================\n\n/**\n * Safe localStorage operations\n */\nexport const storage = {\n  get: (key: string): any => {\n    if (typeof window === 'undefined') return null;\n\n    try {\n      const item = localStorage.getItem(key);\n      return item ? JSON.parse(item) : null;\n    } catch (error) {\n      console.warn(`Error reading from localStorage key \"${key}\":`, error);\n      return null;\n    }\n  },\n\n  set: (key: string, value: any): void => {\n    if (typeof window === 'undefined') return;\n\n    try {\n      localStorage.setItem(key, JSON.stringify(value));\n    } catch (error) {\n      console.warn(`Error writing to localStorage key \"${key}\":`, error);\n    }\n  },\n\n  remove: (key: string): void => {\n    if (typeof window === 'undefined') return;\n\n    try {\n      localStorage.removeItem(key);\n    } catch (error) {\n      console.warn(`Error removing localStorage key \"${key}\":`, error);\n    }\n  },\n\n  clear: (): void => {\n    if (typeof window === 'undefined') return;\n\n    try {\n      localStorage.clear();\n    } catch (error) {\n      console.warn('Error clearing localStorage:', error);\n    }\n  },\n};\n\n// ============================================================================\n// Development Utilities\n// ============================================================================\n\n/**\n * Log store action for debugging\n */\nexport function logStoreAction(\n  storeName: string,\n  actionName: string,\n  payload?: any\n): void {\n  if (process.env.NODE_ENV === 'development') {\n    console.group(`🐻 [${storeName}] ${actionName}`);\n    if (payload !== undefined) {\n      console.log('Payload:', payload);\n    }\n    console.log('Timestamp:', new Date().toISOString());\n    console.groupEnd();\n  }\n}\n\n/**\n * Validate store state structure\n */\nexport function validateStoreState<T extends BaseStoreState>(\n  state: T,\n  requiredKeys: (keyof T)[]\n): boolean {\n  for (const key of requiredKeys) {\n    if (!(key in state)) {\n      console.error(`Missing required store state key: ${String(key)}`);\n      return false;\n    }\n  }\n  return true;\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;;;;;;AAGD;AA+C4C;;AA/BrC,SAAS,0BACd,YAA6B,EAC7B,MAAmB;IAEnB,IAAI,QAAa;IAEjB,6CAA6C;IAC7C,IAAI,OAAO,OAAO,EAAE;QAClB,QAAQ,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EACZ,OACA;YACE,MAAM,OAAO,OAAO,CAAC,IAAI;YACzB,SAAS,OAAO,OAAO,CAAC,OAAO;YAC/B,SAAS,CAAA,GAAA,gJAAA,CAAA,oBAAiB,AAAD,EAAE,IAAM;YACjC,YAAY,OAAO,OAAO,CAAC,UAAU,IAAI,CAAC,CAAC,QAAU,KAAK;YAC1D,eAAe,OAAO,OAAO,CAAC,aAAa,IAAI;YAC/C,oBAAoB,IAAM,CAAC;oBACzB,IAAI,OAAO;wBACT,MAAM,cAAc,CAAC;oBACvB;gBACF;QACF;IAEJ;IAEA,0CAA0C;IAC1C,IAAI,OAAO,QAAQ,EAAE;QACnB,QAAQ,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD,EACb,OACA;YACE,MAAM,OAAO,QAAQ,CAAC,IAAI;YAC1B,SAAS,OAAO,QAAQ,CAAC,OAAO,IAAI,oDAAyB;QAC/D;IAEJ;IAEA,OAAO;AACT;AAUO,SAAS;IACd,OAAO;QACL,cAAc;QACd,gBAAgB,CAAC;QACf,+CAA+C;QACjD;IACF;AACF;AAMO,SAAS,uBACd,GAAkC;IAElC,OAAO;QACL,gBAAgB,CAAC;YACf,IAAI;gBAAE,cAAc;YAAY;QAClC;IACF;AACF;AASO,SAAS,eAAe,SAAiB;IAC9C,OAAO,KAAK,GAAG,MAAM;AACvB;AAMO,SAAS,mBAAmB,KAAa,EAAE,iBAAyB,EAAE;IAC3E,IAAI;QACF,4CAA4C;QAC5C,MAAM,UAAU,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,CAAC,IAAI,CAAC,EAAE;QACnD,IAAI,QAAQ,GAAG,EAAE;YACf,OAAO,QAAQ,GAAG,GAAG,MAAM,0BAA0B;QACvD;IACF,EAAE,OAAO,OAAO;IACd,+CAA+C;IACjD;IAEA,oDAAoD;IACpD,OAAO,KAAK,GAAG,KAAM,iBAAiB,KAAK;AAC7C;AAKO,SAAS;IACd,wCAAmC;QACjC,aAAa,UAAU,CAAC;QACxB,eAAe,UAAU,CAAC;IAC5B;AACF;AASO,SAAS,eACd,YAAoB,EACpB,cAAsB;IAEtB,MAAM,MAAM,KAAK,GAAG;IACpB,MAAM,YAAY,iBAAiB,KAAK,MAAM,kCAAkC;IAChF,OAAO,AAAC,MAAM,eAAgB;AAChC;AAKO,SAAS,wBACd,YAAoB,EACpB,cAAsB;IAEtB,MAAM,MAAM,KAAK,GAAG;IACpB,MAAM,YAAY,iBAAiB,KAAK;IACxC,MAAM,UAAU,MAAM;IACtB,MAAM,YAAY,YAAY;IAC9B,OAAO,KAAK,GAAG,CAAC,GAAG,KAAK,KAAK,CAAC,YAAY,CAAC,KAAK,IAAI;AACtD;AASO,SAAS,oBAAoB,KAAU;IAC5C,IAAI,OAAO,UAAU,UAAU;QAC7B,OAAO;IACT;IAEA,IAAI,OAAO,UAAU,MAAM,SAAS;QAClC,OAAO,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO;IACpC;IAEA,IAAI,OAAO,SAAS;QAClB,OAAO,MAAM,OAAO;IACtB;IAEA,IAAI,OAAO,OAAO;QAChB,OAAO,MAAM,KAAK;IACpB;IAEA,OAAO;AACT;AAKO,SAAS,kBACd,OAAe,EACf,OAAa;IAEb,OAAO;QACL;QACA,SAAS,WAAW;IACtB;AACF;AASO,SAAS;IACd,OAAO,CAAC,aAAa,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;AAChF;AAKO,SAAS,+BACd,IAA8C;IAE9C,OAAQ;QACN,KAAK;YACH,OAAO,MAAM,YAAY;QAC3B,KAAK;YACH,OAAO,MAAM,YAAY;QAC3B,KAAK;YACH,OAAO,MAAM,YAAY;QAC3B,KAAK;YACH,OAAO,MAAM,YAAY;QAC3B;YACE,OAAO;IACX;AACF;AASO,MAAM,UAAU;IACrB,KAAK,CAAC;QACJ,uCAAmC;;QAAW;QAE9C,IAAI;YACF,MAAM,OAAO,aAAa,OAAO,CAAC;YAClC,OAAO,OAAO,KAAK,KAAK,CAAC,QAAQ;QACnC,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,CAAC,qCAAqC,EAAE,IAAI,EAAE,CAAC,EAAE;YAC9D,OAAO;QACT;IACF;IAEA,KAAK,CAAC,KAAa;QACjB,uCAAmC;;QAAM;QAEzC,IAAI;YACF,aAAa,OAAO,CAAC,KAAK,KAAK,SAAS,CAAC;QAC3C,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,CAAC,mCAAmC,EAAE,IAAI,EAAE,CAAC,EAAE;QAC9D;IACF;IAEA,QAAQ,CAAC;QACP,uCAAmC;;QAAM;QAEzC,IAAI;YACF,aAAa,UAAU,CAAC;QAC1B,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,CAAC,iCAAiC,EAAE,IAAI,EAAE,CAAC,EAAE;QAC5D;IACF;IAEA,OAAO;QACL,uCAAmC;;QAAM;QAEzC,IAAI;YACF,aAAa,KAAK;QACpB,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,gCAAgC;QAC/C;IACF;AACF;AASO,SAAS,eACd,SAAiB,EACjB,UAAkB,EAClB,OAAa;IAEb,wCAA4C;QAC1C,QAAQ,KAAK,CAAC,CAAC,IAAI,EAAE,UAAU,EAAE,EAAE,YAAY;QAC/C,IAAI,YAAY,WAAW;YACzB,QAAQ,GAAG,CAAC,YAAY;QAC1B;QACA,QAAQ,GAAG,CAAC,cAAc,IAAI,OAAO,WAAW;QAChD,QAAQ,QAAQ;IAClB;AACF;AAKO,SAAS,mBACd,KAAQ,EACR,YAAyB;IAEzB,KAAK,MAAM,OAAO,aAAc;QAC9B,IAAI,CAAC,CAAC,OAAO,KAAK,GAAG;YACnB,QAAQ,KAAK,CAAC,CAAC,kCAAkC,EAAE,OAAO,MAAM;YAChE,OAAO;QACT;IACF;IACA,OAAO;AACT"}}, {"offset": {"line": 231, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 237, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/stores/constants.ts"], "sourcesContent": ["/**\n * Store Constants\n * Defines constants used across all stores\n */\n\n// ============================================================================\n// Store Names\n// ============================================================================\n\nexport const STORE_NAMES = {\n  AUTH: 'auth-store',\n  APP: 'app-store',\n} as const;\n\n// ============================================================================\n// Storage Keys\n// ============================================================================\n\nexport const STORAGE_KEYS = {\n  AUTH: 'auth-storage',\n  APP: 'app-storage',\n  THEME: 'theme-storage',\n  SETTINGS: 'settings-storage',\n} as const;\n\n// ============================================================================\n// Default Values\n// ============================================================================\n\n/**\n * Default theme configuration\n */\nexport const DEFAULT_THEME = {\n  mode: 'light' as const,\n  primaryColor: '#1890ff',\n  borderRadius: 6,\n  compactMode: false,\n};\n\n/**\n * Default application settings\n */\nexport const DEFAULT_APP_SETTINGS = {\n  language: 'en' as const,\n  timezone: 'UTC',\n  dateFormat: 'YYYY-MM-DD',\n  pageSize: 20,\n  autoRefresh: true,\n  refreshInterval: 30, // seconds\n  features: {\n    darkMode: true,\n    notifications: true,\n    autoSave: true,\n    advancedFilters: true,\n  },\n};\n\n/**\n * Default navigation state\n */\nexport const DEFAULT_NAVIGATION = {\n  currentPath: '/',\n  breadcrumbs: [],\n  sidebarCollapsed: false,\n  activeMenuKey: 'dashboard',\n};\n\n/**\n * Default UI state\n */\nexport const DEFAULT_UI_STATE = {\n  globalLoading: false,\n  loadingMessage: '',\n  globalError: null,\n  errorDetails: null,\n  notifications: [],\n  modals: {},\n};\n\n// ============================================================================\n// Session Configuration\n// ============================================================================\n\n/**\n * Session timeout in minutes\n */\nexport const SESSION_TIMEOUT = 60; // 1 hour\n\n/**\n * Token refresh threshold in minutes\n * Refresh token when it expires in less than this time\n */\nexport const TOKEN_REFRESH_THRESHOLD = 5; // 5 minutes\n\n/**\n * Activity tracking interval in milliseconds\n */\nexport const ACTIVITY_TRACKING_INTERVAL = 60000; // 1 minute\n\n// ============================================================================\n// API Configuration\n// ============================================================================\n\n/**\n * API endpoints for store operations\n */\nexport const STORE_API_ENDPOINTS = {\n  AUTH: {\n    LOGIN: '/api/system-auth/login',\n    LOGOUT: '/api/system-auth/logout',\n    LOGOUT_ALL: '/api/system-auth/logout-all',\n    PROFILE: '/api/system-auth/profile',\n    REFRESH: '/api/system-auth/refresh',\n  },\n} as const;\n\n// ============================================================================\n// Error Messages\n// ============================================================================\n\nexport const ERROR_MESSAGES = {\n  AUTH: {\n    LOGIN_FAILED: 'Login failed. Please check your credentials.',\n    LOGOUT_FAILED: 'Logout failed. Please try again.',\n    SESSION_EXPIRED: 'Your session has expired. Please log in again.',\n    TOKEN_REFRESH_FAILED: 'Failed to refresh authentication token.',\n    PROFILE_UPDATE_FAILED: 'Failed to update profile. Please try again.',\n    UNAUTHORIZED: 'You are not authorized to perform this action.',\n  },\n  APP: {\n    SETTINGS_SAVE_FAILED: 'Failed to save settings. Please try again.',\n    THEME_LOAD_FAILED: 'Failed to load theme configuration.',\n    NETWORK_ERROR: 'Network error. Please check your connection.',\n    UNKNOWN_ERROR: 'An unexpected error occurred. Please try again.',\n  },\n} as const;\n\n// ============================================================================\n// Success Messages\n// ============================================================================\n\nexport const SUCCESS_MESSAGES = {\n  AUTH: {\n    LOGIN_SUCCESS: 'Successfully logged in.',\n    LOGOUT_SUCCESS: 'Successfully logged out.',\n    PROFILE_UPDATED: 'Profile updated successfully.',\n  },\n  APP: {\n    SETTINGS_SAVED: 'Settings saved successfully.',\n    THEME_UPDATED: 'Theme updated successfully.',\n  },\n} as const;\n\n// ============================================================================\n// Store Versions\n// ============================================================================\n\n/**\n * Store versions for migration support\n */\nexport const STORE_VERSIONS = {\n  AUTH: 1,\n  APP: 1,\n} as const;\n\n// ============================================================================\n// Development Configuration\n// ============================================================================\n\n/**\n * Development mode configuration\n */\nexport const DEV_CONFIG = {\n  ENABLE_DEVTOOLS: process.env.NODE_ENV === 'development',\n  ENABLE_LOGGING: process.env.NODE_ENV === 'development',\n  MOCK_API_DELAY: 1000, // milliseconds\n} as const;\n\n// ============================================================================\n// Feature Flags\n// ============================================================================\n\n/**\n * Feature flags for conditional functionality\n */\nexport const FEATURE_FLAGS = {\n  ENABLE_DARK_MODE: true,\n  ENABLE_NOTIFICATIONS: true,\n  ENABLE_AUTO_SAVE: true,\n  ENABLE_ADVANCED_FILTERS: true,\n  ENABLE_REAL_TIME_UPDATES: false, // Future feature\n  ENABLE_OFFLINE_MODE: false, // Future feature\n} as const;\n\n// ============================================================================\n// Validation Rules\n// ============================================================================\n\n/**\n * Validation rules for store data\n */\nexport const VALIDATION_RULES = {\n  EMAIL: /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/,\n  PASSWORD_MIN_LENGTH: 8,\n  NAME_MIN_LENGTH: 2,\n  NAME_MAX_LENGTH: 50,\n  PAGE_SIZE_MIN: 5,\n  PAGE_SIZE_MAX: 100,\n  REFRESH_INTERVAL_MIN: 10, // seconds\n  REFRESH_INTERVAL_MAX: 300, // seconds\n} as const;\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,+EAA+E;AAC/E,cAAc;AACd,+EAA+E;;;;;;;;;;;;;;;;;;;AAsK5D;AApKZ,MAAM,cAAc;IACzB,MAAM;IACN,KAAK;AACP;AAMO,MAAM,eAAe;IAC1B,MAAM;IACN,KAAK;IACL,OAAO;IACP,UAAU;AACZ;AASO,MAAM,gBAAgB;IAC3B,MAAM;IACN,cAAc;IACd,cAAc;IACd,aAAa;AACf;AAKO,MAAM,uBAAuB;IAClC,UAAU;IACV,UAAU;IACV,YAAY;IACZ,UAAU;IACV,aAAa;IACb,iBAAiB;IACjB,UAAU;QACR,UAAU;QACV,eAAe;QACf,UAAU;QACV,iBAAiB;IACnB;AACF;AAKO,MAAM,qBAAqB;IAChC,aAAa;IACb,aAAa,EAAE;IACf,kBAAkB;IAClB,eAAe;AACjB;AAKO,MAAM,mBAAmB;IAC9B,eAAe;IACf,gBAAgB;IAChB,aAAa;IACb,cAAc;IACd,eAAe,EAAE;IACjB,QAAQ,CAAC;AACX;AASO,MAAM,kBAAkB,IAAI,SAAS;AAMrC,MAAM,0BAA0B,GAAG,YAAY;AAK/C,MAAM,6BAA6B,OAAO,WAAW;AASrD,MAAM,sBAAsB;IACjC,MAAM;QACJ,OAAO;QACP,QAAQ;QACR,YAAY;QACZ,SAAS;QACT,SAAS;IACX;AACF;AAMO,MAAM,iBAAiB;IAC5B,MAAM;QACJ,cAAc;QACd,eAAe;QACf,iBAAiB;QACjB,sBAAsB;QACtB,uBAAuB;QACvB,cAAc;IAChB;IACA,KAAK;QACH,sBAAsB;QACtB,mBAAmB;QACnB,eAAe;QACf,eAAe;IACjB;AACF;AAMO,MAAM,mBAAmB;IAC9B,MAAM;QACJ,eAAe;QACf,gBAAgB;QAChB,iBAAiB;IACnB;IACA,KAAK;QACH,gBAAgB;QAChB,eAAe;IACjB;AACF;AASO,MAAM,iBAAiB;IAC5B,MAAM;IACN,KAAK;AACP;AASO,MAAM,aAAa;IACxB,iBAAiB,oDAAyB;IAC1C,gBAAgB,oDAAyB;IACzC,gBAAgB;AAClB;AASO,MAAM,gBAAgB;IAC3B,kBAAkB;IAClB,sBAAsB;IACtB,kBAAkB;IAClB,yBAAyB;IACzB,0BAA0B;IAC1B,qBAAqB;AACvB;AASO,MAAM,mBAAmB;IAC9B,OAAO;IACP,qBAAqB;IACrB,iBAAiB;IACjB,iBAAiB;IACjB,eAAe;IACf,eAAe;IACf,sBAAsB;IACtB,sBAAsB;AACxB"}}, {"offset": {"line": 375, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 381, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/stores/auth-store.ts"], "sourcesContent": ["/**\n * Authentication Store\n * Manages user authentication state, tokens, and session\n */\n\nimport { create } from 'zustand';\nimport {\n  AuthStore,\n  SystemUser,\n  AuthTokens,\n  SystemUserRole\n} from './types';\nimport {\n  createStoreWithMiddleware,\n  createBaseStoreActions,\n  extractErrorMessage,\n  isTokenExpired,\n  getTokenExpiration,\n  clearStoredTokens,\n  isSessionValid,\n  logStoreAction\n} from './utils';\nimport {\n  STORE_NAMES,\n  STORAGE_KEYS,\n  SESSION_TIMEOUT,\n  TOKEN_REFRESH_THRESHOLD,\n  STORE_API_ENDPOINTS,\n  ERROR_MESSAGES,\n  SUCCESS_MESSAGES,\n  STORE_VERSIONS\n} from './constants';\n\n// ============================================================================\n// Initial State\n// ============================================================================\n\nconst initialAuthState = {\n  // Base store state\n  _hasHydrated: false,\n\n  // User data\n  user: null,\n  tokens: null,\n\n  // Authentication status\n  isAuthenticated: false,\n  isLoading: false,\n\n  // Error handling\n  error: null,\n\n  // Session management\n  lastActivity: Date.now(),\n  sessionTimeout: SESSION_TIMEOUT,\n};\n\n// ============================================================================\n// Store Implementation\n// ============================================================================\n\n/**\n * Authentication Store Creator\n */\nconst createAuthStore = () => {\n  return create<AuthStore>()(\n    createStoreWithMiddleware<AuthStore>(\n      (set, get) => ({\n        ...initialAuthState,\n\n        // Base store actions\n        ...createBaseStoreActions<AuthStore>(set),\n\n        // ========================================================================\n        // Authentication Actions\n        // ========================================================================\n\n        /**\n         * Login user with email and password\n         */\n        login: async (email: string, password: string) => {\n          logStoreAction(STORE_NAMES.AUTH, 'login', { email });\n\n          set({ isLoading: true, error: null });\n\n          try {\n            const response = await fetch(STORE_API_ENDPOINTS.AUTH.LOGIN, {\n              method: 'POST',\n              headers: {\n                'Content-Type': 'application/json',\n              },\n              body: JSON.stringify({ email, password }),\n            });\n\n            if (!response.ok) {\n              const errorData = await response.json().catch(() => ({}));\n              throw new Error(errorData.message || ERROR_MESSAGES.AUTH.LOGIN_FAILED);\n            }\n\n            const data = await response.json();\n\n            if (data.success && data.data) {\n              const { user, accessToken, refreshToken } = data.data;\n\n              // Create tokens object\n              const tokens: AuthTokens = {\n                accessToken,\n                refreshToken,\n                expiresAt: getTokenExpiration(accessToken),\n              };\n\n              // Update store state\n              set({\n                user,\n                tokens,\n                isAuthenticated: true,\n                isLoading: false,\n                error: null,\n                lastActivity: Date.now(),\n              });\n\n              logStoreAction(STORE_NAMES.AUTH, 'login_success', { userId: user.id });\n            } else {\n              throw new Error(data.message || ERROR_MESSAGES.AUTH.LOGIN_FAILED);\n            }\n          } catch (error) {\n            const errorMessage = extractErrorMessage(error);\n            logStoreAction(STORE_NAMES.AUTH, 'login_error', { error: errorMessage });\n\n            set({\n              isLoading: false,\n              error: errorMessage,\n              isAuthenticated: false,\n              user: null,\n              tokens: null,\n            });\n\n            throw error;\n          }\n        },\n\n        /**\n         * Logout user from current session\n         */\n        logout: async () => {\n          logStoreAction(STORE_NAMES.AUTH, 'logout');\n\n          const { tokens } = get();\n\n          try {\n            // Call logout API if we have tokens\n            if (tokens?.accessToken) {\n              await fetch(STORE_API_ENDPOINTS.AUTH.LOGOUT, {\n                method: 'POST',\n                headers: {\n                  'Content-Type': 'application/json',\n                  'Authorization': `Bearer ${tokens.accessToken}`,\n                },\n              });\n            }\n          } catch (error) {\n            // Log error but don't prevent logout\n            logStoreAction(STORE_NAMES.AUTH, 'logout_api_error', { error: extractErrorMessage(error) });\n          }\n\n          // Clear local state regardless of API call result\n          clearStoredTokens();\n          set({\n            user: null,\n            tokens: null,\n            isAuthenticated: false,\n            error: null,\n            lastActivity: Date.now(),\n          });\n\n          logStoreAction(STORE_NAMES.AUTH, 'logout_success');\n        },\n\n        /**\n         * Logout user from all devices\n         */\n        logoutAll: async () => {\n          logStoreAction(STORE_NAMES.AUTH, 'logout_all');\n\n          const { tokens } = get();\n\n          try {\n            if (tokens?.accessToken) {\n              await fetch(STORE_API_ENDPOINTS.AUTH.LOGOUT_ALL, {\n                method: 'POST',\n                headers: {\n                  'Content-Type': 'application/json',\n                  'Authorization': `Bearer ${tokens.accessToken}`,\n                },\n              });\n            }\n          } catch (error) {\n            logStoreAction(STORE_NAMES.AUTH, 'logout_all_api_error', { error: extractErrorMessage(error) });\n          }\n\n          // Clear local state\n          clearStoredTokens();\n          set({\n            user: null,\n            tokens: null,\n            isAuthenticated: false,\n            error: null,\n            lastActivity: Date.now(),\n          });\n\n          logStoreAction(STORE_NAMES.AUTH, 'logout_all_success');\n        },\n\n        // ========================================================================\n        // User Profile Actions\n        // ========================================================================\n\n        /**\n         * Update user profile\n         */\n        updateProfile: async (data: Partial<SystemUser>) => {\n          logStoreAction(STORE_NAMES.AUTH, 'update_profile', data);\n\n          const { tokens, user } = get();\n\n          if (!tokens?.accessToken || !user) {\n            throw new Error(ERROR_MESSAGES.AUTH.UNAUTHORIZED);\n          }\n\n          set({ isLoading: true, error: null });\n\n          try {\n            const response = await fetch(STORE_API_ENDPOINTS.AUTH.PROFILE, {\n              method: 'PUT',\n              headers: {\n                'Content-Type': 'application/json',\n                'Authorization': `Bearer ${tokens.accessToken}`,\n              },\n              body: JSON.stringify(data),\n            });\n\n            if (!response.ok) {\n              const errorData = await response.json().catch(() => ({}));\n              throw new Error(errorData.message || ERROR_MESSAGES.AUTH.PROFILE_UPDATE_FAILED);\n            }\n\n            const responseData = await response.json();\n\n            if (responseData.success && responseData.data) {\n              set({\n                user: { ...user, ...responseData.data },\n                isLoading: false,\n                error: null,\n                lastActivity: Date.now(),\n              });\n\n              logStoreAction(STORE_NAMES.AUTH, 'update_profile_success');\n            } else {\n              throw new Error(responseData.message || ERROR_MESSAGES.AUTH.PROFILE_UPDATE_FAILED);\n            }\n          } catch (error) {\n            const errorMessage = extractErrorMessage(error);\n            logStoreAction(STORE_NAMES.AUTH, 'update_profile_error', { error: errorMessage });\n\n            set({\n              isLoading: false,\n              error: errorMessage,\n            });\n\n            throw error;\n          }\n        },\n\n        /**\n         * Refresh authentication tokens\n         */\n        refreshTokens: async () => {\n          logStoreAction(STORE_NAMES.AUTH, 'refresh_tokens');\n\n          const { tokens } = get();\n\n          if (!tokens?.refreshToken) {\n            throw new Error(ERROR_MESSAGES.AUTH.TOKEN_REFRESH_FAILED);\n          }\n\n          try {\n            const response = await fetch(STORE_API_ENDPOINTS.AUTH.REFRESH, {\n              method: 'POST',\n              headers: {\n                'Content-Type': 'application/json',\n              },\n              body: JSON.stringify({ refreshToken: tokens.refreshToken }),\n            });\n\n            if (!response.ok) {\n              throw new Error(ERROR_MESSAGES.AUTH.TOKEN_REFRESH_FAILED);\n            }\n\n            const data = await response.json();\n\n            if (data.success && data.data) {\n              const { accessToken, refreshToken } = data.data;\n\n              const newTokens: AuthTokens = {\n                accessToken,\n                refreshToken,\n                expiresAt: getTokenExpiration(accessToken),\n              };\n\n              set({\n                tokens: newTokens,\n                lastActivity: Date.now(),\n              });\n\n              logStoreAction(STORE_NAMES.AUTH, 'refresh_tokens_success');\n            } else {\n              throw new Error(data.message || ERROR_MESSAGES.AUTH.TOKEN_REFRESH_FAILED);\n            }\n          } catch (error) {\n            const errorMessage = extractErrorMessage(error);\n            logStoreAction(STORE_NAMES.AUTH, 'refresh_tokens_error', { error: errorMessage });\n\n            // If refresh fails, logout user\n            get().logout();\n            throw error;\n          }\n        },\n\n        // ========================================================================\n        // State Management Actions\n        // ========================================================================\n\n        /**\n         * Set user data\n         */\n        setUser: (user: SystemUser | null) => {\n          set({ user, isAuthenticated: !!user });\n          logStoreAction(STORE_NAMES.AUTH, 'set_user', { userId: user?.id });\n        },\n\n        /**\n         * Set authentication tokens\n         */\n        setTokens: (tokens: AuthTokens | null) => {\n          set({ tokens });\n          logStoreAction(STORE_NAMES.AUTH, 'set_tokens', { hasTokens: !!tokens });\n        },\n\n        /**\n         * Set loading state\n         */\n        setLoading: (loading: boolean) => {\n          set({ isLoading: loading });\n        },\n\n        /**\n         * Set error message\n         */\n        setError: (error: string | null) => {\n          set({ error });\n          if (error) {\n            logStoreAction(STORE_NAMES.AUTH, 'set_error', { error });\n          }\n        },\n\n        /**\n         * Clear error message\n         */\n        clearError: () => {\n          set({ error: null });\n        },\n\n        // ========================================================================\n        // Session Management Actions\n        // ========================================================================\n\n        /**\n         * Update last activity timestamp\n         */\n        updateLastActivity: () => {\n          set({ lastActivity: Date.now() });\n        },\n\n        /**\n         * Check if current session is valid\n         */\n        checkSession: () => {\n          const { lastActivity, sessionTimeout, tokens } = get();\n\n          // Check session timeout\n          if (!isSessionValid(lastActivity, sessionTimeout)) {\n            logStoreAction(STORE_NAMES.AUTH, 'session_expired');\n            get().logout();\n            return false;\n          }\n\n          // Check token expiration\n          if (tokens && isTokenExpired(tokens.expiresAt)) {\n            logStoreAction(STORE_NAMES.AUTH, 'token_expired');\n\n            // Try to refresh tokens\n            get().refreshTokens().catch(() => {\n              // If refresh fails, logout will be called automatically\n            });\n\n            return false;\n          }\n\n          return true;\n        },\n\n        /**\n         * Hydrate store from persisted state\n         */\n        hydrate: () => {\n          const state = get();\n\n          // Validate persisted session\n          if (state.isAuthenticated && state.user && state.tokens) {\n            const isValid = state.checkSession();\n            if (!isValid) {\n              // Session is invalid, clear state\n              set({\n                user: null,\n                tokens: null,\n                isAuthenticated: false,\n                error: null,\n              });\n            }\n          }\n\n          set({ _hasHydrated: true });\n          logStoreAction(STORE_NAMES.AUTH, 'hydrated');\n        },\n      }),\n      {\n        persist: {\n          name: STORAGE_KEYS.AUTH,\n          version: STORE_VERSIONS.AUTH,\n          partialize: (state) => ({\n            user: state.user,\n            tokens: state.tokens,\n            isAuthenticated: state.isAuthenticated,\n            lastActivity: state.lastActivity,\n            sessionTimeout: state.sessionTimeout,\n          }),\n        },\n        devtools: {\n          name: STORE_NAMES.AUTH,\n          enabled: process.env.NODE_ENV === 'development',\n        },\n      }\n    )\n  );\n};\n\n// ============================================================================\n// Export Store\n// ============================================================================\n\nexport const useAuthStore = createAuthStore();\n\n// Export store for testing and advanced usage\nexport { createAuthStore };\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AASD;AAUA;AAjBA;AA4bmB;;;;AAhanB,+EAA+E;AAC/E,gBAAgB;AAChB,+EAA+E;AAE/E,MAAM,mBAAmB;IACvB,mBAAmB;IACnB,cAAc;IAEd,YAAY;IACZ,MAAM;IACN,QAAQ;IAER,wBAAwB;IACxB,iBAAiB;IACjB,WAAW;IAEX,iBAAiB;IACjB,OAAO;IAEP,qBAAqB;IACrB,cAAc,KAAK,GAAG;IACtB,gBAAgB,6HAAA,CAAA,kBAAe;AACjC;AAEA,+EAA+E;AAC/E,uBAAuB;AACvB,+EAA+E;AAE/E;;CAEC,GACD,MAAM,kBAAkB;IACtB,OAAO,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,IACV,CAAA,GAAA,yHAAA,CAAA,4BAAyB,AAAD,EACtB,CAAC,KAAK,MAAQ,CAAC;YACb,GAAG,gBAAgB;YAEnB,qBAAqB;YACrB,GAAG,CAAA,GAAA,yHAAA,CAAA,yBAAsB,AAAD,EAAa,IAAI;YAEzC,2EAA2E;YAC3E,yBAAyB;YACzB,2EAA2E;YAE3E;;SAEC,GACD,OAAO,OAAO,OAAe;gBAC3B,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,6HAAA,CAAA,cAAW,CAAC,IAAI,EAAE,SAAS;oBAAE;gBAAM;gBAElD,IAAI;oBAAE,WAAW;oBAAM,OAAO;gBAAK;gBAEnC,IAAI;oBACF,MAAM,WAAW,MAAM,MAAM,6HAAA,CAAA,sBAAmB,CAAC,IAAI,CAAC,KAAK,EAAE;wBAC3D,QAAQ;wBACR,SAAS;4BACP,gBAAgB;wBAClB;wBACA,MAAM,KAAK,SAAS,CAAC;4BAAE;4BAAO;wBAAS;oBACzC;oBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;wBAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;wBACvD,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,6HAAA,CAAA,iBAAc,CAAC,IAAI,CAAC,YAAY;oBACvE;oBAEA,MAAM,OAAO,MAAM,SAAS,IAAI;oBAEhC,IAAI,KAAK,OAAO,IAAI,KAAK,IAAI,EAAE;wBAC7B,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,YAAY,EAAE,GAAG,KAAK,IAAI;wBAErD,uBAAuB;wBACvB,MAAM,SAAqB;4BACzB;4BACA;4BACA,WAAW,CAAA,GAAA,yHAAA,CAAA,qBAAkB,AAAD,EAAE;wBAChC;wBAEA,qBAAqB;wBACrB,IAAI;4BACF;4BACA;4BACA,iBAAiB;4BACjB,WAAW;4BACX,OAAO;4BACP,cAAc,KAAK,GAAG;wBACxB;wBAEA,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,6HAAA,CAAA,cAAW,CAAC,IAAI,EAAE,iBAAiB;4BAAE,QAAQ,KAAK,EAAE;wBAAC;oBACtE,OAAO;wBACL,MAAM,IAAI,MAAM,KAAK,OAAO,IAAI,6HAAA,CAAA,iBAAc,CAAC,IAAI,CAAC,YAAY;oBAClE;gBACF,EAAE,OAAO,OAAO;oBACd,MAAM,eAAe,CAAA,GAAA,yHAAA,CAAA,sBAAmB,AAAD,EAAE;oBACzC,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,6HAAA,CAAA,cAAW,CAAC,IAAI,EAAE,eAAe;wBAAE,OAAO;oBAAa;oBAEtE,IAAI;wBACF,WAAW;wBACX,OAAO;wBACP,iBAAiB;wBACjB,MAAM;wBACN,QAAQ;oBACV;oBAEA,MAAM;gBACR;YACF;YAEA;;SAEC,GACD,QAAQ;gBACN,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,6HAAA,CAAA,cAAW,CAAC,IAAI,EAAE;gBAEjC,MAAM,EAAE,MAAM,EAAE,GAAG;gBAEnB,IAAI;oBACF,oCAAoC;oBACpC,IAAI,QAAQ,aAAa;wBACvB,MAAM,MAAM,6HAAA,CAAA,sBAAmB,CAAC,IAAI,CAAC,MAAM,EAAE;4BAC3C,QAAQ;4BACR,SAAS;gCACP,gBAAgB;gCAChB,iBAAiB,CAAC,OAAO,EAAE,OAAO,WAAW,EAAE;4BACjD;wBACF;oBACF;gBACF,EAAE,OAAO,OAAO;oBACd,qCAAqC;oBACrC,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,6HAAA,CAAA,cAAW,CAAC,IAAI,EAAE,oBAAoB;wBAAE,OAAO,CAAA,GAAA,yHAAA,CAAA,sBAAmB,AAAD,EAAE;oBAAO;gBAC3F;gBAEA,kDAAkD;gBAClD,CAAA,GAAA,yHAAA,CAAA,oBAAiB,AAAD;gBAChB,IAAI;oBACF,MAAM;oBACN,QAAQ;oBACR,iBAAiB;oBACjB,OAAO;oBACP,cAAc,KAAK,GAAG;gBACxB;gBAEA,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,6HAAA,CAAA,cAAW,CAAC,IAAI,EAAE;YACnC;YAEA;;SAEC,GACD,WAAW;gBACT,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,6HAAA,CAAA,cAAW,CAAC,IAAI,EAAE;gBAEjC,MAAM,EAAE,MAAM,EAAE,GAAG;gBAEnB,IAAI;oBACF,IAAI,QAAQ,aAAa;wBACvB,MAAM,MAAM,6HAAA,CAAA,sBAAmB,CAAC,IAAI,CAAC,UAAU,EAAE;4BAC/C,QAAQ;4BACR,SAAS;gCACP,gBAAgB;gCAChB,iBAAiB,CAAC,OAAO,EAAE,OAAO,WAAW,EAAE;4BACjD;wBACF;oBACF;gBACF,EAAE,OAAO,OAAO;oBACd,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,6HAAA,CAAA,cAAW,CAAC,IAAI,EAAE,wBAAwB;wBAAE,OAAO,CAAA,GAAA,yHAAA,CAAA,sBAAmB,AAAD,EAAE;oBAAO;gBAC/F;gBAEA,oBAAoB;gBACpB,CAAA,GAAA,yHAAA,CAAA,oBAAiB,AAAD;gBAChB,IAAI;oBACF,MAAM;oBACN,QAAQ;oBACR,iBAAiB;oBACjB,OAAO;oBACP,cAAc,KAAK,GAAG;gBACxB;gBAEA,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,6HAAA,CAAA,cAAW,CAAC,IAAI,EAAE;YACnC;YAEA,2EAA2E;YAC3E,uBAAuB;YACvB,2EAA2E;YAE3E;;SAEC,GACD,eAAe,OAAO;gBACpB,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,6HAAA,CAAA,cAAW,CAAC,IAAI,EAAE,kBAAkB;gBAEnD,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG;gBAEzB,IAAI,CAAC,QAAQ,eAAe,CAAC,MAAM;oBACjC,MAAM,IAAI,MAAM,6HAAA,CAAA,iBAAc,CAAC,IAAI,CAAC,YAAY;gBAClD;gBAEA,IAAI;oBAAE,WAAW;oBAAM,OAAO;gBAAK;gBAEnC,IAAI;oBACF,MAAM,WAAW,MAAM,MAAM,6HAAA,CAAA,sBAAmB,CAAC,IAAI,CAAC,OAAO,EAAE;wBAC7D,QAAQ;wBACR,SAAS;4BACP,gBAAgB;4BAChB,iBAAiB,CAAC,OAAO,EAAE,OAAO,WAAW,EAAE;wBACjD;wBACA,MAAM,KAAK,SAAS,CAAC;oBACvB;oBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;wBAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;wBACvD,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,6HAAA,CAAA,iBAAc,CAAC,IAAI,CAAC,qBAAqB;oBAChF;oBAEA,MAAM,eAAe,MAAM,SAAS,IAAI;oBAExC,IAAI,aAAa,OAAO,IAAI,aAAa,IAAI,EAAE;wBAC7C,IAAI;4BACF,MAAM;gCAAE,GAAG,IAAI;gCAAE,GAAG,aAAa,IAAI;4BAAC;4BACtC,WAAW;4BACX,OAAO;4BACP,cAAc,KAAK,GAAG;wBACxB;wBAEA,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,6HAAA,CAAA,cAAW,CAAC,IAAI,EAAE;oBACnC,OAAO;wBACL,MAAM,IAAI,MAAM,aAAa,OAAO,IAAI,6HAAA,CAAA,iBAAc,CAAC,IAAI,CAAC,qBAAqB;oBACnF;gBACF,EAAE,OAAO,OAAO;oBACd,MAAM,eAAe,CAAA,GAAA,yHAAA,CAAA,sBAAmB,AAAD,EAAE;oBACzC,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,6HAAA,CAAA,cAAW,CAAC,IAAI,EAAE,wBAAwB;wBAAE,OAAO;oBAAa;oBAE/E,IAAI;wBACF,WAAW;wBACX,OAAO;oBACT;oBAEA,MAAM;gBACR;YACF;YAEA;;SAEC,GACD,eAAe;gBACb,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,6HAAA,CAAA,cAAW,CAAC,IAAI,EAAE;gBAEjC,MAAM,EAAE,MAAM,EAAE,GAAG;gBAEnB,IAAI,CAAC,QAAQ,cAAc;oBACzB,MAAM,IAAI,MAAM,6HAAA,CAAA,iBAAc,CAAC,IAAI,CAAC,oBAAoB;gBAC1D;gBAEA,IAAI;oBACF,MAAM,WAAW,MAAM,MAAM,6HAAA,CAAA,sBAAmB,CAAC,IAAI,CAAC,OAAO,EAAE;wBAC7D,QAAQ;wBACR,SAAS;4BACP,gBAAgB;wBAClB;wBACA,MAAM,KAAK,SAAS,CAAC;4BAAE,cAAc,OAAO,YAAY;wBAAC;oBAC3D;oBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;wBAChB,MAAM,IAAI,MAAM,6HAAA,CAAA,iBAAc,CAAC,IAAI,CAAC,oBAAoB;oBAC1D;oBAEA,MAAM,OAAO,MAAM,SAAS,IAAI;oBAEhC,IAAI,KAAK,OAAO,IAAI,KAAK,IAAI,EAAE;wBAC7B,MAAM,EAAE,WAAW,EAAE,YAAY,EAAE,GAAG,KAAK,IAAI;wBAE/C,MAAM,YAAwB;4BAC5B;4BACA;4BACA,WAAW,CAAA,GAAA,yHAAA,CAAA,qBAAkB,AAAD,EAAE;wBAChC;wBAEA,IAAI;4BACF,QAAQ;4BACR,cAAc,KAAK,GAAG;wBACxB;wBAEA,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,6HAAA,CAAA,cAAW,CAAC,IAAI,EAAE;oBACnC,OAAO;wBACL,MAAM,IAAI,MAAM,KAAK,OAAO,IAAI,6HAAA,CAAA,iBAAc,CAAC,IAAI,CAAC,oBAAoB;oBAC1E;gBACF,EAAE,OAAO,OAAO;oBACd,MAAM,eAAe,CAAA,GAAA,yHAAA,CAAA,sBAAmB,AAAD,EAAE;oBACzC,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,6HAAA,CAAA,cAAW,CAAC,IAAI,EAAE,wBAAwB;wBAAE,OAAO;oBAAa;oBAE/E,gCAAgC;oBAChC,MAAM,MAAM;oBACZ,MAAM;gBACR;YACF;YAEA,2EAA2E;YAC3E,2BAA2B;YAC3B,2EAA2E;YAE3E;;SAEC,GACD,SAAS,CAAC;gBACR,IAAI;oBAAE;oBAAM,iBAAiB,CAAC,CAAC;gBAAK;gBACpC,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,6HAAA,CAAA,cAAW,CAAC,IAAI,EAAE,YAAY;oBAAE,QAAQ,MAAM;gBAAG;YAClE;YAEA;;SAEC,GACD,WAAW,CAAC;gBACV,IAAI;oBAAE;gBAAO;gBACb,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,6HAAA,CAAA,cAAW,CAAC,IAAI,EAAE,cAAc;oBAAE,WAAW,CAAC,CAAC;gBAAO;YACvE;YAEA;;SAEC,GACD,YAAY,CAAC;gBACX,IAAI;oBAAE,WAAW;gBAAQ;YAC3B;YAEA;;SAEC,GACD,UAAU,CAAC;gBACT,IAAI;oBAAE;gBAAM;gBACZ,IAAI,OAAO;oBACT,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,6HAAA,CAAA,cAAW,CAAC,IAAI,EAAE,aAAa;wBAAE;oBAAM;gBACxD;YACF;YAEA;;SAEC,GACD,YAAY;gBACV,IAAI;oBAAE,OAAO;gBAAK;YACpB;YAEA,2EAA2E;YAC3E,6BAA6B;YAC7B,2EAA2E;YAE3E;;SAEC,GACD,oBAAoB;gBAClB,IAAI;oBAAE,cAAc,KAAK,GAAG;gBAAG;YACjC;YAEA;;SAEC,GACD,cAAc;gBACZ,MAAM,EAAE,YAAY,EAAE,cAAc,EAAE,MAAM,EAAE,GAAG;gBAEjD,wBAAwB;gBACxB,IAAI,CAAC,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,cAAc,iBAAiB;oBACjD,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,6HAAA,CAAA,cAAW,CAAC,IAAI,EAAE;oBACjC,MAAM,MAAM;oBACZ,OAAO;gBACT;gBAEA,yBAAyB;gBACzB,IAAI,UAAU,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,SAAS,GAAG;oBAC9C,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,6HAAA,CAAA,cAAW,CAAC,IAAI,EAAE;oBAEjC,wBAAwB;oBACxB,MAAM,aAAa,GAAG,KAAK,CAAC;oBAC1B,wDAAwD;oBAC1D;oBAEA,OAAO;gBACT;gBAEA,OAAO;YACT;YAEA;;SAEC,GACD,SAAS;gBACP,MAAM,QAAQ;gBAEd,6BAA6B;gBAC7B,IAAI,MAAM,eAAe,IAAI,MAAM,IAAI,IAAI,MAAM,MAAM,EAAE;oBACvD,MAAM,UAAU,MAAM,YAAY;oBAClC,IAAI,CAAC,SAAS;wBACZ,kCAAkC;wBAClC,IAAI;4BACF,MAAM;4BACN,QAAQ;4BACR,iBAAiB;4BACjB,OAAO;wBACT;oBACF;gBACF;gBAEA,IAAI;oBAAE,cAAc;gBAAK;gBACzB,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,6HAAA,CAAA,cAAW,CAAC,IAAI,EAAE;YACnC;QACF,CAAC,GACD;QACE,SAAS;YACP,MAAM,6HAAA,CAAA,eAAY,CAAC,IAAI;YACvB,SAAS,6HAAA,CAAA,iBAAc,CAAC,IAAI;YAC5B,YAAY,CAAC,QAAU,CAAC;oBACtB,MAAM,MAAM,IAAI;oBAChB,QAAQ,MAAM,MAAM;oBACpB,iBAAiB,MAAM,eAAe;oBACtC,cAAc,MAAM,YAAY;oBAChC,gBAAgB,MAAM,cAAc;gBACtC,CAAC;QACH;QACA,UAAU;YACR,MAAM,6HAAA,CAAA,cAAW,CAAC,IAAI;YACtB,SAAS,oDAAyB;QACpC;IACF;AAGN;AAMO,MAAM,eAAe"}}, {"offset": {"line": 782, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 788, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/stores/auth-hooks.ts"], "sourcesContent": ["/**\n * Authentication Hooks\n * Custom hooks for easy authentication state access\n */\n\n'use client';\n\nimport { useCallback, useEffect } from 'react';\nimport { useAuthStore } from './auth-store';\nimport type { SystemUser, SystemUserRole } from './types';\nimport { ACTIVITY_TRACKING_INTERVAL } from './constants';\n\n// ============================================================================\n// Basic Authentication Hooks\n// ============================================================================\n\n/**\n * Hook to get current user\n */\nexport const useUser = () => {\n  return useAuthStore((state) => state.user);\n};\n\n/**\n * Hook to get authentication status\n */\nexport const useIsAuthenticated = () => {\n  return useAuthStore((state) => state.isAuthenticated);\n};\n\n/**\n * Hook to get authentication loading state\n */\nexport const useAuthLoading = () => {\n  return useAuthStore((state) => state.isLoading);\n};\n\n/**\n * Hook to get authentication error\n */\nexport const useAuthError = () => {\n  return useAuthStore((state) => state.error);\n};\n\n/**\n * Hook to get authentication tokens\n */\nexport const useAuthTokens = () => {\n  return useAuthStore((state) => state.tokens);\n};\n\n// ============================================================================\n// Authentication Action Hooks\n// ============================================================================\n\n/**\n * Hook to get login function\n */\nexport const useLogin = () => {\n  return useAuthStore((state) => state.login);\n};\n\n/**\n * Hook to get logout function\n */\nexport const useLogout = () => {\n  return useAuthStore((state) => state.logout);\n};\n\n/**\n * Hook to get logout all function\n */\nexport const useLogoutAll = () => {\n  return useAuthStore((state) => state.logoutAll);\n};\n\n/**\n * Hook to get update profile function\n */\nexport const useUpdateProfile = () => {\n  return useAuthStore((state) => state.updateProfile);\n};\n\n/**\n * Hook to get refresh tokens function\n */\nexport const useRefreshTokens = () => {\n  return useAuthStore((state) => state.refreshTokens);\n};\n\n// ============================================================================\n// Utility Hooks\n// ============================================================================\n\n/**\n * Hook to clear authentication error\n */\nexport const useClearAuthError = () => {\n  return useAuthStore((state) => state.clearError);\n};\n\n/**\n * Hook to check session validity\n */\nexport const useCheckSession = () => {\n  return useAuthStore((state) => state.checkSession);\n};\n\n/**\n * Hook to update last activity\n */\nexport const useUpdateActivity = () => {\n  return useAuthStore((state) => state.updateLastActivity);\n};\n\n// ============================================================================\n// Role-based Hooks\n// ============================================================================\n\n/**\n * Hook to check if user has specific role\n */\nexport const useHasRole = (role: SystemUserRole) => {\n  const user = useUser();\n  return user?.role === role;\n};\n\n/**\n * Hook to check if user is admin\n */\nexport const useIsAdmin = () => {\n  return useHasRole('Admin');\n};\n\n/**\n * Hook to check if user is editor\n */\nexport const useIsEditor = () => {\n  return useHasRole('Editor');\n};\n\n/**\n * Hook to check if user is moderator\n */\nexport const useIsModerator = () => {\n  return useHasRole('Moderator');\n};\n\n/**\n * Hook to check if user has admin or editor role\n */\nexport const useCanEdit = () => {\n  const user = useUser();\n  return user?.role === 'Admin' || user?.role === 'Editor';\n};\n\n/**\n * Hook to check if user can perform admin actions\n */\nexport const useCanAdmin = () => {\n  return useIsAdmin();\n};\n\n// ============================================================================\n// Composite Hooks\n// ============================================================================\n\n/**\n * Hook to get complete authentication state\n */\nexport const useAuth = () => {\n  const user = useUser();\n  const isAuthenticated = useIsAuthenticated();\n  const isLoading = useAuthLoading();\n  const error = useAuthError();\n  const tokens = useAuthTokens();\n\n  const login = useLogin();\n  const logout = useLogout();\n  const logoutAll = useLogoutAll();\n  const updateProfile = useUpdateProfile();\n  const clearError = useClearAuthError();\n\n  return {\n    // State\n    user,\n    isAuthenticated,\n    isLoading,\n    error,\n    tokens,\n\n    // Actions\n    login,\n    logout,\n    logoutAll,\n    updateProfile,\n    clearError,\n\n    // Role checks\n    isAdmin: useIsAdmin(),\n    isEditor: useIsEditor(),\n    isModerator: useIsModerator(),\n    canEdit: useCanEdit(),\n    canAdmin: useCanAdmin(),\n  };\n};\n\n/**\n * Hook for authentication with automatic session management\n */\nexport const useAuthWithSession = () => {\n  const auth = useAuth();\n  const checkSession = useCheckSession();\n  const updateActivity = useUpdateActivity();\n\n  // Auto-check session validity\n  useEffect(() => {\n    if (auth.isAuthenticated) {\n      const interval = setInterval(() => {\n        checkSession();\n      }, ACTIVITY_TRACKING_INTERVAL);\n\n      return () => clearInterval(interval);\n    }\n  }, [auth.isAuthenticated, checkSession]);\n\n  // Update activity on user interaction\n  const handleUserActivity = useCallback(() => {\n    if (auth.isAuthenticated) {\n      updateActivity();\n    }\n  }, [auth.isAuthenticated, updateActivity]);\n\n  // Auto-update activity on mouse/keyboard events\n  useEffect(() => {\n    if (auth.isAuthenticated) {\n      const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];\n\n      events.forEach(event => {\n        document.addEventListener(event, handleUserActivity, true);\n      });\n\n      return () => {\n        events.forEach(event => {\n          document.removeEventListener(event, handleUserActivity, true);\n        });\n      };\n    }\n  }, [auth.isAuthenticated, handleUserActivity]);\n\n  return {\n    ...auth,\n    checkSession,\n    updateActivity,\n  };\n};\n\n// ============================================================================\n// Permission Hooks\n// ============================================================================\n\n/**\n * Hook to check multiple permissions\n */\nexport const usePermissions = (requiredRoles: SystemUserRole[]) => {\n  const user = useUser();\n\n  const hasPermission = useCallback((roles: SystemUserRole[]) => {\n    if (!user) return false;\n    return roles.includes(user.role);\n  }, [user]);\n\n  const hasAnyPermission = hasPermission(requiredRoles);\n\n  return {\n    hasPermission: hasAnyPermission,\n    userRole: user?.role,\n    checkRole: hasPermission,\n  };\n};\n\n/**\n * Hook for route protection\n */\nexport const useRouteProtection = (requiredRoles?: SystemUserRole[]) => {\n  const isAuthenticated = useIsAuthenticated();\n  const user = useUser();\n  const isLoading = useAuthLoading();\n\n  const hasAccess = useCallback(() => {\n    if (!isAuthenticated) return false;\n    if (!requiredRoles || requiredRoles.length === 0) return true;\n    if (!user) return false;\n\n    return requiredRoles.includes(user.role);\n  }, [isAuthenticated, user, requiredRoles]);\n\n  return {\n    isAuthenticated,\n    hasAccess: hasAccess(),\n    isLoading,\n    user,\n    shouldRedirect: !isLoading && !isAuthenticated,\n    shouldShowUnauthorized: !isLoading && isAuthenticated && !hasAccess(),\n  };\n};\n\n// ============================================================================\n// Development Hooks\n// ============================================================================\n\n/**\n * Hook for development/debugging authentication state\n */\nexport const useAuthDebug = () => {\n  const state = useAuthStore((state) => state);\n\n  if (process.env.NODE_ENV !== 'development') {\n    return null;\n  }\n\n  return {\n    fullState: state,\n    hasHydrated: state._hasHydrated,\n    lastActivity: new Date(state.lastActivity).toISOString(),\n    sessionTimeout: state.sessionTimeout,\n    tokenExpiry: state.tokens ? new Date(state.tokens.expiresAt).toISOString() : null,\n  };\n};\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;;;;;;;;;;;;;;;AAID;AACA;AAEA;AAmTM;;AAxTN;;;;AAcO,MAAM,UAAU;;IACrB,OAAO,CAAA,GAAA,iIAAA,CAAA,eAAY,AAAD;gCAAE,CAAC,QAAU,MAAM,IAAI;;AAC3C;GAFa;;QACJ,iIAAA,CAAA,eAAY;;;AAMd,MAAM,qBAAqB;;IAChC,OAAO,CAAA,GAAA,iIAAA,CAAA,eAAY,AAAD;2CAAE,CAAC,QAAU,MAAM,eAAe;;AACtD;IAFa;;QACJ,iIAAA,CAAA,eAAY;;;AAMd,MAAM,iBAAiB;;IAC5B,OAAO,CAAA,GAAA,iIAAA,CAAA,eAAY,AAAD;uCAAE,CAAC,QAAU,MAAM,SAAS;;AAChD;IAFa;;QACJ,iIAAA,CAAA,eAAY;;;AAMd,MAAM,eAAe;;IAC1B,OAAO,CAAA,GAAA,iIAAA,CAAA,eAAY,AAAD;qCAAE,CAAC,QAAU,MAAM,KAAK;;AAC5C;IAFa;;QACJ,iIAAA,CAAA,eAAY;;;AAMd,MAAM,gBAAgB;;IAC3B,OAAO,CAAA,GAAA,iIAAA,CAAA,eAAY,AAAD;sCAAE,CAAC,QAAU,MAAM,MAAM;;AAC7C;IAFa;;QACJ,iIAAA,CAAA,eAAY;;;AAUd,MAAM,WAAW;;IACtB,OAAO,CAAA,GAAA,iIAAA,CAAA,eAAY,AAAD;iCAAE,CAAC,QAAU,MAAM,KAAK;;AAC5C;IAFa;;QACJ,iIAAA,CAAA,eAAY;;;AAMd,MAAM,YAAY;;IACvB,OAAO,CAAA,GAAA,iIAAA,CAAA,eAAY,AAAD;kCAAE,CAAC,QAAU,MAAM,MAAM;;AAC7C;IAFa;;QACJ,iIAAA,CAAA,eAAY;;;AAMd,MAAM,eAAe;;IAC1B,OAAO,CAAA,GAAA,iIAAA,CAAA,eAAY,AAAD;qCAAE,CAAC,QAAU,MAAM,SAAS;;AAChD;IAFa;;QACJ,iIAAA,CAAA,eAAY;;;AAMd,MAAM,mBAAmB;;IAC9B,OAAO,CAAA,GAAA,iIAAA,CAAA,eAAY,AAAD;yCAAE,CAAC,QAAU,MAAM,aAAa;;AACpD;IAFa;;QACJ,iIAAA,CAAA,eAAY;;;AAMd,MAAM,mBAAmB;;IAC9B,OAAO,CAAA,GAAA,iIAAA,CAAA,eAAY,AAAD;yCAAE,CAAC,QAAU,MAAM,aAAa;;AACpD;IAFa;;QACJ,iIAAA,CAAA,eAAY;;;AAUd,MAAM,oBAAoB;;IAC/B,OAAO,CAAA,GAAA,iIAAA,CAAA,eAAY,AAAD;0CAAE,CAAC,QAAU,MAAM,UAAU;;AACjD;KAFa;;QACJ,iIAAA,CAAA,eAAY;;;AAMd,MAAM,kBAAkB;;IAC7B,OAAO,CAAA,GAAA,iIAAA,CAAA,eAAY,AAAD;wCAAE,CAAC,QAAU,MAAM,YAAY;;AACnD;KAFa;;QACJ,iIAAA,CAAA,eAAY;;;AAMd,MAAM,oBAAoB;;IAC/B,OAAO,CAAA,GAAA,iIAAA,CAAA,eAAY,AAAD;0CAAE,CAAC,QAAU,MAAM,kBAAkB;;AACzD;KAFa;;QACJ,iIAAA,CAAA,eAAY;;;AAUd,MAAM,aAAa,CAAC;;IACzB,MAAM,OAAO;IACb,OAAO,MAAM,SAAS;AACxB;KAHa;;QACE;;;AAOR,MAAM,aAAa;;IACxB,OAAO,WAAW;AACpB;KAFa;;QACJ;;;AAMF,MAAM,cAAc;;IACzB,OAAO,WAAW;AACpB;KAFa;;QACJ;;;AAMF,MAAM,iBAAiB;;IAC5B,OAAO,WAAW;AACpB;KAFa;;QACJ;;;AAMF,MAAM,aAAa;;IACxB,MAAM,OAAO;IACb,OAAO,MAAM,SAAS,WAAW,MAAM,SAAS;AAClD;KAHa;;QACE;;;AAOR,MAAM,cAAc;;IACzB,OAAO;AACT;KAFa;;QACJ;;;AAUF,MAAM,UAAU;;IACrB,MAAM,OAAO;IACb,MAAM,kBAAkB;IACxB,MAAM,YAAY;IAClB,MAAM,QAAQ;IACd,MAAM,SAAS;IAEf,MAAM,QAAQ;IACd,MAAM,SAAS;IACf,MAAM,YAAY;IAClB,MAAM,gBAAgB;IACtB,MAAM,aAAa;IAEnB,OAAO;QACL,QAAQ;QACR;QACA;QACA;QACA;QACA;QAEA,UAAU;QACV;QACA;QACA;QACA;QACA;QAEA,cAAc;QACd,SAAS;QACT,UAAU;QACV,aAAa;QACb,SAAS;QACT,UAAU;IACZ;AACF;KAnCa;;QACE;QACW;QACN;QACJ;QACC;QAED;QACC;QACG;QACI;QACH;QAkBR;QACC;QACG;QACJ;QACC;;;AAOP,MAAM,qBAAqB;;IAChC,MAAM,OAAO;IACb,MAAM,eAAe;IACrB,MAAM,iBAAiB;IAEvB,8BAA8B;IAC9B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,IAAI,KAAK,eAAe,EAAE;gBACxB,MAAM,WAAW;6DAAY;wBAC3B;oBACF;4DAAG,6HAAA,CAAA,6BAA0B;gBAE7B;oDAAO,IAAM,cAAc;;YAC7B;QACF;uCAAG;QAAC,KAAK,eAAe;QAAE;KAAa;IAEvC,sCAAsC;IACtC,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8DAAE;YACrC,IAAI,KAAK,eAAe,EAAE;gBACxB;YACF;QACF;6DAAG;QAAC,KAAK,eAAe;QAAE;KAAe;IAEzC,gDAAgD;IAChD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,IAAI,KAAK,eAAe,EAAE;gBACxB,MAAM,SAAS;oBAAC;oBAAa;oBAAa;oBAAY;oBAAU;iBAAa;gBAE7E,OAAO,OAAO;oDAAC,CAAA;wBACb,SAAS,gBAAgB,CAAC,OAAO,oBAAoB;oBACvD;;gBAEA;oDAAO;wBACL,OAAO,OAAO;4DAAC,CAAA;gCACb,SAAS,mBAAmB,CAAC,OAAO,oBAAoB;4BAC1D;;oBACF;;YACF;QACF;uCAAG;QAAC,KAAK,eAAe;QAAE;KAAmB;IAE7C,OAAO;QACL,GAAG,IAAI;QACP;QACA;IACF;AACF;KA7Ca;;QACE;QACQ;QACE;;;AAmDlB,MAAM,iBAAiB,CAAC;;IAC7B,MAAM,OAAO;IAEb,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE,CAAC;YACjC,IAAI,CAAC,MAAM,OAAO;YAClB,OAAO,MAAM,QAAQ,CAAC,KAAK,IAAI;QACjC;oDAAG;QAAC;KAAK;IAET,MAAM,mBAAmB,cAAc;IAEvC,OAAO;QACL,eAAe;QACf,UAAU,MAAM;QAChB,WAAW;IACb;AACF;KAfa;;QACE;;;AAmBR,MAAM,qBAAqB,CAAC;;IACjC,MAAM,kBAAkB;IACxB,MAAM,OAAO;IACb,MAAM,YAAY;IAElB,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE;YAC5B,IAAI,CAAC,iBAAiB,OAAO;YAC7B,IAAI,CAAC,iBAAiB,cAAc,MAAM,KAAK,GAAG,OAAO;YACzD,IAAI,CAAC,MAAM,OAAO;YAElB,OAAO,cAAc,QAAQ,CAAC,KAAK,IAAI;QACzC;oDAAG;QAAC;QAAiB;QAAM;KAAc;IAEzC,OAAO;QACL;QACA,WAAW;QACX;QACA;QACA,gBAAgB,CAAC,aAAa,CAAC;QAC/B,wBAAwB,CAAC,aAAa,mBAAmB,CAAC;IAC5D;AACF;KArBa;;QACa;QACX;QACK;;;AA2Bb,MAAM,eAAe;;IAC1B,MAAM,QAAQ,CAAA,GAAA,iIAAA,CAAA,eAAY,AAAD;4CAAE,CAAC,QAAU;;IAEtC,uCAA4C;;IAE5C;IAEA,OAAO;QACL,WAAW;QACX,aAAa,MAAM,YAAY;QAC/B,cAAc,IAAI,KAAK,MAAM,YAAY,EAAE,WAAW;QACtD,gBAAgB,MAAM,cAAc;QACpC,aAAa,MAAM,MAAM,GAAG,IAAI,KAAK,MAAM,MAAM,CAAC,SAAS,EAAE,WAAW,KAAK;IAC/E;AACF;KAda;;QACG,iIAAA,CAAA,eAAY"}}, {"offset": {"line": 1235, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1241, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/stores/auth-utils.ts"], "sourcesContent": ["/**\n * Authentication Utilities\n * Helper functions for authentication operations\n */\n\nimport type { SystemUser, SystemUserRole, AuthTokens } from './types';\nimport { useAuthStore } from './auth-store';\n\n// ============================================================================\n// Token Utilities\n// ============================================================================\n\n/**\n * Get current access token\n */\nexport const getAccessToken = (): string | null => {\n  const tokens = useAuthStore.getState().tokens;\n  return tokens?.accessToken || null;\n};\n\n/**\n * Get current refresh token\n */\nexport const getRefreshToken = (): string | null => {\n  const tokens = useAuthStore.getState().tokens;\n  return tokens?.refreshToken || null;\n};\n\n/**\n * Get authorization header for API requests\n */\nexport const getAuthHeader = (): Record<string, string> => {\n  const token = getAccessToken();\n  return token ? { Authorization: `Bearer ${token}` } : {};\n};\n\n/**\n * Check if user is currently authenticated\n */\nexport const isAuthenticated = (): boolean => {\n  return useAuthStore.getState().isAuthenticated;\n};\n\n/**\n * Get current user\n */\nexport const getCurrentUser = (): SystemUser | null => {\n  return useAuthStore.getState().user;\n};\n\n/**\n * Get current user role\n */\nexport const getCurrentUserRole = (): SystemUserRole | null => {\n  const user = getCurrentUser();\n  return user?.role || null;\n};\n\n// ============================================================================\n// Permission Utilities\n// ============================================================================\n\n/**\n * Check if current user has specific role\n */\nexport const hasRole = (role: SystemUserRole): boolean => {\n  const currentRole = getCurrentUserRole();\n  return currentRole === role;\n};\n\n/**\n * Check if current user has any of the specified roles\n */\nexport const hasAnyRole = (roles: SystemUserRole[]): boolean => {\n  const currentRole = getCurrentUserRole();\n  return currentRole ? roles.includes(currentRole) : false;\n};\n\n/**\n * Check if current user is admin\n */\nexport const isAdmin = (): boolean => {\n  return hasRole('Admin');\n};\n\n/**\n * Check if current user is editor\n */\nexport const isEditor = (): boolean => {\n  return hasRole('Editor');\n};\n\n/**\n * Check if current user is moderator\n */\nexport const isModerator = (): boolean => {\n  return hasRole('Moderator');\n};\n\n/**\n * Check if current user can edit content\n */\nexport const canEdit = (): boolean => {\n  return hasAnyRole(['Admin', 'Editor']);\n};\n\n/**\n * Check if current user can perform admin actions\n */\nexport const canAdmin = (): boolean => {\n  return isAdmin();\n};\n\n/**\n * Check if current user can moderate content\n */\nexport const canModerate = (): boolean => {\n  return hasAnyRole(['Admin', 'Editor', 'Moderator']);\n};\n\n// ============================================================================\n// Session Utilities\n// ============================================================================\n\n/**\n * Force session check\n */\nexport const checkSession = (): boolean => {\n  return useAuthStore.getState().checkSession();\n};\n\n/**\n * Update user activity timestamp\n */\nexport const updateActivity = (): void => {\n  useAuthStore.getState().updateLastActivity();\n};\n\n/**\n * Get session remaining time in minutes\n */\nexport const getAuthSessionRemainingTime = (): number => {\n  const { lastActivity, sessionTimeout } = useAuthStore.getState();\n  const now = Date.now();\n  const timeoutMs = sessionTimeout * 60 * 1000;\n  const elapsed = now - lastActivity;\n  const remaining = timeoutMs - elapsed;\n  return Math.max(0, Math.floor(remaining / (60 * 1000)));\n};\n\n/**\n * Check if session will expire soon (within 5 minutes)\n */\nexport const isSessionExpiringSoon = (): boolean => {\n  return getAuthSessionRemainingTime() <= 5;\n};\n\n// ============================================================================\n// Authentication Actions\n// ============================================================================\n\n/**\n * Login with credentials\n */\nexport const login = async (email: string, password: string): Promise<void> => {\n  return useAuthStore.getState().login(email, password);\n};\n\n/**\n * Logout current user\n */\nexport const logout = async (): Promise<void> => {\n  return useAuthStore.getState().logout();\n};\n\n/**\n * Logout from all devices\n */\nexport const logoutAll = async (): Promise<void> => {\n  return useAuthStore.getState().logoutAll();\n};\n\n/**\n * Update user profile\n */\nexport const updateProfile = async (data: Partial<SystemUser>): Promise<void> => {\n  return useAuthStore.getState().updateProfile(data);\n};\n\n/**\n * Refresh authentication tokens\n */\nexport const refreshTokens = async (): Promise<void> => {\n  return useAuthStore.getState().refreshTokens();\n};\n\n// ============================================================================\n// Error Handling Utilities\n// ============================================================================\n\n/**\n * Get current authentication error\n */\nexport const getAuthError = (): string | null => {\n  return useAuthStore.getState().error;\n};\n\n/**\n * Clear authentication error\n */\nexport const clearAuthError = (): void => {\n  useAuthStore.getState().clearError();\n};\n\n/**\n * Check if there's an authentication error\n */\nexport const hasAuthError = (): boolean => {\n  return !!getAuthError();\n};\n\n// ============================================================================\n// API Request Utilities\n// ============================================================================\n\n/**\n * Create authenticated fetch request\n */\nexport const authenticatedFetch = async (\n  url: string,\n  options: RequestInit = {}\n): Promise<Response> => {\n  const authHeaders = getAuthHeader();\n\n  const config: RequestInit = {\n    ...options,\n    headers: {\n      'Content-Type': 'application/json',\n      ...authHeaders,\n      ...options.headers,\n    },\n  };\n\n  const response = await fetch(url, config);\n\n  // Handle token expiration\n  if (response.status === 401) {\n    const isAuth = isAuthenticated();\n    if (isAuth) {\n      // Try to refresh tokens\n      try {\n        await refreshTokens();\n        // Retry the request with new token\n        const newAuthHeaders = getAuthHeader();\n        const retryConfig: RequestInit = {\n          ...config,\n          headers: {\n            ...config.headers,\n            ...newAuthHeaders,\n          },\n        };\n        return fetch(url, retryConfig);\n      } catch (error) {\n        // Refresh failed, logout user\n        await logout();\n        throw new Error('Authentication expired. Please log in again.');\n      }\n    }\n  }\n\n  return response;\n};\n\n/**\n * Create authenticated API request with JSON response\n */\nexport const authenticatedApiRequest = async <T = any>(\n  url: string,\n  options: RequestInit = {}\n): Promise<T> => {\n  const response = await authenticatedFetch(url, options);\n\n  if (!response.ok) {\n    const errorData = await response.json().catch(() => ({}));\n    throw new Error(errorData.message || `Request failed with status ${response.status}`);\n  }\n\n  return response.json();\n};\n\n// ============================================================================\n// Route Protection Utilities\n// ============================================================================\n\n/**\n * Check if user can access route with required roles\n */\nexport const canAccessRoute = (requiredRoles?: SystemUserRole[]): boolean => {\n  if (!isAuthenticated()) return false;\n  if (!requiredRoles || requiredRoles.length === 0) return true;\n\n  return hasAnyRole(requiredRoles);\n};\n\n/**\n * Get redirect path for unauthenticated users\n */\nexport const getLoginRedirectPath = (currentPath?: string): string => {\n  const loginPath = '/login';\n  if (currentPath && currentPath !== '/') {\n    return `${loginPath}?redirect=${encodeURIComponent(currentPath)}`;\n  }\n  return loginPath;\n};\n\n/**\n * Get redirect path after successful login\n */\nexport const getPostLoginRedirectPath = (searchParams?: URLSearchParams): string => {\n  const redirectParam = searchParams?.get('redirect');\n  return redirectParam || '/dashboard';\n};\n\n// ============================================================================\n// Development Utilities\n// ============================================================================\n\n/**\n * Get full authentication state (development only)\n */\nexport const getAuthState = () => {\n  if (process.env.NODE_ENV !== 'development') {\n    return null;\n  }\n\n  return useAuthStore.getState();\n};\n\n/**\n * Mock login for development/testing\n */\nexport const mockLogin = (user: SystemUser, tokens: AuthTokens): void => {\n  if (process.env.NODE_ENV !== 'development') {\n    return;\n  }\n\n  useAuthStore.getState().setUser(user);\n  useAuthStore.getState().setTokens(tokens);\n};\n\n/**\n * Reset authentication state (development/testing only)\n */\nexport const resetAuthState = (): void => {\n  if (process.env.NODE_ENV !== 'development') {\n    return;\n  }\n\n  useAuthStore.getState().logout();\n};\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGD;AAqUM;;AA5TC,MAAM,iBAAiB;IAC5B,MAAM,SAAS,iIAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,MAAM;IAC7C,OAAO,QAAQ,eAAe;AAChC;AAKO,MAAM,kBAAkB;IAC7B,MAAM,SAAS,iIAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,MAAM;IAC7C,OAAO,QAAQ,gBAAgB;AACjC;AAKO,MAAM,gBAAgB;IAC3B,MAAM,QAAQ;IACd,OAAO,QAAQ;QAAE,eAAe,CAAC,OAAO,EAAE,OAAO;IAAC,IAAI,CAAC;AACzD;AAKO,MAAM,kBAAkB;IAC7B,OAAO,iIAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,eAAe;AAChD;AAKO,MAAM,iBAAiB;IAC5B,OAAO,iIAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,IAAI;AACrC;AAKO,MAAM,qBAAqB;IAChC,MAAM,OAAO;IACb,OAAO,MAAM,QAAQ;AACvB;AASO,MAAM,UAAU,CAAC;IACtB,MAAM,cAAc;IACpB,OAAO,gBAAgB;AACzB;AAKO,MAAM,aAAa,CAAC;IACzB,MAAM,cAAc;IACpB,OAAO,cAAc,MAAM,QAAQ,CAAC,eAAe;AACrD;AAKO,MAAM,UAAU;IACrB,OAAO,QAAQ;AACjB;AAKO,MAAM,WAAW;IACtB,OAAO,QAAQ;AACjB;AAKO,MAAM,cAAc;IACzB,OAAO,QAAQ;AACjB;AAKO,MAAM,UAAU;IACrB,OAAO,WAAW;QAAC;QAAS;KAAS;AACvC;AAKO,MAAM,WAAW;IACtB,OAAO;AACT;AAKO,MAAM,cAAc;IACzB,OAAO,WAAW;QAAC;QAAS;QAAU;KAAY;AACpD;AASO,MAAM,eAAe;IAC1B,OAAO,iIAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,YAAY;AAC7C;AAKO,MAAM,iBAAiB;IAC5B,iIAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,kBAAkB;AAC5C;AAKO,MAAM,8BAA8B;IACzC,MAAM,EAAE,YAAY,EAAE,cAAc,EAAE,GAAG,iIAAA,CAAA,eAAY,CAAC,QAAQ;IAC9D,MAAM,MAAM,KAAK,GAAG;IACpB,MAAM,YAAY,iBAAiB,KAAK;IACxC,MAAM,UAAU,MAAM;IACtB,MAAM,YAAY,YAAY;IAC9B,OAAO,KAAK,GAAG,CAAC,GAAG,KAAK,KAAK,CAAC,YAAY,CAAC,KAAK,IAAI;AACtD;AAKO,MAAM,wBAAwB;IACnC,OAAO,iCAAiC;AAC1C;AASO,MAAM,QAAQ,OAAO,OAAe;IACzC,OAAO,iIAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,KAAK,CAAC,OAAO;AAC9C;AAKO,MAAM,SAAS;IACpB,OAAO,iIAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,MAAM;AACvC;AAKO,MAAM,YAAY;IACvB,OAAO,iIAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,SAAS;AAC1C;AAKO,MAAM,gBAAgB,OAAO;IAClC,OAAO,iIAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,aAAa,CAAC;AAC/C;AAKO,MAAM,gBAAgB;IAC3B,OAAO,iIAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,aAAa;AAC9C;AASO,MAAM,eAAe;IAC1B,OAAO,iIAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,KAAK;AACtC;AAKO,MAAM,iBAAiB;IAC5B,iIAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,UAAU;AACpC;AAKO,MAAM,eAAe;IAC1B,OAAO,CAAC,CAAC;AACX;AASO,MAAM,qBAAqB,OAChC,KACA,UAAuB,CAAC,CAAC;IAEzB,MAAM,cAAc;IAEpB,MAAM,SAAsB;QAC1B,GAAG,OAAO;QACV,SAAS;YACP,gBAAgB;YAChB,GAAG,WAAW;YACd,GAAG,QAAQ,OAAO;QACpB;IACF;IAEA,MAAM,WAAW,MAAM,MAAM,KAAK;IAElC,0BAA0B;IAC1B,IAAI,SAAS,MAAM,KAAK,KAAK;QAC3B,MAAM,SAAS;QACf,IAAI,QAAQ;YACV,wBAAwB;YACxB,IAAI;gBACF,MAAM;gBACN,mCAAmC;gBACnC,MAAM,iBAAiB;gBACvB,MAAM,cAA2B;oBAC/B,GAAG,MAAM;oBACT,SAAS;wBACP,GAAG,OAAO,OAAO;wBACjB,GAAG,cAAc;oBACnB;gBACF;gBACA,OAAO,MAAM,KAAK;YACpB,EAAE,OAAO,OAAO;gBACd,8BAA8B;gBAC9B,MAAM;gBACN,MAAM,IAAI,MAAM;YAClB;QACF;IACF;IAEA,OAAO;AACT;AAKO,MAAM,0BAA0B,OACrC,KACA,UAAuB,CAAC,CAAC;IAEzB,MAAM,WAAW,MAAM,mBAAmB,KAAK;IAE/C,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;QACvD,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,CAAC,2BAA2B,EAAE,SAAS,MAAM,EAAE;IACtF;IAEA,OAAO,SAAS,IAAI;AACtB;AASO,MAAM,iBAAiB,CAAC;IAC7B,IAAI,CAAC,mBAAmB,OAAO;IAC/B,IAAI,CAAC,iBAAiB,cAAc,MAAM,KAAK,GAAG,OAAO;IAEzD,OAAO,WAAW;AACpB;AAKO,MAAM,uBAAuB,CAAC;IACnC,MAAM,YAAY;IAClB,IAAI,eAAe,gBAAgB,KAAK;QACtC,OAAO,GAAG,UAAU,UAAU,EAAE,mBAAmB,cAAc;IACnE;IACA,OAAO;AACT;AAKO,MAAM,2BAA2B,CAAC;IACvC,MAAM,gBAAgB,cAAc,IAAI;IACxC,OAAO,iBAAiB;AAC1B;AASO,MAAM,eAAe;IAC1B,uCAA4C;;IAE5C;IAEA,OAAO,iIAAA,CAAA,eAAY,CAAC,QAAQ;AAC9B;AAKO,MAAM,YAAY,CAAC,MAAkB;IAC1C,uCAA4C;;IAE5C;IAEA,iIAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,OAAO,CAAC;IAChC,iIAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,SAAS,CAAC;AACpC;AAKO,MAAM,iBAAiB;IAC5B,uCAA4C;;IAE5C;IAEA,iIAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,MAAM;AAChC"}}, {"offset": {"line": 1464, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1470, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/stores/app-store.ts"], "sourcesContent": ["/**\n * Application Store\n * Manages application state, theme, settings, navigation, and UI state\n */\n\nimport { create } from 'zustand';\nimport { \n  AppStore, \n  ThemeConfig,\n  AppSettings,\n  NavigationState,\n  UIState\n} from './types';\nimport { \n  createStoreWithMiddleware,\n  createBaseStoreActions,\n  generateNotificationId,\n  getDefaultNotificationDuration,\n  logStoreAction\n} from './utils';\nimport { \n  STORE_NAMES,\n  STORAGE_KEYS,\n  DEFAULT_THEME,\n  DEFAULT_APP_SETTINGS,\n  DEFAULT_NAVIGATION,\n  DEFAULT_UI_STATE,\n  SUCCESS_MESSAGES,\n  ERROR_MESSAGES,\n  STORE_VERSIONS\n} from './constants';\n\n// ============================================================================\n// Initial State\n// ============================================================================\n\nconst initialAppState = {\n  // Base store state\n  _hasHydrated: false,\n  \n  // Configuration\n  theme: DEFAULT_THEME,\n  settings: DEFAULT_APP_SETTINGS,\n  \n  // Navigation\n  navigation: DEFAULT_NAVIGATION,\n  \n  // UI state\n  ui: DEFAULT_UI_STATE,\n  \n  // System info\n  version: process.env.NEXT_PUBLIC_APP_VERSION || '1.0.0',\n  buildTime: process.env.NEXT_PUBLIC_BUILD_TIME || new Date().toISOString(),\n  environment: (process.env.NODE_ENV as 'development' | 'staging' | 'production') || 'development',\n};\n\n// ============================================================================\n// Store Implementation\n// ============================================================================\n\n/**\n * Application Store Creator\n */\nconst createAppStore = () => {\n  return create<AppStore>()(\n    createStoreWithMiddleware<AppStore>(\n      (set, get) => ({\n        ...initialAppState,\n        \n        // Base store actions\n        ...createBaseStoreActions<AppStore>(set),\n        \n        // ========================================================================\n        // Theme Management Actions\n        // ========================================================================\n        \n        /**\n         * Set theme configuration\n         */\n        setTheme: (themeUpdate: Partial<ThemeConfig>) => {\n          const currentTheme = get().theme;\n          const newTheme = { ...currentTheme, ...themeUpdate };\n          \n          set({ theme: newTheme });\n          logStoreAction(STORE_NAMES.APP, 'set_theme', themeUpdate);\n        },\n        \n        /**\n         * Toggle between light and dark mode\n         */\n        toggleTheme: () => {\n          const currentMode = get().theme.mode;\n          const newMode = currentMode === 'light' ? 'dark' : 'light';\n          \n          get().setTheme({ mode: newMode });\n          logStoreAction(STORE_NAMES.APP, 'toggle_theme', { newMode });\n        },\n        \n        // ========================================================================\n        // Settings Management Actions\n        // ========================================================================\n        \n        /**\n         * Update application settings\n         */\n        updateSettings: (settingsUpdate: Partial<AppSettings>) => {\n          const currentSettings = get().settings;\n          const newSettings = { ...currentSettings, ...settingsUpdate };\n          \n          set({ settings: newSettings });\n          logStoreAction(STORE_NAMES.APP, 'update_settings', settingsUpdate);\n        },\n        \n        /**\n         * Reset settings to default values\n         */\n        resetSettings: () => {\n          set({ settings: DEFAULT_APP_SETTINGS });\n          logStoreAction(STORE_NAMES.APP, 'reset_settings');\n        },\n        \n        // ========================================================================\n        // Navigation Actions\n        // ========================================================================\n        \n        /**\n         * Set current path\n         */\n        setCurrentPath: (path: string) => {\n          const currentNavigation = get().navigation;\n          const newNavigation = { ...currentNavigation, currentPath: path };\n          \n          set({ navigation: newNavigation });\n          logStoreAction(STORE_NAMES.APP, 'set_current_path', { path });\n        },\n        \n        /**\n         * Set breadcrumbs\n         */\n        setBreadcrumbs: (breadcrumbs: NavigationState['breadcrumbs']) => {\n          const currentNavigation = get().navigation;\n          const newNavigation = { ...currentNavigation, breadcrumbs };\n          \n          set({ navigation: newNavigation });\n          logStoreAction(STORE_NAMES.APP, 'set_breadcrumbs', { count: breadcrumbs.length });\n        },\n        \n        /**\n         * Toggle sidebar collapsed state\n         */\n        toggleSidebar: () => {\n          const currentNavigation = get().navigation;\n          const newCollapsed = !currentNavigation.sidebarCollapsed;\n          const newNavigation = { ...currentNavigation, sidebarCollapsed: newCollapsed };\n          \n          set({ navigation: newNavigation });\n          logStoreAction(STORE_NAMES.APP, 'toggle_sidebar', { collapsed: newCollapsed });\n        },\n        \n        /**\n         * Set active menu key\n         */\n        setActiveMenu: (key: string) => {\n          const currentNavigation = get().navigation;\n          const newNavigation = { ...currentNavigation, activeMenuKey: key };\n          \n          set({ navigation: newNavigation });\n          logStoreAction(STORE_NAMES.APP, 'set_active_menu', { key });\n        },\n        \n        // ========================================================================\n        // UI State Management Actions\n        // ========================================================================\n        \n        /**\n         * Set global loading state\n         */\n        setGlobalLoading: (loading: boolean, message?: string) => {\n          const currentUI = get().ui;\n          const newUI = { \n            ...currentUI, \n            globalLoading: loading,\n            loadingMessage: message || ''\n          };\n          \n          set({ ui: newUI });\n          logStoreAction(STORE_NAMES.APP, 'set_global_loading', { loading, message });\n        },\n        \n        /**\n         * Set global error\n         */\n        setGlobalError: (error: string | null, details?: any) => {\n          const currentUI = get().ui;\n          const newUI = { \n            ...currentUI, \n            globalError: error,\n            errorDetails: details || null\n          };\n          \n          set({ ui: newUI });\n          logStoreAction(STORE_NAMES.APP, 'set_global_error', { error, hasDetails: !!details });\n        },\n        \n        /**\n         * Clear global error\n         */\n        clearGlobalError: () => {\n          const currentUI = get().ui;\n          const newUI = { \n            ...currentUI, \n            globalError: null,\n            errorDetails: null\n          };\n          \n          set({ ui: newUI });\n          logStoreAction(STORE_NAMES.APP, 'clear_global_error');\n        },\n        \n        // ========================================================================\n        // Notifications Actions\n        // ========================================================================\n        \n        /**\n         * Add notification\n         */\n        addNotification: (notification: Omit<UIState['notifications'][0], 'id' | 'timestamp'>) => {\n          const id = generateNotificationId();\n          const timestamp = Date.now();\n          const duration = notification.duration || getDefaultNotificationDuration(notification.type);\n          \n          const newNotification = {\n            ...notification,\n            id,\n            timestamp,\n            duration,\n          };\n          \n          const currentUI = get().ui;\n          const newNotifications = [...currentUI.notifications, newNotification];\n          const newUI = { ...currentUI, notifications: newNotifications };\n          \n          set({ ui: newUI });\n          logStoreAction(STORE_NAMES.APP, 'add_notification', { type: notification.type, id });\n          \n          // Auto-remove notification after duration\n          if (duration > 0) {\n            setTimeout(() => {\n              get().removeNotification(id);\n            }, duration);\n          }\n        },\n        \n        /**\n         * Remove notification\n         */\n        removeNotification: (id: string) => {\n          const currentUI = get().ui;\n          const newNotifications = currentUI.notifications.filter(n => n.id !== id);\n          const newUI = { ...currentUI, notifications: newNotifications };\n          \n          set({ ui: newUI });\n          logStoreAction(STORE_NAMES.APP, 'remove_notification', { id });\n        },\n        \n        /**\n         * Clear all notifications\n         */\n        clearNotifications: () => {\n          const currentUI = get().ui;\n          const newUI = { ...currentUI, notifications: [] };\n          \n          set({ ui: newUI });\n          logStoreAction(STORE_NAMES.APP, 'clear_notifications');\n        },\n        \n        // ========================================================================\n        // Modals Actions\n        // ========================================================================\n        \n        /**\n         * Show modal\n         */\n        showModal: (key: string, data?: any) => {\n          const currentUI = get().ui;\n          const newModals = { \n            ...currentUI.modals, \n            [key]: { visible: true, data } \n          };\n          const newUI = { ...currentUI, modals: newModals };\n          \n          set({ ui: newUI });\n          logStoreAction(STORE_NAMES.APP, 'show_modal', { key, hasData: !!data });\n        },\n        \n        /**\n         * Hide modal\n         */\n        hideModal: (key: string) => {\n          const currentUI = get().ui;\n          const newModals = { \n            ...currentUI.modals, \n            [key]: { visible: false, data: undefined } \n          };\n          const newUI = { ...currentUI, modals: newModals };\n          \n          set({ ui: newUI });\n          logStoreAction(STORE_NAMES.APP, 'hide_modal', { key });\n        },\n        \n        /**\n         * Hide all modals\n         */\n        hideAllModals: () => {\n          const currentUI = get().ui;\n          const newModals: Record<string, { visible: boolean; data?: any }> = {};\n          \n          // Set all modals to hidden\n          Object.keys(currentUI.modals).forEach(key => {\n            newModals[key] = { visible: false, data: undefined };\n          });\n          \n          const newUI = { ...currentUI, modals: newModals };\n          \n          set({ ui: newUI });\n          logStoreAction(STORE_NAMES.APP, 'hide_all_modals');\n        },\n      }),\n      {\n        persist: {\n          name: STORAGE_KEYS.APP,\n          version: STORE_VERSIONS.APP,\n          partialize: (state) => ({\n            theme: state.theme,\n            settings: state.settings,\n            navigation: {\n              sidebarCollapsed: state.navigation.sidebarCollapsed,\n              activeMenuKey: state.navigation.activeMenuKey,\n            },\n          }),\n        },\n        devtools: {\n          name: STORE_NAMES.APP,\n          enabled: process.env.NODE_ENV === 'development',\n        },\n      }\n    )\n  );\n};\n\n// ============================================================================\n// Export Store\n// ============================================================================\n\nexport const useAppStore = createAppStore();\n\n// Export store for testing and advanced usage\nexport { createAppStore };\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAUD;AAOA;AA+BW;AA9CX;;;;AA2BA,+EAA+E;AAC/E,gBAAgB;AAChB,+EAA+E;AAE/E,MAAM,kBAAkB;IACtB,mBAAmB;IACnB,cAAc;IAEd,gBAAgB;IAChB,OAAO,6HAAA,CAAA,gBAAa;IACpB,UAAU,6HAAA,CAAA,uBAAoB;IAE9B,aAAa;IACb,YAAY,6HAAA,CAAA,qBAAkB;IAE9B,WAAW;IACX,IAAI,6HAAA,CAAA,mBAAgB;IAEpB,cAAc;IACd,SAAS,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,uBAAuB,IAAI;IAChD,WAAW,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,sBAAsB,IAAI,IAAI,OAAO,WAAW;IACvE,aAAa,mDAAsE;AACrF;AAEA,+EAA+E;AAC/E,uBAAuB;AACvB,+EAA+E;AAE/E;;CAEC,GACD,MAAM,iBAAiB;IACrB,OAAO,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,IACV,CAAA,GAAA,yHAAA,CAAA,4BAAyB,AAAD,EACtB,CAAC,KAAK,MAAQ,CAAC;YACb,GAAG,eAAe;YAElB,qBAAqB;YACrB,GAAG,CAAA,GAAA,yHAAA,CAAA,yBAAsB,AAAD,EAAY,IAAI;YAExC,2EAA2E;YAC3E,2BAA2B;YAC3B,2EAA2E;YAE3E;;SAEC,GACD,UAAU,CAAC;gBACT,MAAM,eAAe,MAAM,KAAK;gBAChC,MAAM,WAAW;oBAAE,GAAG,YAAY;oBAAE,GAAG,WAAW;gBAAC;gBAEnD,IAAI;oBAAE,OAAO;gBAAS;gBACtB,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,6HAAA,CAAA,cAAW,CAAC,GAAG,EAAE,aAAa;YAC/C;YAEA;;SAEC,GACD,aAAa;gBACX,MAAM,cAAc,MAAM,KAAK,CAAC,IAAI;gBACpC,MAAM,UAAU,gBAAgB,UAAU,SAAS;gBAEnD,MAAM,QAAQ,CAAC;oBAAE,MAAM;gBAAQ;gBAC/B,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,6HAAA,CAAA,cAAW,CAAC,GAAG,EAAE,gBAAgB;oBAAE;gBAAQ;YAC5D;YAEA,2EAA2E;YAC3E,8BAA8B;YAC9B,2EAA2E;YAE3E;;SAEC,GACD,gBAAgB,CAAC;gBACf,MAAM,kBAAkB,MAAM,QAAQ;gBACtC,MAAM,cAAc;oBAAE,GAAG,eAAe;oBAAE,GAAG,cAAc;gBAAC;gBAE5D,IAAI;oBAAE,UAAU;gBAAY;gBAC5B,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,6HAAA,CAAA,cAAW,CAAC,GAAG,EAAE,mBAAmB;YACrD;YAEA;;SAEC,GACD,eAAe;gBACb,IAAI;oBAAE,UAAU,6HAAA,CAAA,uBAAoB;gBAAC;gBACrC,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,6HAAA,CAAA,cAAW,CAAC,GAAG,EAAE;YAClC;YAEA,2EAA2E;YAC3E,qBAAqB;YACrB,2EAA2E;YAE3E;;SAEC,GACD,gBAAgB,CAAC;gBACf,MAAM,oBAAoB,MAAM,UAAU;gBAC1C,MAAM,gBAAgB;oBAAE,GAAG,iBAAiB;oBAAE,aAAa;gBAAK;gBAEhE,IAAI;oBAAE,YAAY;gBAAc;gBAChC,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,6HAAA,CAAA,cAAW,CAAC,GAAG,EAAE,oBAAoB;oBAAE;gBAAK;YAC7D;YAEA;;SAEC,GACD,gBAAgB,CAAC;gBACf,MAAM,oBAAoB,MAAM,UAAU;gBAC1C,MAAM,gBAAgB;oBAAE,GAAG,iBAAiB;oBAAE;gBAAY;gBAE1D,IAAI;oBAAE,YAAY;gBAAc;gBAChC,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,6HAAA,CAAA,cAAW,CAAC,GAAG,EAAE,mBAAmB;oBAAE,OAAO,YAAY,MAAM;gBAAC;YACjF;YAEA;;SAEC,GACD,eAAe;gBACb,MAAM,oBAAoB,MAAM,UAAU;gBAC1C,MAAM,eAAe,CAAC,kBAAkB,gBAAgB;gBACxD,MAAM,gBAAgB;oBAAE,GAAG,iBAAiB;oBAAE,kBAAkB;gBAAa;gBAE7E,IAAI;oBAAE,YAAY;gBAAc;gBAChC,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,6HAAA,CAAA,cAAW,CAAC,GAAG,EAAE,kBAAkB;oBAAE,WAAW;gBAAa;YAC9E;YAEA;;SAEC,GACD,eAAe,CAAC;gBACd,MAAM,oBAAoB,MAAM,UAAU;gBAC1C,MAAM,gBAAgB;oBAAE,GAAG,iBAAiB;oBAAE,eAAe;gBAAI;gBAEjE,IAAI;oBAAE,YAAY;gBAAc;gBAChC,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,6HAAA,CAAA,cAAW,CAAC,GAAG,EAAE,mBAAmB;oBAAE;gBAAI;YAC3D;YAEA,2EAA2E;YAC3E,8BAA8B;YAC9B,2EAA2E;YAE3E;;SAEC,GACD,kBAAkB,CAAC,SAAkB;gBACnC,MAAM,YAAY,MAAM,EAAE;gBAC1B,MAAM,QAAQ;oBACZ,GAAG,SAAS;oBACZ,eAAe;oBACf,gBAAgB,WAAW;gBAC7B;gBAEA,IAAI;oBAAE,IAAI;gBAAM;gBAChB,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,6HAAA,CAAA,cAAW,CAAC,GAAG,EAAE,sBAAsB;oBAAE;oBAAS;gBAAQ;YAC3E;YAEA;;SAEC,GACD,gBAAgB,CAAC,OAAsB;gBACrC,MAAM,YAAY,MAAM,EAAE;gBAC1B,MAAM,QAAQ;oBACZ,GAAG,SAAS;oBACZ,aAAa;oBACb,cAAc,WAAW;gBAC3B;gBAEA,IAAI;oBAAE,IAAI;gBAAM;gBAChB,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,6HAAA,CAAA,cAAW,CAAC,GAAG,EAAE,oBAAoB;oBAAE;oBAAO,YAAY,CAAC,CAAC;gBAAQ;YACrF;YAEA;;SAEC,GACD,kBAAkB;gBAChB,MAAM,YAAY,MAAM,EAAE;gBAC1B,MAAM,QAAQ;oBACZ,GAAG,SAAS;oBACZ,aAAa;oBACb,cAAc;gBAChB;gBAEA,IAAI;oBAAE,IAAI;gBAAM;gBAChB,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,6HAAA,CAAA,cAAW,CAAC,GAAG,EAAE;YAClC;YAEA,2EAA2E;YAC3E,wBAAwB;YACxB,2EAA2E;YAE3E;;SAEC,GACD,iBAAiB,CAAC;gBAChB,MAAM,KAAK,CAAA,GAAA,yHAAA,CAAA,yBAAsB,AAAD;gBAChC,MAAM,YAAY,KAAK,GAAG;gBAC1B,MAAM,WAAW,aAAa,QAAQ,IAAI,CAAA,GAAA,yHAAA,CAAA,iCAA8B,AAAD,EAAE,aAAa,IAAI;gBAE1F,MAAM,kBAAkB;oBACtB,GAAG,YAAY;oBACf;oBACA;oBACA;gBACF;gBAEA,MAAM,YAAY,MAAM,EAAE;gBAC1B,MAAM,mBAAmB;uBAAI,UAAU,aAAa;oBAAE;iBAAgB;gBACtE,MAAM,QAAQ;oBAAE,GAAG,SAAS;oBAAE,eAAe;gBAAiB;gBAE9D,IAAI;oBAAE,IAAI;gBAAM;gBAChB,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,6HAAA,CAAA,cAAW,CAAC,GAAG,EAAE,oBAAoB;oBAAE,MAAM,aAAa,IAAI;oBAAE;gBAAG;gBAElF,0CAA0C;gBAC1C,IAAI,WAAW,GAAG;oBAChB,WAAW;wBACT,MAAM,kBAAkB,CAAC;oBAC3B,GAAG;gBACL;YACF;YAEA;;SAEC,GACD,oBAAoB,CAAC;gBACnB,MAAM,YAAY,MAAM,EAAE;gBAC1B,MAAM,mBAAmB,UAAU,aAAa,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBACtE,MAAM,QAAQ;oBAAE,GAAG,SAAS;oBAAE,eAAe;gBAAiB;gBAE9D,IAAI;oBAAE,IAAI;gBAAM;gBAChB,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,6HAAA,CAAA,cAAW,CAAC,GAAG,EAAE,uBAAuB;oBAAE;gBAAG;YAC9D;YAEA;;SAEC,GACD,oBAAoB;gBAClB,MAAM,YAAY,MAAM,EAAE;gBAC1B,MAAM,QAAQ;oBAAE,GAAG,SAAS;oBAAE,eAAe,EAAE;gBAAC;gBAEhD,IAAI;oBAAE,IAAI;gBAAM;gBAChB,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,6HAAA,CAAA,cAAW,CAAC,GAAG,EAAE;YAClC;YAEA,2EAA2E;YAC3E,iBAAiB;YACjB,2EAA2E;YAE3E;;SAEC,GACD,WAAW,CAAC,KAAa;gBACvB,MAAM,YAAY,MAAM,EAAE;gBAC1B,MAAM,YAAY;oBAChB,GAAG,UAAU,MAAM;oBACnB,CAAC,IAAI,EAAE;wBAAE,SAAS;wBAAM;oBAAK;gBAC/B;gBACA,MAAM,QAAQ;oBAAE,GAAG,SAAS;oBAAE,QAAQ;gBAAU;gBAEhD,IAAI;oBAAE,IAAI;gBAAM;gBAChB,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,6HAAA,CAAA,cAAW,CAAC,GAAG,EAAE,cAAc;oBAAE;oBAAK,SAAS,CAAC,CAAC;gBAAK;YACvE;YAEA;;SAEC,GACD,WAAW,CAAC;gBACV,MAAM,YAAY,MAAM,EAAE;gBAC1B,MAAM,YAAY;oBAChB,GAAG,UAAU,MAAM;oBACnB,CAAC,IAAI,EAAE;wBAAE,SAAS;wBAAO,MAAM;oBAAU;gBAC3C;gBACA,MAAM,QAAQ;oBAAE,GAAG,SAAS;oBAAE,QAAQ;gBAAU;gBAEhD,IAAI;oBAAE,IAAI;gBAAM;gBAChB,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,6HAAA,CAAA,cAAW,CAAC,GAAG,EAAE,cAAc;oBAAE;gBAAI;YACtD;YAEA;;SAEC,GACD,eAAe;gBACb,MAAM,YAAY,MAAM,EAAE;gBAC1B,MAAM,YAA8D,CAAC;gBAErE,2BAA2B;gBAC3B,OAAO,IAAI,CAAC,UAAU,MAAM,EAAE,OAAO,CAAC,CAAA;oBACpC,SAAS,CAAC,IAAI,GAAG;wBAAE,SAAS;wBAAO,MAAM;oBAAU;gBACrD;gBAEA,MAAM,QAAQ;oBAAE,GAAG,SAAS;oBAAE,QAAQ;gBAAU;gBAEhD,IAAI;oBAAE,IAAI;gBAAM;gBAChB,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,6HAAA,CAAA,cAAW,CAAC,GAAG,EAAE;YAClC;QACF,CAAC,GACD;QACE,SAAS;YACP,MAAM,6HAAA,CAAA,eAAY,CAAC,GAAG;YACtB,SAAS,6HAAA,CAAA,iBAAc,CAAC,GAAG;YAC3B,YAAY,CAAC,QAAU,CAAC;oBACtB,OAAO,MAAM,KAAK;oBAClB,UAAU,MAAM,QAAQ;oBACxB,YAAY;wBACV,kBAAkB,MAAM,UAAU,CAAC,gBAAgB;wBACnD,eAAe,MAAM,UAAU,CAAC,aAAa;oBAC/C;gBACF,CAAC;QACH;QACA,UAAU;YACR,MAAM,6HAAA,CAAA,cAAW,CAAC,GAAG;YACrB,SAAS,oDAAyB;QACpC;IACF;AAGN;AAMO,MAAM,cAAc"}}, {"offset": {"line": 1839, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1845, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/stores/app-hooks.ts"], "sourcesContent": ["/**\n * Application Hooks\n * Custom hooks for easy application state access\n */\n\n'use client';\n\nimport { useCallback, useEffect } from 'react';\nimport { useAppStore } from './app-store';\nimport type { ThemeConfig, AppSettings, NavigationState, UIState } from './types';\n\n// ============================================================================\n// Theme Hooks\n// ============================================================================\n\n/**\n * Hook to get current theme configuration\n */\nexport const useTheme = () => {\n  return useAppStore((state) => state.theme);\n};\n\n/**\n * Hook to get current theme mode\n */\nexport const useThemeMode = () => {\n  return useAppStore((state) => state.theme.mode);\n};\n\n/**\n * Hook to check if dark mode is active\n */\nexport const useIsDarkMode = () => {\n  return useAppStore((state) => state.theme.mode === 'dark');\n};\n\n/**\n * Hook to get theme actions\n */\nexport const useThemeActions = () => {\n  const setTheme = useAppStore((state) => state.setTheme);\n  const toggleTheme = useAppStore((state) => state.toggleTheme);\n\n  return {\n    setTheme,\n    toggleTheme,\n  };\n};\n\n// ============================================================================\n// Settings Hooks\n// ============================================================================\n\n/**\n * Hook to get application settings\n */\nexport const useAppSettings = () => {\n  return useAppStore((state) => state.settings);\n};\n\n/**\n * Hook to get settings actions\n */\nexport const useSettingsActions = () => {\n  const updateSettings = useAppStore((state) => state.updateSettings);\n  const resetSettings = useAppStore((state) => state.resetSettings);\n\n  return {\n    updateSettings,\n    resetSettings,\n  };\n};\n\n/**\n * Hook to get specific setting value\n */\nexport const useSetting = <K extends keyof AppSettings>(key: K) => {\n  return useAppStore((state) => state.settings[key]);\n};\n\n// ============================================================================\n// Navigation Hooks\n// ============================================================================\n\n/**\n * Hook to get navigation state\n */\nexport const useNavigation = () => {\n  return useAppStore((state) => state.navigation);\n};\n\n/**\n * Hook to get current path\n */\nexport const useCurrentPath = () => {\n  return useAppStore((state) => state.navigation.currentPath);\n};\n\n/**\n * Hook to get breadcrumbs\n */\nexport const useBreadcrumbs = () => {\n  return useAppStore((state) => state.navigation.breadcrumbs);\n};\n\n/**\n * Hook to get sidebar state\n */\nexport const useSidebarState = () => {\n  const collapsed = useAppStore((state) => state.navigation.sidebarCollapsed);\n  const toggleSidebar = useAppStore((state) => state.toggleSidebar);\n\n  return {\n    collapsed,\n    toggleSidebar,\n  };\n};\n\n/**\n * Hook to get active menu key\n */\nexport const useActiveMenu = () => {\n  return useAppStore((state) => state.navigation.activeMenuKey);\n};\n\n/**\n * Hook to get navigation actions\n */\nexport const useNavigationActions = () => {\n  const setCurrentPath = useAppStore((state) => state.setCurrentPath);\n  const setBreadcrumbs = useAppStore((state) => state.setBreadcrumbs);\n  const toggleSidebar = useAppStore((state) => state.toggleSidebar);\n  const setActiveMenu = useAppStore((state) => state.setActiveMenu);\n\n  return {\n    setCurrentPath,\n    setBreadcrumbs,\n    toggleSidebar,\n    setActiveMenu,\n  };\n};\n\n// ============================================================================\n// UI State Hooks\n// ============================================================================\n\n/**\n * Hook to get UI state\n */\nexport const useUIState = () => {\n  return useAppStore((state) => state.ui);\n};\n\n/**\n * Hook to get global loading state\n */\nexport const useGlobalLoading = () => {\n  const loading = useAppStore((state) => state.ui.globalLoading);\n  const message = useAppStore((state) => state.ui.loadingMessage);\n\n  return { loading, message };\n};\n\n/**\n * Hook to get global error state\n */\nexport const useGlobalError = () => {\n  const error = useAppStore((state) => state.ui.globalError);\n  const details = useAppStore((state) => state.ui.errorDetails);\n\n  return { error, details };\n};\n\n/**\n * Hook to get UI actions\n */\nexport const useUIActions = () => {\n  const setGlobalLoading = useAppStore((state) => state.setGlobalLoading);\n  const setGlobalError = useAppStore((state) => state.setGlobalError);\n  const clearGlobalError = useAppStore((state) => state.clearGlobalError);\n\n  return {\n    setGlobalLoading,\n    setGlobalError,\n    clearGlobalError,\n  };\n};\n\n// ============================================================================\n// Notifications Hooks\n// ============================================================================\n\n/**\n * Hook to get notifications\n */\nexport const useNotifications = () => {\n  return useAppStore((state) => state.ui.notifications);\n};\n\n/**\n * Hook to get notification actions\n */\nexport const useNotificationActions = () => {\n  const addNotification = useAppStore((state) => state.addNotification);\n  const removeNotification = useAppStore((state) => state.removeNotification);\n  const clearNotifications = useAppStore((state) => state.clearNotifications);\n\n  return {\n    addNotification,\n    removeNotification,\n    clearNotifications,\n  };\n};\n\n/**\n * Hook for easy notification creation\n */\nexport const useNotify = () => {\n  const addNotification = useAppStore((state) => state.addNotification);\n\n  const notify = useCallback(() => ({\n    success: (message: string, title?: string) => {\n      addNotification({\n        type: 'success',\n        title: title || 'Success',\n        message,\n      });\n    },\n    error: (message: string, title?: string) => {\n      addNotification({\n        type: 'error',\n        title: title || 'Error',\n        message,\n      });\n    },\n    warning: (message: string, title?: string) => {\n      addNotification({\n        type: 'warning',\n        title: title || 'Warning',\n        message,\n      });\n    },\n    info: (message: string, title?: string) => {\n      addNotification({\n        type: 'info',\n        title: title || 'Info',\n        message,\n      });\n    },\n  }), [addNotification]);\n\n  return notify();\n};\n\n// ============================================================================\n// Modals Hooks\n// ============================================================================\n\n/**\n * Hook to get modals state\n */\nexport const useModals = () => {\n  return useAppStore((state) => state.ui.modals);\n};\n\n/**\n * Hook to get specific modal state\n */\nexport const useModal = (key: string) => {\n  const modal = useAppStore((state) => state.ui.modals[key]);\n  const showModal = useAppStore((state) => state.showModal);\n  const hideModal = useAppStore((state) => state.hideModal);\n\n  const show = useCallback((data?: any) => {\n    showModal(key, data);\n  }, [showModal, key]);\n\n  const hide = useCallback(() => {\n    hideModal(key);\n  }, [hideModal, key]);\n\n  return {\n    visible: modal?.visible || false,\n    data: modal?.data,\n    show,\n    hide,\n  };\n};\n\n/**\n * Hook to get modal actions\n */\nexport const useModalActions = () => {\n  const showModal = useAppStore((state) => state.showModal);\n  const hideModal = useAppStore((state) => state.hideModal);\n  const hideAllModals = useAppStore((state) => state.hideAllModals);\n\n  return {\n    showModal,\n    hideModal,\n    hideAllModals,\n  };\n};\n\n// ============================================================================\n// System Info Hooks\n// ============================================================================\n\n/**\n * Hook to get application version\n */\nexport const useAppVersion = () => {\n  return useAppStore((state) => state.version);\n};\n\n/**\n * Hook to get build time\n */\nexport const useBuildTime = () => {\n  return useAppStore((state) => state.buildTime);\n};\n\n/**\n * Hook to get environment\n */\nexport const useEnvironment = () => {\n  return useAppStore((state) => state.environment);\n};\n\n/**\n * Hook to get system info\n */\nexport const useSystemInfo = () => {\n  const version = useAppVersion();\n  const buildTime = useBuildTime();\n  const environment = useEnvironment();\n\n  return {\n    version,\n    buildTime,\n    environment,\n    isDevelopment: environment === 'development',\n    isProduction: environment === 'production',\n  };\n};\n\n// ============================================================================\n// Composite Hooks\n// ============================================================================\n\n/**\n * Hook to get complete application state\n */\nexport const useApp = () => {\n  const theme = useTheme();\n  const settings = useAppSettings();\n  const navigation = useNavigation();\n  const ui = useUIState();\n  const systemInfo = useSystemInfo();\n\n  const themeActions = useThemeActions();\n  const settingsActions = useSettingsActions();\n  const navigationActions = useNavigationActions();\n  const uiActions = useUIActions();\n  const notificationActions = useNotificationActions();\n  const modalActions = useModalActions();\n\n  return {\n    // State\n    theme,\n    settings,\n    navigation,\n    ui,\n    systemInfo,\n\n    // Actions\n    ...themeActions,\n    ...settingsActions,\n    ...navigationActions,\n    ...uiActions,\n    ...notificationActions,\n    ...modalActions,\n  };\n};\n\n/**\n * Hook for responsive design utilities\n */\nexport const useResponsive = () => {\n  const sidebarCollapsed = useAppStore((state) => state.navigation.sidebarCollapsed);\n  const toggleSidebar = useAppStore((state) => state.toggleSidebar);\n\n  // Auto-collapse sidebar on mobile\n  useEffect(() => {\n    const handleResize = () => {\n      const isMobile = window.innerWidth < 768;\n      if (isMobile && !sidebarCollapsed) {\n        toggleSidebar();\n      }\n    };\n\n    window.addEventListener('resize', handleResize);\n    handleResize(); // Check on mount\n\n    return () => window.removeEventListener('resize', handleResize);\n  }, [sidebarCollapsed, toggleSidebar]);\n\n  return {\n    isMobile: typeof window !== 'undefined' ? window.innerWidth < 768 : false,\n    isTablet: typeof window !== 'undefined' ? window.innerWidth >= 768 && window.innerWidth < 1024 : false,\n    isDesktop: typeof window !== 'undefined' ? window.innerWidth >= 1024 : false,\n  };\n};\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAID;AACA;;AAHA;;;AAaO,MAAM,WAAW;;IACtB,OAAO,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;gCAAE,CAAC,QAAU,MAAM,KAAK;;AAC3C;GAFa;;QACJ,gIAAA,CAAA,cAAW;;;AAMb,MAAM,eAAe;;IAC1B,OAAO,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;oCAAE,CAAC,QAAU,MAAM,KAAK,CAAC,IAAI;;AAChD;IAFa;;QACJ,gIAAA,CAAA,cAAW;;;AAMb,MAAM,gBAAgB;;IAC3B,OAAO,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;qCAAE,CAAC,QAAU,MAAM,KAAK,CAAC,IAAI,KAAK;;AACrD;IAFa;;QACJ,gIAAA,CAAA,cAAW;;;AAMb,MAAM,kBAAkB;;IAC7B,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;iDAAE,CAAC,QAAU,MAAM,QAAQ;;IACtD,MAAM,cAAc,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;oDAAE,CAAC,QAAU,MAAM,WAAW;;IAE5D,OAAO;QACL;QACA;IACF;AACF;IARa;;QACM,gIAAA,CAAA,cAAW;QACR,gIAAA,CAAA,cAAW;;;AAe1B,MAAM,iBAAiB;;IAC5B,OAAO,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;sCAAE,CAAC,QAAU,MAAM,QAAQ;;AAC9C;IAFa;;QACJ,gIAAA,CAAA,cAAW;;;AAMb,MAAM,qBAAqB;;IAChC,MAAM,iBAAiB,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;0DAAE,CAAC,QAAU,MAAM,cAAc;;IAClE,MAAM,gBAAgB,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;yDAAE,CAAC,QAAU,MAAM,aAAa;;IAEhE,OAAO;QACL;QACA;IACF;AACF;IARa;;QACY,gIAAA,CAAA,cAAW;QACZ,gIAAA,CAAA,cAAW;;;AAW5B,MAAM,aAAa,CAA8B;;IACtD,OAAO,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;kCAAE,CAAC,QAAU,MAAM,QAAQ,CAAC,IAAI;;AACnD;IAFa;;QACJ,gIAAA,CAAA,cAAW;;;AAUb,MAAM,gBAAgB;;IAC3B,OAAO,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;qCAAE,CAAC,QAAU,MAAM,UAAU;;AAChD;IAFa;;QACJ,gIAAA,CAAA,cAAW;;;AAMb,MAAM,iBAAiB;;IAC5B,OAAO,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;sCAAE,CAAC,QAAU,MAAM,UAAU,CAAC,WAAW;;AAC5D;IAFa;;QACJ,gIAAA,CAAA,cAAW;;;AAMb,MAAM,iBAAiB;;IAC5B,OAAO,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;sCAAE,CAAC,QAAU,MAAM,UAAU,CAAC,WAAW;;AAC5D;IAFa;;QACJ,gIAAA,CAAA,cAAW;;;AAMb,MAAM,kBAAkB;;IAC7B,MAAM,YAAY,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;kDAAE,CAAC,QAAU,MAAM,UAAU,CAAC,gBAAgB;;IAC1E,MAAM,gBAAgB,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;sDAAE,CAAC,QAAU,MAAM,aAAa;;IAEhE,OAAO;QACL;QACA;IACF;AACF;KARa;;QACO,gIAAA,CAAA,cAAW;QACP,gIAAA,CAAA,cAAW;;;AAW5B,MAAM,gBAAgB;;IAC3B,OAAO,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;qCAAE,CAAC,QAAU,MAAM,UAAU,CAAC,aAAa;;AAC9D;KAFa;;QACJ,gIAAA,CAAA,cAAW;;;AAMb,MAAM,uBAAuB;;IAClC,MAAM,iBAAiB,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;4DAAE,CAAC,QAAU,MAAM,cAAc;;IAClE,MAAM,iBAAiB,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;4DAAE,CAAC,QAAU,MAAM,cAAc;;IAClE,MAAM,gBAAgB,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;2DAAE,CAAC,QAAU,MAAM,aAAa;;IAChE,MAAM,gBAAgB,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;2DAAE,CAAC,QAAU,MAAM,aAAa;;IAEhE,OAAO;QACL;QACA;QACA;QACA;IACF;AACF;KAZa;;QACY,gIAAA,CAAA,cAAW;QACX,gIAAA,CAAA,cAAW;QACZ,gIAAA,CAAA,cAAW;QACX,gIAAA,CAAA,cAAW;;;AAiB5B,MAAM,aAAa;;IACxB,OAAO,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;kCAAE,CAAC,QAAU,MAAM,EAAE;;AACxC;KAFa;;QACJ,gIAAA,CAAA,cAAW;;;AAMb,MAAM,mBAAmB;;IAC9B,MAAM,UAAU,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;iDAAE,CAAC,QAAU,MAAM,EAAE,CAAC,aAAa;;IAC7D,MAAM,UAAU,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;iDAAE,CAAC,QAAU,MAAM,EAAE,CAAC,cAAc;;IAE9D,OAAO;QAAE;QAAS;IAAQ;AAC5B;KALa;;QACK,gIAAA,CAAA,cAAW;QACX,gIAAA,CAAA,cAAW;;;AAQtB,MAAM,iBAAiB;;IAC5B,MAAM,QAAQ,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;6CAAE,CAAC,QAAU,MAAM,EAAE,CAAC,WAAW;;IACzD,MAAM,UAAU,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;+CAAE,CAAC,QAAU,MAAM,EAAE,CAAC,YAAY;;IAE5D,OAAO;QAAE;QAAO;IAAQ;AAC1B;KALa;;QACG,gIAAA,CAAA,cAAW;QACT,gIAAA,CAAA,cAAW;;;AAQtB,MAAM,eAAe;;IAC1B,MAAM,mBAAmB,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;sDAAE,CAAC,QAAU,MAAM,gBAAgB;;IACtE,MAAM,iBAAiB,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;oDAAE,CAAC,QAAU,MAAM,cAAc;;IAClE,MAAM,mBAAmB,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;sDAAE,CAAC,QAAU,MAAM,gBAAgB;;IAEtE,OAAO;QACL;QACA;QACA;IACF;AACF;KAVa;;QACc,gIAAA,CAAA,cAAW;QACb,gIAAA,CAAA,cAAW;QACT,gIAAA,CAAA,cAAW;;;AAgB/B,MAAM,mBAAmB;;IAC9B,OAAO,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;wCAAE,CAAC,QAAU,MAAM,EAAE,CAAC,aAAa;;AACtD;KAFa;;QACJ,gIAAA,CAAA,cAAW;;;AAMb,MAAM,yBAAyB;;IACpC,MAAM,kBAAkB,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;+DAAE,CAAC,QAAU,MAAM,eAAe;;IACpE,MAAM,qBAAqB,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;kEAAE,CAAC,QAAU,MAAM,kBAAkB;;IAC1E,MAAM,qBAAqB,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;kEAAE,CAAC,QAAU,MAAM,kBAAkB;;IAE1E,OAAO;QACL;QACA;QACA;IACF;AACF;KAVa;;QACa,gIAAA,CAAA,cAAW;QACR,gIAAA,CAAA,cAAW;QACX,gIAAA,CAAA,cAAW;;;AAYjC,MAAM,YAAY;;IACvB,MAAM,kBAAkB,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;kDAAE,CAAC,QAAU,MAAM,eAAe;;IAEpE,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yCAAE,IAAM,CAAC;gBAChC,OAAO;qDAAE,CAAC,SAAiB;wBACzB,gBAAgB;4BACd,MAAM;4BACN,OAAO,SAAS;4BAChB;wBACF;oBACF;;gBACA,KAAK;qDAAE,CAAC,SAAiB;wBACvB,gBAAgB;4BACd,MAAM;4BACN,OAAO,SAAS;4BAChB;wBACF;oBACF;;gBACA,OAAO;qDAAE,CAAC,SAAiB;wBACzB,gBAAgB;4BACd,MAAM;4BACN,OAAO,SAAS;4BAChB;wBACF;oBACF;;gBACA,IAAI;qDAAE,CAAC,SAAiB;wBACtB,gBAAgB;4BACd,MAAM;4BACN,OAAO,SAAS;4BAChB;wBACF;oBACF;;YACF,CAAC;wCAAG;QAAC;KAAgB;IAErB,OAAO;AACT;KAnCa;;QACa,gIAAA,CAAA,cAAW;;;AA2C9B,MAAM,YAAY;;IACvB,OAAO,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;iCAAE,CAAC,QAAU,MAAM,EAAE,CAAC,MAAM;;AAC/C;KAFa;;QACJ,gIAAA,CAAA,cAAW;;;AAMb,MAAM,WAAW,CAAC;;IACvB,MAAM,QAAQ,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;uCAAE,CAAC,QAAU,MAAM,EAAE,CAAC,MAAM,CAAC,IAAI;;IACzD,MAAM,YAAY,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;2CAAE,CAAC,QAAU,MAAM,SAAS;;IACxD,MAAM,YAAY,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;2CAAE,CAAC,QAAU,MAAM,SAAS;;IAExD,MAAM,OAAO,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sCAAE,CAAC;YACxB,UAAU,KAAK;QACjB;qCAAG;QAAC;QAAW;KAAI;IAEnB,MAAM,OAAO,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sCAAE;YACvB,UAAU;QACZ;qCAAG;QAAC;QAAW;KAAI;IAEnB,OAAO;QACL,SAAS,OAAO,WAAW;QAC3B,MAAM,OAAO;QACb;QACA;IACF;AACF;KAnBa;;QACG,gIAAA,CAAA,cAAW;QACP,gIAAA,CAAA,cAAW;QACX,gIAAA,CAAA,cAAW;;;AAqBxB,MAAM,kBAAkB;;IAC7B,MAAM,YAAY,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;kDAAE,CAAC,QAAU,MAAM,SAAS;;IACxD,MAAM,YAAY,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;kDAAE,CAAC,QAAU,MAAM,SAAS;;IACxD,MAAM,gBAAgB,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;sDAAE,CAAC,QAAU,MAAM,aAAa;;IAEhE,OAAO;QACL;QACA;QACA;IACF;AACF;KAVa;;QACO,gIAAA,CAAA,cAAW;QACX,gIAAA,CAAA,cAAW;QACP,gIAAA,CAAA,cAAW;;;AAgB5B,MAAM,gBAAgB;;IAC3B,OAAO,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;qCAAE,CAAC,QAAU,MAAM,OAAO;;AAC7C;KAFa;;QACJ,gIAAA,CAAA,cAAW;;;AAMb,MAAM,eAAe;;IAC1B,OAAO,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;oCAAE,CAAC,QAAU,MAAM,SAAS;;AAC/C;KAFa;;QACJ,gIAAA,CAAA,cAAW;;;AAMb,MAAM,iBAAiB;;IAC5B,OAAO,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;sCAAE,CAAC,QAAU,MAAM,WAAW;;AACjD;KAFa;;QACJ,gIAAA,CAAA,cAAW;;;AAMb,MAAM,gBAAgB;;IAC3B,MAAM,UAAU;IAChB,MAAM,YAAY;IAClB,MAAM,cAAc;IAEpB,OAAO;QACL;QACA;QACA;QACA,eAAe,gBAAgB;QAC/B,cAAc,gBAAgB;IAChC;AACF;KAZa;;QACK;QACE;QACE;;;AAkBf,MAAM,SAAS;;IACpB,MAAM,QAAQ;IACd,MAAM,WAAW;IACjB,MAAM,aAAa;IACnB,MAAM,KAAK;IACX,MAAM,aAAa;IAEnB,MAAM,eAAe;IACrB,MAAM,kBAAkB;IACxB,MAAM,oBAAoB;IAC1B,MAAM,YAAY;IAClB,MAAM,sBAAsB;IAC5B,MAAM,eAAe;IAErB,OAAO;QACL,QAAQ;QACR;QACA;QACA;QACA;QACA;QAEA,UAAU;QACV,GAAG,YAAY;QACf,GAAG,eAAe;QAClB,GAAG,iBAAiB;QACpB,GAAG,SAAS;QACZ,GAAG,mBAAmB;QACtB,GAAG,YAAY;IACjB;AACF;KA9Ba;;QACG;QACG;QACE;QACR;QACQ;QAEE;QACG;QACE;QACR;QACU;QACP;;;AAuBhB,MAAM,gBAAgB;;IAC3B,MAAM,mBAAmB,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;uDAAE,CAAC,QAAU,MAAM,UAAU,CAAC,gBAAgB;;IACjF,MAAM,gBAAgB,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;oDAAE,CAAC,QAAU,MAAM,aAAa;;IAEhE,kCAAkC;IAClC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM;wDAAe;oBACnB,MAAM,WAAW,OAAO,UAAU,GAAG;oBACrC,IAAI,YAAY,CAAC,kBAAkB;wBACjC;oBACF;gBACF;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC,gBAAgB,iBAAiB;YAEjC;2CAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;kCAAG;QAAC;QAAkB;KAAc;IAEpC,OAAO;QACL,UAAU,uCAAgC,OAAO,UAAU,GAAG;QAC9D,UAAU,uCAAgC,OAAO,UAAU,IAAI,OAAO,OAAO,UAAU,GAAG;QAC1F,WAAW,uCAAgC,OAAO,UAAU,IAAI;IAClE;AACF;KAxBa;;QACc,gIAAA,CAAA,cAAW;QACd,gIAAA,CAAA,cAAW"}}, {"offset": {"line": 2449, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2455, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/stores/app-utils.ts"], "sourcesContent": ["/**\n * Application Utilities\n * Helper functions for application operations\n */\n\nimport type { ThemeConfig, AppSettings, NavigationState, UIState } from './types';\nimport { useAppStore } from './app-store';\n\n// ============================================================================\n// Theme Utilities\n// ============================================================================\n\n/**\n * Get current theme configuration\n */\nexport const getCurrentTheme = (): ThemeConfig => {\n  return useAppStore.getState().theme;\n};\n\n/**\n * Get current theme mode\n */\nexport const getCurrentThemeMode = (): 'light' | 'dark' => {\n  return useAppStore.getState().theme.mode;\n};\n\n/**\n * Check if dark mode is active\n */\nexport const isDarkMode = (): boolean => {\n  return getCurrentThemeMode() === 'dark';\n};\n\n/**\n * Toggle theme mode\n */\nexport const toggleTheme = (): void => {\n  useAppStore.getState().toggleTheme();\n};\n\n/**\n * Set theme mode\n */\nexport const setThemeMode = (mode: 'light' | 'dark'): void => {\n  useAppStore.getState().setTheme({ mode });\n};\n\n/**\n * Apply theme to document\n */\nexport const applyThemeToDocument = (): void => {\n  if (typeof document === 'undefined') return;\n\n  const theme = getCurrentTheme();\n  const { mode, primaryColor, borderRadius } = theme;\n\n  // Apply theme class to body\n  document.body.className = document.body.className.replace(/theme-\\w+/g, '');\n  document.body.classList.add(`theme-${mode}`);\n\n  // Apply CSS custom properties\n  const root = document.documentElement;\n  root.style.setProperty('--primary-color', primaryColor);\n  root.style.setProperty('--border-radius', `${borderRadius}px`);\n};\n\n// ============================================================================\n// Settings Utilities\n// ============================================================================\n\n/**\n * Get current application settings\n */\nexport const getCurrentSettings = (): AppSettings => {\n  return useAppStore.getState().settings;\n};\n\n/**\n * Get specific setting value\n */\nexport const getSetting = <K extends keyof AppSettings>(key: K): AppSettings[K] => {\n  return useAppStore.getState().settings[key];\n};\n\n/**\n * Update application settings\n */\nexport const updateSettings = (settings: Partial<AppSettings>): void => {\n  useAppStore.getState().updateSettings(settings);\n};\n\n/**\n * Reset settings to default\n */\nexport const resetSettings = (): void => {\n  useAppStore.getState().resetSettings();\n};\n\n// ============================================================================\n// Navigation Utilities\n// ============================================================================\n\n/**\n * Get current navigation state\n */\nexport const getCurrentNavigation = (): NavigationState => {\n  return useAppStore.getState().navigation;\n};\n\n/**\n * Get current path\n */\nexport const getCurrentPath = (): string => {\n  return useAppStore.getState().navigation.currentPath;\n};\n\n/**\n * Set current path\n */\nexport const setCurrentPath = (path: string): void => {\n  useAppStore.getState().setCurrentPath(path);\n};\n\n/**\n * Get breadcrumbs\n */\nexport const getBreadcrumbs = (): NavigationState['breadcrumbs'] => {\n  return useAppStore.getState().navigation.breadcrumbs;\n};\n\n/**\n * Set breadcrumbs\n */\nexport const setBreadcrumbs = (breadcrumbs: NavigationState['breadcrumbs']): void => {\n  useAppStore.getState().setBreadcrumbs(breadcrumbs);\n};\n\n/**\n * Check if sidebar is collapsed\n */\nexport const isSidebarCollapsed = (): boolean => {\n  return useAppStore.getState().navigation.sidebarCollapsed;\n};\n\n/**\n * Toggle sidebar\n */\nexport const toggleSidebar = (): void => {\n  useAppStore.getState().toggleSidebar();\n};\n\n/**\n * Get active menu key\n */\nexport const getActiveMenuKey = (): string => {\n  return useAppStore.getState().navigation.activeMenuKey;\n};\n\n/**\n * Set active menu key\n */\nexport const setActiveMenu = (key: string): void => {\n  useAppStore.getState().setActiveMenu(key);\n};\n\n// ============================================================================\n// UI State Utilities\n// ============================================================================\n\n/**\n * Get current UI state\n */\nexport const getCurrentUIState = (): UIState => {\n  return useAppStore.getState().ui;\n};\n\n/**\n * Check if global loading is active\n */\nexport const isGlobalLoading = (): boolean => {\n  return useAppStore.getState().ui.globalLoading;\n};\n\n/**\n * Set global loading state\n */\nexport const setGlobalLoading = (loading: boolean, message?: string): void => {\n  useAppStore.getState().setGlobalLoading(loading, message);\n};\n\n/**\n * Get global error\n */\nexport const getGlobalError = (): string | null => {\n  return useAppStore.getState().ui.globalError;\n};\n\n/**\n * Set global error\n */\nexport const setGlobalError = (error: string | null, details?: any): void => {\n  useAppStore.getState().setGlobalError(error, details);\n};\n\n/**\n * Clear global error\n */\nexport const clearGlobalError = (): void => {\n  useAppStore.getState().clearGlobalError();\n};\n\n// ============================================================================\n// Notifications Utilities\n// ============================================================================\n\n/**\n * Get current notifications\n */\nexport const getNotifications = (): UIState['notifications'] => {\n  return useAppStore.getState().ui.notifications;\n};\n\n/**\n * Add notification\n */\nexport const addNotification = (\n  notification: Omit<UIState['notifications'][0], 'id' | 'timestamp'>\n): void => {\n  useAppStore.getState().addNotification(notification);\n};\n\n/**\n * Remove notification\n */\nexport const removeNotification = (id: string): void => {\n  useAppStore.getState().removeNotification(id);\n};\n\n/**\n * Clear all notifications\n */\nexport const clearNotifications = (): void => {\n  useAppStore.getState().clearNotifications();\n};\n\n/**\n * Show success notification\n */\nexport const notifySuccess = (message: string, title?: string): void => {\n  addNotification({\n    type: 'success',\n    title: title || 'Success',\n    message,\n  });\n};\n\n/**\n * Show error notification\n */\nexport const notifyError = (message: string, title?: string): void => {\n  addNotification({\n    type: 'error',\n    title: title || 'Error',\n    message,\n  });\n};\n\n/**\n * Show warning notification\n */\nexport const notifyWarning = (message: string, title?: string): void => {\n  addNotification({\n    type: 'warning',\n    title: title || 'Warning',\n    message,\n  });\n};\n\n/**\n * Show info notification\n */\nexport const notifyInfo = (message: string, title?: string): void => {\n  addNotification({\n    type: 'info',\n    title: title || 'Info',\n    message,\n  });\n};\n\n// ============================================================================\n// Modals Utilities\n// ============================================================================\n\n/**\n * Get modals state\n */\nexport const getModalsState = (): UIState['modals'] => {\n  return useAppStore.getState().ui.modals;\n};\n\n/**\n * Check if modal is visible\n */\nexport const isModalVisible = (key: string): boolean => {\n  const modal = useAppStore.getState().ui.modals[key];\n  return modal?.visible || false;\n};\n\n/**\n * Get modal data\n */\nexport const getModalData = (key: string): any => {\n  const modal = useAppStore.getState().ui.modals[key];\n  return modal?.data;\n};\n\n/**\n * Show modal\n */\nexport const showModal = (key: string, data?: any): void => {\n  useAppStore.getState().showModal(key, data);\n};\n\n/**\n * Hide modal\n */\nexport const hideModal = (key: string): void => {\n  useAppStore.getState().hideModal(key);\n};\n\n/**\n * Hide all modals\n */\nexport const hideAllModals = (): void => {\n  useAppStore.getState().hideAllModals();\n};\n\n// ============================================================================\n// System Info Utilities\n// ============================================================================\n\n/**\n * Get application version\n */\nexport const getAppVersion = (): string => {\n  return useAppStore.getState().version;\n};\n\n/**\n * Get build time\n */\nexport const getBuildTime = (): string => {\n  return useAppStore.getState().buildTime;\n};\n\n/**\n * Get environment\n */\nexport const getEnvironment = (): 'development' | 'staging' | 'production' => {\n  return useAppStore.getState().environment;\n};\n\n/**\n * Check if development environment\n */\nexport const isDevelopment = (): boolean => {\n  return getEnvironment() === 'development';\n};\n\n/**\n * Check if production environment\n */\nexport const isProduction = (): boolean => {\n  return getEnvironment() === 'production';\n};\n\n// ============================================================================\n// Responsive Utilities\n// ============================================================================\n\n/**\n * Check if current viewport is mobile\n */\nexport const isMobile = (): boolean => {\n  if (typeof window === 'undefined') return false;\n  return window.innerWidth < 768;\n};\n\n/**\n * Check if current viewport is tablet\n */\nexport const isTablet = (): boolean => {\n  if (typeof window === 'undefined') return false;\n  return window.innerWidth >= 768 && window.innerWidth < 1024;\n};\n\n/**\n * Check if current viewport is desktop\n */\nexport const isDesktop = (): boolean => {\n  if (typeof window === 'undefined') return false;\n  return window.innerWidth >= 1024;\n};\n\n/**\n * Get current breakpoint\n */\nexport const getCurrentBreakpoint = (): 'mobile' | 'tablet' | 'desktop' => {\n  if (isMobile()) return 'mobile';\n  if (isTablet()) return 'tablet';\n  return 'desktop';\n};\n\n// ============================================================================\n// Development Utilities\n// ============================================================================\n\n/**\n * Get full application state (development only)\n */\nexport const getAppState = () => {\n  if (process.env.NODE_ENV !== 'development') {\n    return null;\n  }\n\n  return useAppStore.getState();\n};\n\n/**\n * Reset application state (development/testing only)\n */\nexport const resetAppState = (): void => {\n  if (process.env.NODE_ENV !== 'development') {\n    return;\n  }\n\n  const store = useAppStore.getState();\n  store.resetSettings();\n  store.clearNotifications();\n  store.hideAllModals();\n  store.clearGlobalError();\n  store.setGlobalLoading(false);\n};\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGD;AA+ZM;;AAtZC,MAAM,kBAAkB;IAC7B,OAAO,gIAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,KAAK;AACrC;AAKO,MAAM,sBAAsB;IACjC,OAAO,gIAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,KAAK,CAAC,IAAI;AAC1C;AAKO,MAAM,aAAa;IACxB,OAAO,0BAA0B;AACnC;AAKO,MAAM,cAAc;IACzB,gIAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,WAAW;AACpC;AAKO,MAAM,eAAe,CAAC;IAC3B,gIAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAAE;IAAK;AACzC;AAKO,MAAM,uBAAuB;IAClC,IAAI,OAAO,aAAa,aAAa;IAErC,MAAM,QAAQ;IACd,MAAM,EAAE,IAAI,EAAE,YAAY,EAAE,YAAY,EAAE,GAAG;IAE7C,4BAA4B;IAC5B,SAAS,IAAI,CAAC,SAAS,GAAG,SAAS,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,cAAc;IACxE,SAAS,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,MAAM;IAE3C,8BAA8B;IAC9B,MAAM,OAAO,SAAS,eAAe;IACrC,KAAK,KAAK,CAAC,WAAW,CAAC,mBAAmB;IAC1C,KAAK,KAAK,CAAC,WAAW,CAAC,mBAAmB,GAAG,aAAa,EAAE,CAAC;AAC/D;AASO,MAAM,qBAAqB;IAChC,OAAO,gIAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,QAAQ;AACxC;AAKO,MAAM,aAAa,CAA8B;IACtD,OAAO,gIAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,QAAQ,CAAC,IAAI;AAC7C;AAKO,MAAM,iBAAiB,CAAC;IAC7B,gIAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,cAAc,CAAC;AACxC;AAKO,MAAM,gBAAgB;IAC3B,gIAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,aAAa;AACtC;AASO,MAAM,uBAAuB;IAClC,OAAO,gIAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,UAAU;AAC1C;AAKO,MAAM,iBAAiB;IAC5B,OAAO,gIAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,UAAU,CAAC,WAAW;AACtD;AAKO,MAAM,iBAAiB,CAAC;IAC7B,gIAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,cAAc,CAAC;AACxC;AAKO,MAAM,iBAAiB;IAC5B,OAAO,gIAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,UAAU,CAAC,WAAW;AACtD;AAKO,MAAM,iBAAiB,CAAC;IAC7B,gIAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,cAAc,CAAC;AACxC;AAKO,MAAM,qBAAqB;IAChC,OAAO,gIAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,UAAU,CAAC,gBAAgB;AAC3D;AAKO,MAAM,gBAAgB;IAC3B,gIAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,aAAa;AACtC;AAKO,MAAM,mBAAmB;IAC9B,OAAO,gIAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,UAAU,CAAC,aAAa;AACxD;AAKO,MAAM,gBAAgB,CAAC;IAC5B,gIAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,aAAa,CAAC;AACvC;AASO,MAAM,oBAAoB;IAC/B,OAAO,gIAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,EAAE;AAClC;AAKO,MAAM,kBAAkB;IAC7B,OAAO,gIAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,EAAE,CAAC,aAAa;AAChD;AAKO,MAAM,mBAAmB,CAAC,SAAkB;IACjD,gIAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,gBAAgB,CAAC,SAAS;AACnD;AAKO,MAAM,iBAAiB;IAC5B,OAAO,gIAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,EAAE,CAAC,WAAW;AAC9C;AAKO,MAAM,iBAAiB,CAAC,OAAsB;IACnD,gIAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,cAAc,CAAC,OAAO;AAC/C;AAKO,MAAM,mBAAmB;IAC9B,gIAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,gBAAgB;AACzC;AASO,MAAM,mBAAmB;IAC9B,OAAO,gIAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,EAAE,CAAC,aAAa;AAChD;AAKO,MAAM,kBAAkB,CAC7B;IAEA,gIAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,eAAe,CAAC;AACzC;AAKO,MAAM,qBAAqB,CAAC;IACjC,gIAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,kBAAkB,CAAC;AAC5C;AAKO,MAAM,qBAAqB;IAChC,gIAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,kBAAkB;AAC3C;AAKO,MAAM,gBAAgB,CAAC,SAAiB;IAC7C,gBAAgB;QACd,MAAM;QACN,OAAO,SAAS;QAChB;IACF;AACF;AAKO,MAAM,cAAc,CAAC,SAAiB;IAC3C,gBAAgB;QACd,MAAM;QACN,OAAO,SAAS;QAChB;IACF;AACF;AAKO,MAAM,gBAAgB,CAAC,SAAiB;IAC7C,gBAAgB;QACd,MAAM;QACN,OAAO,SAAS;QAChB;IACF;AACF;AAKO,MAAM,aAAa,CAAC,SAAiB;IAC1C,gBAAgB;QACd,MAAM;QACN,OAAO,SAAS;QAChB;IACF;AACF;AASO,MAAM,iBAAiB;IAC5B,OAAO,gIAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,EAAE,CAAC,MAAM;AACzC;AAKO,MAAM,iBAAiB,CAAC;IAC7B,MAAM,QAAQ,gIAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,EAAE,CAAC,MAAM,CAAC,IAAI;IACnD,OAAO,OAAO,WAAW;AAC3B;AAKO,MAAM,eAAe,CAAC;IAC3B,MAAM,QAAQ,gIAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,EAAE,CAAC,MAAM,CAAC,IAAI;IACnD,OAAO,OAAO;AAChB;AAKO,MAAM,YAAY,CAAC,KAAa;IACrC,gIAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,SAAS,CAAC,KAAK;AACxC;AAKO,MAAM,YAAY,CAAC;IACxB,gIAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,SAAS,CAAC;AACnC;AAKO,MAAM,gBAAgB;IAC3B,gIAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,aAAa;AACtC;AASO,MAAM,gBAAgB;IAC3B,OAAO,gIAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,OAAO;AACvC;AAKO,MAAM,eAAe;IAC1B,OAAO,gIAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,SAAS;AACzC;AAKO,MAAM,iBAAiB;IAC5B,OAAO,gIAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,WAAW;AAC3C;AAKO,MAAM,gBAAgB;IAC3B,OAAO,qBAAqB;AAC9B;AAKO,MAAM,eAAe;IAC1B,OAAO,qBAAqB;AAC9B;AASO,MAAM,WAAW;IACtB,uCAAmC;;IAAY;IAC/C,OAAO,OAAO,UAAU,GAAG;AAC7B;AAKO,MAAM,WAAW;IACtB,uCAAmC;;IAAY;IAC/C,OAAO,OAAO,UAAU,IAAI,OAAO,OAAO,UAAU,GAAG;AACzD;AAKO,MAAM,YAAY;IACvB,uCAAmC;;IAAY;IAC/C,OAAO,OAAO,UAAU,IAAI;AAC9B;AAKO,MAAM,uBAAuB;IAClC,IAAI,YAAY,OAAO;IACvB,IAAI,YAAY,OAAO;IACvB,OAAO;AACT;AASO,MAAM,cAAc;IACzB,uCAA4C;;IAE5C;IAEA,OAAO,gIAAA,CAAA,cAAW,CAAC,QAAQ;AAC7B;AAKO,MAAM,gBAAgB;IAC3B,uCAA4C;;IAE5C;IAEA,MAAM,QAAQ,gIAAA,CAAA,cAAW,CAAC,QAAQ;IAClC,MAAM,aAAa;IACnB,MAAM,kBAAkB;IACxB,MAAM,aAAa;IACnB,MAAM,gBAAgB;IACtB,MAAM,gBAAgB,CAAC;AACzB"}}, {"offset": {"line": 2717, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2723, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/stores/store-context.tsx"], "sourcesContent": ["/**\n * Store Context - React context for accessing stores\n * Provides centralized access to all Zustand stores\n */\n\n'use client';\n\nimport React, { createContext, useContext, ReactNode } from 'react';\nimport { useAuth } from './auth-hooks';\nimport { useApp } from './app-hooks';\nimport type { AuthStore, AppStore } from './types';\n\n/**\n * Store context interface\n */\ninterface StoreContextType {\n  authStore: AuthStore;\n  appStore: AppStore;\n}\n\n/**\n * Store context\n */\nconst StoreContext = createContext<StoreContextType | null>(null);\n\n/**\n * Store context provider props\n */\ninterface StoreContextProviderProps {\n  children: ReactNode;\n}\n\n/**\n * Store context provider component\n * Provides all stores to child components\n */\nexport function StoreContextProvider({ children }: StoreContextProviderProps) {\n  // Get store instances\n  const authStore = useAuth();\n  const appStore = useApp();\n\n  const contextValue: StoreContextType = {\n    authStore,\n    appStore,\n  };\n\n  return (\n    <StoreContext.Provider value={contextValue}>\n      {children}\n    </StoreContext.Provider>\n  );\n}\n\n/**\n * Hook to access store context\n * Throws error if used outside provider\n */\nexport function useStoreContext(): StoreContextType {\n  const context = useContext(StoreContext);\n\n  if (!context) {\n    throw new Error(\n      'useStoreContext must be used within a StoreContextProvider'\n    );\n  }\n\n  return context;\n}\n\n/**\n * Hook to access auth store from context\n */\nexport function useAuthStoreContext() {\n  const { authStore } = useStoreContext();\n  return authStore;\n}\n\n/**\n * Hook to access app store from context\n */\nexport function useAppStoreContext() {\n  const { appStore } = useStoreContext();\n  return appStore;\n}\n\n/**\n * Hook to check if store context is available\n */\nexport function useIsStoreContextAvailable(): boolean {\n  const context = useContext(StoreContext);\n  return context !== null;\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;AAID;AACA;AACA;;;AAJA;;;;AAeA;;CAEC,GACD,MAAM,6BAAe,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA2B;AAarD,SAAS,qBAAqB,EAAE,QAAQ,EAA6B;;IAC1E,sBAAsB;IACtB,MAAM,YAAY,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IACxB,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,SAAM,AAAD;IAEtB,MAAM,eAAiC;QACrC;QACA;IACF;IAEA,qBACE,6LAAC,aAAa,QAAQ;QAAC,OAAO;kBAC3B;;;;;;AAGP;GAfgB;;QAEI,iIAAA,CAAA,UAAO;QACR,gIAAA,CAAA,SAAM;;;KAHT;AAqBT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAE3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MACR;IAEJ;IAEA,OAAO;AACT;IAVgB;AAeT,SAAS;;IACd,MAAM,EAAE,SAAS,EAAE,GAAG;IACtB,OAAO;AACT;IAHgB;;QACQ;;;AAOjB,SAAS;;IACd,MAAM,EAAE,QAAQ,EAAE,GAAG;IACrB,OAAO;AACT;IAHgB;;QACO;;;AAOhB,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,OAAO,YAAY;AACrB;IAHgB"}}, {"offset": {"line": 2811, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2817, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/stores/store-provider.tsx"], "sourcesContent": ["/**\n * Store Provider - Main provider component for all stores\n * Wraps the application with necessary store providers\n */\n\n'use client';\n\nimport React, { ReactNode, useEffect } from 'react';\nimport { StoreContextProvider } from './store-context';\nimport { useAuth } from './auth-hooks';\nimport { useApp } from './app-hooks';\nimport { STORAGE_KEYS } from './constants';\n\n/**\n * Store provider props\n */\ninterface StoreProviderProps {\n  children: ReactNode;\n}\n\n/**\n * Store initialization component\n * Handles store initialization and hydration\n */\nfunction StoreInitializer({ children }: { children: ReactNode }) {\n  const authStore = useAuth();\n  const appStore = useApp();\n\n  useEffect(() => {\n    // Initialize stores on mount\n    const initializeStores = async () => {\n      try {\n        console.log('✅ Stores initialized successfully');\n      } catch (error) {\n        console.error('❌ Failed to initialize stores:', error);\n      }\n    };\n\n    initializeStores();\n  }, []);\n\n  return <>{children}</>;\n}\n\n/**\n * Main store provider component\n * Provides all stores and handles initialization\n */\nexport function StoreProvider({ children }: StoreProviderProps) {\n  return (\n    <StoreContextProvider>\n      <StoreInitializer>\n        {children}\n      </StoreInitializer>\n    </StoreContextProvider>\n  );\n}\n\n/**\n * HOC to wrap components with store provider\n */\nexport function withStoreProvider<P extends object>(\n  Component: React.ComponentType<P>\n) {\n  const WrappedComponent = (props: P) => (\n    <StoreProvider>\n      <Component {...props} />\n    </StoreProvider>\n  );\n\n  WrappedComponent.displayName = `withStoreProvider(${Component.displayName || Component.name})`;\n\n  return WrappedComponent;\n}\n\n/**\n * Store provider utilities\n */\nexport const StoreProviderUtils = {\n  /**\n   * Check if stores are properly initialized\n   */\n  checkStoreInitialization: () => {\n    try {\n      return {\n        auth: true,\n        app: true,\n        all: true,\n      };\n    } catch (error) {\n      console.error('Failed to check store initialization:', error);\n      return {\n        auth: false,\n        app: false,\n        all: false,\n      };\n    }\n  },\n\n  /**\n   * Reset all stores to initial state\n   */\n  resetAllStores: () => {\n    try {\n      console.log('✅ All stores reset successfully');\n    } catch (error) {\n      console.error('❌ Failed to reset stores:', error);\n    }\n  },\n\n  /**\n   * Clear all persisted store data\n   */\n  clearPersistedData: () => {\n    try {\n      Object.values(STORAGE_KEYS).forEach(key => {\n        localStorage.removeItem(key);\n      });\n\n      console.log('✅ All persisted store data cleared');\n    } catch (error) {\n      console.error('❌ Failed to clear persisted data:', error);\n    }\n  },\n};\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;AAID;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAeA;;;CAGC,GACD,SAAS,iBAAiB,EAAE,QAAQ,EAA2B;;IAC7D,MAAM,YAAY,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IACxB,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,SAAM,AAAD;IAEtB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,6BAA6B;YAC7B,MAAM;+DAAmB;oBACvB,IAAI;wBACF,QAAQ,GAAG,CAAC;oBACd,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,kCAAkC;oBAClD;gBACF;;YAEA;QACF;qCAAG,EAAE;IAEL,qBAAO;kBAAG;;AACZ;GAlBS;;QACW,iIAAA,CAAA,UAAO;QACR,gIAAA,CAAA,SAAM;;;KAFhB;AAwBF,SAAS,cAAc,EAAE,QAAQ,EAAsB;IAC5D,qBACE,6LAAC,qIAAA,CAAA,uBAAoB;kBACnB,cAAA,6LAAC;sBACE;;;;;;;;;;;AAIT;MARgB;AAaT,SAAS,kBACd,SAAiC;IAEjC,MAAM,mBAAmB,CAAC,sBACxB,6LAAC;sBACC,cAAA,6LAAC;gBAAW,GAAG,KAAK;;;;;;;;;;;IAIxB,iBAAiB,WAAW,GAAG,CAAC,kBAAkB,EAAE,UAAU,WAAW,IAAI,UAAU,IAAI,CAAC,CAAC,CAAC;IAE9F,OAAO;AACT;AAKO,MAAM,qBAAqB;IAChC;;GAEC,GACD,0BAA0B;QACxB,IAAI;YACF,OAAO;gBACL,MAAM;gBACN,KAAK;gBACL,KAAK;YACP;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;YACvD,OAAO;gBACL,MAAM;gBACN,KAAK;gBACL,KAAK;YACP;QACF;IACF;IAEA;;GAEC,GACD,gBAAgB;QACd,IAAI;YACF,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C;IACF;IAEA;;GAEC,GACD,oBAAoB;QAClB,IAAI;YACF,OAAO,MAAM,CAAC,6HAAA,CAAA,eAAY,EAAE,OAAO,CAAC,CAAA;gBAClC,aAAa,UAAU,CAAC;YAC1B;YAEA,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;QACrD;IACF;AACF"}}, {"offset": {"line": 2952, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2958, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/stores/provider-hooks.ts"], "sourcesContent": ["/**\n * Provider Hooks - Custom hooks for using stores with providers\n * Provides convenient access to stores through context\n */\n\n'use client';\n\nimport { useCallback, useMemo } from 'react';\nimport {\n  useStoreContext,\n  useAuthStoreContext,\n  useAppStoreContext,\n  useIsStoreContextAvailable\n} from './store-context';\nimport type { SystemUser, AuthState, AppState } from './types';\n\n/**\n * Hook to use auth store with provider context\n * Provides auth state and actions\n */\nexport function useAuthProvider() {\n  const authStore = useAuthStoreContext();\n\n  const login = useCallback(async (credentials: { username: string; password: string }) => {\n    return authStore.login(credentials);\n  }, [authStore]);\n\n  const logout = useCallback(async () => {\n    return authStore.logout();\n  }, [authStore]);\n\n  const logoutAll = useCallback(async () => {\n    return authStore.logoutAll();\n  }, [authStore]);\n\n  const refreshToken = useCallback(async () => {\n    return authStore.refreshToken();\n  }, [authStore]);\n\n  const updateProfile = useCallback(async (data: Partial<SystemUser>) => {\n    return authStore.updateProfile(data);\n  }, [authStore]);\n\n  const changePassword = useCallback(async (data: { currentPassword: string; newPassword: string }) => {\n    return authStore.changePassword(data);\n  }, [authStore]);\n\n  return useMemo(() => ({\n    // State\n    user: authStore.user,\n    token: authStore.token,\n    refreshToken: authStore.refreshToken,\n    isAuthenticated: authStore.isAuthenticated,\n    isLoading: authStore.isLoading,\n    error: authStore.error,\n    isInitialized: authStore.isInitialized,\n\n    // Actions\n    login,\n    logout,\n    logoutAll,\n    refreshToken: refreshToken,\n    updateProfile,\n    changePassword,\n    clearError: authStore.clearError,\n    reset: authStore.reset,\n  }), [\n    authStore.user,\n    authStore.token,\n    authStore.refreshToken,\n    authStore.isAuthenticated,\n    authStore.isLoading,\n    authStore.error,\n    authStore.isInitialized,\n    login,\n    logout,\n    logoutAll,\n    refreshToken,\n    updateProfile,\n    changePassword,\n    authStore.clearError,\n    authStore.reset,\n  ]);\n}\n\n/**\n * Hook to use app store with provider context\n * Provides app state and actions\n */\nexport function useAppProvider() {\n  const appStore = useAppStoreContext();\n\n  const setTheme = useCallback((theme: 'light' | 'dark') => {\n    appStore.setTheme(theme);\n  }, [appStore]);\n\n  const toggleTheme = useCallback(() => {\n    appStore.toggleTheme();\n  }, [appStore]);\n\n  const setLanguage = useCallback((language: string) => {\n    appStore.setLanguage(language);\n  }, [appStore]);\n\n  const setSidebarCollapsed = useCallback((collapsed: boolean) => {\n    appStore.setSidebarCollapsed(collapsed);\n  }, [appStore]);\n\n  const toggleSidebar = useCallback(() => {\n    appStore.toggleSidebar();\n  }, [appStore]);\n\n  const setLoading = useCallback((loading: boolean) => {\n    appStore.setLoading(loading);\n  }, [appStore]);\n\n  const showNotification = useCallback((notification: { type: 'success' | 'error' | 'warning' | 'info'; message: string; description?: string }) => {\n    appStore.showNotification(notification);\n  }, [appStore]);\n\n  const hideNotification = useCallback(() => {\n    appStore.hideNotification();\n  }, [appStore]);\n\n  return useMemo(() => ({\n    // State\n    theme: appStore.theme,\n    language: appStore.language,\n    sidebarCollapsed: appStore.sidebarCollapsed,\n    isLoading: appStore.isLoading,\n    notification: appStore.notification,\n    isInitialized: appStore.isInitialized,\n\n    // Actions\n    setTheme,\n    toggleTheme,\n    setLanguage,\n    setSidebarCollapsed,\n    toggleSidebar,\n    setLoading,\n    showNotification,\n    hideNotification,\n    reset: appStore.reset,\n  }), [\n    appStore.theme,\n    appStore.language,\n    appStore.sidebarCollapsed,\n    appStore.isLoading,\n    appStore.notification,\n    appStore.isInitialized,\n    setTheme,\n    toggleTheme,\n    setLanguage,\n    setSidebarCollapsed,\n    toggleSidebar,\n    setLoading,\n    showNotification,\n    hideNotification,\n    appStore.reset,\n  ]);\n}\n\n/**\n * Hook to check if stores are available\n */\nexport function useStoreAvailability() {\n  const isAvailable = useIsStoreContextAvailable();\n\n  return useMemo(() => ({\n    isAvailable,\n    isStoreReady: isAvailable,\n  }), [isAvailable]);\n}\n\n/**\n * Hook to get all stores\n */\nexport function useStores() {\n  const context = useStoreContext();\n\n  return useMemo(() => ({\n    authStore: context.authStore,\n    appStore: context.appStore,\n  }), [context.authStore, context.appStore]);\n}\n\n/**\n * Hook for development/debugging purposes\n */\nexport function useStoreDebug() {\n  const { authStore, appStore } = useStores();\n\n  return useMemo(() => ({\n    authState: {\n      user: authStore.user,\n      isAuthenticated: authStore.isAuthenticated,\n      isLoading: authStore.isLoading,\n      error: authStore.error,\n      isInitialized: authStore.isInitialized,\n    } as AuthState,\n    appState: {\n      theme: appStore.theme,\n      language: appStore.language,\n      sidebarCollapsed: appStore.sidebarCollapsed,\n      isLoading: appStore.isLoading,\n      notification: appStore.notification,\n      isInitialized: appStore.isInitialized,\n    } as AppState,\n    actions: {\n      resetAuth: authStore.reset,\n      resetApp: appStore.reset,\n      clearAuthError: authStore.clearError,\n      hideNotification: appStore.hideNotification,\n    },\n  }), [authStore, appStore]);\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;AAID;AACA;;AAHA;;;AAeO,SAAS;;IACd,MAAM,YAAY,CAAA,GAAA,qIAAA,CAAA,sBAAmB,AAAD;IAEpC,MAAM,QAAQ,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8CAAE,OAAO;YAC/B,OAAO,UAAU,KAAK,CAAC;QACzB;6CAAG;QAAC;KAAU;IAEd,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;+CAAE;YACzB,OAAO,UAAU,MAAM;QACzB;8CAAG;QAAC;KAAU;IAEd,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAAE;YAC5B,OAAO,UAAU,SAAS;QAC5B;iDAAG;QAAC;KAAU;IAEd,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE;YAC/B,OAAO,UAAU,YAAY;QAC/B;oDAAG;QAAC;KAAU;IAEd,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE,OAAO;YACvC,OAAO,UAAU,aAAa,CAAC;QACjC;qDAAG;QAAC;KAAU;IAEd,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE,OAAO;YACxC,OAAO,UAAU,cAAc,CAAC;QAClC;sDAAG;QAAC;KAAU;IAEd,OAAO,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;mCAAE,IAAM,CAAC;gBACpB,QAAQ;gBACR,MAAM,UAAU,IAAI;gBACpB,OAAO,UAAU,KAAK;gBACtB,cAAc,UAAU,YAAY;gBACpC,iBAAiB,UAAU,eAAe;gBAC1C,WAAW,UAAU,SAAS;gBAC9B,OAAO,UAAU,KAAK;gBACtB,eAAe,UAAU,aAAa;gBAEtC,UAAU;gBACV;gBACA;gBACA;gBACA,cAAc;gBACd;gBACA;gBACA,YAAY,UAAU,UAAU;gBAChC,OAAO,UAAU,KAAK;YACxB,CAAC;kCAAG;QACF,UAAU,IAAI;QACd,UAAU,KAAK;QACf,UAAU,YAAY;QACtB,UAAU,eAAe;QACzB,UAAU,SAAS;QACnB,UAAU,KAAK;QACf,UAAU,aAAa;QACvB;QACA;QACA;QACA;QACA;QACA;QACA,UAAU,UAAU;QACpB,UAAU,KAAK;KAChB;AACH;GA/DgB;;QACI,qIAAA,CAAA,sBAAmB;;;AAoEhC,SAAS;;IACd,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,qBAAkB,AAAD;IAElC,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;gDAAE,CAAC;YAC5B,SAAS,QAAQ,CAAC;QACpB;+CAAG;QAAC;KAAS;IAEb,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAAE;YAC9B,SAAS,WAAW;QACtB;kDAAG;QAAC;KAAS;IAEb,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAAE,CAAC;YAC/B,SAAS,WAAW,CAAC;QACvB;kDAAG;QAAC;KAAS;IAEb,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2DAAE,CAAC;YACvC,SAAS,mBAAmB,CAAC;QAC/B;0DAAG;QAAC;KAAS;IAEb,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE;YAChC,SAAS,aAAa;QACxB;oDAAG;QAAC;KAAS;IAEb,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAAE,CAAC;YAC9B,SAAS,UAAU,CAAC;QACtB;iDAAG;QAAC;KAAS;IAEb,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAAE,CAAC;YACpC,SAAS,gBAAgB,CAAC;QAC5B;uDAAG;QAAC;KAAS;IAEb,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAAE;YACnC,SAAS,gBAAgB;QAC3B;uDAAG;QAAC;KAAS;IAEb,OAAO,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;kCAAE,IAAM,CAAC;gBACpB,QAAQ;gBACR,OAAO,SAAS,KAAK;gBACrB,UAAU,SAAS,QAAQ;gBAC3B,kBAAkB,SAAS,gBAAgB;gBAC3C,WAAW,SAAS,SAAS;gBAC7B,cAAc,SAAS,YAAY;gBACnC,eAAe,SAAS,aAAa;gBAErC,UAAU;gBACV;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA,OAAO,SAAS,KAAK;YACvB,CAAC;iCAAG;QACF,SAAS,KAAK;QACd,SAAS,QAAQ;QACjB,SAAS,gBAAgB;QACzB,SAAS,SAAS;QAClB,SAAS,YAAY;QACrB,SAAS,aAAa;QACtB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,SAAS,KAAK;KACf;AACH;IAvEgB;;QACG,qIAAA,CAAA,qBAAkB;;;AA2E9B,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,qIAAA,CAAA,6BAA0B,AAAD;IAE7C,OAAO,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;wCAAE,IAAM,CAAC;gBACpB;gBACA,cAAc;YAChB,CAAC;uCAAG;QAAC;KAAY;AACnB;IAPgB;;QACM,qIAAA,CAAA,6BAA0B;;;AAWzC,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD;IAE9B,OAAO,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;6BAAE,IAAM,CAAC;gBACpB,WAAW,QAAQ,SAAS;gBAC5B,UAAU,QAAQ,QAAQ;YAC5B,CAAC;4BAAG;QAAC,QAAQ,SAAS;QAAE,QAAQ,QAAQ;KAAC;AAC3C;IAPgB;;QACE,qIAAA,CAAA,kBAAe;;;AAW1B,SAAS;;IACd,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG;IAEhC,OAAO,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;iCAAE,IAAM,CAAC;gBACpB,WAAW;oBACT,MAAM,UAAU,IAAI;oBACpB,iBAAiB,UAAU,eAAe;oBAC1C,WAAW,UAAU,SAAS;oBAC9B,OAAO,UAAU,KAAK;oBACtB,eAAe,UAAU,aAAa;gBACxC;gBACA,UAAU;oBACR,OAAO,SAAS,KAAK;oBACrB,UAAU,SAAS,QAAQ;oBAC3B,kBAAkB,SAAS,gBAAgB;oBAC3C,WAAW,SAAS,SAAS;oBAC7B,cAAc,SAAS,YAAY;oBACnC,eAAe,SAAS,aAAa;gBACvC;gBACA,SAAS;oBACP,WAAW,UAAU,KAAK;oBAC1B,UAAU,SAAS,KAAK;oBACxB,gBAAgB,UAAU,UAAU;oBACpC,kBAAkB,SAAS,gBAAgB;gBAC7C;YACF,CAAC;gCAAG;QAAC;QAAW;KAAS;AAC3B;IA1BgB;;QACkB"}}, {"offset": {"line": 3239, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3245, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/stores/index.ts"], "sourcesContent": ["/**\n * Store Index - Central export for all stores\n * Provides barrel exports for all store modules\n */\n\n// Store types and interfaces\nexport * from './types';\n\n// Store utilities\nexport * from './utils';\n\n// Individual stores\nexport * from './auth-store';\nexport * from './auth-hooks';\nexport * from './auth-utils';\nexport * from './app-store';\nexport * from './app-hooks';\nexport * from './app-utils';\n\n// Store providers and context\nexport * from './store-provider';\nexport * from './store-context';\nexport * from './provider-hooks';\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,6BAA6B"}}, {"offset": {"line": 3264, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3289, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/lib/query-client.ts"], "sourcesContent": ["/**\n * TanStack Query Client Configuration\n * Centralized configuration for React Query client\n */\n\nimport { QueryClient, DefaultOptions } from '@tanstack/react-query';\n\n/**\n * Default query options for the application\n */\nconst defaultQueryOptions: DefaultOptions = {\n  queries: {\n    // Stale time - how long data is considered fresh (5 minutes)\n    staleTime: 5 * 60 * 1000,\n\n    // Cache time - how long data stays in cache when unused (10 minutes)\n    gcTime: 10 * 60 * 1000,\n\n    // Retry configuration\n    retry: (failureCount, error: any) => {\n      // Don't retry on 4xx errors (client errors)\n      if (error?.status >= 400 && error?.status < 500) {\n        return false;\n      }\n      // Retry up to 3 times for other errors\n      return failureCount < 3;\n    },\n\n    // Retry delay with exponential backoff\n    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),\n\n    // Refetch on window focus (disabled for CMS to avoid unnecessary requests)\n    refetchOnWindowFocus: false,\n\n    // Refetch on reconnect\n    refetchOnReconnect: true,\n\n    // Refetch on mount if data is stale\n    refetchOnMount: true,\n  },\n  mutations: {\n    // Retry mutations once on failure\n    retry: 1,\n\n    // Retry delay for mutations\n    retryDelay: 1000,\n  },\n};\n\n/**\n * Development-specific query options\n */\nconst developmentQueryOptions: DefaultOptions = {\n  queries: {\n    ...defaultQueryOptions.queries,\n    // Shorter stale time in development for faster feedback\n    staleTime: 1 * 60 * 1000, // 1 minute\n    // Shorter cache time in development\n    gcTime: 2 * 60 * 1000, // 2 minutes\n    // Enable refetch on window focus in development\n    refetchOnWindowFocus: true,\n  },\n  mutations: {\n    ...defaultQueryOptions.mutations,\n  },\n};\n\n/**\n * Production-specific query options\n */\nconst productionQueryOptions: DefaultOptions = {\n  queries: {\n    ...defaultQueryOptions.queries,\n    // Longer stale time in production for better performance\n    staleTime: 10 * 60 * 1000, // 10 minutes\n    // Longer cache time in production\n    gcTime: 30 * 60 * 1000, // 30 minutes\n  },\n  mutations: {\n    ...defaultQueryOptions.mutations,\n  },\n};\n\n/**\n * Get query options based on environment\n */\nfunction getQueryOptions(): DefaultOptions {\n  const isDevelopment = process.env.NODE_ENV === 'development';\n  return isDevelopment ? developmentQueryOptions : productionQueryOptions;\n}\n\n/**\n * Create and configure the QueryClient instance\n */\nexport function createQueryClient(): QueryClient {\n  return new QueryClient({\n    defaultOptions: getQueryOptions(),\n    logger: {\n      log: (message) => {\n        if (process.env.NODE_ENV === 'development') {\n          console.log(`[QueryClient] ${message}`);\n        }\n      },\n      warn: (message) => {\n        console.warn(`[QueryClient] ${message}`);\n      },\n      error: (message) => {\n        console.error(`[QueryClient] ${message}`);\n      },\n    },\n  });\n}\n\n/**\n * Singleton QueryClient instance\n */\nlet queryClient: QueryClient | undefined = undefined;\n\n/**\n * Get the singleton QueryClient instance\n */\nexport function getQueryClient(): QueryClient {\n  if (typeof window === 'undefined') {\n    // Server-side: always create a new client\n    return createQueryClient();\n  }\n\n  // Client-side: create client once and reuse\n  if (!queryClient) {\n    queryClient = createQueryClient();\n  }\n\n  return queryClient;\n}\n\n/**\n * Query client configuration constants\n */\nexport const QUERY_CONFIG = {\n  // Cache times\n  STALE_TIME: {\n    SHORT: 1 * 60 * 1000,      // 1 minute\n    MEDIUM: 5 * 60 * 1000,     // 5 minutes\n    LONG: 10 * 60 * 1000,      // 10 minutes\n    VERY_LONG: 30 * 60 * 1000, // 30 minutes\n  },\n\n  // Retry configuration\n  RETRY: {\n    NONE: 0,\n    ONCE: 1,\n    TWICE: 2,\n    DEFAULT: 3,\n  },\n\n  // Refetch intervals\n  REFETCH_INTERVAL: {\n    FAST: 30 * 1000,      // 30 seconds\n    MEDIUM: 60 * 1000,    // 1 minute\n    SLOW: 5 * 60 * 1000,  // 5 minutes\n  },\n} as const;\n\n/**\n * Query key factories for consistent key generation\n */\nexport const queryKeys = {\n  // System authentication\n  auth: {\n    all: ['auth'] as const,\n    profile: () => [...queryKeys.auth.all, 'profile'] as const,\n    users: () => [...queryKeys.auth.all, 'users'] as const,\n    user: (id: string) => [...queryKeys.auth.users(), id] as const,\n  },\n\n  // Football data\n  football: {\n    all: ['football'] as const,\n    leagues: () => [...queryKeys.football.all, 'leagues'] as const,\n    league: (id: string) => [...queryKeys.football.leagues(), id] as const,\n    teams: () => [...queryKeys.football.all, 'teams'] as const,\n    team: (id: string) => [...queryKeys.football.teams(), id] as const,\n    fixtures: () => [...queryKeys.football.all, 'fixtures'] as const,\n    fixture: (id: string) => [...queryKeys.football.fixtures(), id] as const,\n    sync: () => [...queryKeys.football.all, 'sync'] as const,\n    syncStatus: () => [...queryKeys.football.sync(), 'status'] as const,\n  },\n\n  // Broadcast links\n  broadcast: {\n    all: ['broadcast'] as const,\n    links: () => [...queryKeys.broadcast.all, 'links'] as const,\n    link: (id: string) => [...queryKeys.broadcast.links(), id] as const,\n    fixture: (fixtureId: string) => [...queryKeys.broadcast.all, 'fixture', fixtureId] as const,\n  },\n\n  // Health checks\n  health: {\n    all: ['health'] as const,\n    api: () => [...queryKeys.health.all, 'api'] as const,\n  },\n} as const;\n\n/**\n * Setup query error handling for a QueryClient instance\n */\nexport function setupQueryErrorHandling(queryClient: QueryClient) {\n  // This function can be used to setup global error handling\n  // For now, it's a placeholder that can be extended later\n  console.log('[QueryClient] Error handling setup completed');\n}\n\n/**\n * Utility functions for query management\n */\nexport const queryUtils = {\n  /**\n   * Invalidate all queries for a specific domain\n   */\n  invalidateAuth: (client: QueryClient) => {\n    return client.invalidateQueries({ queryKey: queryKeys.auth.all });\n  },\n\n  invalidateFootball: (client: QueryClient) => {\n    return client.invalidateQueries({ queryKey: queryKeys.football.all });\n  },\n\n  invalidateBroadcast: (client: QueryClient) => {\n    return client.invalidateQueries({ queryKey: queryKeys.broadcast.all });\n  },\n\n  /**\n   * Clear all cached data\n   */\n  clearAll: (client: QueryClient) => {\n    return client.clear();\n  },\n\n  /**\n   * Remove specific queries from cache\n   */\n  removeQueries: (client: QueryClient, queryKey: readonly unknown[]) => {\n    return client.removeQueries({ queryKey });\n  },\n\n  /**\n   * Prefetch data\n   */\n  prefetchAuth: (client: QueryClient) => {\n    // Prefetch user profile if authenticated\n    // Implementation will be added when auth hooks are ready\n  },\n} as const;\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;AAoFuB;AAlFxB;;AAEA;;CAEC,GACD,MAAM,sBAAsC;IAC1C,SAAS;QACP,6DAA6D;QAC7D,WAAW,IAAI,KAAK;QAEpB,qEAAqE;QACrE,QAAQ,KAAK,KAAK;QAElB,sBAAsB;QACtB,OAAO,CAAC,cAAc;YACpB,4CAA4C;YAC5C,IAAI,OAAO,UAAU,OAAO,OAAO,SAAS,KAAK;gBAC/C,OAAO;YACT;YACA,uCAAuC;YACvC,OAAO,eAAe;QACxB;QAEA,uCAAuC;QACvC,YAAY,CAAC,eAAiB,KAAK,GAAG,CAAC,OAAO,KAAK,cAAc;QAEjE,2EAA2E;QAC3E,sBAAsB;QAEtB,uBAAuB;QACvB,oBAAoB;QAEpB,oCAAoC;QACpC,gBAAgB;IAClB;IACA,WAAW;QACT,kCAAkC;QAClC,OAAO;QAEP,4BAA4B;QAC5B,YAAY;IACd;AACF;AAEA;;CAEC,GACD,MAAM,0BAA0C;IAC9C,SAAS;QACP,GAAG,oBAAoB,OAAO;QAC9B,wDAAwD;QACxD,WAAW,IAAI,KAAK;QACpB,oCAAoC;QACpC,QAAQ,IAAI,KAAK;QACjB,gDAAgD;QAChD,sBAAsB;IACxB;IACA,WAAW;QACT,GAAG,oBAAoB,SAAS;IAClC;AACF;AAEA;;CAEC,GACD,MAAM,yBAAyC;IAC7C,SAAS;QACP,GAAG,oBAAoB,OAAO;QAC9B,yDAAyD;QACzD,WAAW,KAAK,KAAK;QACrB,kCAAkC;QAClC,QAAQ,KAAK,KAAK;IACpB;IACA,WAAW;QACT,GAAG,oBAAoB,SAAS;IAClC;AACF;AAEA;;CAEC,GACD,SAAS;IACP,MAAM,gBAAgB,oDAAyB;IAC/C,OAAO,uCAAgB;AACzB;AAKO,SAAS;IACd,OAAO,IAAI,gLAAA,CAAA,cAAW,CAAC;QACrB,gBAAgB;QAChB,QAAQ;YACN,KAAK,CAAC;gBACJ,wCAA4C;oBAC1C,QAAQ,GAAG,CAAC,CAAC,cAAc,EAAE,SAAS;gBACxC;YACF;YACA,MAAM,CAAC;gBACL,QAAQ,IAAI,CAAC,CAAC,cAAc,EAAE,SAAS;YACzC;YACA,OAAO,CAAC;gBACN,QAAQ,KAAK,CAAC,CAAC,cAAc,EAAE,SAAS;YAC1C;QACF;IACF;AACF;AAEA;;CAEC,GACD,IAAI,cAAuC;AAKpC,SAAS;IACd,uCAAmC;;IAGnC;IAEA,4CAA4C;IAC5C,IAAI,CAAC,aAAa;QAChB,cAAc;IAChB;IAEA,OAAO;AACT;AAKO,MAAM,eAAe;IAC1B,cAAc;IACd,YAAY;QACV,OAAO,IAAI,KAAK;QAChB,QAAQ,IAAI,KAAK;QACjB,MAAM,KAAK,KAAK;QAChB,WAAW,KAAK,KAAK;IACvB;IAEA,sBAAsB;IACtB,OAAO;QACL,MAAM;QACN,MAAM;QACN,OAAO;QACP,SAAS;IACX;IAEA,oBAAoB;IACpB,kBAAkB;QAChB,MAAM,KAAK;QACX,QAAQ,KAAK;QACb,MAAM,IAAI,KAAK;IACjB;AACF;AAKO,MAAM,YAAY;IACvB,wBAAwB;IACxB,MAAM;QACJ,KAAK;YAAC;SAAO;QACb,SAAS,IAAM;mBAAI,UAAU,IAAI,CAAC,GAAG;gBAAE;aAAU;QACjD,OAAO,IAAM;mBAAI,UAAU,IAAI,CAAC,GAAG;gBAAE;aAAQ;QAC7C,MAAM,CAAC,KAAe;mBAAI,UAAU,IAAI,CAAC,KAAK;gBAAI;aAAG;IACvD;IAEA,gBAAgB;IAChB,UAAU;QACR,KAAK;YAAC;SAAW;QACjB,SAAS,IAAM;mBAAI,UAAU,QAAQ,CAAC,GAAG;gBAAE;aAAU;QACrD,QAAQ,CAAC,KAAe;mBAAI,UAAU,QAAQ,CAAC,OAAO;gBAAI;aAAG;QAC7D,OAAO,IAAM;mBAAI,UAAU,QAAQ,CAAC,GAAG;gBAAE;aAAQ;QACjD,MAAM,CAAC,KAAe;mBAAI,UAAU,QAAQ,CAAC,KAAK;gBAAI;aAAG;QACzD,UAAU,IAAM;mBAAI,UAAU,QAAQ,CAAC,GAAG;gBAAE;aAAW;QACvD,SAAS,CAAC,KAAe;mBAAI,UAAU,QAAQ,CAAC,QAAQ;gBAAI;aAAG;QAC/D,MAAM,IAAM;mBAAI,UAAU,QAAQ,CAAC,GAAG;gBAAE;aAAO;QAC/C,YAAY,IAAM;mBAAI,UAAU,QAAQ,CAAC,IAAI;gBAAI;aAAS;IAC5D;IAEA,kBAAkB;IAClB,WAAW;QACT,KAAK;YAAC;SAAY;QAClB,OAAO,IAAM;mBAAI,UAAU,SAAS,CAAC,GAAG;gBAAE;aAAQ;QAClD,MAAM,CAAC,KAAe;mBAAI,UAAU,SAAS,CAAC,KAAK;gBAAI;aAAG;QAC1D,SAAS,CAAC,YAAsB;mBAAI,UAAU,SAAS,CAAC,GAAG;gBAAE;gBAAW;aAAU;IACpF;IAEA,gBAAgB;IAChB,QAAQ;QACN,KAAK;YAAC;SAAS;QACf,KAAK,IAAM;mBAAI,UAAU,MAAM,CAAC,GAAG;gBAAE;aAAM;IAC7C;AACF;AAKO,SAAS,wBAAwB,WAAwB;IAC9D,2DAA2D;IAC3D,yDAAyD;IACzD,QAAQ,GAAG,CAAC;AACd;AAKO,MAAM,aAAa;IACxB;;GAEC,GACD,gBAAgB,CAAC;QACf,OAAO,OAAO,iBAAiB,CAAC;YAAE,UAAU,UAAU,IAAI,CAAC,GAAG;QAAC;IACjE;IAEA,oBAAoB,CAAC;QACnB,OAAO,OAAO,iBAAiB,CAAC;YAAE,UAAU,UAAU,QAAQ,CAAC,GAAG;QAAC;IACrE;IAEA,qBAAqB,CAAC;QACpB,OAAO,OAAO,iBAAiB,CAAC;YAAE,UAAU,UAAU,SAAS,CAAC,GAAG;QAAC;IACtE;IAEA;;GAEC,GACD,UAAU,CAAC;QACT,OAAO,OAAO,KAAK;IACrB;IAEA;;GAEC,GACD,eAAe,CAAC,QAAqB;QACnC,OAAO,OAAO,aAAa,CAAC;YAAE;QAAS;IACzC;IAEA;;GAEC,GACD,cAAc,CAAC;IACb,yCAAyC;IACzC,yDAAyD;IAC3D;AACF"}}, {"offset": {"line": 3557, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3563, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/lib/query-devtools.tsx"], "sourcesContent": ["/**\n * Query DevTools Configuration\n * Development tools for TanStack Query debugging\n */\n\n'use client';\n\nimport React from 'react';\n\n/**\n * Lazy-loaded React Query DevTools\n * Only loads in development mode\n */\nconst ReactQueryDevtools = React.lazy(() =>\n  import('@tanstack/react-query-devtools').then((module) => ({\n    default: module.ReactQueryDevtools,\n  }))\n);\n\n/**\n * DevTools component with error boundary\n */\nexport function QueryDevTools() {\n  // Only render in development\n  if (process.env.NODE_ENV !== 'development') {\n    return null;\n  }\n\n  return (\n    <React.Suspense fallback={null}>\n      <ReactQueryDevtools\n        initialIsOpen={false}\n        position=\"bottom-right\"\n        buttonPosition=\"bottom-right\"\n        panelProps={{\n          style: {\n            zIndex: 99999,\n          },\n        }}\n      />\n    </React.Suspense>\n  );\n}\n\n/**\n * Query DevTools with error boundary\n */\nexport function QueryDevToolsWithErrorBoundary() {\n  return (\n    <QueryDevToolsErrorBoundary>\n      <QueryDevTools />\n    </QueryDevToolsErrorBoundary>\n  );\n}\n\n/**\n * Error boundary for DevTools\n */\nclass QueryDevToolsErrorBoundary extends React.Component<\n  { children: React.ReactNode },\n  { hasError: boolean }\n> {\n  constructor(props: { children: React.ReactNode }) {\n    super(props);\n    this.state = { hasError: false };\n  }\n\n  static getDerivedStateFromError(): { hasError: boolean } {\n    return { hasError: true };\n  }\n\n  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {\n    console.error('[QueryDevTools Error]', error, errorInfo);\n  }\n\n  render() {\n    if (this.state.hasError) {\n      return null; // Silently fail in production\n    }\n\n    return this.props.children;\n  }\n}\n\n/**\n * Development utilities for query debugging\n */\nexport const queryDevUtils = {\n  /**\n   * Log query information\n   */\n  logQuery: (queryKey: unknown[], data: unknown, status: string) => {\n    if (process.env.NODE_ENV === 'development') {\n      console.group(`[Query] ${queryKey.join(' → ')}`);\n      console.log('Status:', status);\n      console.log('Data:', data);\n      console.log('Key:', queryKey);\n      console.groupEnd();\n    }\n  },\n\n  /**\n   * Log mutation information\n   */\n  logMutation: (mutationKey: unknown[], variables: unknown, status: string) => {\n    if (process.env.NODE_ENV === 'development') {\n      console.group(`[Mutation] ${mutationKey?.join(' → ') || 'Unknown'}`);\n      console.log('Status:', status);\n      console.log('Variables:', variables);\n      console.log('Key:', mutationKey);\n      console.groupEnd();\n    }\n  },\n\n  /**\n   * Log cache operations\n   */\n  logCacheOperation: (operation: string, queryKey: unknown[], data?: unknown) => {\n    if (process.env.NODE_ENV === 'development') {\n      console.log(`[Cache ${operation}]`, {\n        key: queryKey,\n        data: data ? '✓' : '✗',\n      });\n    }\n  },\n\n  /**\n   * Performance monitoring\n   */\n  measureQueryTime: <T>(\n    queryKey: unknown[],\n    queryFn: () => Promise<T>\n  ): Promise<T> => {\n    if (process.env.NODE_ENV !== 'development') {\n      return queryFn();\n    }\n\n    const startTime = performance.now();\n    const label = `Query: ${queryKey.join(' → ')}`;\n\n    console.time(label);\n\n    return queryFn()\n      .then((result) => {\n        const endTime = performance.now();\n        console.timeEnd(label);\n        console.log(`[Query Performance] ${label}: ${(endTime - startTime).toFixed(2)}ms`);\n        return result;\n      })\n      .catch((error) => {\n        const endTime = performance.now();\n        console.timeEnd(label);\n        console.log(`[Query Performance] ${label}: ${(endTime - startTime).toFixed(2)}ms (ERROR)`);\n        throw error;\n      });\n  },\n};\n\n/**\n * Query debugging hooks for development\n */\nexport const useQueryDebug = () => {\n  if (process.env.NODE_ENV !== 'development') {\n    return {\n      logQuery: () => {},\n      logMutation: () => {},\n      logCacheOperation: () => {},\n    };\n  }\n\n  return queryDevUtils;\n};\n\n/**\n * Development-only query inspector\n */\nexport function QueryInspector({ \n  queryKey, \n  data, \n  status, \n  error \n}: {\n  queryKey: unknown[];\n  data: unknown;\n  status: string;\n  error: unknown;\n}) {\n  if (process.env.NODE_ENV !== 'development') {\n    return null;\n  }\n\n  return (\n    <div style={{\n      position: 'fixed',\n      top: 10,\n      right: 10,\n      background: 'rgba(0, 0, 0, 0.8)',\n      color: 'white',\n      padding: '10px',\n      borderRadius: '5px',\n      fontSize: '12px',\n      fontFamily: 'monospace',\n      zIndex: 10000,\n      maxWidth: '300px',\n      maxHeight: '200px',\n      overflow: 'auto',\n    }}>\n      <div><strong>Query Key:</strong> {JSON.stringify(queryKey)}</div>\n      <div><strong>Status:</strong> {status}</div>\n      {error && <div><strong>Error:</strong> {String(error)}</div>}\n      <details>\n        <summary>Data</summary>\n        <pre>{JSON.stringify(data, null, 2)}</pre>\n      </details>\n    </div>\n  );\n}\n\n/**\n * Development query stats component\n */\nexport function QueryStats() {\n  if (process.env.NODE_ENV !== 'development') {\n    return null;\n  }\n\n  // This would integrate with QueryClient to show stats\n  // For now, just a placeholder\n  return (\n    <div style={{\n      position: 'fixed',\n      bottom: 10,\n      left: 10,\n      background: 'rgba(0, 0, 0, 0.8)',\n      color: 'white',\n      padding: '10px',\n      borderRadius: '5px',\n      fontSize: '12px',\n      fontFamily: 'monospace',\n      zIndex: 10000,\n    }}>\n      <div>Query Stats (Dev Mode)</div>\n      <div>Active Queries: -</div>\n      <div>Cache Size: -</div>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;AAID;AAiBM;AAnBN;;;AAIA;;;CAGC,GACD,MAAM,mCAAqB,6JAAA,CAAA,UAAK,CAAC,IAAI,CAAC,IACpC,mKAAyC,IAAI,CAAC,CAAC,SAAW,CAAC;YACzD,SAAS,OAAO,kBAAkB;QACpC,CAAC;KAHG;AASC,SAAS;IACd,6BAA6B;IAC7B,uCAA4C;;IAE5C;IAEA,qBACE,6LAAC,6JAAA,CAAA,UAAK,CAAC,QAAQ;QAAC,UAAU;kBACxB,cAAA,6LAAC;YACC,eAAe;YACf,UAAS;YACT,gBAAe;YACf,YAAY;gBACV,OAAO;oBACL,QAAQ;gBACV;YACF;;;;;;;;;;;AAIR;MApBgB;AAyBT,SAAS;IACd,qBACE,6LAAC;kBACC,cAAA,6LAAC;;;;;;;;;;AAGP;MANgB;AAQhB;;CAEC,GACD,MAAM,mCAAmC,6JAAA,CAAA,UAAK,CAAC,SAAS;IAItD,YAAY,KAAoC,CAAE;QAChD,KAAK,CAAC;QACN,IAAI,CAAC,KAAK,GAAG;YAAE,UAAU;QAAM;IACjC;IAEA,OAAO,2BAAkD;QACvD,OAAO;YAAE,UAAU;QAAK;IAC1B;IAEA,kBAAkB,KAAY,EAAE,SAA0B,EAAE;QAC1D,QAAQ,KAAK,CAAC,yBAAyB,OAAO;IAChD;IAEA,SAAS;QACP,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;YACvB,OAAO,MAAM,8BAA8B;QAC7C;QAEA,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;IAC5B;AACF;AAKO,MAAM,gBAAgB;IAC3B;;GAEC,GACD,UAAU,CAAC,UAAqB,MAAe;QAC7C,wCAA4C;YAC1C,QAAQ,KAAK,CAAC,CAAC,QAAQ,EAAE,SAAS,IAAI,CAAC,QAAQ;YAC/C,QAAQ,GAAG,CAAC,WAAW;YACvB,QAAQ,GAAG,CAAC,SAAS;YACrB,QAAQ,GAAG,CAAC,QAAQ;YACpB,QAAQ,QAAQ;QAClB;IACF;IAEA;;GAEC,GACD,aAAa,CAAC,aAAwB,WAAoB;QACxD,wCAA4C;YAC1C,QAAQ,KAAK,CAAC,CAAC,WAAW,EAAE,aAAa,KAAK,UAAU,WAAW;YACnE,QAAQ,GAAG,CAAC,WAAW;YACvB,QAAQ,GAAG,CAAC,cAAc;YAC1B,QAAQ,GAAG,CAAC,QAAQ;YACpB,QAAQ,QAAQ;QAClB;IACF;IAEA;;GAEC,GACD,mBAAmB,CAAC,WAAmB,UAAqB;QAC1D,wCAA4C;YAC1C,QAAQ,GAAG,CAAC,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC,EAAE;gBAClC,KAAK;gBACL,MAAM,OAAO,MAAM;YACrB;QACF;IACF;IAEA;;GAEC,GACD,kBAAkB,CAChB,UACA;QAEA,uCAA4C;;QAE5C;QAEA,MAAM,YAAY,YAAY,GAAG;QACjC,MAAM,QAAQ,CAAC,OAAO,EAAE,SAAS,IAAI,CAAC,QAAQ;QAE9C,QAAQ,IAAI,CAAC;QAEb,OAAO,UACJ,IAAI,CAAC,CAAC;YACL,MAAM,UAAU,YAAY,GAAG;YAC/B,QAAQ,OAAO,CAAC;YAChB,QAAQ,GAAG,CAAC,CAAC,oBAAoB,EAAE,MAAM,EAAE,EAAE,CAAC,UAAU,SAAS,EAAE,OAAO,CAAC,GAAG,EAAE,CAAC;YACjF,OAAO;QACT,GACC,KAAK,CAAC,CAAC;YACN,MAAM,UAAU,YAAY,GAAG;YAC/B,QAAQ,OAAO,CAAC;YAChB,QAAQ,GAAG,CAAC,CAAC,oBAAoB,EAAE,MAAM,EAAE,EAAE,CAAC,UAAU,SAAS,EAAE,OAAO,CAAC,GAAG,UAAU,CAAC;YACzF,MAAM;QACR;IACJ;AACF;AAKO,MAAM,gBAAgB;IAC3B,uCAA4C;;IAM5C;IAEA,OAAO;AACT;AAKO,SAAS,eAAe,EAC7B,QAAQ,EACR,IAAI,EACJ,MAAM,EACN,KAAK,EAMN;IACC,uCAA4C;;IAE5C;IAEA,qBACE,6LAAC;QAAI,OAAO;YACV,UAAU;YACV,KAAK;YACL,OAAO;YACP,YAAY;YACZ,OAAO;YACP,SAAS;YACT,cAAc;YACd,UAAU;YACV,YAAY;YACZ,QAAQ;YACR,UAAU;YACV,WAAW;YACX,UAAU;QACZ;;0BACE,6LAAC;;kCAAI,6LAAC;kCAAO;;;;;;oBAAmB;oBAAE,KAAK,SAAS,CAAC;;;;;;;0BACjD,6LAAC;;kCAAI,6LAAC;kCAAO;;;;;;oBAAgB;oBAAE;;;;;;;YAC9B,uBAAS,6LAAC;;kCAAI,6LAAC;kCAAO;;;;;;oBAAe;oBAAE,OAAO;;;;;;;0BAC/C,6LAAC;;kCACC,6LAAC;kCAAQ;;;;;;kCACT,6LAAC;kCAAK,KAAK,SAAS,CAAC,MAAM,MAAM;;;;;;;;;;;;;;;;;;AAIzC;MAxCgB;AA6CT,SAAS;IACd,uCAA4C;;IAE5C;IAEA,sDAAsD;IACtD,8BAA8B;IAC9B,qBACE,6LAAC;QAAI,OAAO;YACV,UAAU;YACV,QAAQ;YACR,MAAM;YACN,YAAY;YACZ,OAAO;YACP,SAAS;YACT,cAAc;YACd,UAAU;YACV,YAAY;YACZ,QAAQ;QACV;;0BACE,6LAAC;0BAAI;;;;;;0BACL,6LAAC;0BAAI;;;;;;0BACL,6LAAC;0BAAI;;;;;;;;;;;;AAGX;MAzBgB"}}, {"offset": {"line": 3874, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3880, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/lib/query-provider.tsx"], "sourcesContent": ["/**\n * Query Provider Component\n * Wrapper for QueryClientProvider with Next.js integration\n */\n\n'use client';\n\nimport React, { ReactNode } from 'react';\nimport { QueryClient, QueryClientProvider } from '@tanstack/react-query';\nimport { getQueryClient, setupQueryErrorHandling } from './query-client';\nimport { QueryDevToolsWithErrorBoundary } from './query-devtools';\n\n/**\n * Query provider props\n */\ninterface QueryProviderProps {\n  children: ReactNode;\n}\n\n/**\n * Query provider component\n * Provides QueryClient to the application with proper SSR handling\n */\nexport function QueryProvider({ children }: QueryProviderProps) {\n  // Create query client instance (singleton on client, new on server)\n  const [queryClient] = React.useState(() => {\n    const client = getQueryClient();\n\n    // Setup error handling\n    setupQueryErrorHandling(client);\n\n    return client;\n  });\n\n  return (\n    <QueryClientProvider client={queryClient}>\n      {children}\n      {process.env.NODE_ENV === 'development' && (\n        <QueryDevToolsWithErrorBoundary />\n      )}\n    </QueryClientProvider>\n  );\n}\n\n/**\n * Query provider with error boundary\n */\nexport function QueryProviderWithErrorBoundary({ children }: QueryProviderProps) {\n  return (\n    <QueryProviderErrorBoundary>\n      <QueryProvider>\n        {children}\n      </QueryProvider>\n    </QueryProviderErrorBoundary>\n  );\n}\n\n/**\n * Error boundary for Query Provider\n */\nclass QueryProviderErrorBoundary extends React.Component<\n  { children: ReactNode },\n  { hasError: boolean; error?: Error }\n> {\n  constructor(props: { children: ReactNode }) {\n    super(props);\n    this.state = { hasError: false };\n  }\n\n  static getDerivedStateFromError(error: Error): { hasError: boolean; error: Error } {\n    return { hasError: true, error };\n  }\n\n  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {\n    console.error('[QueryProvider Error]', error, errorInfo);\n\n    // Log to external service in production\n    if (process.env.NODE_ENV === 'production') {\n      // TODO: Integrate with error reporting service\n    }\n  }\n\n  render() {\n    if (this.state.hasError) {\n      return (\n        <QueryProviderErrorFallback\n          error={this.state.error}\n          onRetry={() => this.setState({ hasError: false, error: undefined })}\n        />\n      );\n    }\n\n    return this.props.children;\n  }\n}\n\n/**\n * Error fallback component for Query Provider\n */\nfunction QueryProviderErrorFallback({\n  error,\n  onRetry\n}: {\n  error?: Error;\n  onRetry: () => void;\n}) {\n  return (\n    <div style={{\n      display: 'flex',\n      flexDirection: 'column',\n      alignItems: 'center',\n      justifyContent: 'center',\n      minHeight: '100vh',\n      padding: '20px',\n      textAlign: 'center',\n      fontFamily: 'system-ui, sans-serif',\n    }}>\n      <h1 style={{ color: '#dc2626', marginBottom: '16px' }}>\n        Query Provider Error\n      </h1>\n      <p style={{ color: '#6b7280', marginBottom: '24px', maxWidth: '500px' }}>\n        An error occurred while initializing the query system. This might be due to a\n        network issue or a configuration problem.\n      </p>\n      {error && process.env.NODE_ENV === 'development' && (\n        <details style={{\n          marginBottom: '24px',\n          padding: '16px',\n          backgroundColor: '#f3f4f6',\n          borderRadius: '8px',\n          textAlign: 'left',\n          maxWidth: '600px',\n          width: '100%',\n        }}>\n          <summary style={{ cursor: 'pointer', fontWeight: 'bold' }}>\n            Error Details (Development)\n          </summary>\n          <pre style={{\n            marginTop: '12px',\n            fontSize: '12px',\n            overflow: 'auto',\n            whiteSpace: 'pre-wrap',\n          }}>\n            {error.message}\n            {error.stack && `\\n\\n${error.stack}`}\n          </pre>\n        </details>\n      )}\n      <button\n        onClick={onRetry}\n        style={{\n          padding: '12px 24px',\n          backgroundColor: '#3b82f6',\n          color: 'white',\n          border: 'none',\n          borderRadius: '6px',\n          cursor: 'pointer',\n          fontSize: '16px',\n        }}\n      >\n        Retry\n      </button>\n    </div>\n  );\n}\n\n/**\n * HOC to wrap components with Query Provider\n */\nexport function withQueryProvider<P extends object>(\n  Component: React.ComponentType<P>\n) {\n  const WrappedComponent = (props: P) => (\n    <QueryProvider>\n      <Component {...props} />\n    </QueryProvider>\n  );\n\n  WrappedComponent.displayName = `withQueryProvider(${Component.displayName || Component.name})`;\n\n  return WrappedComponent;\n}\n\n/**\n * Query provider utilities\n */\nexport const QueryProviderUtils = {\n  /**\n   * Check if QueryClient is available\n   */\n  isQueryClientAvailable: (): boolean => {\n    try {\n      getQueryClient();\n      return true;\n    } catch {\n      return false;\n    }\n  },\n\n  /**\n   * Get current QueryClient instance\n   */\n  getCurrentQueryClient: (): QueryClient | null => {\n    try {\n      return getQueryClient();\n    } catch {\n      return null;\n    }\n  },\n\n  /**\n   * Reset QueryClient (development only)\n   */\n  resetQueryClient: (): void => {\n    if (process.env.NODE_ENV === 'development') {\n      const client = QueryProviderUtils.getCurrentQueryClient();\n      if (client) {\n        client.clear();\n        console.log('[Dev] QueryClient reset');\n      }\n    }\n  },\n} as const;\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;AAID;AAEA;AACA;AA4MQ;AA9MR;;;AAHA;;;;;AAkBO,SAAS,cAAc,EAAE,QAAQ,EAAsB;;IAC5D,oEAAoE;IACpE,MAAM,CAAC,YAAY,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ;kCAAC;YACnC,MAAM,SAAS,CAAA,GAAA,gIAAA,CAAA,iBAAc,AAAD;YAE5B,uBAAuB;YACvB,CAAA,GAAA,gIAAA,CAAA,0BAAuB,AAAD,EAAE;YAExB,OAAO;QACT;;IAEA,qBACE,6LAAC,yLAAA,CAAA,sBAAmB;QAAC,QAAQ;;YAC1B;YACA,oDAAyB,+BACxB,6LAAC,mIAAA,CAAA,iCAA8B;;;;;;;;;;;AAIvC;GAnBgB;KAAA;AAwBT,SAAS,+BAA+B,EAAE,QAAQ,EAAsB;IAC7E,qBACE,6LAAC;kBACC,cAAA,6LAAC;sBACE;;;;;;;;;;;AAIT;MARgB;AAUhB;;CAEC,GACD,MAAM,mCAAmC,6JAAA,CAAA,UAAK,CAAC,SAAS;IAItD,YAAY,KAA8B,CAAE;QAC1C,KAAK,CAAC;QACN,IAAI,CAAC,KAAK,GAAG;YAAE,UAAU;QAAM;IACjC;IAEA,OAAO,yBAAyB,KAAY,EAAuC;QACjF,OAAO;YAAE,UAAU;YAAM;QAAM;IACjC;IAEA,kBAAkB,KAAY,EAAE,SAA0B,EAAE;QAC1D,QAAQ,KAAK,CAAC,yBAAyB,OAAO;QAE9C,wCAAwC;QACxC,IAAI,oDAAyB,cAAc;QACzC,+CAA+C;QACjD;IACF;IAEA,SAAS;QACP,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;YACvB,qBACE,6LAAC;gBACC,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK;gBACvB,SAAS,IAAM,IAAI,CAAC,QAAQ,CAAC;wBAAE,UAAU;wBAAO,OAAO;oBAAU;;;;;;QAGvE;QAEA,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;IAC5B;AACF;AAEA;;CAEC,GACD,SAAS,2BAA2B,EAClC,KAAK,EACL,OAAO,EAIR;IACC,qBACE,6LAAC;QAAI,OAAO;YACV,SAAS;YACT,eAAe;YACf,YAAY;YACZ,gBAAgB;YAChB,WAAW;YACX,SAAS;YACT,WAAW;YACX,YAAY;QACd;;0BACE,6LAAC;gBAAG,OAAO;oBAAE,OAAO;oBAAW,cAAc;gBAAO;0BAAG;;;;;;0BAGvD,6LAAC;gBAAE,OAAO;oBAAE,OAAO;oBAAW,cAAc;oBAAQ,UAAU;gBAAQ;0BAAG;;;;;;YAIxE,SAAS,oDAAyB,+BACjC,6LAAC;gBAAQ,OAAO;oBACd,cAAc;oBACd,SAAS;oBACT,iBAAiB;oBACjB,cAAc;oBACd,WAAW;oBACX,UAAU;oBACV,OAAO;gBACT;;kCACE,6LAAC;wBAAQ,OAAO;4BAAE,QAAQ;4BAAW,YAAY;wBAAO;kCAAG;;;;;;kCAG3D,6LAAC;wBAAI,OAAO;4BACV,WAAW;4BACX,UAAU;4BACV,UAAU;4BACV,YAAY;wBACd;;4BACG,MAAM,OAAO;4BACb,MAAM,KAAK,IAAI,CAAC,IAAI,EAAE,MAAM,KAAK,EAAE;;;;;;;;;;;;;0BAI1C,6LAAC;gBACC,SAAS;gBACT,OAAO;oBACL,SAAS;oBACT,iBAAiB;oBACjB,OAAO;oBACP,QAAQ;oBACR,cAAc;oBACd,QAAQ;oBACR,UAAU;gBACZ;0BACD;;;;;;;;;;;;AAKP;MAjES;AAsEF,SAAS,kBACd,SAAiC;IAEjC,MAAM,mBAAmB,CAAC,sBACxB,6LAAC;sBACC,cAAA,6LAAC;gBAAW,GAAG,KAAK;;;;;;;;;;;IAIxB,iBAAiB,WAAW,GAAG,CAAC,kBAAkB,EAAE,UAAU,WAAW,IAAI,UAAU,IAAI,CAAC,CAAC,CAAC;IAE9F,OAAO;AACT;AAKO,MAAM,qBAAqB;IAChC;;GAEC,GACD,wBAAwB;QACtB,IAAI;YACF,CAAA,GAAA,gIAAA,CAAA,iBAAc,AAAD;YACb,OAAO;QACT,EAAE,OAAM;YACN,OAAO;QACT;IACF;IAEA;;GAEC,GACD,uBAAuB;QACrB,IAAI;YACF,OAAO,CAAA,GAAA,gIAAA,CAAA,iBAAc,AAAD;QACtB,EAAE,OAAM;YACN,OAAO;QACT;IACF;IAEA;;GAEC,GACD,kBAAkB;QAChB,wCAA4C;YAC1C,MAAM,SAAS,mBAAmB,qBAAqB;YACvD,IAAI,QAAQ;gBACV,OAAO,KAAK;gBACZ,QAAQ,GAAG,CAAC;YACd;QACF;IACF;AACF"}}, {"offset": {"line": 4149, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4155, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/theme/config.ts"], "sourcesContent": ["/**\n * Ant Design Theme Configuration\n * Custom theme configuration for APISportsGame CMS\n */\n\nimport type { ThemeConfig } from 'antd';\n\n/**\n * Color palette for the CMS\n */\nexport const colors = {\n  // Primary colors (Sports theme - Blue)\n  primary: {\n    50: '#eff6ff',\n    100: '#dbeafe',\n    200: '#bfdbfe',\n    300: '#93c5fd',\n    400: '#60a5fa',\n    500: '#3b82f6', // Main primary\n    600: '#2563eb',\n    700: '#1d4ed8',\n    800: '#1e40af',\n    900: '#1e3a8a',\n  },\n  \n  // Success colors (Green)\n  success: {\n    50: '#f0fdf4',\n    100: '#dcfce7',\n    200: '#bbf7d0',\n    300: '#86efac',\n    400: '#4ade80',\n    500: '#22c55e', // Main success\n    600: '#16a34a',\n    700: '#15803d',\n    800: '#166534',\n    900: '#14532d',\n  },\n  \n  // Warning colors (Orange)\n  warning: {\n    50: '#fffbeb',\n    100: '#fef3c7',\n    200: '#fde68a',\n    300: '#fcd34d',\n    400: '#fbbf24',\n    500: '#f59e0b', // Main warning\n    600: '#d97706',\n    700: '#b45309',\n    800: '#92400e',\n    900: '#78350f',\n  },\n  \n  // Error colors (Red)\n  error: {\n    50: '#fef2f2',\n    100: '#fee2e2',\n    200: '#fecaca',\n    300: '#fca5a5',\n    400: '#f87171',\n    500: '#ef4444', // Main error\n    600: '#dc2626',\n    700: '#b91c1c',\n    800: '#991b1b',\n    900: '#7f1d1d',\n  },\n  \n  // Neutral colors (Gray)\n  neutral: {\n    50: '#f9fafb',\n    100: '#f3f4f6',\n    200: '#e5e7eb',\n    300: '#d1d5db',\n    400: '#9ca3af',\n    500: '#6b7280',\n    600: '#4b5563',\n    700: '#374151',\n    800: '#1f2937',\n    900: '#111827',\n  },\n} as const;\n\n/**\n * Light theme configuration\n */\nexport const lightTheme: ThemeConfig = {\n  token: {\n    // Color tokens\n    colorPrimary: colors.primary[500],\n    colorSuccess: colors.success[500],\n    colorWarning: colors.warning[500],\n    colorError: colors.error[500],\n    colorInfo: colors.primary[500],\n    \n    // Background colors\n    colorBgContainer: '#ffffff',\n    colorBgElevated: '#ffffff',\n    colorBgLayout: colors.neutral[50],\n    colorBgSpotlight: colors.neutral[100],\n    \n    // Text colors\n    colorText: colors.neutral[900],\n    colorTextSecondary: colors.neutral[600],\n    colorTextTertiary: colors.neutral[500],\n    colorTextQuaternary: colors.neutral[400],\n    \n    // Border colors\n    colorBorder: colors.neutral[200],\n    colorBorderSecondary: colors.neutral[100],\n    \n    // Typography\n    fontFamily: '\"Inter\", -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, sans-serif',\n    fontSize: 14,\n    fontSizeHeading1: 32,\n    fontSizeHeading2: 24,\n    fontSizeHeading3: 20,\n    fontSizeHeading4: 16,\n    fontSizeHeading5: 14,\n    \n    // Layout\n    borderRadius: 8,\n    borderRadiusLG: 12,\n    borderRadiusSM: 6,\n    borderRadiusXS: 4,\n    \n    // Spacing\n    padding: 16,\n    paddingLG: 24,\n    paddingSM: 12,\n    paddingXS: 8,\n    paddingXXS: 4,\n    \n    margin: 16,\n    marginLG: 24,\n    marginSM: 12,\n    marginXS: 8,\n    marginXXS: 4,\n    \n    // Shadows\n    boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',\n    boxShadowSecondary: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',\n    boxShadowTertiary: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',\n    \n    // Motion\n    motionDurationFast: '0.1s',\n    motionDurationMid: '0.2s',\n    motionDurationSlow: '0.3s',\n    \n    // Z-index\n    zIndexBase: 0,\n    zIndexPopupBase: 1000,\n  },\n  \n  components: {\n    // Layout components\n    Layout: {\n      headerBg: '#ffffff',\n      headerHeight: 64,\n      headerPadding: '0 24px',\n      siderBg: '#ffffff',\n      triggerBg: colors.neutral[100],\n      triggerColor: colors.neutral[600],\n    },\n    \n    // Menu component\n    Menu: {\n      itemBg: 'transparent',\n      itemSelectedBg: colors.primary[50],\n      itemSelectedColor: colors.primary[600],\n      itemHoverBg: colors.neutral[50],\n      itemHoverColor: colors.neutral[900],\n      itemActiveBg: colors.primary[100],\n      subMenuItemBg: 'transparent',\n    },\n    \n    // Button component\n    Button: {\n      borderRadius: 8,\n      controlHeight: 40,\n      controlHeightLG: 48,\n      controlHeightSM: 32,\n      paddingInline: 16,\n      paddingInlineLG: 20,\n      paddingInlineSM: 12,\n    },\n    \n    // Input component\n    Input: {\n      borderRadius: 8,\n      controlHeight: 40,\n      controlHeightLG: 48,\n      controlHeightSM: 32,\n      paddingInline: 12,\n    },\n    \n    // Table component\n    Table: {\n      headerBg: colors.neutral[50],\n      headerColor: colors.neutral[700],\n      rowHoverBg: colors.neutral[25],\n      borderColor: colors.neutral[200],\n    },\n    \n    // Card component\n    Card: {\n      headerBg: 'transparent',\n      borderRadiusLG: 12,\n      paddingLG: 24,\n    },\n    \n    // Modal component\n    Modal: {\n      borderRadiusLG: 12,\n      paddingLG: 24,\n    },\n    \n    // Notification component\n    Notification: {\n      borderRadiusLG: 12,\n      paddingLG: 16,\n    },\n    \n    // Message component\n    Message: {\n      borderRadiusLG: 8,\n      paddingLG: 12,\n    },\n  },\n};\n\n/**\n * Dark theme configuration\n */\nexport const darkTheme: ThemeConfig = {\n  token: {\n    // Color tokens\n    colorPrimary: colors.primary[400],\n    colorSuccess: colors.success[400],\n    colorWarning: colors.warning[400],\n    colorError: colors.error[400],\n    colorInfo: colors.primary[400],\n    \n    // Background colors\n    colorBgContainer: colors.neutral[800],\n    colorBgElevated: colors.neutral[700],\n    colorBgLayout: colors.neutral[900],\n    colorBgSpotlight: colors.neutral[800],\n    \n    // Text colors\n    colorText: colors.neutral[100],\n    colorTextSecondary: colors.neutral[300],\n    colorTextTertiary: colors.neutral[400],\n    colorTextQuaternary: colors.neutral[500],\n    \n    // Border colors\n    colorBorder: colors.neutral[600],\n    colorBorderSecondary: colors.neutral[700],\n    \n    // Typography (inherit from light theme)\n    fontFamily: lightTheme.token?.fontFamily,\n    fontSize: lightTheme.token?.fontSize,\n    fontSizeHeading1: lightTheme.token?.fontSizeHeading1,\n    fontSizeHeading2: lightTheme.token?.fontSizeHeading2,\n    fontSizeHeading3: lightTheme.token?.fontSizeHeading3,\n    fontSizeHeading4: lightTheme.token?.fontSizeHeading4,\n    fontSizeHeading5: lightTheme.token?.fontSizeHeading5,\n    \n    // Layout (inherit from light theme)\n    borderRadius: lightTheme.token?.borderRadius,\n    borderRadiusLG: lightTheme.token?.borderRadiusLG,\n    borderRadiusSM: lightTheme.token?.borderRadiusSM,\n    borderRadiusXS: lightTheme.token?.borderRadiusXS,\n    \n    // Spacing (inherit from light theme)\n    padding: lightTheme.token?.padding,\n    paddingLG: lightTheme.token?.paddingLG,\n    paddingSM: lightTheme.token?.paddingSM,\n    paddingXS: lightTheme.token?.paddingXS,\n    paddingXXS: lightTheme.token?.paddingXXS,\n    \n    margin: lightTheme.token?.margin,\n    marginLG: lightTheme.token?.marginLG,\n    marginSM: lightTheme.token?.marginSM,\n    marginXS: lightTheme.token?.marginXS,\n    marginXXS: lightTheme.token?.marginXXS,\n    \n    // Shadows (darker for dark theme)\n    boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.3), 0 1px 2px 0 rgba(0, 0, 0, 0.2)',\n    boxShadowSecondary: '0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2)',\n    boxShadowTertiary: '0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2)',\n    \n    // Motion (inherit from light theme)\n    motionDurationFast: lightTheme.token?.motionDurationFast,\n    motionDurationMid: lightTheme.token?.motionDurationMid,\n    motionDurationSlow: lightTheme.token?.motionDurationSlow,\n    \n    // Z-index (inherit from light theme)\n    zIndexBase: lightTheme.token?.zIndexBase,\n    zIndexPopupBase: lightTheme.token?.zIndexPopupBase,\n  },\n  \n  components: {\n    // Layout components\n    Layout: {\n      headerBg: colors.neutral[800],\n      headerHeight: 64,\n      headerPadding: '0 24px',\n      siderBg: colors.neutral[800],\n      triggerBg: colors.neutral[700],\n      triggerColor: colors.neutral[300],\n    },\n    \n    // Menu component\n    Menu: {\n      itemBg: 'transparent',\n      itemSelectedBg: colors.primary[900],\n      itemSelectedColor: colors.primary[300],\n      itemHoverBg: colors.neutral[700],\n      itemHoverColor: colors.neutral[100],\n      itemActiveBg: colors.primary[800],\n      subMenuItemBg: 'transparent',\n    },\n    \n    // Inherit other components from light theme with dark adjustments\n    Button: lightTheme.components?.Button,\n    Input: lightTheme.components?.Input,\n    Table: {\n      ...lightTheme.components?.Table,\n      headerBg: colors.neutral[700],\n      headerColor: colors.neutral[200],\n      rowHoverBg: colors.neutral[750],\n      borderColor: colors.neutral[600],\n    },\n    Card: lightTheme.components?.Card,\n    Modal: lightTheme.components?.Modal,\n    Notification: lightTheme.components?.Notification,\n    Message: lightTheme.components?.Message,\n  },\n};\n\n/**\n * Theme configuration map\n */\nexport const themeConfigs = {\n  light: lightTheme,\n  dark: darkTheme,\n} as const;\n\n/**\n * Default theme\n */\nexport const defaultTheme = 'light' as const;\n\n/**\n * Theme type\n */\nexport type ThemeMode = keyof typeof themeConfigs;\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;AAOM,MAAM,SAAS;IACpB,uCAAuC;IACvC,SAAS;QACP,IAAI;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;IACP;IAEA,yBAAyB;IACzB,SAAS;QACP,IAAI;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;IACP;IAEA,0BAA0B;IAC1B,SAAS;QACP,IAAI;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;IACP;IAEA,qBAAqB;IACrB,OAAO;QACL,IAAI;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;IACP;IAEA,wBAAwB;IACxB,SAAS;QACP,IAAI;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;IACP;AACF;AAKO,MAAM,aAA0B;IACrC,OAAO;QACL,eAAe;QACf,cAAc,OAAO,OAAO,CAAC,IAAI;QACjC,cAAc,OAAO,OAAO,CAAC,IAAI;QACjC,cAAc,OAAO,OAAO,CAAC,IAAI;QACjC,YAAY,OAAO,KAAK,CAAC,IAAI;QAC7B,WAAW,OAAO,OAAO,CAAC,IAAI;QAE9B,oBAAoB;QACpB,kBAAkB;QAClB,iBAAiB;QACjB,eAAe,OAAO,OAAO,CAAC,GAAG;QACjC,kBAAkB,OAAO,OAAO,CAAC,IAAI;QAErC,cAAc;QACd,WAAW,OAAO,OAAO,CAAC,IAAI;QAC9B,oBAAoB,OAAO,OAAO,CAAC,IAAI;QACvC,mBAAmB,OAAO,OAAO,CAAC,IAAI;QACtC,qBAAqB,OAAO,OAAO,CAAC,IAAI;QAExC,gBAAgB;QAChB,aAAa,OAAO,OAAO,CAAC,IAAI;QAChC,sBAAsB,OAAO,OAAO,CAAC,IAAI;QAEzC,aAAa;QACb,YAAY;QACZ,UAAU;QACV,kBAAkB;QAClB,kBAAkB;QAClB,kBAAkB;QAClB,kBAAkB;QAClB,kBAAkB;QAElB,SAAS;QACT,cAAc;QACd,gBAAgB;QAChB,gBAAgB;QAChB,gBAAgB;QAEhB,UAAU;QACV,SAAS;QACT,WAAW;QACX,WAAW;QACX,WAAW;QACX,YAAY;QAEZ,QAAQ;QACR,UAAU;QACV,UAAU;QACV,UAAU;QACV,WAAW;QAEX,UAAU;QACV,WAAW;QACX,oBAAoB;QACpB,mBAAmB;QAEnB,SAAS;QACT,oBAAoB;QACpB,mBAAmB;QACnB,oBAAoB;QAEpB,UAAU;QACV,YAAY;QACZ,iBAAiB;IACnB;IAEA,YAAY;QACV,oBAAoB;QACpB,QAAQ;YACN,UAAU;YACV,cAAc;YACd,eAAe;YACf,SAAS;YACT,WAAW,OAAO,OAAO,CAAC,IAAI;YAC9B,cAAc,OAAO,OAAO,CAAC,IAAI;QACnC;QAEA,iBAAiB;QACjB,MAAM;YACJ,QAAQ;YACR,gBAAgB,OAAO,OAAO,CAAC,GAAG;YAClC,mBAAmB,OAAO,OAAO,CAAC,IAAI;YACtC,aAAa,OAAO,OAAO,CAAC,GAAG;YAC/B,gBAAgB,OAAO,OAAO,CAAC,IAAI;YACnC,cAAc,OAAO,OAAO,CAAC,IAAI;YACjC,eAAe;QACjB;QAEA,mBAAmB;QACnB,QAAQ;YACN,cAAc;YACd,eAAe;YACf,iBAAiB;YACjB,iBAAiB;YACjB,eAAe;YACf,iBAAiB;YACjB,iBAAiB;QACnB;QAEA,kBAAkB;QAClB,OAAO;YACL,cAAc;YACd,eAAe;YACf,iBAAiB;YACjB,iBAAiB;YACjB,eAAe;QACjB;QAEA,kBAAkB;QAClB,OAAO;YACL,UAAU,OAAO,OAAO,CAAC,GAAG;YAC5B,aAAa,OAAO,OAAO,CAAC,IAAI;YAChC,YAAY,OAAO,OAAO,CAAC,GAAG;YAC9B,aAAa,OAAO,OAAO,CAAC,IAAI;QAClC;QAEA,iBAAiB;QACjB,MAAM;YACJ,UAAU;YACV,gBAAgB;YAChB,WAAW;QACb;QAEA,kBAAkB;QAClB,OAAO;YACL,gBAAgB;YAChB,WAAW;QACb;QAEA,yBAAyB;QACzB,cAAc;YACZ,gBAAgB;YAChB,WAAW;QACb;QAEA,oBAAoB;QACpB,SAAS;YACP,gBAAgB;YAChB,WAAW;QACb;IACF;AACF;AAKO,MAAM,YAAyB;IACpC,OAAO;QACL,eAAe;QACf,cAAc,OAAO,OAAO,CAAC,IAAI;QACjC,cAAc,OAAO,OAAO,CAAC,IAAI;QACjC,cAAc,OAAO,OAAO,CAAC,IAAI;QACjC,YAAY,OAAO,KAAK,CAAC,IAAI;QAC7B,WAAW,OAAO,OAAO,CAAC,IAAI;QAE9B,oBAAoB;QACpB,kBAAkB,OAAO,OAAO,CAAC,IAAI;QACrC,iBAAiB,OAAO,OAAO,CAAC,IAAI;QACpC,eAAe,OAAO,OAAO,CAAC,IAAI;QAClC,kBAAkB,OAAO,OAAO,CAAC,IAAI;QAErC,cAAc;QACd,WAAW,OAAO,OAAO,CAAC,IAAI;QAC9B,oBAAoB,OAAO,OAAO,CAAC,IAAI;QACvC,mBAAmB,OAAO,OAAO,CAAC,IAAI;QACtC,qBAAqB,OAAO,OAAO,CAAC,IAAI;QAExC,gBAAgB;QAChB,aAAa,OAAO,OAAO,CAAC,IAAI;QAChC,sBAAsB,OAAO,OAAO,CAAC,IAAI;QAEzC,wCAAwC;QACxC,YAAY,WAAW,KAAK,EAAE;QAC9B,UAAU,WAAW,KAAK,EAAE;QAC5B,kBAAkB,WAAW,KAAK,EAAE;QACpC,kBAAkB,WAAW,KAAK,EAAE;QACpC,kBAAkB,WAAW,KAAK,EAAE;QACpC,kBAAkB,WAAW,KAAK,EAAE;QACpC,kBAAkB,WAAW,KAAK,EAAE;QAEpC,oCAAoC;QACpC,cAAc,WAAW,KAAK,EAAE;QAChC,gBAAgB,WAAW,KAAK,EAAE;QAClC,gBAAgB,WAAW,KAAK,EAAE;QAClC,gBAAgB,WAAW,KAAK,EAAE;QAElC,qCAAqC;QACrC,SAAS,WAAW,KAAK,EAAE;QAC3B,WAAW,WAAW,KAAK,EAAE;QAC7B,WAAW,WAAW,KAAK,EAAE;QAC7B,WAAW,WAAW,KAAK,EAAE;QAC7B,YAAY,WAAW,KAAK,EAAE;QAE9B,QAAQ,WAAW,KAAK,EAAE;QAC1B,UAAU,WAAW,KAAK,EAAE;QAC5B,UAAU,WAAW,KAAK,EAAE;QAC5B,UAAU,WAAW,KAAK,EAAE;QAC5B,WAAW,WAAW,KAAK,EAAE;QAE7B,kCAAkC;QAClC,WAAW;QACX,oBAAoB;QACpB,mBAAmB;QAEnB,oCAAoC;QACpC,oBAAoB,WAAW,KAAK,EAAE;QACtC,mBAAmB,WAAW,KAAK,EAAE;QACrC,oBAAoB,WAAW,KAAK,EAAE;QAEtC,qCAAqC;QACrC,YAAY,WAAW,KAAK,EAAE;QAC9B,iBAAiB,WAAW,KAAK,EAAE;IACrC;IAEA,YAAY;QACV,oBAAoB;QACpB,QAAQ;YACN,UAAU,OAAO,OAAO,CAAC,IAAI;YAC7B,cAAc;YACd,eAAe;YACf,SAAS,OAAO,OAAO,CAAC,IAAI;YAC5B,WAAW,OAAO,OAAO,CAAC,IAAI;YAC9B,cAAc,OAAO,OAAO,CAAC,IAAI;QACnC;QAEA,iBAAiB;QACjB,MAAM;YACJ,QAAQ;YACR,gBAAgB,OAAO,OAAO,CAAC,IAAI;YACnC,mBAAmB,OAAO,OAAO,CAAC,IAAI;YACtC,aAAa,OAAO,OAAO,CAAC,IAAI;YAChC,gBAAgB,OAAO,OAAO,CAAC,IAAI;YACnC,cAAc,OAAO,OAAO,CAAC,IAAI;YACjC,eAAe;QACjB;QAEA,kEAAkE;QAClE,QAAQ,WAAW,UAAU,EAAE;QAC/B,OAAO,WAAW,UAAU,EAAE;QAC9B,OAAO;YACL,GAAG,WAAW,UAAU,EAAE,KAAK;YAC/B,UAAU,OAAO,OAAO,CAAC,IAAI;YAC7B,aAAa,OAAO,OAAO,CAAC,IAAI;YAChC,YAAY,OAAO,OAAO,CAAC,IAAI;YAC/B,aAAa,OAAO,OAAO,CAAC,IAAI;QAClC;QACA,MAAM,WAAW,UAAU,EAAE;QAC7B,OAAO,WAAW,UAAU,EAAE;QAC9B,cAAc,WAAW,UAAU,EAAE;QACrC,SAAS,WAAW,UAAU,EAAE;IAClC;AACF;AAKO,MAAM,eAAe;IAC1B,OAAO;IACP,MAAM;AACR;AAKO,MAAM,eAAe"}}, {"offset": {"line": 4458, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4464, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/theme/utils.ts"], "sourcesContent": ["/**\n * Theme Utilities\n * Helper functions for theme management\n */\n\nimport { colors, themeConfigs, type ThemeMode } from './config';\n\n/**\n * Apply theme to document\n */\nexport function applyThemeToDocument(theme: ThemeMode): void {\n  const root = document.documentElement;\n\n  // Set theme attribute\n  root.setAttribute('data-theme', theme);\n\n  // Apply theme class\n  root.classList.remove('theme-light', 'theme-dark');\n  root.classList.add(`theme-${theme}`);\n\n  // Apply meta theme-color for mobile browsers\n  const metaThemeColor = document.querySelector('meta[name=\"theme-color\"]');\n  const themeConfig = themeConfigs[theme];\n\n  if (metaThemeColor && themeConfig.token?.colorPrimary) {\n    metaThemeColor.setAttribute('content', themeConfig.token.colorPrimary);\n  }\n}\n\n/**\n * Get system theme preference\n */\nexport function getSystemTheme(): ThemeMode {\n  if (typeof window === 'undefined') return 'light';\n\n  const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n  return mediaQuery.matches ? 'dark' : 'light';\n}\n\n/**\n * Get stored theme from localStorage\n */\nexport function getStoredTheme(): ThemeMode | null {\n  if (typeof window === 'undefined') return null;\n\n  try {\n    const stored = localStorage.getItem('apisportsgame_theme');\n    if (stored === 'light' || stored === 'dark') {\n      return stored;\n    }\n  } catch (error) {\n    console.warn('Failed to get stored theme:', error);\n  }\n\n  return null;\n}\n\n/**\n * Store theme in localStorage\n */\nexport function storeTheme(theme: ThemeMode): void {\n  if (typeof window === 'undefined') return;\n\n  try {\n    localStorage.setItem('apisportsgame_theme', theme);\n  } catch (error) {\n    console.warn('Failed to store theme:', error);\n  }\n}\n\n/**\n * Get effective theme (stored > system > default)\n */\nexport function getEffectiveTheme(): ThemeMode {\n  const stored = getStoredTheme();\n  if (stored) return stored;\n\n  return getSystemTheme();\n}\n\n/**\n * Toggle theme between light and dark\n */\nexport function toggleTheme(currentTheme: ThemeMode): ThemeMode {\n  return currentTheme === 'light' ? 'dark' : 'light';\n}\n\n/**\n * Check if theme is dark\n */\nexport function isDarkTheme(theme: ThemeMode): boolean {\n  return theme === 'dark';\n}\n\n/**\n * Check if theme is light\n */\nexport function isLightTheme(theme: ThemeMode): boolean {\n  return theme === 'light';\n}\n\n/**\n * Get theme colors\n */\nexport function getThemeColors(theme: ThemeMode) {\n  const config = themeConfigs[theme];\n\n  if (!config || !config.token) {\n    // Fallback to default colors if config is not available\n    return {\n      primary: colors.primary[500],\n      success: colors.success[500],\n      warning: colors.warning[500],\n      error: colors.error[500],\n      info: colors.primary[500],\n\n      background: {\n        container: theme === 'dark' ? colors.neutral[800] : '#ffffff',\n        layout: theme === 'dark' ? colors.neutral[900] : colors.neutral[50],\n        elevated: theme === 'dark' ? colors.neutral[700] : '#ffffff',\n      },\n\n      text: {\n        primary: theme === 'dark' ? colors.neutral[100] : colors.neutral[900],\n        secondary: theme === 'dark' ? colors.neutral[300] : colors.neutral[600],\n        tertiary: theme === 'dark' ? colors.neutral[400] : colors.neutral[500],\n      },\n\n      border: {\n        primary: theme === 'dark' ? colors.neutral[600] : colors.neutral[200],\n        secondary: theme === 'dark' ? colors.neutral[700] : colors.neutral[100],\n      },\n    };\n  }\n\n  return {\n    primary: config.token?.colorPrimary || colors.primary[500],\n    success: config.token?.colorSuccess || colors.success[500],\n    warning: config.token?.colorWarning || colors.warning[500],\n    error: config.token?.colorError || colors.error[500],\n    info: config.token?.colorInfo || colors.primary[500],\n\n    background: {\n      container: config.token?.colorBgContainer || '#ffffff',\n      layout: config.token?.colorBgLayout || colors.neutral[50],\n      elevated: config.token?.colorBgElevated || '#ffffff',\n    },\n\n    text: {\n      primary: config.token?.colorText || colors.neutral[900],\n      secondary: config.token?.colorTextSecondary || colors.neutral[600],\n      tertiary: config.token?.colorTextTertiary || colors.neutral[500],\n    },\n\n    border: {\n      primary: config.token?.colorBorder || colors.neutral[200],\n      secondary: config.token?.colorBorderSecondary || colors.neutral[100],\n    },\n  };\n}\n\n/**\n * Generate CSS variables for theme\n */\nexport function generateThemeCSSVariables(theme: ThemeMode): Record<string, string> {\n  const themeColors = getThemeColors(theme);\n  const config = themeConfigs[theme];\n\n  // Fallback values if config is not available\n  const fallbackConfig = {\n    borderRadius: 8,\n    borderRadiusLG: 12,\n    borderRadiusSM: 6,\n    padding: 16,\n    paddingLG: 24,\n    paddingSM: 12,\n    boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',\n    boxShadowSecondary: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',\n  };\n\n  return {\n    // Color variables\n    '--theme-primary': themeColors.primary,\n    '--theme-success': themeColors.success,\n    '--theme-warning': themeColors.warning,\n    '--theme-error': themeColors.error,\n    '--theme-info': themeColors.info,\n\n    // Background variables\n    '--theme-bg-container': themeColors.background.container,\n    '--theme-bg-layout': themeColors.background.layout,\n    '--theme-bg-elevated': themeColors.background.elevated,\n\n    // Text variables\n    '--theme-text-primary': themeColors.text.primary,\n    '--theme-text-secondary': themeColors.text.secondary,\n    '--theme-text-tertiary': themeColors.text.tertiary,\n\n    // Border variables\n    '--theme-border-primary': themeColors.border.primary,\n    '--theme-border-secondary': themeColors.border.secondary,\n\n    // Border radius variables\n    '--theme-border-radius': `${config?.token?.borderRadius || fallbackConfig.borderRadius}px`,\n    '--theme-border-radius-lg': `${config?.token?.borderRadiusLG || fallbackConfig.borderRadiusLG}px`,\n    '--theme-border-radius-sm': `${config?.token?.borderRadiusSM || fallbackConfig.borderRadiusSM}px`,\n\n    // Spacing variables\n    '--theme-padding': `${config?.token?.padding || fallbackConfig.padding}px`,\n    '--theme-padding-lg': `${config?.token?.paddingLG || fallbackConfig.paddingLG}px`,\n    '--theme-padding-sm': `${config?.token?.paddingSM || fallbackConfig.paddingSM}px`,\n\n    // Shadow variables\n    '--theme-shadow': config?.token?.boxShadow || fallbackConfig.boxShadow,\n    '--theme-shadow-lg': config?.token?.boxShadowSecondary || fallbackConfig.boxShadowSecondary,\n  };\n}\n\n/**\n * Apply CSS variables to document\n */\nexport function applyCSSVariables(theme: ThemeMode): void {\n  if (typeof document === 'undefined') return;\n\n  const variables = generateThemeCSSVariables(theme);\n  const root = document.documentElement;\n\n  Object.entries(variables).forEach(([property, value]) => {\n    root.style.setProperty(property, value);\n  });\n}\n\n/**\n * Create theme media query listener\n */\nexport function createThemeMediaQueryListener(\n  callback: (theme: ThemeMode) => void\n): (() => void) | null {\n  if (typeof window === 'undefined') return null;\n\n  const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n\n  const listener = (event: MediaQueryListEvent) => {\n    const theme = event.matches ? 'dark' : 'light';\n    callback(theme);\n  };\n\n  mediaQuery.addEventListener('change', listener);\n\n  // Return cleanup function\n  return () => {\n    mediaQuery.removeEventListener('change', listener);\n  };\n}\n\n/**\n * Theme utilities object\n */\nexport const themeUtils = {\n  apply: applyThemeToDocument,\n  getSystem: getSystemTheme,\n  getStored: getStoredTheme,\n  store: storeTheme,\n  getEffective: getEffectiveTheme,\n  toggle: toggleTheme,\n  isDark: isDarkTheme,\n  isLight: isLightTheme,\n  getColors: getThemeColors,\n  generateCSSVariables: generateThemeCSSVariables,\n  applyCSSVariables: applyCSSVariables,\n  createMediaQueryListener: createThemeMediaQueryListener,\n} as const;\n\n/**\n * Theme constants\n */\nexport const THEME_CONSTANTS = {\n  STORAGE_KEY: 'apisportsgame_theme',\n  ATTRIBUTE_NAME: 'data-theme',\n  CLASS_PREFIX: 'theme-',\n  MEDIA_QUERY: '(prefers-color-scheme: dark)',\n} as const;\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;;;;;AAED;;AAKO,SAAS,qBAAqB,KAAgB;IACnD,MAAM,OAAO,SAAS,eAAe;IAErC,sBAAsB;IACtB,KAAK,YAAY,CAAC,cAAc;IAEhC,oBAAoB;IACpB,KAAK,SAAS,CAAC,MAAM,CAAC,eAAe;IACrC,KAAK,SAAS,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,OAAO;IAEnC,6CAA6C;IAC7C,MAAM,iBAAiB,SAAS,aAAa,CAAC;IAC9C,MAAM,cAAc,yHAAA,CAAA,eAAY,CAAC,MAAM;IAEvC,IAAI,kBAAkB,YAAY,KAAK,EAAE,cAAc;QACrD,eAAe,YAAY,CAAC,WAAW,YAAY,KAAK,CAAC,YAAY;IACvE;AACF;AAKO,SAAS;IACd,uCAAmC;;IAAc;IAEjD,MAAM,aAAa,OAAO,UAAU,CAAC;IACrC,OAAO,WAAW,OAAO,GAAG,SAAS;AACvC;AAKO,SAAS;IACd,uCAAmC;;IAAW;IAE9C,IAAI;QACF,MAAM,SAAS,aAAa,OAAO,CAAC;QACpC,IAAI,WAAW,WAAW,WAAW,QAAQ;YAC3C,OAAO;QACT;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,+BAA+B;IAC9C;IAEA,OAAO;AACT;AAKO,SAAS,WAAW,KAAgB;IACzC,uCAAmC;;IAAM;IAEzC,IAAI;QACF,aAAa,OAAO,CAAC,uBAAuB;IAC9C,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,0BAA0B;IACzC;AACF;AAKO,SAAS;IACd,MAAM,SAAS;IACf,IAAI,QAAQ,OAAO;IAEnB,OAAO;AACT;AAKO,SAAS,YAAY,YAAuB;IACjD,OAAO,iBAAiB,UAAU,SAAS;AAC7C;AAKO,SAAS,YAAY,KAAgB;IAC1C,OAAO,UAAU;AACnB;AAKO,SAAS,aAAa,KAAgB;IAC3C,OAAO,UAAU;AACnB;AAKO,SAAS,eAAe,KAAgB;IAC7C,MAAM,SAAS,yHAAA,CAAA,eAAY,CAAC,MAAM;IAElC,IAAI,CAAC,UAAU,CAAC,OAAO,KAAK,EAAE;QAC5B,wDAAwD;QACxD,OAAO;YACL,SAAS,yHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,IAAI;YAC5B,SAAS,yHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,IAAI;YAC5B,SAAS,yHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,IAAI;YAC5B,OAAO,yHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,IAAI;YACxB,MAAM,yHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,IAAI;YAEzB,YAAY;gBACV,WAAW,UAAU,SAAS,yHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,IAAI,GAAG;gBACpD,QAAQ,UAAU,SAAS,yHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,IAAI,GAAG,yHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,GAAG;gBACnE,UAAU,UAAU,SAAS,yHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,IAAI,GAAG;YACrD;YAEA,MAAM;gBACJ,SAAS,UAAU,SAAS,yHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,IAAI,GAAG,yHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,IAAI;gBACrE,WAAW,UAAU,SAAS,yHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,IAAI,GAAG,yHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,IAAI;gBACvE,UAAU,UAAU,SAAS,yHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,IAAI,GAAG,yHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,IAAI;YACxE;YAEA,QAAQ;gBACN,SAAS,UAAU,SAAS,yHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,IAAI,GAAG,yHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,IAAI;gBACrE,WAAW,UAAU,SAAS,yHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,IAAI,GAAG,yHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,IAAI;YACzE;QACF;IACF;IAEA,OAAO;QACL,SAAS,OAAO,KAAK,EAAE,gBAAgB,yHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,IAAI;QAC1D,SAAS,OAAO,KAAK,EAAE,gBAAgB,yHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,IAAI;QAC1D,SAAS,OAAO,KAAK,EAAE,gBAAgB,yHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,IAAI;QAC1D,OAAO,OAAO,KAAK,EAAE,cAAc,yHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,IAAI;QACpD,MAAM,OAAO,KAAK,EAAE,aAAa,yHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,IAAI;QAEpD,YAAY;YACV,WAAW,OAAO,KAAK,EAAE,oBAAoB;YAC7C,QAAQ,OAAO,KAAK,EAAE,iBAAiB,yHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,GAAG;YACzD,UAAU,OAAO,KAAK,EAAE,mBAAmB;QAC7C;QAEA,MAAM;YACJ,SAAS,OAAO,KAAK,EAAE,aAAa,yHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,IAAI;YACvD,WAAW,OAAO,KAAK,EAAE,sBAAsB,yHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,IAAI;YAClE,UAAU,OAAO,KAAK,EAAE,qBAAqB,yHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,IAAI;QAClE;QAEA,QAAQ;YACN,SAAS,OAAO,KAAK,EAAE,eAAe,yHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,IAAI;YACzD,WAAW,OAAO,KAAK,EAAE,wBAAwB,yHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,IAAI;QACtE;IACF;AACF;AAKO,SAAS,0BAA0B,KAAgB;IACxD,MAAM,cAAc,eAAe;IACnC,MAAM,SAAS,yHAAA,CAAA,eAAY,CAAC,MAAM;IAElC,6CAA6C;IAC7C,MAAM,iBAAiB;QACrB,cAAc;QACd,gBAAgB;QAChB,gBAAgB;QAChB,SAAS;QACT,WAAW;QACX,WAAW;QACX,WAAW;QACX,oBAAoB;IACtB;IAEA,OAAO;QACL,kBAAkB;QAClB,mBAAmB,YAAY,OAAO;QACtC,mBAAmB,YAAY,OAAO;QACtC,mBAAmB,YAAY,OAAO;QACtC,iBAAiB,YAAY,KAAK;QAClC,gBAAgB,YAAY,IAAI;QAEhC,uBAAuB;QACvB,wBAAwB,YAAY,UAAU,CAAC,SAAS;QACxD,qBAAqB,YAAY,UAAU,CAAC,MAAM;QAClD,uBAAuB,YAAY,UAAU,CAAC,QAAQ;QAEtD,iBAAiB;QACjB,wBAAwB,YAAY,IAAI,CAAC,OAAO;QAChD,0BAA0B,YAAY,IAAI,CAAC,SAAS;QACpD,yBAAyB,YAAY,IAAI,CAAC,QAAQ;QAElD,mBAAmB;QACnB,0BAA0B,YAAY,MAAM,CAAC,OAAO;QACpD,4BAA4B,YAAY,MAAM,CAAC,SAAS;QAExD,0BAA0B;QAC1B,yBAAyB,GAAG,QAAQ,OAAO,gBAAgB,eAAe,YAAY,CAAC,EAAE,CAAC;QAC1F,4BAA4B,GAAG,QAAQ,OAAO,kBAAkB,eAAe,cAAc,CAAC,EAAE,CAAC;QACjG,4BAA4B,GAAG,QAAQ,OAAO,kBAAkB,eAAe,cAAc,CAAC,EAAE,CAAC;QAEjG,oBAAoB;QACpB,mBAAmB,GAAG,QAAQ,OAAO,WAAW,eAAe,OAAO,CAAC,EAAE,CAAC;QAC1E,sBAAsB,GAAG,QAAQ,OAAO,aAAa,eAAe,SAAS,CAAC,EAAE,CAAC;QACjF,sBAAsB,GAAG,QAAQ,OAAO,aAAa,eAAe,SAAS,CAAC,EAAE,CAAC;QAEjF,mBAAmB;QACnB,kBAAkB,QAAQ,OAAO,aAAa,eAAe,SAAS;QACtE,qBAAqB,QAAQ,OAAO,sBAAsB,eAAe,kBAAkB;IAC7F;AACF;AAKO,SAAS,kBAAkB,KAAgB;IAChD,IAAI,OAAO,aAAa,aAAa;IAErC,MAAM,YAAY,0BAA0B;IAC5C,MAAM,OAAO,SAAS,eAAe;IAErC,OAAO,OAAO,CAAC,WAAW,OAAO,CAAC,CAAC,CAAC,UAAU,MAAM;QAClD,KAAK,KAAK,CAAC,WAAW,CAAC,UAAU;IACnC;AACF;AAKO,SAAS,8BACd,QAAoC;IAEpC,uCAAmC;;IAAW;IAE9C,MAAM,aAAa,OAAO,UAAU,CAAC;IAErC,MAAM,WAAW,CAAC;QAChB,MAAM,QAAQ,MAAM,OAAO,GAAG,SAAS;QACvC,SAAS;IACX;IAEA,WAAW,gBAAgB,CAAC,UAAU;IAEtC,0BAA0B;IAC1B,OAAO;QACL,WAAW,mBAAmB,CAAC,UAAU;IAC3C;AACF;AAKO,MAAM,aAAa;IACxB,OAAO;IACP,WAAW;IACX,WAAW;IACX,OAAO;IACP,cAAc;IACd,QAAQ;IACR,QAAQ;IACR,SAAS;IACT,WAAW;IACX,sBAAsB;IACtB,mBAAmB;IACnB,0BAA0B;AAC5B;AAKO,MAAM,kBAAkB;IAC7B,aAAa;IACb,gBAAgB;IAChB,cAAc;IACd,aAAa;AACf"}}, {"offset": {"line": 4683, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4689, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/theme/theme-provider.tsx"], "sourcesContent": ["/**\n * Theme Provider\n * Ant Design theme provider with global state integration\n */\n\n'use client';\n\nimport React, { ReactNode, useEffect } from 'react';\nimport { ConfigProvider, App as AntApp } from 'antd';\nimport { useAppProvider } from '@/stores';\nimport { themeConfigs, defaultTheme, type ThemeMode } from './config';\nimport { applyThemeToDocument } from './utils';\n\n/**\n * Theme provider props\n */\ninterface ThemeProviderProps {\n  children: ReactNode;\n}\n\n/**\n * Theme provider component\n * Wraps children with Ant Design ConfigProvider and applies theme\n */\nexport function ThemeProvider({ children }: ThemeProviderProps) {\n  const app = useAppProvider();\n  // Extract theme mode from theme config object\n  const currentTheme = (app.theme?.mode as ThemeMode) || defaultTheme;\n  const themeConfig = themeConfigs[currentTheme];\n\n  // Apply theme to document when theme changes\n  useEffect(() => {\n    applyThemeToDocument(currentTheme);\n  }, [currentTheme]);\n\n  return (\n    <ConfigProvider\n      theme={themeConfig}\n      componentSize=\"middle\"\n      direction=\"ltr\"\n    >\n      <AntApp>\n        <ThemeInitializer theme={currentTheme}>\n          {children}\n        </ThemeInitializer>\n      </AntApp>\n    </ConfigProvider>\n  );\n}\n\n/**\n * Theme initializer component\n * Handles theme initialization and CSS variables\n */\nfunction ThemeInitializer({\n  children,\n  theme\n}: {\n  children: ReactNode;\n  theme: ThemeMode;\n}) {\n  useEffect(() => {\n    // Set theme attribute on document\n    document.documentElement.setAttribute('data-theme', theme);\n\n    // Apply CSS variables for the current theme\n    const root = document.documentElement;\n    const themeConfig = themeConfigs[theme];\n\n    if (themeConfig.token) {\n      // Apply color variables\n      if (themeConfig.token.colorPrimary) {\n        root.style.setProperty('--ant-color-primary', themeConfig.token.colorPrimary);\n      }\n      if (themeConfig.token.colorSuccess) {\n        root.style.setProperty('--ant-color-success', themeConfig.token.colorSuccess);\n      }\n      if (themeConfig.token.colorWarning) {\n        root.style.setProperty('--ant-color-warning', themeConfig.token.colorWarning);\n      }\n      if (themeConfig.token.colorError) {\n        root.style.setProperty('--ant-color-error', themeConfig.token.colorError);\n      }\n\n      // Apply background variables\n      if (themeConfig.token.colorBgContainer) {\n        root.style.setProperty('--ant-color-bg-container', themeConfig.token.colorBgContainer);\n      }\n      if (themeConfig.token.colorBgLayout) {\n        root.style.setProperty('--ant-color-bg-layout', themeConfig.token.colorBgLayout);\n      }\n\n      // Apply text variables\n      if (themeConfig.token.colorText) {\n        root.style.setProperty('--ant-color-text', themeConfig.token.colorText);\n      }\n      if (themeConfig.token.colorTextSecondary) {\n        root.style.setProperty('--ant-color-text-secondary', themeConfig.token.colorTextSecondary);\n      }\n\n      // Apply border variables\n      if (themeConfig.token.colorBorder) {\n        root.style.setProperty('--ant-color-border', themeConfig.token.colorBorder);\n      }\n\n      // Apply border radius variables\n      if (themeConfig.token.borderRadius) {\n        root.style.setProperty('--ant-border-radius', `${themeConfig.token.borderRadius}px`);\n      }\n      if (themeConfig.token.borderRadiusLG) {\n        root.style.setProperty('--ant-border-radius-lg', `${themeConfig.token.borderRadiusLG}px`);\n      }\n    }\n\n    console.log(`🎨 Theme applied: ${theme}`);\n  }, [theme]);\n\n  return <>{children}</>;\n}\n\n/**\n * Theme provider error boundary\n */\nexport class ThemeProviderErrorBoundary extends React.Component<\n  { children: ReactNode },\n  { hasError: boolean; error?: Error }\n> {\n  constructor(props: { children: ReactNode }) {\n    super(props);\n    this.state = { hasError: false };\n  }\n\n  static getDerivedStateFromError(error: Error): { hasError: boolean; error: Error } {\n    return { hasError: true, error };\n  }\n\n  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {\n    console.error('[ThemeProvider Error]', error, errorInfo);\n  }\n\n  render() {\n    if (this.state.hasError) {\n      return (\n        <div style={{\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center',\n          justifyContent: 'center',\n          minHeight: '100vh',\n          padding: '20px',\n          textAlign: 'center',\n          fontFamily: 'system-ui, sans-serif',\n        }}>\n          <h1 style={{ color: '#dc2626', marginBottom: '16px' }}>\n            Theme Provider Error\n          </h1>\n          <p style={{ color: '#6b7280', marginBottom: '24px' }}>\n            An error occurred while loading the theme system.\n          </p>\n          {this.state.error && process.env.NODE_ENV === 'development' && (\n            <details style={{\n              marginBottom: '24px',\n              padding: '16px',\n              backgroundColor: '#f3f4f6',\n              borderRadius: '8px',\n              textAlign: 'left',\n              maxWidth: '600px',\n              width: '100%',\n            }}>\n              <summary style={{ cursor: 'pointer', fontWeight: 'bold' }}>\n                Error Details (Development)\n              </summary>\n              <pre style={{\n                marginTop: '12px',\n                fontSize: '12px',\n                overflow: 'auto',\n                whiteSpace: 'pre-wrap',\n              }}>\n                {this.state.error.message}\n                {this.state.error.stack && `\\n\\n${this.state.error.stack}`}\n              </pre>\n            </details>\n          )}\n          <button\n            onClick={() => window.location.reload()}\n            style={{\n              padding: '12px 24px',\n              backgroundColor: '#3b82f6',\n              color: 'white',\n              border: 'none',\n              borderRadius: '6px',\n              cursor: 'pointer',\n              fontSize: '16px',\n            }}\n          >\n            Reload Page\n          </button>\n        </div>\n      );\n    }\n\n    return this.props.children;\n  }\n}\n\n/**\n * Theme provider with error boundary\n */\nexport function ThemeProviderWithErrorBoundary({ children }: ThemeProviderProps) {\n  return (\n    <ThemeProviderErrorBoundary>\n      <ThemeProvider>\n        {children}\n      </ThemeProvider>\n    </ThemeProviderErrorBoundary>\n  );\n}\n\n/**\n * HOC to wrap components with theme provider\n */\nexport function withThemeProvider<P extends object>(\n  Component: React.ComponentType<P>\n) {\n  const WrappedComponent = (props: P) => (\n    <ThemeProvider>\n      <Component {...props} />\n    </ThemeProvider>\n  );\n\n  WrappedComponent.displayName = `withThemeProvider(${Component.displayName || Component.name})`;\n\n  return WrappedComponent;\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;AAID;AAEA;AACA;AACA;AAFA;AADA;AAAA;AAuJ+B;;;AA1J/B;;;;;;AAmBO,SAAS,cAAc,EAAE,QAAQ,EAAsB;;IAC5D,MAAM,MAAM,CAAA,GAAA,qIAAA,CAAA,iBAAc,AAAD;IACzB,8CAA8C;IAC9C,MAAM,eAAe,AAAC,IAAI,KAAK,EAAE,QAAsB,yHAAA,CAAA,eAAY;IACnE,MAAM,cAAc,yHAAA,CAAA,eAAY,CAAC,aAAa;IAE9C,6CAA6C;IAC7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,CAAA,GAAA,wHAAA,CAAA,uBAAoB,AAAD,EAAE;QACvB;kCAAG;QAAC;KAAa;IAEjB,qBACE,6LAAC,yNAAA,CAAA,iBAAc;QACb,OAAO;QACP,eAAc;QACd,WAAU;kBAEV,cAAA,6LAAC,+KAAA,CAAA,MAAM;sBACL,cAAA,6LAAC;gBAAiB,OAAO;0BACtB;;;;;;;;;;;;;;;;AAKX;GAxBgB;;QACF,qIAAA,CAAA,iBAAc;;;KADZ;AA0BhB;;;CAGC,GACD,SAAS,iBAAiB,EACxB,QAAQ,EACR,KAAK,EAIN;;IACC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,kCAAkC;YAClC,SAAS,eAAe,CAAC,YAAY,CAAC,cAAc;YAEpD,4CAA4C;YAC5C,MAAM,OAAO,SAAS,eAAe;YACrC,MAAM,cAAc,yHAAA,CAAA,eAAY,CAAC,MAAM;YAEvC,IAAI,YAAY,KAAK,EAAE;gBACrB,wBAAwB;gBACxB,IAAI,YAAY,KAAK,CAAC,YAAY,EAAE;oBAClC,KAAK,KAAK,CAAC,WAAW,CAAC,uBAAuB,YAAY,KAAK,CAAC,YAAY;gBAC9E;gBACA,IAAI,YAAY,KAAK,CAAC,YAAY,EAAE;oBAClC,KAAK,KAAK,CAAC,WAAW,CAAC,uBAAuB,YAAY,KAAK,CAAC,YAAY;gBAC9E;gBACA,IAAI,YAAY,KAAK,CAAC,YAAY,EAAE;oBAClC,KAAK,KAAK,CAAC,WAAW,CAAC,uBAAuB,YAAY,KAAK,CAAC,YAAY;gBAC9E;gBACA,IAAI,YAAY,KAAK,CAAC,UAAU,EAAE;oBAChC,KAAK,KAAK,CAAC,WAAW,CAAC,qBAAqB,YAAY,KAAK,CAAC,UAAU;gBAC1E;gBAEA,6BAA6B;gBAC7B,IAAI,YAAY,KAAK,CAAC,gBAAgB,EAAE;oBACtC,KAAK,KAAK,CAAC,WAAW,CAAC,4BAA4B,YAAY,KAAK,CAAC,gBAAgB;gBACvF;gBACA,IAAI,YAAY,KAAK,CAAC,aAAa,EAAE;oBACnC,KAAK,KAAK,CAAC,WAAW,CAAC,yBAAyB,YAAY,KAAK,CAAC,aAAa;gBACjF;gBAEA,uBAAuB;gBACvB,IAAI,YAAY,KAAK,CAAC,SAAS,EAAE;oBAC/B,KAAK,KAAK,CAAC,WAAW,CAAC,oBAAoB,YAAY,KAAK,CAAC,SAAS;gBACxE;gBACA,IAAI,YAAY,KAAK,CAAC,kBAAkB,EAAE;oBACxC,KAAK,KAAK,CAAC,WAAW,CAAC,8BAA8B,YAAY,KAAK,CAAC,kBAAkB;gBAC3F;gBAEA,yBAAyB;gBACzB,IAAI,YAAY,KAAK,CAAC,WAAW,EAAE;oBACjC,KAAK,KAAK,CAAC,WAAW,CAAC,sBAAsB,YAAY,KAAK,CAAC,WAAW;gBAC5E;gBAEA,gCAAgC;gBAChC,IAAI,YAAY,KAAK,CAAC,YAAY,EAAE;oBAClC,KAAK,KAAK,CAAC,WAAW,CAAC,uBAAuB,GAAG,YAAY,KAAK,CAAC,YAAY,CAAC,EAAE,CAAC;gBACrF;gBACA,IAAI,YAAY,KAAK,CAAC,cAAc,EAAE;oBACpC,KAAK,KAAK,CAAC,WAAW,CAAC,0BAA0B,GAAG,YAAY,KAAK,CAAC,cAAc,CAAC,EAAE,CAAC;gBAC1F;YACF;YAEA,QAAQ,GAAG,CAAC,CAAC,kBAAkB,EAAE,OAAO;QAC1C;qCAAG;QAAC;KAAM;IAEV,qBAAO;kBAAG;;AACZ;IAhES;MAAA;AAqEF,MAAM,mCAAmC,6JAAA,CAAA,UAAK,CAAC,SAAS;IAI7D,YAAY,KAA8B,CAAE;QAC1C,KAAK,CAAC;QACN,IAAI,CAAC,KAAK,GAAG;YAAE,UAAU;QAAM;IACjC;IAEA,OAAO,yBAAyB,KAAY,EAAuC;QACjF,OAAO;YAAE,UAAU;YAAM;QAAM;IACjC;IAEA,kBAAkB,KAAY,EAAE,SAA0B,EAAE;QAC1D,QAAQ,KAAK,CAAC,yBAAyB,OAAO;IAChD;IAEA,SAAS;QACP,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;YACvB,qBACE,6LAAC;gBAAI,OAAO;oBACV,SAAS;oBACT,eAAe;oBACf,YAAY;oBACZ,gBAAgB;oBAChB,WAAW;oBACX,SAAS;oBACT,WAAW;oBACX,YAAY;gBACd;;kCACE,6LAAC;wBAAG,OAAO;4BAAE,OAAO;4BAAW,cAAc;wBAAO;kCAAG;;;;;;kCAGvD,6LAAC;wBAAE,OAAO;4BAAE,OAAO;4BAAW,cAAc;wBAAO;kCAAG;;;;;;oBAGrD,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI,oDAAyB,+BAC5C,6LAAC;wBAAQ,OAAO;4BACd,cAAc;4BACd,SAAS;4BACT,iBAAiB;4BACjB,cAAc;4BACd,WAAW;4BACX,UAAU;4BACV,OAAO;wBACT;;0CACE,6LAAC;gCAAQ,OAAO;oCAAE,QAAQ;oCAAW,YAAY;gCAAO;0CAAG;;;;;;0CAG3D,6LAAC;gCAAI,OAAO;oCACV,WAAW;oCACX,UAAU;oCACV,UAAU;oCACV,YAAY;gCACd;;oCACG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO;oCACxB,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE;;;;;;;;;;;;;kCAIhE,6LAAC;wBACC,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;wBACrC,OAAO;4BACL,SAAS;4BACT,iBAAiB;4BACjB,OAAO;4BACP,QAAQ;4BACR,cAAc;4BACd,QAAQ;4BACR,UAAU;wBACZ;kCACD;;;;;;;;;;;;QAKP;QAEA,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;IAC5B;AACF;AAKO,SAAS,+BAA+B,EAAE,QAAQ,EAAsB;IAC7E,qBACE,6LAAC;kBACC,cAAA,6LAAC;sBACE;;;;;;;;;;;AAIT;MARgB;AAaT,SAAS,kBACd,SAAiC;IAEjC,MAAM,mBAAmB,CAAC,sBACxB,6LAAC;sBACC,cAAA,6LAAC;gBAAW,GAAG,KAAK;;;;;;;;;;;IAIxB,iBAAiB,WAAW,GAAG,CAAC,kBAAkB,EAAE,UAAU,WAAW,IAAI,UAAU,IAAI,CAAC,CAAC,CAAC;IAE9F,OAAO;AACT"}}, {"offset": {"line": 4985, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4991, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/theme/hooks.ts"], "sourcesContent": ["/**\n * Theme Hooks\n * Custom hooks for theme management\n */\n\n'use client';\n\nimport { useEffect, useState, useCallback } from 'react';\nimport { useAppProvider } from '@/stores';\nimport {\n  themeUtils,\n  type ThemeMode,\n  getThemeColors,\n  createThemeMediaQueryListener\n} from './utils';\n\n/**\n * Hook to use theme with global state integration\n */\nexport function useTheme() {\n  const app = useAppProvider();\n  const currentTheme = (app.theme?.mode as ThemeMode) || 'light';\n\n  const setTheme = useCallback((theme: ThemeMode) => {\n    app.setTheme({ mode: theme });\n    themeUtils.store(theme);\n    themeUtils.apply(theme);\n  }, [app]);\n\n  const toggleTheme = useCallback(() => {\n    const newTheme = themeUtils.toggle(currentTheme);\n    setTheme(newTheme);\n  }, [currentTheme, setTheme]);\n\n  const resetToSystem = useCallback(() => {\n    const systemTheme = themeUtils.getSystem();\n    setTheme(systemTheme);\n  }, [setTheme]);\n\n  return {\n    // Current theme state\n    theme: currentTheme,\n    isDark: themeUtils.isDark(currentTheme),\n    isLight: themeUtils.isLight(currentTheme),\n\n    // Theme colors\n    colors: getThemeColors(currentTheme),\n\n    // Theme actions\n    setTheme,\n    toggleTheme,\n    resetToSystem,\n\n    // Theme utilities\n    utils: themeUtils,\n  };\n}\n\n/**\n * Hook to listen to system theme changes\n */\nexport function useSystemTheme() {\n  const [systemTheme, setSystemTheme] = useState<ThemeMode>(() => {\n    if (typeof window === 'undefined') return 'light';\n    return themeUtils.getSystem();\n  });\n\n  useEffect(() => {\n    const cleanup = createThemeMediaQueryListener((theme) => {\n      setSystemTheme(theme);\n    });\n\n    return cleanup || undefined;\n  }, []);\n\n  return {\n    systemTheme,\n    isSystemDark: themeUtils.isDark(systemTheme),\n    isSystemLight: themeUtils.isLight(systemTheme),\n  };\n}\n\n/**\n * Hook to sync theme with system preferences\n */\nexport function useThemeSync(autoSync: boolean = false) {\n  const { theme, setTheme } = useTheme();\n  const { systemTheme } = useSystemTheme();\n\n  useEffect(() => {\n    if (autoSync && theme !== systemTheme) {\n      setTheme(systemTheme);\n    }\n  }, [autoSync, theme, systemTheme, setTheme]);\n\n  const syncWithSystem = useCallback(() => {\n    setTheme(systemTheme);\n  }, [systemTheme, setTheme]);\n\n  return {\n    theme,\n    systemTheme,\n    isInSync: theme === systemTheme,\n    syncWithSystem,\n  };\n}\n\n/**\n * Hook to get theme-aware styles\n */\nexport function useThemeStyles() {\n  const { theme, colors } = useTheme();\n\n  const getStyle = useCallback((\n    lightStyle: React.CSSProperties,\n    darkStyle: React.CSSProperties\n  ): React.CSSProperties => {\n    return themeUtils.isDark(theme) ? darkStyle : lightStyle;\n  }, [theme]);\n\n  const getColor = useCallback((\n    colorKey: keyof typeof colors\n  ) => {\n    return colors[colorKey];\n  }, [colors]);\n\n  const getBackgroundColor = useCallback((\n    backgroundKey: keyof typeof colors.background\n  ) => {\n    return colors.background[backgroundKey];\n  }, [colors]);\n\n  const getTextColor = useCallback((\n    textKey: keyof typeof colors.text\n  ) => {\n    return colors.text[textKey];\n  }, [colors]);\n\n  const getBorderColor = useCallback((\n    borderKey: keyof typeof colors.border\n  ) => {\n    return colors.border[borderKey];\n  }, [colors]);\n\n  return {\n    theme,\n    colors,\n    getStyle,\n    getColor,\n    getBackgroundColor,\n    getTextColor,\n    getBorderColor,\n\n    // Common styles\n    containerStyle: {\n      backgroundColor: colors.background.container,\n      color: colors.text.primary,\n    },\n\n    cardStyle: {\n      backgroundColor: colors.background.elevated,\n      color: colors.text.primary,\n      border: `1px solid ${colors.border.primary}`,\n    },\n\n    headerStyle: {\n      backgroundColor: colors.background.container,\n      color: colors.text.primary,\n      borderBottom: `1px solid ${colors.border.primary}`,\n    },\n  };\n}\n\n/**\n * Hook to manage theme persistence\n */\nexport function useThemePersistence() {\n  const { theme, setTheme } = useTheme();\n\n  const loadStoredTheme = useCallback(() => {\n    const stored = themeUtils.getStored();\n    if (stored && stored !== theme) {\n      setTheme(stored);\n    }\n  }, [theme, setTheme]);\n\n  const clearStoredTheme = useCallback(() => {\n    if (typeof window !== 'undefined') {\n      localStorage.removeItem('apisportsgame_theme');\n    }\n  }, []);\n\n  const resetToDefault = useCallback(() => {\n    clearStoredTheme();\n    setTheme('light');\n  }, [clearStoredTheme, setTheme]);\n\n  return {\n    theme,\n    loadStoredTheme,\n    clearStoredTheme,\n    resetToDefault,\n    hasStoredTheme: themeUtils.getStored() !== null,\n  };\n}\n\n/**\n * Hook for theme debugging (development only)\n */\nexport function useThemeDebug() {\n  const { theme, colors } = useTheme();\n  const { systemTheme } = useSystemTheme();\n\n  if (process.env.NODE_ENV !== 'development') {\n    return null;\n  }\n\n  const debugInfo = {\n    currentTheme: theme,\n    systemTheme,\n    storedTheme: themeUtils.getStored(),\n    effectiveTheme: themeUtils.getEffective(),\n    colors,\n    cssVariables: themeUtils.generateCSSVariables(theme),\n  };\n\n  const logThemeInfo = useCallback(() => {\n    console.group('🎨 Theme Debug Info');\n    console.log('Current Theme:', debugInfo.currentTheme);\n    console.log('System Theme:', debugInfo.systemTheme);\n    console.log('Stored Theme:', debugInfo.storedTheme);\n    console.log('Effective Theme:', debugInfo.effectiveTheme);\n    console.log('Colors:', debugInfo.colors);\n    console.log('CSS Variables:', debugInfo.cssVariables);\n    console.groupEnd();\n  }, [debugInfo]);\n\n  return {\n    debugInfo,\n    logThemeInfo,\n  };\n}\n\n/**\n * Hook to preload theme assets\n */\nexport function useThemePreload() {\n  const [isPreloaded, setIsPreloaded] = useState(false);\n\n  useEffect(() => {\n    // Preload theme-related assets\n    const preloadAssets = async () => {\n      try {\n        // Apply initial theme\n        const effectiveTheme = themeUtils.getEffective();\n        themeUtils.apply(effectiveTheme);\n        themeUtils.applyCSSVariables(effectiveTheme);\n\n        setIsPreloaded(true);\n        console.log('🎨 Theme assets preloaded');\n      } catch (error) {\n        console.error('Failed to preload theme assets:', error);\n        setIsPreloaded(true); // Set to true anyway to prevent blocking\n      }\n    };\n\n    preloadAssets();\n  }, []);\n\n  return {\n    isPreloaded,\n  };\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;AAID;AACA;AACA;AADA;AA6MM;;AAhNN;;;;AAcO,SAAS;;IACd,MAAM,MAAM,CAAA,GAAA,qIAAA,CAAA,iBAAc,AAAD;IACzB,MAAM,eAAe,AAAC,IAAI,KAAK,EAAE,QAAsB;IAEvD,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0CAAE,CAAC;YAC5B,IAAI,QAAQ,CAAC;gBAAE,MAAM;YAAM;YAC3B,wHAAA,CAAA,aAAU,CAAC,KAAK,CAAC;YACjB,wHAAA,CAAA,aAAU,CAAC,KAAK,CAAC;QACnB;yCAAG;QAAC;KAAI;IAER,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6CAAE;YAC9B,MAAM,WAAW,wHAAA,CAAA,aAAU,CAAC,MAAM,CAAC;YACnC,SAAS;QACX;4CAAG;QAAC;QAAc;KAAS;IAE3B,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;+CAAE;YAChC,MAAM,cAAc,wHAAA,CAAA,aAAU,CAAC,SAAS;YACxC,SAAS;QACX;8CAAG;QAAC;KAAS;IAEb,OAAO;QACL,sBAAsB;QACtB,OAAO;QACP,QAAQ,wHAAA,CAAA,aAAU,CAAC,MAAM,CAAC;QAC1B,SAAS,wHAAA,CAAA,aAAU,CAAC,OAAO,CAAC;QAE5B,eAAe;QACf,QAAQ,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD,EAAE;QAEvB,gBAAgB;QAChB;QACA;QACA;QAEA,kBAAkB;QAClB,OAAO,wHAAA,CAAA,aAAU;IACnB;AACF;GArCgB;;QACF,qIAAA,CAAA,iBAAc;;;AAyCrB,SAAS;;IACd,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;mCAAa;YACxD,uCAAmC;;YAAc;YACjD,OAAO,wHAAA,CAAA,aAAU,CAAC,SAAS;QAC7B;;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM,UAAU,CAAA,GAAA,wHAAA,CAAA,gCAA6B,AAAD;oDAAE,CAAC;oBAC7C,eAAe;gBACjB;;YAEA,OAAO,WAAW;QACpB;mCAAG,EAAE;IAEL,OAAO;QACL;QACA,cAAc,wHAAA,CAAA,aAAU,CAAC,MAAM,CAAC;QAChC,eAAe,wHAAA,CAAA,aAAU,CAAC,OAAO,CAAC;IACpC;AACF;IAnBgB;AAwBT,SAAS,aAAa,WAAoB,KAAK;;IACpD,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG;IAC5B,MAAM,EAAE,WAAW,EAAE,GAAG;IAExB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,YAAY,UAAU,aAAa;gBACrC,SAAS;YACX;QACF;iCAAG;QAAC;QAAU;QAAO;QAAa;KAAS;IAE3C,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;oDAAE;YACjC,SAAS;QACX;mDAAG;QAAC;QAAa;KAAS;IAE1B,OAAO;QACL;QACA;QACA,UAAU,UAAU;QACpB;IACF;AACF;IApBgB;;QACc;QACJ;;;AAuBnB,SAAS;;IACd,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG;IAE1B,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;gDAAE,CAC3B,YACA;YAEA,OAAO,wHAAA,CAAA,aAAU,CAAC,MAAM,CAAC,SAAS,YAAY;QAChD;+CAAG;QAAC;KAAM;IAEV,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;gDAAE,CAC3B;YAEA,OAAO,MAAM,CAAC,SAAS;QACzB;+CAAG;QAAC;KAAO;IAEX,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0DAAE,CACrC;YAEA,OAAO,OAAO,UAAU,CAAC,cAAc;QACzC;yDAAG;QAAC;KAAO;IAEX,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;oDAAE,CAC/B;YAEA,OAAO,OAAO,IAAI,CAAC,QAAQ;QAC7B;mDAAG;QAAC;KAAO;IAEX,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE,CACjC;YAEA,OAAO,OAAO,MAAM,CAAC,UAAU;QACjC;qDAAG;QAAC;KAAO;IAEX,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QAEA,gBAAgB;QAChB,gBAAgB;YACd,iBAAiB,OAAO,UAAU,CAAC,SAAS;YAC5C,OAAO,OAAO,IAAI,CAAC,OAAO;QAC5B;QAEA,WAAW;YACT,iBAAiB,OAAO,UAAU,CAAC,QAAQ;YAC3C,OAAO,OAAO,IAAI,CAAC,OAAO;YAC1B,QAAQ,CAAC,UAAU,EAAE,OAAO,MAAM,CAAC,OAAO,EAAE;QAC9C;QAEA,aAAa;YACX,iBAAiB,OAAO,UAAU,CAAC,SAAS;YAC5C,OAAO,OAAO,IAAI,CAAC,OAAO;YAC1B,cAAc,CAAC,UAAU,EAAE,OAAO,MAAM,CAAC,OAAO,EAAE;QACpD;IACF;AACF;IA7DgB;;QACY;;;AAiErB,SAAS;;IACd,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG;IAE5B,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4DAAE;YAClC,MAAM,SAAS,wHAAA,CAAA,aAAU,CAAC,SAAS;YACnC,IAAI,UAAU,WAAW,OAAO;gBAC9B,SAAS;YACX;QACF;2DAAG;QAAC;QAAO;KAAS;IAEpB,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6DAAE;YACnC,wCAAmC;gBACjC,aAAa,UAAU,CAAC;YAC1B;QACF;4DAAG,EAAE;IAEL,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2DAAE;YACjC;YACA,SAAS;QACX;0DAAG;QAAC;QAAkB;KAAS;IAE/B,OAAO;QACL;QACA;QACA;QACA;QACA,gBAAgB,wHAAA,CAAA,aAAU,CAAC,SAAS,OAAO;IAC7C;AACF;IA5BgB;;QACc;;;AAgCvB,SAAS;;IACd,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG;IAC1B,MAAM,EAAE,WAAW,EAAE,GAAG;IAExB,uCAA4C;;IAE5C;IAEA,MAAM,YAAY;QAChB,cAAc;QACd;QACA,aAAa,wHAAA,CAAA,aAAU,CAAC,SAAS;QACjC,gBAAgB,wHAAA,CAAA,aAAU,CAAC,YAAY;QACvC;QACA,cAAc,wHAAA,CAAA,aAAU,CAAC,oBAAoB,CAAC;IAChD;IAEA,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAAE;YAC/B,QAAQ,KAAK,CAAC;YACd,QAAQ,GAAG,CAAC,kBAAkB,UAAU,YAAY;YACpD,QAAQ,GAAG,CAAC,iBAAiB,UAAU,WAAW;YAClD,QAAQ,GAAG,CAAC,iBAAiB,UAAU,WAAW;YAClD,QAAQ,GAAG,CAAC,oBAAoB,UAAU,cAAc;YACxD,QAAQ,GAAG,CAAC,WAAW,UAAU,MAAM;YACvC,QAAQ,GAAG,CAAC,kBAAkB,UAAU,YAAY;YACpD,QAAQ,QAAQ;QAClB;kDAAG;QAAC;KAAU;IAEd,OAAO;QACL;QACA;IACF;AACF;IAhCgB;;QACY;QACF;;;AAmCnB,SAAS;;IACd,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,+BAA+B;YAC/B,MAAM;2DAAgB;oBACpB,IAAI;wBACF,sBAAsB;wBACtB,MAAM,iBAAiB,wHAAA,CAAA,aAAU,CAAC,YAAY;wBAC9C,wHAAA,CAAA,aAAU,CAAC,KAAK,CAAC;wBACjB,wHAAA,CAAA,aAAU,CAAC,iBAAiB,CAAC;wBAE7B,eAAe;wBACf,QAAQ,GAAG,CAAC;oBACd,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,mCAAmC;wBACjD,eAAe,OAAO,yCAAyC;oBACjE;gBACF;;YAEA;QACF;oCAAG,EAAE;IAEL,OAAO;QACL;IACF;AACF;IA1BgB"}}, {"offset": {"line": 5312, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5318, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/theme/index.ts"], "sourcesContent": ["/**\n * Theme Index\n * Central export for all theme functionality\n */\n\n// Theme configuration\nexport * from './config';\n\n// Theme provider\nexport * from './theme-provider';\n\n// Theme utilities\nexport * from './utils';\n\n// Theme hooks\nexport * from './hooks';\n\n// CSS variables (import in your main CSS file)\n// import './variables.css';\n\n/**\n * Theme library metadata\n */\nexport const THEME_VERSION = '1.0.0';\nexport const THEME_NAME = 'APISportsGame Theme System';\n\n/**\n * Setup function for theme system\n */\nexport function setupTheme() {\n  console.log(`${THEME_NAME} v${THEME_VERSION} initialized`);\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,sBAAsB;;;;;;;;;;AAkBf,MAAM,gBAAgB;AACtB,MAAM,aAAa;AAKnB,SAAS;IACd,QAAQ,GAAG,CAAC,GAAG,WAAW,EAAE,EAAE,cAAc,YAAY,CAAC;AAC3D"}}, {"offset": {"line": 5339, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5357, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/providers/app-provider.tsx"], "sourcesContent": ["/**\n * App Provider - Combined provider for all application contexts\n * Integrates Store Provider and Query Provider\n */\n\n'use client';\n\nimport React, { ReactNode } from 'react';\nimport { StoreProvider } from '@/stores';\nimport { QueryProviderWithErrorBoundary } from '@/lib/query-provider';\nimport { ThemeProviderWithErrorBoundary } from '@/theme';\n\n/**\n * App provider props\n */\ninterface AppProviderProps {\n  children: ReactNode;\n}\n\n/**\n * Combined app provider component\n * Provides all necessary contexts for the application\n */\nexport function AppProvider({ children }: AppProviderProps) {\n  return (\n    <QueryProviderWithErrorBoundary>\n      <StoreProvider>\n        <ThemeProviderWithErrorBoundary>\n          <AppInitializer>\n            {children}\n          </AppInitializer>\n        </ThemeProviderWithErrorBoundary>\n      </StoreProvider>\n    </QueryProviderWithErrorBoundary>\n  );\n}\n\n/**\n * App initializer component\n * Handles app-wide initialization logic\n */\nfunction AppInitializer({ children }: { children: ReactNode }) {\n  React.useEffect(() => {\n    // Initialize app-wide settings\n    initializeApp();\n  }, []);\n\n  return <>{children}</>;\n}\n\n/**\n * Initialize application\n */\nasync function initializeApp() {\n  try {\n    console.log('🚀 APISportsGame CMS initializing...');\n\n    // Initialize theme\n    initializeTheme();\n\n    // Initialize error tracking (production only)\n    if (process.env.NODE_ENV === 'production') {\n      initializeErrorTracking();\n    }\n\n    // Initialize performance monitoring (development only)\n    if (process.env.NODE_ENV === 'development') {\n      initializePerformanceMonitoring();\n    }\n\n    console.log('✅ APISportsGame CMS initialized successfully');\n  } catch (error) {\n    console.error('❌ Failed to initialize APISportsGame CMS:', error);\n  }\n}\n\n/**\n * Initialize theme system\n */\nfunction initializeTheme() {\n  try {\n    // Get saved theme from localStorage\n    const savedTheme = localStorage.getItem('apisportsgame_theme');\n\n    if (savedTheme) {\n      // Apply saved theme\n      document.documentElement.setAttribute('data-theme', savedTheme);\n    } else {\n      // Detect system theme preference\n      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\n      const defaultTheme = prefersDark ? 'dark' : 'light';\n      document.documentElement.setAttribute('data-theme', defaultTheme);\n    }\n\n    console.log('🎨 Theme system initialized');\n  } catch (error) {\n    console.warn('⚠️ Failed to initialize theme system:', error);\n  }\n}\n\n/**\n * Initialize error tracking (production only)\n */\nfunction initializeErrorTracking() {\n  try {\n    // Global error handler\n    window.addEventListener('error', (event) => {\n      console.error('[Global Error]', event.error);\n      // TODO: Send to error tracking service\n    });\n\n    // Unhandled promise rejection handler\n    window.addEventListener('unhandledrejection', (event) => {\n      console.error('[Unhandled Promise Rejection]', event.reason);\n      // TODO: Send to error tracking service\n    });\n\n    console.log('🔍 Error tracking initialized');\n  } catch (error) {\n    console.warn('⚠️ Failed to initialize error tracking:', error);\n  }\n}\n\n/**\n * Initialize performance monitoring (development only)\n */\nfunction initializePerformanceMonitoring() {\n  try {\n    // Performance observer for navigation timing\n    if ('PerformanceObserver' in window) {\n      const observer = new PerformanceObserver((list) => {\n        for (const entry of list.getEntries()) {\n          if (entry.entryType === 'navigation') {\n            console.log('[Performance] Navigation timing:', entry);\n          }\n        }\n      });\n\n      observer.observe({ entryTypes: ['navigation'] });\n    }\n\n    // Log initial performance metrics\n    setTimeout(() => {\n      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;\n      if (navigation) {\n        console.log('[Performance] Page load metrics:', {\n          domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,\n          loadComplete: navigation.loadEventEnd - navigation.loadEventStart,\n          totalTime: navigation.loadEventEnd - navigation.fetchStart,\n        });\n      }\n    }, 1000);\n\n    console.log('📊 Performance monitoring initialized');\n  } catch (error) {\n    console.warn('⚠️ Failed to initialize performance monitoring:', error);\n  }\n}\n\n/**\n * App provider error boundary\n */\nexport class AppProviderErrorBoundary extends React.Component<\n  { children: ReactNode },\n  { hasError: boolean; error?: Error }\n> {\n  constructor(props: { children: ReactNode }) {\n    super(props);\n    this.state = { hasError: false };\n  }\n\n  static getDerivedStateFromError(error: Error): { hasError: boolean; error: Error } {\n    return { hasError: true, error };\n  }\n\n  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {\n    console.error('[AppProvider Error]', error, errorInfo);\n  }\n\n  render() {\n    if (this.state.hasError) {\n      return (\n        <AppErrorFallback\n          error={this.state.error}\n          onRetry={() => this.setState({ hasError: false, error: undefined })}\n        />\n      );\n    }\n\n    return this.props.children;\n  }\n}\n\n/**\n * App error fallback component\n */\nfunction AppErrorFallback({\n  error,\n  onRetry\n}: {\n  error?: Error;\n  onRetry: () => void;\n}) {\n  return (\n    <div style={{\n      display: 'flex',\n      flexDirection: 'column',\n      alignItems: 'center',\n      justifyContent: 'center',\n      minHeight: '100vh',\n      padding: '20px',\n      textAlign: 'center',\n      fontFamily: 'system-ui, sans-serif',\n      backgroundColor: '#f9fafb',\n    }}>\n      <div style={{\n        backgroundColor: 'white',\n        padding: '40px',\n        borderRadius: '12px',\n        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',\n        maxWidth: '600px',\n        width: '100%',\n      }}>\n        <h1 style={{\n          color: '#dc2626',\n          marginBottom: '16px',\n          fontSize: '24px',\n          fontWeight: 'bold',\n        }}>\n          APISportsGame CMS Error\n        </h1>\n        <p style={{\n          color: '#6b7280',\n          marginBottom: '24px',\n          lineHeight: '1.6',\n        }}>\n          An unexpected error occurred while loading the application.\n          Please try refreshing the page or contact support if the problem persists.\n        </p>\n\n        {error && process.env.NODE_ENV === 'development' && (\n          <details style={{\n            marginBottom: '24px',\n            padding: '16px',\n            backgroundColor: '#f3f4f6',\n            borderRadius: '8px',\n            textAlign: 'left',\n          }}>\n            <summary style={{\n              cursor: 'pointer',\n              fontWeight: 'bold',\n              marginBottom: '12px',\n            }}>\n              Error Details (Development)\n            </summary>\n            <pre style={{\n              fontSize: '12px',\n              overflow: 'auto',\n              whiteSpace: 'pre-wrap',\n              margin: 0,\n            }}>\n              {error.message}\n              {error.stack && `\\n\\n${error.stack}`}\n            </pre>\n          </details>\n        )}\n\n        <div style={{ display: 'flex', gap: '12px', justifyContent: 'center' }}>\n          <button\n            onClick={onRetry}\n            style={{\n              padding: '12px 24px',\n              backgroundColor: '#3b82f6',\n              color: 'white',\n              border: 'none',\n              borderRadius: '6px',\n              cursor: 'pointer',\n              fontSize: '16px',\n              fontWeight: '500',\n            }}\n          >\n            Try Again\n          </button>\n          <button\n            onClick={() => window.location.reload()}\n            style={{\n              padding: '12px 24px',\n              backgroundColor: '#6b7280',\n              color: 'white',\n              border: 'none',\n              borderRadius: '6px',\n              cursor: 'pointer',\n              fontSize: '16px',\n              fontWeight: '500',\n            }}\n          >\n            Refresh Page\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n}\n\n/**\n * HOC to wrap components with App Provider\n */\nexport function withAppProvider<P extends object>(\n  Component: React.ComponentType<P>\n) {\n  const WrappedComponent = (props: P) => (\n    <AppProvider>\n      <Component {...props} />\n    </AppProvider>\n  );\n\n  WrappedComponent.displayName = `withAppProvider(${Component.displayName || Component.name})`;\n\n  return WrappedComponent;\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;AAID;AACA;AACA;AACA;AAFA;AAEA;AAmDQ;;;AAxDR;;;;;AAkBO,SAAS,YAAY,EAAE,QAAQ,EAAoB;IACxD,qBACE,6LAAC,mIAAA,CAAA,iCAA8B;kBAC7B,cAAA,6LAAC,sIAAA,CAAA,gBAAa;sBACZ,cAAA,6LAAC,qIAAA,CAAA,iCAA8B;0BAC7B,cAAA,6LAAC;8BACE;;;;;;;;;;;;;;;;;;;;;AAMb;KAZgB;AAchB;;;CAGC,GACD,SAAS,eAAe,EAAE,QAAQ,EAA2B;;IAC3D,6JAAA,CAAA,UAAK,CAAC,SAAS;oCAAC;YACd,+BAA+B;YAC/B;QACF;mCAAG,EAAE;IAEL,qBAAO;kBAAG;;AACZ;GAPS;MAAA;AAST;;CAEC,GACD,eAAe;IACb,IAAI;QACF,QAAQ,GAAG,CAAC;QAEZ,mBAAmB;QACnB;QAEA,8CAA8C;QAC9C,uCAA2C;;QAE3C;QAEA,uDAAuD;QACvD,wCAA4C;YAC1C;QACF;QAEA,QAAQ,GAAG,CAAC;IACd,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6CAA6C;IAC7D;AACF;AAEA;;CAEC,GACD,SAAS;IACP,IAAI;QACF,oCAAoC;QACpC,MAAM,aAAa,aAAa,OAAO,CAAC;QAExC,IAAI,YAAY;YACd,oBAAoB;YACpB,SAAS,eAAe,CAAC,YAAY,CAAC,cAAc;QACtD,OAAO;YACL,iCAAiC;YACjC,MAAM,cAAc,OAAO,UAAU,CAAC,gCAAgC,OAAO;YAC7E,MAAM,eAAe,cAAc,SAAS;YAC5C,SAAS,eAAe,CAAC,YAAY,CAAC,cAAc;QACtD;QAEA,QAAQ,GAAG,CAAC;IACd,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,yCAAyC;IACxD;AACF;AAEA;;CAEC,GACD,SAAS;IACP,IAAI;QACF,uBAAuB;QACvB,OAAO,gBAAgB,CAAC,SAAS,CAAC;YAChC,QAAQ,KAAK,CAAC,kBAAkB,MAAM,KAAK;QAC3C,uCAAuC;QACzC;QAEA,sCAAsC;QACtC,OAAO,gBAAgB,CAAC,sBAAsB,CAAC;YAC7C,QAAQ,KAAK,CAAC,iCAAiC,MAAM,MAAM;QAC3D,uCAAuC;QACzC;QAEA,QAAQ,GAAG,CAAC;IACd,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,2CAA2C;IAC1D;AACF;AAEA;;CAEC,GACD,SAAS;IACP,IAAI;QACF,6CAA6C;QAC7C,IAAI,yBAAyB,QAAQ;YACnC,MAAM,WAAW,IAAI,oBAAoB,CAAC;gBACxC,KAAK,MAAM,SAAS,KAAK,UAAU,GAAI;oBACrC,IAAI,MAAM,SAAS,KAAK,cAAc;wBACpC,QAAQ,GAAG,CAAC,oCAAoC;oBAClD;gBACF;YACF;YAEA,SAAS,OAAO,CAAC;gBAAE,YAAY;oBAAC;iBAAa;YAAC;QAChD;QAEA,kCAAkC;QAClC,WAAW;YACT,MAAM,aAAa,YAAY,gBAAgB,CAAC,aAAa,CAAC,EAAE;YAChE,IAAI,YAAY;gBACd,QAAQ,GAAG,CAAC,oCAAoC;oBAC9C,kBAAkB,WAAW,wBAAwB,GAAG,WAAW,0BAA0B;oBAC7F,cAAc,WAAW,YAAY,GAAG,WAAW,cAAc;oBACjE,WAAW,WAAW,YAAY,GAAG,WAAW,UAAU;gBAC5D;YACF;QACF,GAAG;QAEH,QAAQ,GAAG,CAAC;IACd,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,mDAAmD;IAClE;AACF;AAKO,MAAM,iCAAiC,6JAAA,CAAA,UAAK,CAAC,SAAS;IAI3D,YAAY,KAA8B,CAAE;QAC1C,KAAK,CAAC;QACN,IAAI,CAAC,KAAK,GAAG;YAAE,UAAU;QAAM;IACjC;IAEA,OAAO,yBAAyB,KAAY,EAAuC;QACjF,OAAO;YAAE,UAAU;YAAM;QAAM;IACjC;IAEA,kBAAkB,KAAY,EAAE,SAA0B,EAAE;QAC1D,QAAQ,KAAK,CAAC,uBAAuB,OAAO;IAC9C;IAEA,SAAS;QACP,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;YACvB,qBACE,6LAAC;gBACC,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK;gBACvB,SAAS,IAAM,IAAI,CAAC,QAAQ,CAAC;wBAAE,UAAU;wBAAO,OAAO;oBAAU;;;;;;QAGvE;QAEA,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;IAC5B;AACF;AAEA;;CAEC,GACD,SAAS,iBAAiB,EACxB,KAAK,EACL,OAAO,EAIR;IACC,qBACE,6LAAC;QAAI,OAAO;YACV,SAAS;YACT,eAAe;YACf,YAAY;YACZ,gBAAgB;YAChB,WAAW;YACX,SAAS;YACT,WAAW;YACX,YAAY;YACZ,iBAAiB;QACnB;kBACE,cAAA,6LAAC;YAAI,OAAO;gBACV,iBAAiB;gBACjB,SAAS;gBACT,cAAc;gBACd,WAAW;gBACX,UAAU;gBACV,OAAO;YACT;;8BACE,6LAAC;oBAAG,OAAO;wBACT,OAAO;wBACP,cAAc;wBACd,UAAU;wBACV,YAAY;oBACd;8BAAG;;;;;;8BAGH,6LAAC;oBAAE,OAAO;wBACR,OAAO;wBACP,cAAc;wBACd,YAAY;oBACd;8BAAG;;;;;;gBAKF,SAAS,oDAAyB,+BACjC,6LAAC;oBAAQ,OAAO;wBACd,cAAc;wBACd,SAAS;wBACT,iBAAiB;wBACjB,cAAc;wBACd,WAAW;oBACb;;sCACE,6LAAC;4BAAQ,OAAO;gCACd,QAAQ;gCACR,YAAY;gCACZ,cAAc;4BAChB;sCAAG;;;;;;sCAGH,6LAAC;4BAAI,OAAO;gCACV,UAAU;gCACV,UAAU;gCACV,YAAY;gCACZ,QAAQ;4BACV;;gCACG,MAAM,OAAO;gCACb,MAAM,KAAK,IAAI,CAAC,IAAI,EAAE,MAAM,KAAK,EAAE;;;;;;;;;;;;;8BAK1C,6LAAC;oBAAI,OAAO;wBAAE,SAAS;wBAAQ,KAAK;wBAAQ,gBAAgB;oBAAS;;sCACnE,6LAAC;4BACC,SAAS;4BACT,OAAO;gCACL,SAAS;gCACT,iBAAiB;gCACjB,OAAO;gCACP,QAAQ;gCACR,cAAc;gCACd,QAAQ;gCACR,UAAU;gCACV,YAAY;4BACd;sCACD;;;;;;sCAGD,6LAAC;4BACC,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;4BACrC,OAAO;gCACL,SAAS;gCACT,iBAAiB;gCACjB,OAAO;gCACP,QAAQ;gCACR,cAAc;gCACd,QAAQ;gCACR,UAAU;gCACV,YAAY;4BACd;sCACD;;;;;;;;;;;;;;;;;;;;;;;AAOX;MA1GS;AA+GF,SAAS,gBACd,SAAiC;IAEjC,MAAM,mBAAmB,CAAC,sBACxB,6LAAC;sBACC,cAAA,6LAAC;gBAAW,GAAG,KAAK;;;;;;;;;;;IAIxB,iBAAiB,WAAW,GAAG,CAAC,gBAAgB,EAAE,UAAU,WAAW,IAAI,UAAU,IAAI,CAAC,CAAC,CAAC;IAE5F,OAAO;AACT"}}, {"offset": {"line": 5731, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5737, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/lib/query-error-handler.ts"], "sourcesContent": ["/**\n * Query Error Handler\n * Centralized error handling for TanStack Query\n */\n\nimport { QueryClient } from '@tanstack/react-query';\n\n/**\n * API Error interface\n */\nexport interface ApiError {\n  status: number;\n  statusText: string;\n  message: string;\n  details?: any;\n  timestamp: string;\n}\n\n/**\n * Error types for different scenarios\n */\nexport enum ErrorType {\n  NETWORK = 'NETWORK',\n  AUTHENTICATION = 'AUTHENTICATION',\n  AUTHORIZATION = 'AUTHORIZATION',\n  VALIDATION = 'VALIDATION',\n  SERVER = 'SERVER',\n  UNKNOWN = 'UNKNOWN',\n}\n\n/**\n * Determine error type based on status code\n */\nexport function getErrorType(status: number): ErrorType {\n  if (status === 401) return ErrorType.AUTHENTICATION;\n  if (status === 403) return ErrorType.AUTHORIZATION;\n  if (status >= 400 && status < 500) return ErrorType.VALIDATION;\n  if (status >= 500) return ErrorType.SERVER;\n  if (status === 0) return ErrorType.NETWORK;\n  return ErrorType.UNKNOWN;\n}\n\n/**\n * Create standardized API error\n */\nexport function createApiError(\n  status: number,\n  statusText: string,\n  message: string,\n  details?: any\n): ApiError {\n  return {\n    status,\n    statusText,\n    message,\n    details,\n    timestamp: new Date().toISOString(),\n  };\n}\n\n/**\n * Parse error response from API\n */\nexport async function parseErrorResponse(response: Response): Promise<ApiError> {\n  let message = response.statusText || 'An error occurred';\n  let details = null;\n\n  try {\n    const errorData = await response.json();\n    message = errorData.message || errorData.error || message;\n    details = errorData.details || errorData;\n  } catch {\n    // If response is not JSON, use status text\n  }\n\n  return createApiError(response.status, response.statusText, message, details);\n}\n\n/**\n * Global error handler for queries\n */\nexport function createGlobalErrorHandler() {\n  return (error: unknown) => {\n    console.error('[Query Error]', error);\n\n    // Handle different types of errors\n    if (error instanceof Error) {\n      // Network errors, parsing errors, etc.\n      console.error('Error details:', {\n        name: error.name,\n        message: error.message,\n        stack: error.stack,\n      });\n    }\n\n    // Handle API errors\n    if (isApiError(error)) {\n      handleApiError(error);\n    }\n  };\n}\n\n/**\n * Check if error is an API error\n */\nexport function isApiError(error: unknown): error is ApiError {\n  return (\n    typeof error === 'object' &&\n    error !== null &&\n    'status' in error &&\n    'message' in error\n  );\n}\n\n/**\n * Handle specific API error types\n */\nexport function handleApiError(error: ApiError) {\n  const errorType = getErrorType(error.status);\n\n  switch (errorType) {\n    case ErrorType.AUTHENTICATION:\n      handleAuthenticationError(error);\n      break;\n    case ErrorType.AUTHORIZATION:\n      handleAuthorizationError(error);\n      break;\n    case ErrorType.VALIDATION:\n      handleValidationError(error);\n      break;\n    case ErrorType.SERVER:\n      handleServerError(error);\n      break;\n    case ErrorType.NETWORK:\n      handleNetworkError(error);\n      break;\n    default:\n      handleUnknownError(error);\n  }\n}\n\n/**\n * Handle authentication errors (401)\n */\nfunction handleAuthenticationError(error: ApiError) {\n  console.warn('[Auth Error]', error.message);\n  \n  // In development mode, authentication is disabled\n  if (process.env.NODE_ENV === 'development') {\n    console.log('[Dev Mode] Authentication error ignored');\n    return;\n  }\n  \n  // In production, redirect to login or refresh token\n  // This will be implemented when auth system is ready\n}\n\n/**\n * Handle authorization errors (403)\n */\nfunction handleAuthorizationError(error: ApiError) {\n  console.warn('[Authorization Error]', error.message);\n  \n  // Show user-friendly message about insufficient permissions\n  // This will be integrated with notification system\n}\n\n/**\n * Handle validation errors (400-499)\n */\nfunction handleValidationError(error: ApiError) {\n  console.warn('[Validation Error]', error.message);\n  \n  // These are usually handled by individual components\n  // Global handler just logs for debugging\n}\n\n/**\n * Handle server errors (500+)\n */\nfunction handleServerError(error: ApiError) {\n  console.error('[Server Error]', error.message);\n  \n  // Show generic error message to user\n  // Log detailed error for debugging\n}\n\n/**\n * Handle network errors\n */\nfunction handleNetworkError(error: ApiError) {\n  console.error('[Network Error]', error.message);\n  \n  // Show network connectivity message\n  // Suggest retry or check connection\n}\n\n/**\n * Handle unknown errors\n */\nfunction handleUnknownError(error: ApiError) {\n  console.error('[Unknown Error]', error);\n  \n  // Show generic error message\n  // Log for investigation\n}\n\n/**\n * Error boundary for query errors\n */\nexport function setupQueryErrorHandling(queryClient: QueryClient) {\n  // Set up global error handler\n  queryClient.setDefaultOptions({\n    queries: {\n      ...queryClient.getDefaultOptions().queries,\n      throwOnError: false, // Handle errors gracefully\n    },\n    mutations: {\n      ...queryClient.getDefaultOptions().mutations,\n      throwOnError: false, // Handle errors gracefully\n    },\n  });\n\n  // Set up global error handler\n  queryClient.setMutationDefaults(['mutation'], {\n    onError: createGlobalErrorHandler(),\n  });\n}\n\n/**\n * Utility functions for error handling\n */\nexport const errorUtils = {\n  /**\n   * Check if error should trigger retry\n   */\n  shouldRetry: (error: unknown): boolean => {\n    if (isApiError(error)) {\n      const errorType = getErrorType(error.status);\n      // Don't retry client errors (4xx)\n      return errorType !== ErrorType.VALIDATION && \n             errorType !== ErrorType.AUTHENTICATION && \n             errorType !== ErrorType.AUTHORIZATION;\n    }\n    return true; // Retry network and unknown errors\n  },\n\n  /**\n   * Get user-friendly error message\n   */\n  getUserMessage: (error: unknown): string => {\n    if (isApiError(error)) {\n      const errorType = getErrorType(error.status);\n      \n      switch (errorType) {\n        case ErrorType.AUTHENTICATION:\n          return 'Please log in to continue';\n        case ErrorType.AUTHORIZATION:\n          return 'You do not have permission to perform this action';\n        case ErrorType.VALIDATION:\n          return error.message || 'Please check your input and try again';\n        case ErrorType.SERVER:\n          return 'Server error occurred. Please try again later';\n        case ErrorType.NETWORK:\n          return 'Network error. Please check your connection';\n        default:\n          return 'An unexpected error occurred';\n      }\n    }\n    \n    return 'An unexpected error occurred';\n  },\n\n  /**\n   * Check if error is retryable\n   */\n  isRetryable: (error: unknown): boolean => {\n    if (isApiError(error)) {\n      return error.status >= 500 || error.status === 0;\n    }\n    return true;\n  },\n} as const;\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;AAiJK;AA/HC,IAAA,AAAK,mCAAA;;;;;;;WAAA;;AAYL,SAAS,aAAa,MAAc;IACzC,IAAI,WAAW,KAAK;IACpB,IAAI,WAAW,KAAK;IACpB,IAAI,UAAU,OAAO,SAAS,KAAK;IACnC,IAAI,UAAU,KAAK;IACnB,IAAI,WAAW,GAAG;IAClB;AACF;AAKO,SAAS,eACd,MAAc,EACd,UAAkB,EAClB,OAAe,EACf,OAAa;IAEb,OAAO;QACL;QACA;QACA;QACA;QACA,WAAW,IAAI,OAAO,WAAW;IACnC;AACF;AAKO,eAAe,mBAAmB,QAAkB;IACzD,IAAI,UAAU,SAAS,UAAU,IAAI;IACrC,IAAI,UAAU;IAEd,IAAI;QACF,MAAM,YAAY,MAAM,SAAS,IAAI;QACrC,UAAU,UAAU,OAAO,IAAI,UAAU,KAAK,IAAI;QAClD,UAAU,UAAU,OAAO,IAAI;IACjC,EAAE,OAAM;IACN,2CAA2C;IAC7C;IAEA,OAAO,eAAe,SAAS,MAAM,EAAE,SAAS,UAAU,EAAE,SAAS;AACvE;AAKO,SAAS;IACd,OAAO,CAAC;QACN,QAAQ,KAAK,CAAC,iBAAiB;QAE/B,mCAAmC;QACnC,IAAI,iBAAiB,OAAO;YAC1B,uCAAuC;YACvC,QAAQ,KAAK,CAAC,kBAAkB;gBAC9B,MAAM,MAAM,IAAI;gBAChB,SAAS,MAAM,OAAO;gBACtB,OAAO,MAAM,KAAK;YACpB;QACF;QAEA,oBAAoB;QACpB,IAAI,WAAW,QAAQ;YACrB,eAAe;QACjB;IACF;AACF;AAKO,SAAS,WAAW,KAAc;IACvC,OACE,OAAO,UAAU,YACjB,UAAU,QACV,YAAY,SACZ,aAAa;AAEjB;AAKO,SAAS,eAAe,KAAe;IAC5C,MAAM,YAAY,aAAa,MAAM,MAAM;IAE3C,OAAQ;QACN;YACE,0BAA0B;YAC1B;QACF;YACE,yBAAyB;YACzB;QACF;YACE,sBAAsB;YACtB;QACF;YACE,kBAAkB;YAClB;QACF;YACE,mBAAmB;YACnB;QACF;YACE,mBAAmB;IACvB;AACF;AAEA;;CAEC,GACD,SAAS,0BAA0B,KAAe;IAChD,QAAQ,IAAI,CAAC,gBAAgB,MAAM,OAAO;IAE1C,kDAAkD;IAClD,wCAA4C;QAC1C,QAAQ,GAAG,CAAC;QACZ;IACF;AAEA,oDAAoD;AACpD,qDAAqD;AACvD;AAEA;;CAEC,GACD,SAAS,yBAAyB,KAAe;IAC/C,QAAQ,IAAI,CAAC,yBAAyB,MAAM,OAAO;AAEnD,4DAA4D;AAC5D,mDAAmD;AACrD;AAEA;;CAEC,GACD,SAAS,sBAAsB,KAAe;IAC5C,QAAQ,IAAI,CAAC,sBAAsB,MAAM,OAAO;AAEhD,qDAAqD;AACrD,yCAAyC;AAC3C;AAEA;;CAEC,GACD,SAAS,kBAAkB,KAAe;IACxC,QAAQ,KAAK,CAAC,kBAAkB,MAAM,OAAO;AAE7C,qCAAqC;AACrC,mCAAmC;AACrC;AAEA;;CAEC,GACD,SAAS,mBAAmB,KAAe;IACzC,QAAQ,KAAK,CAAC,mBAAmB,MAAM,OAAO;AAE9C,oCAAoC;AACpC,oCAAoC;AACtC;AAEA;;CAEC,GACD,SAAS,mBAAmB,KAAe;IACzC,QAAQ,KAAK,CAAC,mBAAmB;AAEjC,6BAA6B;AAC7B,wBAAwB;AAC1B;AAKO,SAAS,wBAAwB,WAAwB;IAC9D,8BAA8B;IAC9B,YAAY,iBAAiB,CAAC;QAC5B,SAAS;YACP,GAAG,YAAY,iBAAiB,GAAG,OAAO;YAC1C,cAAc;QAChB;QACA,WAAW;YACT,GAAG,YAAY,iBAAiB,GAAG,SAAS;YAC5C,cAAc;QAChB;IACF;IAEA,8BAA8B;IAC9B,YAAY,mBAAmB,CAAC;QAAC;KAAW,EAAE;QAC5C,SAAS;IACX;AACF;AAKO,MAAM,aAAa;IACxB;;GAEC,GACD,aAAa,CAAC;QACZ,IAAI,WAAW,QAAQ;YACrB,MAAM,YAAY,aAAa,MAAM,MAAM;YAC3C,kCAAkC;YAClC,OAAO,8BACA,kCACA;QACT;QACA,OAAO,MAAM,mCAAmC;IAClD;IAEA;;GAEC,GACD,gBAAgB,CAAC;QACf,IAAI,WAAW,QAAQ;YACrB,MAAM,YAAY,aAAa,MAAM,MAAM;YAE3C,OAAQ;gBACN;oBACE,OAAO;gBACT;oBACE,OAAO;gBACT;oBACE,OAAO,MAAM,OAAO,IAAI;gBAC1B;oBACE,OAAO;gBACT;oBACE,OAAO;gBACT;oBACE,OAAO;YACX;QACF;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,aAAa,CAAC;QACZ,IAAI,WAAW,QAAQ;YACrB,OAAO,MAAM,MAAM,IAAI,OAAO,MAAM,MAAM,KAAK;QACjD;QACA,OAAO;IACT;AACF"}}, {"offset": {"line": 5944, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5950, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/lib/query-utils.ts"], "sourcesContent": ["/**\n * Query Utilities and Helpers\n * Common utilities for working with TanStack Query\n */\n\nimport { QueryClient, UseQueryOptions, UseMutationOptions } from '@tanstack/react-query';\nimport { QUERY_CONFIG } from './query-client';\nimport { ApiError, isApiError, errorUtils } from './query-error-handler';\n\n/**\n * Base API response interface\n */\nexport interface ApiResponse<T = any> {\n  data: T;\n  message?: string;\n  success: boolean;\n  timestamp: string;\n}\n\n/**\n * Paginated response interface\n */\nexport interface PaginatedResponse<T = any> extends ApiResponse<T[]> {\n  pagination: {\n    page: number;\n    limit: number;\n    total: number;\n    totalPages: number;\n    hasNext: boolean;\n    hasPrev: boolean;\n  };\n}\n\n/**\n * Query options builder for common patterns\n */\nexport const queryOptionsBuilder = {\n  /**\n   * Build options for real-time data (short cache)\n   */\n  realTime: <T>(options?: Partial<UseQueryOptions<T>>): Partial<UseQueryOptions<T>> => ({\n    staleTime: QUERY_CONFIG.STALE_TIME.SHORT,\n    gcTime: QUERY_CONFIG.STALE_TIME.MEDIUM,\n    refetchInterval: QUERY_CONFIG.REFETCH_INTERVAL.FAST,\n    ...options,\n  }),\n\n  /**\n   * Build options for static data (long cache)\n   */\n  static: <T>(options?: Partial<UseQueryOptions<T>>): Partial<UseQueryOptions<T>> => ({\n    staleTime: QUERY_CONFIG.STALE_TIME.VERY_LONG,\n    gcTime: QUERY_CONFIG.STALE_TIME.VERY_LONG,\n    refetchOnWindowFocus: false,\n    refetchOnReconnect: false,\n    ...options,\n  }),\n\n  /**\n   * Build options for user-specific data\n   */\n  userSpecific: <T>(options?: Partial<UseQueryOptions<T>>): Partial<UseQueryOptions<T>> => ({\n    staleTime: QUERY_CONFIG.STALE_TIME.MEDIUM,\n    gcTime: QUERY_CONFIG.STALE_TIME.LONG,\n    refetchOnWindowFocus: true,\n    ...options,\n  }),\n\n  /**\n   * Build options for background sync data\n   */\n  backgroundSync: <T>(options?: Partial<UseQueryOptions<T>>): Partial<UseQueryOptions<T>> => ({\n    staleTime: QUERY_CONFIG.STALE_TIME.LONG,\n    gcTime: QUERY_CONFIG.STALE_TIME.VERY_LONG,\n    refetchInterval: QUERY_CONFIG.REFETCH_INTERVAL.SLOW,\n    refetchIntervalInBackground: true,\n    ...options,\n  }),\n};\n\n/**\n * Mutation options builder for common patterns\n */\nexport const mutationOptionsBuilder = {\n  /**\n   * Build options for optimistic updates\n   */\n  optimistic: <T, V>(options?: Partial<UseMutationOptions<T, ApiError, V>>): Partial<UseMutationOptions<T, ApiError, V>> => ({\n    retry: QUERY_CONFIG.RETRY.ONCE,\n    ...options,\n  }),\n\n  /**\n   * Build options for critical operations\n   */\n  critical: <T, V>(options?: Partial<UseMutationOptions<T, ApiError, V>>): Partial<UseMutationOptions<T, ApiError, V>> => ({\n    retry: QUERY_CONFIG.RETRY.DEFAULT,\n    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),\n    ...options,\n  }),\n\n  /**\n   * Build options for background operations\n   */\n  background: <T, V>(options?: Partial<UseMutationOptions<T, ApiError, V>>): Partial<UseMutationOptions<T, ApiError, V>> => ({\n    retry: QUERY_CONFIG.RETRY.TWICE,\n    ...options,\n  }),\n};\n\n/**\n * Cache management utilities\n */\nexport const cacheUtils = {\n  /**\n   * Invalidate queries by pattern\n   */\n  invalidateByPattern: async (queryClient: QueryClient, pattern: string[]) => {\n    await queryClient.invalidateQueries({ queryKey: pattern });\n  },\n\n  /**\n   * Remove queries by pattern\n   */\n  removeByPattern: (queryClient: QueryClient, pattern: string[]) => {\n    queryClient.removeQueries({ queryKey: pattern });\n  },\n\n  /**\n   * Update query data\n   */\n  updateQueryData: <T>(\n    queryClient: QueryClient,\n    queryKey: string[],\n    updater: (oldData: T | undefined) => T\n  ) => {\n    queryClient.setQueryData(queryKey, updater);\n  },\n\n  /**\n   * Optimistically update list data\n   */\n  optimisticListUpdate: <T extends { id: string | number }>(\n    queryClient: QueryClient,\n    queryKey: string[],\n    item: T,\n    operation: 'add' | 'update' | 'remove'\n  ) => {\n    queryClient.setQueryData<T[]>(queryKey, (oldData) => {\n      if (!oldData) return operation === 'add' ? [item] : [];\n\n      switch (operation) {\n        case 'add':\n          return [...oldData, item];\n        case 'update':\n          return oldData.map((existing) =>\n            existing.id === item.id ? { ...existing, ...item } : existing\n          );\n        case 'remove':\n          return oldData.filter((existing) => existing.id !== item.id);\n        default:\n          return oldData;\n      }\n    });\n  },\n\n  /**\n   * Optimistically update paginated data\n   */\n  optimisticPaginatedUpdate: <T extends { id: string | number }>(\n    queryClient: QueryClient,\n    queryKey: string[],\n    item: T,\n    operation: 'add' | 'update' | 'remove'\n  ) => {\n    queryClient.setQueryData<PaginatedResponse<T>>(queryKey, (oldData) => {\n      if (!oldData) return oldData;\n\n      const updatedData = cacheUtils.optimisticListUpdate(\n        queryClient,\n        ['temp'],\n        item,\n        operation\n      );\n\n      return {\n        ...oldData,\n        data: updatedData || oldData.data,\n      };\n    });\n  },\n};\n\n/**\n * Query state utilities\n */\nexport const queryStateUtils = {\n  /**\n   * Check if any queries are loading\n   */\n  isAnyLoading: (queryClient: QueryClient, queryKeys: string[][]): boolean => {\n    return queryKeys.some((key) => {\n      const query = queryClient.getQueryState(key);\n      return query?.fetchStatus === 'fetching';\n    });\n  },\n\n  /**\n   * Check if any queries have errors\n   */\n  hasAnyErrors: (queryClient: QueryClient, queryKeys: string[][]): boolean => {\n    return queryKeys.some((key) => {\n      const query = queryClient.getQueryState(key);\n      return query?.status === 'error';\n    });\n  },\n\n  /**\n   * Get all errors from queries\n   */\n  getAllErrors: (queryClient: QueryClient, queryKeys: string[][]): ApiError[] => {\n    return queryKeys\n      .map((key) => {\n        const query = queryClient.getQueryState(key);\n        return query?.error;\n      })\n      .filter((error): error is ApiError => isApiError(error));\n  },\n\n  /**\n   * Check if data is stale\n   */\n  isStale: (queryClient: QueryClient, queryKey: string[]): boolean => {\n    const query = queryClient.getQueryState(queryKey);\n    return query ? query.isStale : true;\n  },\n};\n\n/**\n * Development utilities\n */\nexport const devUtils = {\n  /**\n   * Log query cache state\n   */\n  logCacheState: (queryClient: QueryClient) => {\n    if (process.env.NODE_ENV === 'development') {\n      const cache = queryClient.getQueryCache();\n      console.log('[Query Cache]', {\n        queries: cache.getAll().length,\n        state: cache.getAll().map((query) => ({\n          key: query.queryKey,\n          status: query.state.status,\n          dataUpdatedAt: query.state.dataUpdatedAt,\n          error: query.state.error,\n        })),\n      });\n    }\n  },\n\n  /**\n   * Clear all cache (development only)\n   */\n  clearAllCache: (queryClient: QueryClient) => {\n    if (process.env.NODE_ENV === 'development') {\n      queryClient.clear();\n      console.log('[Dev] Query cache cleared');\n    }\n  },\n\n  /**\n   * Force refetch all queries (development only)\n   */\n  refetchAll: async (queryClient: QueryClient) => {\n    if (process.env.NODE_ENV === 'development') {\n      await queryClient.refetchQueries();\n      console.log('[Dev] All queries refetched');\n    }\n  },\n};\n\n/**\n * Error handling utilities\n */\nexport const queryErrorUtils = {\n  /**\n   * Handle query error with user feedback\n   */\n  handleQueryError: (error: unknown, context?: string) => {\n    const message = errorUtils.getUserMessage(error);\n    console.error(`[Query Error${context ? ` - ${context}` : ''}]`, error);\n    \n    // This will be integrated with notification system\n    // For now, just log the user-friendly message\n    console.log('[User Message]', message);\n    \n    return message;\n  },\n\n  /**\n   * Handle mutation error with user feedback\n   */\n  handleMutationError: (error: unknown, context?: string) => {\n    const message = errorUtils.getUserMessage(error);\n    console.error(`[Mutation Error${context ? ` - ${context}` : ''}]`, error);\n    \n    // This will be integrated with notification system\n    // For now, just log the user-friendly message\n    console.log('[User Message]', message);\n    \n    return message;\n  },\n};\n\n/**\n * Type guards for API responses\n */\nexport const typeGuards = {\n  /**\n   * Check if response is a valid API response\n   */\n  isApiResponse: <T>(data: unknown): data is ApiResponse<T> => {\n    return (\n      typeof data === 'object' &&\n      data !== null &&\n      'data' in data &&\n      'success' in data &&\n      'timestamp' in data\n    );\n  },\n\n  /**\n   * Check if response is a paginated response\n   */\n  isPaginatedResponse: <T>(data: unknown): data is PaginatedResponse<T> => {\n    return (\n      typeGuards.isApiResponse(data) &&\n      'pagination' in data &&\n      typeof (data as any).pagination === 'object'\n    );\n  },\n};\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;AAGD;AACA;AA+OQ;;;AAlND,MAAM,sBAAsB;IACjC;;GAEC,GACD,UAAU,CAAI,UAAuE,CAAC;YACpF,WAAW,gIAAA,CAAA,eAAY,CAAC,UAAU,CAAC,KAAK;YACxC,QAAQ,gIAAA,CAAA,eAAY,CAAC,UAAU,CAAC,MAAM;YACtC,iBAAiB,gIAAA,CAAA,eAAY,CAAC,gBAAgB,CAAC,IAAI;YACnD,GAAG,OAAO;QACZ,CAAC;IAED;;GAEC,GACD,QAAQ,CAAI,UAAuE,CAAC;YAClF,WAAW,gIAAA,CAAA,eAAY,CAAC,UAAU,CAAC,SAAS;YAC5C,QAAQ,gIAAA,CAAA,eAAY,CAAC,UAAU,CAAC,SAAS;YACzC,sBAAsB;YACtB,oBAAoB;YACpB,GAAG,OAAO;QACZ,CAAC;IAED;;GAEC,GACD,cAAc,CAAI,UAAuE,CAAC;YACxF,WAAW,gIAAA,CAAA,eAAY,CAAC,UAAU,CAAC,MAAM;YACzC,QAAQ,gIAAA,CAAA,eAAY,CAAC,UAAU,CAAC,IAAI;YACpC,sBAAsB;YACtB,GAAG,OAAO;QACZ,CAAC;IAED;;GAEC,GACD,gBAAgB,CAAI,UAAuE,CAAC;YAC1F,WAAW,gIAAA,CAAA,eAAY,CAAC,UAAU,CAAC,IAAI;YACvC,QAAQ,gIAAA,CAAA,eAAY,CAAC,UAAU,CAAC,SAAS;YACzC,iBAAiB,gIAAA,CAAA,eAAY,CAAC,gBAAgB,CAAC,IAAI;YACnD,6BAA6B;YAC7B,GAAG,OAAO;QACZ,CAAC;AACH;AAKO,MAAM,yBAAyB;IACpC;;GAEC,GACD,YAAY,CAAO,UAAuG,CAAC;YACzH,OAAO,gIAAA,CAAA,eAAY,CAAC,KAAK,CAAC,IAAI;YAC9B,GAAG,OAAO;QACZ,CAAC;IAED;;GAEC,GACD,UAAU,CAAO,UAAuG,CAAC;YACvH,OAAO,gIAAA,CAAA,eAAY,CAAC,KAAK,CAAC,OAAO;YACjC,YAAY,CAAC,eAAiB,KAAK,GAAG,CAAC,OAAO,KAAK,cAAc;YACjE,GAAG,OAAO;QACZ,CAAC;IAED;;GAEC,GACD,YAAY,CAAO,UAAuG,CAAC;YACzH,OAAO,gIAAA,CAAA,eAAY,CAAC,KAAK,CAAC,KAAK;YAC/B,GAAG,OAAO;QACZ,CAAC;AACH;AAKO,MAAM,aAAa;IACxB;;GAEC,GACD,qBAAqB,OAAO,aAA0B;QACpD,MAAM,YAAY,iBAAiB,CAAC;YAAE,UAAU;QAAQ;IAC1D;IAEA;;GAEC,GACD,iBAAiB,CAAC,aAA0B;QAC1C,YAAY,aAAa,CAAC;YAAE,UAAU;QAAQ;IAChD;IAEA;;GAEC,GACD,iBAAiB,CACf,aACA,UACA;QAEA,YAAY,YAAY,CAAC,UAAU;IACrC;IAEA;;GAEC,GACD,sBAAsB,CACpB,aACA,UACA,MACA;QAEA,YAAY,YAAY,CAAM,UAAU,CAAC;YACvC,IAAI,CAAC,SAAS,OAAO,cAAc,QAAQ;gBAAC;aAAK,GAAG,EAAE;YAEtD,OAAQ;gBACN,KAAK;oBACH,OAAO;2BAAI;wBAAS;qBAAK;gBAC3B,KAAK;oBACH,OAAO,QAAQ,GAAG,CAAC,CAAC,WAClB,SAAS,EAAE,KAAK,KAAK,EAAE,GAAG;4BAAE,GAAG,QAAQ;4BAAE,GAAG,IAAI;wBAAC,IAAI;gBAEzD,KAAK;oBACH,OAAO,QAAQ,MAAM,CAAC,CAAC,WAAa,SAAS,EAAE,KAAK,KAAK,EAAE;gBAC7D;oBACE,OAAO;YACX;QACF;IACF;IAEA;;GAEC,GACD,2BAA2B,CACzB,aACA,UACA,MACA;QAEA,YAAY,YAAY,CAAuB,UAAU,CAAC;YACxD,IAAI,CAAC,SAAS,OAAO;YAErB,MAAM,cAAc,WAAW,oBAAoB,CACjD,aACA;gBAAC;aAAO,EACR,MACA;YAGF,OAAO;gBACL,GAAG,OAAO;gBACV,MAAM,eAAe,QAAQ,IAAI;YACnC;QACF;IACF;AACF;AAKO,MAAM,kBAAkB;IAC7B;;GAEC,GACD,cAAc,CAAC,aAA0B;QACvC,OAAO,UAAU,IAAI,CAAC,CAAC;YACrB,MAAM,QAAQ,YAAY,aAAa,CAAC;YACxC,OAAO,OAAO,gBAAgB;QAChC;IACF;IAEA;;GAEC,GACD,cAAc,CAAC,aAA0B;QACvC,OAAO,UAAU,IAAI,CAAC,CAAC;YACrB,MAAM,QAAQ,YAAY,aAAa,CAAC;YACxC,OAAO,OAAO,WAAW;QAC3B;IACF;IAEA;;GAEC,GACD,cAAc,CAAC,aAA0B;QACvC,OAAO,UACJ,GAAG,CAAC,CAAC;YACJ,MAAM,QAAQ,YAAY,aAAa,CAAC;YACxC,OAAO,OAAO;QAChB,GACC,MAAM,CAAC,CAAC,QAA6B,CAAA,GAAA,0IAAA,CAAA,aAAU,AAAD,EAAE;IACrD;IAEA;;GAEC,GACD,SAAS,CAAC,aAA0B;QAClC,MAAM,QAAQ,YAAY,aAAa,CAAC;QACxC,OAAO,QAAQ,MAAM,OAAO,GAAG;IACjC;AACF;AAKO,MAAM,WAAW;IACtB;;GAEC,GACD,eAAe,CAAC;QACd,wCAA4C;YAC1C,MAAM,QAAQ,YAAY,aAAa;YACvC,QAAQ,GAAG,CAAC,iBAAiB;gBAC3B,SAAS,MAAM,MAAM,GAAG,MAAM;gBAC9B,OAAO,MAAM,MAAM,GAAG,GAAG,CAAC,CAAC,QAAU,CAAC;wBACpC,KAAK,MAAM,QAAQ;wBACnB,QAAQ,MAAM,KAAK,CAAC,MAAM;wBAC1B,eAAe,MAAM,KAAK,CAAC,aAAa;wBACxC,OAAO,MAAM,KAAK,CAAC,KAAK;oBAC1B,CAAC;YACH;QACF;IACF;IAEA;;GAEC,GACD,eAAe,CAAC;QACd,wCAA4C;YAC1C,YAAY,KAAK;YACjB,QAAQ,GAAG,CAAC;QACd;IACF;IAEA;;GAEC,GACD,YAAY,OAAO;QACjB,wCAA4C;YAC1C,MAAM,YAAY,cAAc;YAChC,QAAQ,GAAG,CAAC;QACd;IACF;AACF;AAKO,MAAM,kBAAkB;IAC7B;;GAEC,GACD,kBAAkB,CAAC,OAAgB;QACjC,MAAM,UAAU,0IAAA,CAAA,aAAU,CAAC,cAAc,CAAC;QAC1C,QAAQ,KAAK,CAAC,CAAC,YAAY,EAAE,UAAU,CAAC,GAAG,EAAE,SAAS,GAAG,GAAG,CAAC,CAAC,EAAE;QAEhE,mDAAmD;QACnD,8CAA8C;QAC9C,QAAQ,GAAG,CAAC,kBAAkB;QAE9B,OAAO;IACT;IAEA;;GAEC,GACD,qBAAqB,CAAC,OAAgB;QACpC,MAAM,UAAU,0IAAA,CAAA,aAAU,CAAC,cAAc,CAAC;QAC1C,QAAQ,KAAK,CAAC,CAAC,eAAe,EAAE,UAAU,CAAC,GAAG,EAAE,SAAS,GAAG,GAAG,CAAC,CAAC,EAAE;QAEnE,mDAAmD;QACnD,8CAA8C;QAC9C,QAAQ,GAAG,CAAC,kBAAkB;QAE9B,OAAO;IACT;AACF;AAKO,MAAM,aAAa;IACxB;;GAEC,GACD,eAAe,CAAI;QACjB,OACE,OAAO,SAAS,YAChB,SAAS,QACT,UAAU,QACV,aAAa,QACb,eAAe;IAEnB;IAEA;;GAEC,GACD,qBAAqB,CAAI;QACvB,OACE,WAAW,aAAa,CAAC,SACzB,gBAAgB,QAChB,OAAO,AAAC,KAAa,UAAU,KAAK;IAExC;AACF"}}, {"offset": {"line": 6187, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 6193, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/hooks/api/base-hooks.ts"], "sourcesContent": ["/**\n * Base API Hooks\n * Foundation hooks for API operations with TanStack Query\n */\n\n'use client';\n\nimport { \n  useQuery, \n  useMutation, \n  useQueryClient,\n  UseQueryOptions,\n  UseMutationOptions \n} from '@tanstack/react-query';\nimport { \n  queryOptionsBuilder, \n  mutationOptionsBuilder,\n  ApiResponse,\n  PaginatedResponse,\n  BaseQueryOptions,\n  BaseMutationOptions\n} from '@/lib/query-utils';\nimport { ApiError, isApiError, errorUtils } from '@/lib/query-error-handler';\n\n/**\n * Base query hook with error handling and type safety\n */\nexport function useBaseQuery<TData>(\n  queryKey: readonly unknown[],\n  queryFn: () => Promise<TData>,\n  options?: BaseQueryOptions<TData>\n) {\n  return useQuery({\n    queryKey,\n    queryFn,\n    ...options,\n    onError: (error: unknown) => {\n      console.error(`[Query Error] ${queryKey.join(' → ')}:`, error);\n      if (options?.onError) {\n        options.onError(error as ApiError);\n      }\n    },\n  });\n}\n\n/**\n * Base mutation hook with error handling and type safety\n */\nexport function useBaseMutation<TData, TVariables>(\n  mutationFn: (variables: TVariables) => Promise<TData>,\n  options?: BaseMutationOptions<TData, TVariables>\n) {\n  const queryClient = useQueryClient();\n\n  return useMutation({\n    mutationFn,\n    ...options,\n    onError: (error: unknown, variables: TVariables, context: unknown) => {\n      console.error('[Mutation Error]:', error);\n      if (options?.onError) {\n        options.onError(error as ApiError, variables, context);\n      }\n    },\n    onSuccess: (data: TData, variables: TVariables, context: unknown) => {\n      if (options?.onSuccess) {\n        options.onSuccess(data, variables, context);\n      }\n    },\n  });\n}\n\n/**\n * Paginated query hook for list endpoints\n */\nexport function usePaginatedQuery<TData>(\n  queryKey: readonly unknown[],\n  queryFn: () => Promise<PaginatedResponse<TData>>,\n  options?: BaseQueryOptions<PaginatedResponse<TData>>\n) {\n  return useBaseQuery(queryKey, queryFn, {\n    ...queryOptionsBuilder.userSpecific(),\n    ...options,\n  });\n}\n\n/**\n * Real-time query hook for frequently updated data\n */\nexport function useRealTimeQuery<TData>(\n  queryKey: readonly unknown[],\n  queryFn: () => Promise<TData>,\n  options?: BaseQueryOptions<TData>\n) {\n  return useBaseQuery(queryKey, queryFn, {\n    ...queryOptionsBuilder.realTime(),\n    ...options,\n  });\n}\n\n/**\n * Static query hook for rarely changing data\n */\nexport function useStaticQuery<TData>(\n  queryKey: readonly unknown[],\n  queryFn: () => Promise<TData>,\n  options?: BaseQueryOptions<TData>\n) {\n  return useBaseQuery(queryKey, queryFn, {\n    ...queryOptionsBuilder.static(),\n    ...options,\n  });\n}\n\n/**\n * Background sync query hook for data that updates in background\n */\nexport function useBackgroundSyncQuery<TData>(\n  queryKey: readonly unknown[],\n  queryFn: () => Promise<TData>,\n  options?: BaseQueryOptions<TData>\n) {\n  return useBaseQuery(queryKey, queryFn, {\n    ...queryOptionsBuilder.backgroundSync(),\n    ...options,\n  });\n}\n\n/**\n * Optimistic mutation hook for immediate UI updates\n */\nexport function useOptimisticMutation<TData, TVariables>(\n  mutationFn: (variables: TVariables) => Promise<TData>,\n  options?: BaseMutationOptions<TData, TVariables>\n) {\n  return useBaseMutation(mutationFn, {\n    ...mutationOptionsBuilder.optimistic(),\n    ...options,\n  });\n}\n\n/**\n * Critical mutation hook for important operations with retries\n */\nexport function useCriticalMutation<TData, TVariables>(\n  mutationFn: (variables: TVariables) => Promise<TData>,\n  options?: BaseMutationOptions<TData, TVariables>\n) {\n  return useBaseMutation(mutationFn, {\n    ...mutationOptionsBuilder.critical(),\n    ...options,\n  });\n}\n\n/**\n * Background mutation hook for non-critical operations\n */\nexport function useBackgroundMutation<TData, TVariables>(\n  mutationFn: (variables: TVariables) => Promise<TData>,\n  options?: BaseMutationOptions<TData, TVariables>\n) {\n  return useBaseMutation(mutationFn, {\n    ...mutationOptionsBuilder.background(),\n    ...options,\n  });\n}\n\n/**\n * Hook utilities for common operations\n */\nexport const useApiHookUtils = () => {\n  const queryClient = useQueryClient();\n\n  return {\n    /**\n     * Invalidate queries by pattern\n     */\n    invalidateQueries: (queryKey: readonly unknown[]) => {\n      return queryClient.invalidateQueries({ queryKey });\n    },\n\n    /**\n     * Remove queries from cache\n     */\n    removeQueries: (queryKey: readonly unknown[]) => {\n      return queryClient.removeQueries({ queryKey });\n    },\n\n    /**\n     * Update query data optimistically\n     */\n    updateQueryData: <T>(queryKey: readonly unknown[], updater: (oldData: T | undefined) => T) => {\n      queryClient.setQueryData(queryKey, updater);\n    },\n\n    /**\n     * Get cached query data\n     */\n    getQueryData: <T>(queryKey: readonly unknown[]): T | undefined => {\n      return queryClient.getQueryData(queryKey);\n    },\n\n    /**\n     * Prefetch query data\n     */\n    prefetchQuery: <T>(queryKey: readonly unknown[], queryFn: () => Promise<T>) => {\n      return queryClient.prefetchQuery({ queryKey, queryFn });\n    },\n\n    /**\n     * Check if query is loading\n     */\n    isQueryLoading: (queryKey: readonly unknown[]): boolean => {\n      const query = queryClient.getQueryState(queryKey);\n      return query?.fetchStatus === 'fetching';\n    },\n\n    /**\n     * Get query error\n     */\n    getQueryError: (queryKey: readonly unknown[]): ApiError | null => {\n      const query = queryClient.getQueryState(queryKey);\n      return isApiError(query?.error) ? query.error : null;\n    },\n\n    /**\n     * Handle API error with user feedback\n     */\n    handleApiError: (error: unknown, context?: string): string => {\n      return errorUtils.getUserMessage(error);\n    },\n  };\n};\n\n/**\n * Hook for API status monitoring\n */\nexport const useApiStatus = () => {\n  const queryClient = useQueryClient();\n\n  return {\n    /**\n     * Get overall API status\n     */\n    getApiStatus: () => {\n      const queries = queryClient.getQueryCache().getAll();\n      const totalQueries = queries.length;\n      const loadingQueries = queries.filter(q => q.state.fetchStatus === 'fetching').length;\n      const errorQueries = queries.filter(q => q.state.status === 'error').length;\n      const successQueries = queries.filter(q => q.state.status === 'success').length;\n\n      return {\n        total: totalQueries,\n        loading: loadingQueries,\n        error: errorQueries,\n        success: successQueries,\n        isLoading: loadingQueries > 0,\n        hasErrors: errorQueries > 0,\n        healthScore: totalQueries > 0 ? (successQueries / totalQueries) * 100 : 100,\n      };\n    },\n\n    /**\n     * Get queries by status\n     */\n    getQueriesByStatus: (status: 'loading' | 'error' | 'success' | 'idle') => {\n      const queries = queryClient.getQueryCache().getAll();\n      \n      switch (status) {\n        case 'loading':\n          return queries.filter(q => q.state.fetchStatus === 'fetching');\n        case 'error':\n          return queries.filter(q => q.state.status === 'error');\n        case 'success':\n          return queries.filter(q => q.state.status === 'success');\n        case 'idle':\n          return queries.filter(q => q.state.fetchStatus === 'idle');\n        default:\n          return [];\n      }\n    },\n\n    /**\n     * Clear all errors\n     */\n    clearAllErrors: () => {\n      const errorQueries = queryClient.getQueryCache().getAll()\n        .filter(q => q.state.status === 'error');\n      \n      errorQueries.forEach(query => {\n        queryClient.resetQueries({ queryKey: query.queryKey });\n      });\n    },\n  };\n};\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;;AAWD;AAQA;AAfA;AAAA;AAAA;;AAFA;;;;AAsBO,SAAS,aACd,QAA4B,EAC5B,OAA6B,EAC7B,OAAiC;;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd;QACA;QACA,GAAG,OAAO;QACV,OAAO;qCAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,CAAC,cAAc,EAAE,SAAS,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE;gBACxD,IAAI,SAAS,SAAS;oBACpB,QAAQ,OAAO,CAAC;gBAClB;YACF;;IACF;AACF;GAhBgB;;QAKP,8KAAA,CAAA,WAAQ;;;AAgBV,SAAS,gBACd,UAAqD,EACrD,OAAgD;;IAEhD,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB;QACA,GAAG,OAAO;QACV,OAAO;2CAAE,CAAC,OAAgB,WAAuB;gBAC/C,QAAQ,KAAK,CAAC,qBAAqB;gBACnC,IAAI,SAAS,SAAS;oBACpB,QAAQ,OAAO,CAAC,OAAmB,WAAW;gBAChD;YACF;;QACA,SAAS;2CAAE,CAAC,MAAa,WAAuB;gBAC9C,IAAI,SAAS,WAAW;oBACtB,QAAQ,SAAS,CAAC,MAAM,WAAW;gBACrC;YACF;;IACF;AACF;IArBgB;;QAIM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAoBb,SAAS,kBACd,QAA4B,EAC5B,OAAgD,EAChD,OAAoD;;IAEpD,OAAO,aAAa,UAAU,SAAS;QACrC,GAAG,+HAAA,CAAA,sBAAmB,CAAC,YAAY,EAAE;QACrC,GAAG,OAAO;IACZ;AACF;IATgB;;QAKP;;;AASF,SAAS,iBACd,QAA4B,EAC5B,OAA6B,EAC7B,OAAiC;;IAEjC,OAAO,aAAa,UAAU,SAAS;QACrC,GAAG,+HAAA,CAAA,sBAAmB,CAAC,QAAQ,EAAE;QACjC,GAAG,OAAO;IACZ;AACF;IATgB;;QAKP;;;AASF,SAAS,eACd,QAA4B,EAC5B,OAA6B,EAC7B,OAAiC;;IAEjC,OAAO,aAAa,UAAU,SAAS;QACrC,GAAG,+HAAA,CAAA,sBAAmB,CAAC,MAAM,EAAE;QAC/B,GAAG,OAAO;IACZ;AACF;IATgB;;QAKP;;;AASF,SAAS,uBACd,QAA4B,EAC5B,OAA6B,EAC7B,OAAiC;;IAEjC,OAAO,aAAa,UAAU,SAAS;QACrC,GAAG,+HAAA,CAAA,sBAAmB,CAAC,cAAc,EAAE;QACvC,GAAG,OAAO;IACZ;AACF;IATgB;;QAKP;;;AASF,SAAS,sBACd,UAAqD,EACrD,OAAgD;;IAEhD,OAAO,gBAAgB,YAAY;QACjC,GAAG,+HAAA,CAAA,yBAAsB,CAAC,UAAU,EAAE;QACtC,GAAG,OAAO;IACZ;AACF;IARgB;;QAIP;;;AASF,SAAS,oBACd,UAAqD,EACrD,OAAgD;;IAEhD,OAAO,gBAAgB,YAAY;QACjC,GAAG,+HAAA,CAAA,yBAAsB,CAAC,QAAQ,EAAE;QACpC,GAAG,OAAO;IACZ;AACF;IARgB;;QAIP;;;AASF,SAAS,sBACd,UAAqD,EACrD,OAAgD;;IAEhD,OAAO,gBAAgB,YAAY;QACjC,GAAG,+HAAA,CAAA,yBAAsB,CAAC,UAAU,EAAE;QACtC,GAAG,OAAO;IACZ;AACF;IARgB;;QAIP;;;AASF,MAAM,kBAAkB;;IAC7B,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO;QACL;;KAEC,GACD,mBAAmB,CAAC;YAClB,OAAO,YAAY,iBAAiB,CAAC;gBAAE;YAAS;QAClD;QAEA;;KAEC,GACD,eAAe,CAAC;YACd,OAAO,YAAY,aAAa,CAAC;gBAAE;YAAS;QAC9C;QAEA;;KAEC,GACD,iBAAiB,CAAI,UAA8B;YACjD,YAAY,YAAY,CAAC,UAAU;QACrC;QAEA;;KAEC,GACD,cAAc,CAAI;YAChB,OAAO,YAAY,YAAY,CAAC;QAClC;QAEA;;KAEC,GACD,eAAe,CAAI,UAA8B;YAC/C,OAAO,YAAY,aAAa,CAAC;gBAAE;gBAAU;YAAQ;QACvD;QAEA;;KAEC,GACD,gBAAgB,CAAC;YACf,MAAM,QAAQ,YAAY,aAAa,CAAC;YACxC,OAAO,OAAO,gBAAgB;QAChC;QAEA;;KAEC,GACD,eAAe,CAAC;YACd,MAAM,QAAQ,YAAY,aAAa,CAAC;YACxC,OAAO,CAAA,GAAA,0IAAA,CAAA,aAAU,AAAD,EAAE,OAAO,SAAS,MAAM,KAAK,GAAG;QAClD;QAEA;;KAEC,GACD,gBAAgB,CAAC,OAAgB;YAC/B,OAAO,0IAAA,CAAA,aAAU,CAAC,cAAc,CAAC;QACnC;IACF;AACF;IA9Da;;QACS,yLAAA,CAAA,iBAAc;;;AAkE7B,MAAM,eAAe;;IAC1B,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO;QACL;;KAEC,GACD,cAAc;YACZ,MAAM,UAAU,YAAY,aAAa,GAAG,MAAM;YAClD,MAAM,eAAe,QAAQ,MAAM;YACnC,MAAM,iBAAiB,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,CAAC,WAAW,KAAK,YAAY,MAAM;YACrF,MAAM,eAAe,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,CAAC,MAAM,KAAK,SAAS,MAAM;YAC3E,MAAM,iBAAiB,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,CAAC,MAAM,KAAK,WAAW,MAAM;YAE/E,OAAO;gBACL,OAAO;gBACP,SAAS;gBACT,OAAO;gBACP,SAAS;gBACT,WAAW,iBAAiB;gBAC5B,WAAW,eAAe;gBAC1B,aAAa,eAAe,IAAI,AAAC,iBAAiB,eAAgB,MAAM;YAC1E;QACF;QAEA;;KAEC,GACD,oBAAoB,CAAC;YACnB,MAAM,UAAU,YAAY,aAAa,GAAG,MAAM;YAElD,OAAQ;gBACN,KAAK;oBACH,OAAO,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,CAAC,WAAW,KAAK;gBACrD,KAAK;oBACH,OAAO,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,CAAC,MAAM,KAAK;gBAChD,KAAK;oBACH,OAAO,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,CAAC,MAAM,KAAK;gBAChD,KAAK;oBACH,OAAO,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,CAAC,WAAW,KAAK;gBACrD;oBACE,OAAO,EAAE;YACb;QACF;QAEA;;KAEC,GACD,gBAAgB;YACd,MAAM,eAAe,YAAY,aAAa,GAAG,MAAM,GACpD,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,CAAC,MAAM,KAAK;YAElC,aAAa,OAAO,CAAC,CAAA;gBACnB,YAAY,YAAY,CAAC;oBAAE,UAAU,MAAM,QAAQ;gBAAC;YACtD;QACF;IACF;AACF;KAzDa;;QACS,yLAAA,CAAA,iBAAc"}}, {"offset": {"line": 6472, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 6478, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/hooks/api/auth-hooks.ts"], "sourcesContent": ["/**\n * Authentication API Hooks\n * Hooks for system authentication operations\n */\n\n'use client';\n\nimport { queryKeys } from '@/lib/query-client';\nimport { AuthQueries } from '@/lib/query-types';\nimport { useBaseQuery, useBaseMutation, useApiHookUtils } from './base-hooks';\n\n/**\n * Hook for user login\n */\nexport function useLogin() {\n  const { invalidateQueries } = useApiHookUtils();\n\n  return useBaseMutation<AuthQueries.LoginResponse, AuthQueries.LoginRequest>(\n    async (credentials) => {\n      const response = await fetch('/api/system-auth/login', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(credentials),\n      });\n\n      if (!response.ok) {\n        throw new Error(`Login failed: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      onSuccess: (data) => {\n        // Invalidate auth queries on successful login\n        invalidateQueries(queryKeys.auth.all);\n        console.log('✅ Login successful:', data.user.username);\n      },\n      onError: (error) => {\n        console.error('❌ Login failed:', error);\n      },\n    }\n  );\n}\n\n/**\n * Hook for user logout\n */\nexport function useLogout() {\n  const { invalidateQueries } = useApiHookUtils();\n\n  return useBaseMutation<{ message: string }, void>(\n    async () => {\n      const response = await fetch('/api/system-auth/logout', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n      });\n\n      if (!response.ok) {\n        throw new Error(`Logout failed: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      onSuccess: () => {\n        // Clear all auth-related queries on logout\n        invalidateQueries(queryKeys.auth.all);\n        console.log('✅ Logout successful');\n      },\n      onError: (error) => {\n        console.error('❌ Logout failed:', error);\n      },\n    }\n  );\n}\n\n/**\n * Hook for logout from all devices\n */\nexport function useLogoutAll() {\n  const { invalidateQueries } = useApiHookUtils();\n\n  return useBaseMutation<{ message: string }, void>(\n    async () => {\n      const response = await fetch('/api/system-auth/logout-all', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n      });\n\n      if (!response.ok) {\n        throw new Error(`Logout all failed: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      onSuccess: () => {\n        // Clear all auth-related queries on logout all\n        invalidateQueries(queryKeys.auth.all);\n        console.log('✅ Logout all successful');\n      },\n      onError: (error) => {\n        console.error('❌ Logout all failed:', error);\n      },\n    }\n  );\n}\n\n/**\n * Hook for getting user profile\n */\nexport function useProfile() {\n  return useBaseQuery(\n    queryKeys.auth.profile(),\n    async (): Promise<AuthQueries.ProfileResponse> => {\n      const response = await fetch('/api/system-auth/profile');\n\n      if (!response.ok) {\n        throw new Error(`Failed to fetch profile: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      enabled: false, // Disable by default, enable when user is authenticated\n      staleTime: 5 * 60 * 1000, // 5 minutes\n      retry: (failureCount, error: any) => {\n        // Don't retry on 401 (unauthorized)\n        if (error?.status === 401) return false;\n        return failureCount < 2;\n      },\n    }\n  );\n}\n\n/**\n * Hook for updating user profile\n */\nexport function useUpdateProfile() {\n  const { invalidateQueries } = useApiHookUtils();\n\n  return useBaseMutation<AuthQueries.ProfileResponse, AuthQueries.UpdateProfileRequest>(\n    async (data) => {\n      const response = await fetch('/api/system-auth/profile', {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(data),\n      });\n\n      if (!response.ok) {\n        throw new Error(`Failed to update profile: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      onSuccess: () => {\n        // Invalidate profile query to refetch updated data\n        invalidateQueries(queryKeys.auth.profile());\n        console.log('✅ Profile updated successfully');\n      },\n      onError: (error) => {\n        console.error('❌ Profile update failed:', error);\n      },\n    }\n  );\n}\n\n/**\n * Hook for changing password\n */\nexport function useChangePassword() {\n  return useBaseMutation<{ message: string }, AuthQueries.ChangePasswordRequest>(\n    async (data) => {\n      const response = await fetch('/api/system-auth/change-password', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(data),\n      });\n\n      if (!response.ok) {\n        throw new Error(`Failed to change password: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      onSuccess: () => {\n        console.log('✅ Password changed successfully');\n      },\n      onError: (error) => {\n        console.error('❌ Password change failed:', error);\n      },\n    }\n  );\n}\n\n/**\n * Hook for creating new system user (Admin only)\n */\nexport function useCreateUser() {\n  const { invalidateQueries } = useApiHookUtils();\n\n  return useBaseMutation<AuthQueries.ProfileResponse, AuthQueries.CreateUserRequest>(\n    async (data) => {\n      const response = await fetch('/api/system-auth/users', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(data),\n      });\n\n      if (!response.ok) {\n        throw new Error(`Failed to create user: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      onSuccess: () => {\n        // Invalidate users list to show new user\n        invalidateQueries(queryKeys.auth.users());\n        console.log('✅ User created successfully');\n      },\n      onError: (error) => {\n        console.error('❌ User creation failed:', error);\n      },\n    }\n  );\n}\n\n/**\n * Hook for getting system users list (Admin only)\n */\nexport function useSystemUsers() {\n  return useBaseQuery(\n    queryKeys.auth.users(),\n    async (): Promise<AuthQueries.ProfileResponse[]> => {\n      const response = await fetch('/api/system-auth/users');\n\n      if (!response.ok) {\n        throw new Error(`Failed to fetch users: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      enabled: false, // Enable only for Admin users\n      staleTime: 2 * 60 * 1000, // 2 minutes\n    }\n  );\n}\n\n/**\n * Hook for getting specific system user (Admin only)\n */\nexport function useSystemUser(userId: string) {\n  return useBaseQuery(\n    queryKeys.auth.user(userId),\n    async (): Promise<AuthQueries.ProfileResponse> => {\n      const response = await fetch(`/api/system-auth/users/${userId}`);\n\n      if (!response.ok) {\n        throw new Error(`Failed to fetch user: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      enabled: !!userId, // Only fetch if userId is provided\n      staleTime: 5 * 60 * 1000, // 5 minutes\n    }\n  );\n}\n\n/**\n * Composite hook for authentication state and actions\n */\nexport function useAuth() {\n  const login = useLogin();\n  const logout = useLogout();\n  const logoutAll = useLogoutAll();\n  const profile = useProfile();\n  const updateProfile = useUpdateProfile();\n  const changePassword = useChangePassword();\n  const createUser = useCreateUser();\n\n  return {\n    // Queries\n    profile,\n    \n    // Mutations\n    login,\n    logout,\n    logoutAll,\n    updateProfile,\n    changePassword,\n    createUser,\n    \n    // Computed state\n    isAuthenticated: !!profile.data,\n    user: profile.data,\n    isLoading: profile.isLoading || login.isPending || logout.isPending,\n    error: profile.error || login.error || logout.error,\n    \n    // Actions\n    loginUser: login.mutate,\n    logoutUser: logout.mutate,\n    logoutAllDevices: logoutAll.mutate,\n    updateUserProfile: updateProfile.mutate,\n    changeUserPassword: changePassword.mutate,\n    createNewUser: createUser.mutate,\n  };\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;AAID;AAEA;;AAJA;;;AASO,SAAS;;IACd,MAAM,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,kBAAe,AAAD;IAE5C,OAAO,CAAA,GAAA,uIAAA,CAAA,kBAAe,AAAD;oCACnB,OAAO;YACL,MAAM,WAAW,MAAM,MAAM,0BAA0B;gBACrD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,cAAc,EAAE,SAAS,UAAU,EAAE;YACxD;YAEA,OAAO,SAAS,IAAI;QACtB;mCACA;QACE,SAAS;wCAAE,CAAC;gBACV,8CAA8C;gBAC9C,kBAAkB,gIAAA,CAAA,YAAS,CAAC,IAAI,CAAC,GAAG;gBACpC,QAAQ,GAAG,CAAC,uBAAuB,KAAK,IAAI,CAAC,QAAQ;YACvD;;QACA,OAAO;wCAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,mBAAmB;YACnC;;IACF;AAEJ;GA9BgB;;QACgB,uIAAA,CAAA,kBAAe;QAEtC,uIAAA,CAAA,kBAAe;;;AAgCjB,SAAS;;IACd,MAAM,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,kBAAe,AAAD;IAE5C,OAAO,CAAA,GAAA,uIAAA,CAAA,kBAAe,AAAD;qCACnB;YACE,MAAM,WAAW,MAAM,MAAM,2BAA2B;gBACtD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,eAAe,EAAE,SAAS,UAAU,EAAE;YACzD;YAEA,OAAO,SAAS,IAAI;QACtB;oCACA;QACE,SAAS;yCAAE;gBACT,2CAA2C;gBAC3C,kBAAkB,gIAAA,CAAA,YAAS,CAAC,IAAI,CAAC,GAAG;gBACpC,QAAQ,GAAG,CAAC;YACd;;QACA,OAAO;yCAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,oBAAoB;YACpC;;IACF;AAEJ;IA7BgB;;QACgB,uIAAA,CAAA,kBAAe;QAEtC,uIAAA,CAAA,kBAAe;;;AA+BjB,SAAS;;IACd,MAAM,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,kBAAe,AAAD;IAE5C,OAAO,CAAA,GAAA,uIAAA,CAAA,kBAAe,AAAD;wCACnB;YACE,MAAM,WAAW,MAAM,MAAM,+BAA+B;gBAC1D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,mBAAmB,EAAE,SAAS,UAAU,EAAE;YAC7D;YAEA,OAAO,SAAS,IAAI;QACtB;uCACA;QACE,SAAS;4CAAE;gBACT,+CAA+C;gBAC/C,kBAAkB,gIAAA,CAAA,YAAS,CAAC,IAAI,CAAC,GAAG;gBACpC,QAAQ,GAAG,CAAC;YACd;;QACA,OAAO;4CAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,wBAAwB;YACxC;;IACF;AAEJ;IA7BgB;;QACgB,uIAAA,CAAA,kBAAe;QAEtC,uIAAA,CAAA,kBAAe;;;AA+BjB,SAAS;;IACd,OAAO,CAAA,GAAA,uIAAA,CAAA,eAAY,AAAD,EAChB,gIAAA,CAAA,YAAS,CAAC,IAAI,CAAC,OAAO;mCACtB;YACE,MAAM,WAAW,MAAM,MAAM;YAE7B,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,yBAAyB,EAAE,SAAS,UAAU,EAAE;YACnE;YAEA,OAAO,SAAS,IAAI;QACtB;kCACA;QACE,SAAS;QACT,WAAW,IAAI,KAAK;QACpB,KAAK;uCAAE,CAAC,cAAc;gBACpB,oCAAoC;gBACpC,IAAI,OAAO,WAAW,KAAK,OAAO;gBAClC,OAAO,eAAe;YACxB;;IACF;AAEJ;IAtBgB;;QACP,uIAAA,CAAA,eAAY;;;AA0Bd,SAAS;;IACd,MAAM,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,kBAAe,AAAD;IAE5C,OAAO,CAAA,GAAA,uIAAA,CAAA,kBAAe,AAAD;4CACnB,OAAO;YACL,MAAM,WAAW,MAAM,MAAM,4BAA4B;gBACvD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,0BAA0B,EAAE,SAAS,UAAU,EAAE;YACpE;YAEA,OAAO,SAAS,IAAI;QACtB;2CACA;QACE,SAAS;gDAAE;gBACT,mDAAmD;gBACnD,kBAAkB,gIAAA,CAAA,YAAS,CAAC,IAAI,CAAC,OAAO;gBACxC,QAAQ,GAAG,CAAC;YACd;;QACA,OAAO;gDAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,4BAA4B;YAC5C;;IACF;AAEJ;IA9BgB;;QACgB,uIAAA,CAAA,kBAAe;QAEtC,uIAAA,CAAA,kBAAe;;;AAgCjB,SAAS;;IACd,OAAO,CAAA,GAAA,uIAAA,CAAA,kBAAe,AAAD;6CACnB,OAAO;YACL,MAAM,WAAW,MAAM,MAAM,oCAAoC;gBAC/D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,2BAA2B,EAAE,SAAS,UAAU,EAAE;YACrE;YAEA,OAAO,SAAS,IAAI;QACtB;4CACA;QACE,SAAS;iDAAE;gBACT,QAAQ,GAAG,CAAC;YACd;;QACA,OAAO;iDAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,6BAA6B;YAC7C;;IACF;AAEJ;IA1BgB;;QACP,uIAAA,CAAA,kBAAe;;;AA8BjB,SAAS;;IACd,MAAM,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,kBAAe,AAAD;IAE5C,OAAO,CAAA,GAAA,uIAAA,CAAA,kBAAe,AAAD;yCACnB,OAAO;YACL,MAAM,WAAW,MAAM,MAAM,0BAA0B;gBACrD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,uBAAuB,EAAE,SAAS,UAAU,EAAE;YACjE;YAEA,OAAO,SAAS,IAAI;QACtB;wCACA;QACE,SAAS;6CAAE;gBACT,yCAAyC;gBACzC,kBAAkB,gIAAA,CAAA,YAAS,CAAC,IAAI,CAAC,KAAK;gBACtC,QAAQ,GAAG,CAAC;YACd;;QACA,OAAO;6CAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,2BAA2B;YAC3C;;IACF;AAEJ;IA9BgB;;QACgB,uIAAA,CAAA,kBAAe;QAEtC,uIAAA,CAAA,kBAAe;;;AAgCjB,SAAS;;IACd,OAAO,CAAA,GAAA,uIAAA,CAAA,eAAY,AAAD,EAChB,gIAAA,CAAA,YAAS,CAAC,IAAI,CAAC,KAAK;uCACpB;YACE,MAAM,WAAW,MAAM,MAAM;YAE7B,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,uBAAuB,EAAE,SAAS,UAAU,EAAE;YACjE;YAEA,OAAO,SAAS,IAAI;QACtB;sCACA;QACE,SAAS;QACT,WAAW,IAAI,KAAK;IACtB;AAEJ;IAjBgB;;QACP,uIAAA,CAAA,eAAY;;;AAqBd,SAAS,cAAc,MAAc;;IAC1C,OAAO,CAAA,GAAA,uIAAA,CAAA,eAAY,AAAD,EAChB,gIAAA,CAAA,YAAS,CAAC,IAAI,CAAC,IAAI,CAAC;sCACpB;YACE,MAAM,WAAW,MAAM,MAAM,CAAC,uBAAuB,EAAE,QAAQ;YAE/D,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,sBAAsB,EAAE,SAAS,UAAU,EAAE;YAChE;YAEA,OAAO,SAAS,IAAI;QACtB;qCACA;QACE,SAAS,CAAC,CAAC;QACX,WAAW,IAAI,KAAK;IACtB;AAEJ;IAjBgB;;QACP,uIAAA,CAAA,eAAY;;;AAqBd,SAAS;;IACd,MAAM,QAAQ;IACd,MAAM,SAAS;IACf,MAAM,YAAY;IAClB,MAAM,UAAU;IAChB,MAAM,gBAAgB;IACtB,MAAM,iBAAiB;IACvB,MAAM,aAAa;IAEnB,OAAO;QACL,UAAU;QACV;QAEA,YAAY;QACZ;QACA;QACA;QACA;QACA;QACA;QAEA,iBAAiB;QACjB,iBAAiB,CAAC,CAAC,QAAQ,IAAI;QAC/B,MAAM,QAAQ,IAAI;QAClB,WAAW,QAAQ,SAAS,IAAI,MAAM,SAAS,IAAI,OAAO,SAAS;QACnE,OAAO,QAAQ,KAAK,IAAI,MAAM,KAAK,IAAI,OAAO,KAAK;QAEnD,UAAU;QACV,WAAW,MAAM,MAAM;QACvB,YAAY,OAAO,MAAM;QACzB,kBAAkB,UAAU,MAAM;QAClC,mBAAmB,cAAc,MAAM;QACvC,oBAAoB,eAAe,MAAM;QACzC,eAAe,WAAW,MAAM;IAClC;AACF;IAnCgB;;QACA;QACC;QACG;QACF;QACM;QACC;QACJ"}}, {"offset": {"line": 6835, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 6841, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/hooks/api/football-hooks.ts"], "sourcesContent": ["/**\n * Football Data API Hooks\n * Hooks for football leagues, teams, and fixtures operations\n */\n\n'use client';\n\nimport { queryKeys } from '@/lib/query-client';\nimport { FootballQueries } from '@/lib/query-types';\nimport { PaginatedResponse } from '@/lib/query-utils';\nimport { \n  useBaseQuery, \n  usePaginatedQuery, \n  useBackgroundSyncQuery,\n  useBaseMutation,\n  useApiHookUtils \n} from './base-hooks';\n\n/**\n * Hook for getting football leagues\n */\nexport function useLeagues(params?: FootballQueries.LeagueQueryParams) {\n  const queryParams = new URLSearchParams();\n  if (params?.page) queryParams.set('page', params.page.toString());\n  if (params?.limit) queryParams.set('limit', params.limit.toString());\n  if (params?.country) queryParams.set('country', params.country);\n  if (params?.isActive !== undefined) queryParams.set('isActive', params.isActive.toString());\n  if (params?.query) queryParams.set('query', params.query);\n\n  return usePaginatedQuery(\n    [...queryKeys.football.leagues(), params],\n    async (): Promise<PaginatedResponse<FootballQueries.League>> => {\n      const response = await fetch(`/api/football/leagues?${queryParams.toString()}`);\n\n      if (!response.ok) {\n        throw new Error(`Failed to fetch leagues: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      staleTime: 10 * 60 * 1000, // 10 minutes - leagues don't change often\n    }\n  );\n}\n\n/**\n * Hook for getting specific league\n */\nexport function useLeague(leagueId: string) {\n  return useBaseQuery(\n    queryKeys.football.league(leagueId),\n    async (): Promise<FootballQueries.League> => {\n      const response = await fetch(`/api/football/leagues/${leagueId}`);\n\n      if (!response.ok) {\n        throw new Error(`Failed to fetch league: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      enabled: !!leagueId,\n      staleTime: 10 * 60 * 1000, // 10 minutes\n    }\n  );\n}\n\n/**\n * Hook for getting football teams\n */\nexport function useTeams(params?: FootballQueries.TeamQueryParams) {\n  const queryParams = new URLSearchParams();\n  if (params?.page) queryParams.set('page', params.page.toString());\n  if (params?.limit) queryParams.set('limit', params.limit.toString());\n  if (params?.leagueId) queryParams.set('leagueId', params.leagueId);\n  if (params?.country) queryParams.set('country', params.country);\n  if (params?.query) queryParams.set('query', params.query);\n\n  return usePaginatedQuery(\n    [...queryKeys.football.teams(), params],\n    async (): Promise<PaginatedResponse<FootballQueries.Team>> => {\n      const response = await fetch(`/api/football/teams?${queryParams.toString()}`);\n\n      if (!response.ok) {\n        throw new Error(`Failed to fetch teams: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      staleTime: 5 * 60 * 1000, // 5 minutes\n    }\n  );\n}\n\n/**\n * Hook for getting specific team\n */\nexport function useTeam(teamId: string) {\n  return useBaseQuery(\n    queryKeys.football.team(teamId),\n    async (): Promise<FootballQueries.Team> => {\n      const response = await fetch(`/api/football/teams/${teamId}`);\n\n      if (!response.ok) {\n        throw new Error(`Failed to fetch team: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      enabled: !!teamId,\n      staleTime: 5 * 60 * 1000, // 5 minutes\n    }\n  );\n}\n\n/**\n * Hook for getting football fixtures\n */\nexport function useFixtures(params?: FootballQueries.FixtureQueryParams) {\n  const queryParams = new URLSearchParams();\n  if (params?.page) queryParams.set('page', params.page.toString());\n  if (params?.limit) queryParams.set('limit', params.limit.toString());\n  if (params?.leagueId) queryParams.set('leagueId', params.leagueId);\n  if (params?.teamId) queryParams.set('teamId', params.teamId);\n  if (params?.status) queryParams.set('status', params.status);\n  if (params?.dateFrom) queryParams.set('dateFrom', params.dateFrom);\n  if (params?.dateTo) queryParams.set('dateTo', params.dateTo);\n  if (params?.query) queryParams.set('query', params.query);\n\n  return usePaginatedQuery(\n    [...queryKeys.football.fixtures(), params],\n    async (): Promise<PaginatedResponse<FootballQueries.Fixture>> => {\n      const response = await fetch(`/api/football/fixtures?${queryParams.toString()}`);\n\n      if (!response.ok) {\n        throw new Error(`Failed to fetch fixtures: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      staleTime: 1 * 60 * 1000, // 1 minute - fixtures change frequently\n    }\n  );\n}\n\n/**\n * Hook for getting specific fixture\n */\nexport function useFixture(fixtureId: string) {\n  return useBaseQuery(\n    queryKeys.football.fixture(fixtureId),\n    async (): Promise<FootballQueries.Fixture> => {\n      const response = await fetch(`/api/football/fixtures/${fixtureId}`);\n\n      if (!response.ok) {\n        throw new Error(`Failed to fetch fixture: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      enabled: !!fixtureId,\n      staleTime: 30 * 1000, // 30 seconds - live fixtures need frequent updates\n    }\n  );\n}\n\n/**\n * Hook for getting sync status\n */\nexport function useSyncStatus() {\n  return useBackgroundSyncQuery(\n    queryKeys.football.syncStatus(),\n    async (): Promise<FootballQueries.SyncStatus> => {\n      const response = await fetch('/api/football/fixtures/sync/status');\n\n      if (!response.ok) {\n        throw new Error(`Failed to fetch sync status: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      staleTime: 30 * 1000, // 30 seconds\n      refetchInterval: 60 * 1000, // Refetch every minute\n    }\n  );\n}\n\n/**\n * Hook for triggering fixtures sync\n */\nexport function useSyncFixtures() {\n  const { invalidateQueries } = useApiHookUtils();\n\n  return useBaseMutation<{ message: string; syncId: string }, void>(\n    async () => {\n      const response = await fetch('/api/football/fixtures/sync', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n      });\n\n      if (!response.ok) {\n        throw new Error(`Failed to start sync: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      onSuccess: () => {\n        // Invalidate sync status and fixtures to show updated data\n        invalidateQueries(queryKeys.football.syncStatus());\n        invalidateQueries(queryKeys.football.fixtures());\n        console.log('✅ Fixtures sync started');\n      },\n      onError: (error) => {\n        console.error('❌ Fixtures sync failed:', error);\n      },\n    }\n  );\n}\n\n/**\n * Hook for triggering daily sync\n */\nexport function useDailySync() {\n  const { invalidateQueries } = useApiHookUtils();\n\n  return useBaseMutation<{ message: string; syncId: string }, void>(\n    async () => {\n      const response = await fetch('/api/football/fixtures/sync/daily', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n      });\n\n      if (!response.ok) {\n        throw new Error(`Failed to start daily sync: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      onSuccess: () => {\n        // Invalidate sync status and fixtures\n        invalidateQueries(queryKeys.football.syncStatus());\n        invalidateQueries(queryKeys.football.fixtures());\n        console.log('✅ Daily sync started');\n      },\n      onError: (error) => {\n        console.error('❌ Daily sync failed:', error);\n      },\n    }\n  );\n}\n\n/**\n * Composite hook for football data operations\n */\nexport function useFootball() {\n  const syncFixtures = useSyncFixtures();\n  const dailySync = useDailySync();\n  const syncStatus = useSyncStatus();\n\n  return {\n    // Sync operations\n    syncFixtures,\n    dailySync,\n    syncStatus,\n    \n    // Sync actions\n    startSync: syncFixtures.mutate,\n    startDailySync: dailySync.mutate,\n    \n    // Sync state\n    isSyncing: syncFixtures.isPending || dailySync.isPending,\n    syncError: syncFixtures.error || dailySync.error,\n    lastSyncStatus: syncStatus.data,\n  };\n}\n\n/**\n * Hook for live fixtures (real-time updates)\n */\nexport function useLiveFixtures() {\n  return useFixtures({\n    status: 'live',\n    limit: 50,\n  });\n}\n\n/**\n * Hook for today's fixtures\n */\nexport function useTodayFixtures() {\n  const today = new Date().toISOString().split('T')[0];\n  \n  return useFixtures({\n    dateFrom: today,\n    dateTo: today,\n    limit: 100,\n  });\n}\n\n/**\n * Hook for upcoming fixtures\n */\nexport function useUpcomingFixtures(days: number = 7) {\n  const today = new Date();\n  const futureDate = new Date(today.getTime() + days * 24 * 60 * 60 * 1000);\n  \n  return useFixtures({\n    dateFrom: today.toISOString().split('T')[0],\n    dateTo: futureDate.toISOString().split('T')[0],\n    status: 'scheduled',\n    limit: 100,\n  });\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;;;;AAID;AAGA;;AALA;;;AAgBO,SAAS,WAAW,MAA0C;;IACnE,MAAM,cAAc,IAAI;IACxB,IAAI,QAAQ,MAAM,YAAY,GAAG,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;IAC9D,IAAI,QAAQ,OAAO,YAAY,GAAG,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;IACjE,IAAI,QAAQ,SAAS,YAAY,GAAG,CAAC,WAAW,OAAO,OAAO;IAC9D,IAAI,QAAQ,aAAa,WAAW,YAAY,GAAG,CAAC,YAAY,OAAO,QAAQ,CAAC,QAAQ;IACxF,IAAI,QAAQ,OAAO,YAAY,GAAG,CAAC,SAAS,OAAO,KAAK;IAExD,OAAO,CAAA,GAAA,uIAAA,CAAA,oBAAiB,AAAD,EACrB;WAAI,gIAAA,CAAA,YAAS,CAAC,QAAQ,CAAC,OAAO;QAAI;KAAO;wCACzC;YACE,MAAM,WAAW,MAAM,MAAM,CAAC,sBAAsB,EAAE,YAAY,QAAQ,IAAI;YAE9E,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,yBAAyB,EAAE,SAAS,UAAU,EAAE;YACnE;YAEA,OAAO,SAAS,IAAI;QACtB;uCACA;QACE,WAAW,KAAK,KAAK;IACvB;AAEJ;GAvBgB;;QAQP,uIAAA,CAAA,oBAAiB;;;AAoBnB,SAAS,UAAU,QAAgB;;IACxC,OAAO,CAAA,GAAA,uIAAA,CAAA,eAAY,AAAD,EAChB,gIAAA,CAAA,YAAS,CAAC,QAAQ,CAAC,MAAM,CAAC;kCAC1B;YACE,MAAM,WAAW,MAAM,MAAM,CAAC,sBAAsB,EAAE,UAAU;YAEhE,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,SAAS,UAAU,EAAE;YAClE;YAEA,OAAO,SAAS,IAAI;QACtB;iCACA;QACE,SAAS,CAAC,CAAC;QACX,WAAW,KAAK,KAAK;IACvB;AAEJ;IAjBgB;;QACP,uIAAA,CAAA,eAAY;;;AAqBd,SAAS,SAAS,MAAwC;;IAC/D,MAAM,cAAc,IAAI;IACxB,IAAI,QAAQ,MAAM,YAAY,GAAG,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;IAC9D,IAAI,QAAQ,OAAO,YAAY,GAAG,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;IACjE,IAAI,QAAQ,UAAU,YAAY,GAAG,CAAC,YAAY,OAAO,QAAQ;IACjE,IAAI,QAAQ,SAAS,YAAY,GAAG,CAAC,WAAW,OAAO,OAAO;IAC9D,IAAI,QAAQ,OAAO,YAAY,GAAG,CAAC,SAAS,OAAO,KAAK;IAExD,OAAO,CAAA,GAAA,uIAAA,CAAA,oBAAiB,AAAD,EACrB;WAAI,gIAAA,CAAA,YAAS,CAAC,QAAQ,CAAC,KAAK;QAAI;KAAO;sCACvC;YACE,MAAM,WAAW,MAAM,MAAM,CAAC,oBAAoB,EAAE,YAAY,QAAQ,IAAI;YAE5E,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,uBAAuB,EAAE,SAAS,UAAU,EAAE;YACjE;YAEA,OAAO,SAAS,IAAI;QACtB;qCACA;QACE,WAAW,IAAI,KAAK;IACtB;AAEJ;IAvBgB;;QAQP,uIAAA,CAAA,oBAAiB;;;AAoBnB,SAAS,QAAQ,MAAc;;IACpC,OAAO,CAAA,GAAA,uIAAA,CAAA,eAAY,AAAD,EAChB,gIAAA,CAAA,YAAS,CAAC,QAAQ,CAAC,IAAI,CAAC;gCACxB;YACE,MAAM,WAAW,MAAM,MAAM,CAAC,oBAAoB,EAAE,QAAQ;YAE5D,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,sBAAsB,EAAE,SAAS,UAAU,EAAE;YAChE;YAEA,OAAO,SAAS,IAAI;QACtB;+BACA;QACE,SAAS,CAAC,CAAC;QACX,WAAW,IAAI,KAAK;IACtB;AAEJ;IAjBgB;;QACP,uIAAA,CAAA,eAAY;;;AAqBd,SAAS,YAAY,MAA2C;;IACrE,MAAM,cAAc,IAAI;IACxB,IAAI,QAAQ,MAAM,YAAY,GAAG,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;IAC9D,IAAI,QAAQ,OAAO,YAAY,GAAG,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;IACjE,IAAI,QAAQ,UAAU,YAAY,GAAG,CAAC,YAAY,OAAO,QAAQ;IACjE,IAAI,QAAQ,QAAQ,YAAY,GAAG,CAAC,UAAU,OAAO,MAAM;IAC3D,IAAI,QAAQ,QAAQ,YAAY,GAAG,CAAC,UAAU,OAAO,MAAM;IAC3D,IAAI,QAAQ,UAAU,YAAY,GAAG,CAAC,YAAY,OAAO,QAAQ;IACjE,IAAI,QAAQ,QAAQ,YAAY,GAAG,CAAC,UAAU,OAAO,MAAM;IAC3D,IAAI,QAAQ,OAAO,YAAY,GAAG,CAAC,SAAS,OAAO,KAAK;IAExD,OAAO,CAAA,GAAA,uIAAA,CAAA,oBAAiB,AAAD,EACrB;WAAI,gIAAA,CAAA,YAAS,CAAC,QAAQ,CAAC,QAAQ;QAAI;KAAO;yCAC1C;YACE,MAAM,WAAW,MAAM,MAAM,CAAC,uBAAuB,EAAE,YAAY,QAAQ,IAAI;YAE/E,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,0BAA0B,EAAE,SAAS,UAAU,EAAE;YACpE;YAEA,OAAO,SAAS,IAAI;QACtB;wCACA;QACE,WAAW,IAAI,KAAK;IACtB;AAEJ;IA1BgB;;QAWP,uIAAA,CAAA,oBAAiB;;;AAoBnB,SAAS,WAAW,SAAiB;;IAC1C,OAAO,CAAA,GAAA,uIAAA,CAAA,eAAY,AAAD,EAChB,gIAAA,CAAA,YAAS,CAAC,QAAQ,CAAC,OAAO,CAAC;mCAC3B;YACE,MAAM,WAAW,MAAM,MAAM,CAAC,uBAAuB,EAAE,WAAW;YAElE,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,yBAAyB,EAAE,SAAS,UAAU,EAAE;YACnE;YAEA,OAAO,SAAS,IAAI;QACtB;kCACA;QACE,SAAS,CAAC,CAAC;QACX,WAAW,KAAK;IAClB;AAEJ;IAjBgB;;QACP,uIAAA,CAAA,eAAY;;;AAqBd,SAAS;;IACd,OAAO,CAAA,GAAA,uIAAA,CAAA,yBAAsB,AAAD,EAC1B,gIAAA,CAAA,YAAS,CAAC,QAAQ,CAAC,UAAU;gDAC7B;YACE,MAAM,WAAW,MAAM,MAAM;YAE7B,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,6BAA6B,EAAE,SAAS,UAAU,EAAE;YACvE;YAEA,OAAO,SAAS,IAAI;QACtB;+CACA;QACE,WAAW,KAAK;QAChB,iBAAiB,KAAK;IACxB;AAEJ;IAjBgB;;QACP,uIAAA,CAAA,yBAAsB;;;AAqBxB,SAAS;;IACd,MAAM,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,kBAAe,AAAD;IAE5C,OAAO,CAAA,GAAA,uIAAA,CAAA,kBAAe,AAAD;2CACnB;YACE,MAAM,WAAW,MAAM,MAAM,+BAA+B;gBAC1D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,sBAAsB,EAAE,SAAS,UAAU,EAAE;YAChE;YAEA,OAAO,SAAS,IAAI;QACtB;0CACA;QACE,SAAS;+CAAE;gBACT,2DAA2D;gBAC3D,kBAAkB,gIAAA,CAAA,YAAS,CAAC,QAAQ,CAAC,UAAU;gBAC/C,kBAAkB,gIAAA,CAAA,YAAS,CAAC,QAAQ,CAAC,QAAQ;gBAC7C,QAAQ,GAAG,CAAC;YACd;;QACA,OAAO;+CAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,2BAA2B;YAC3C;;IACF;AAEJ;IA9BgB;;QACgB,uIAAA,CAAA,kBAAe;QAEtC,uIAAA,CAAA,kBAAe;;;AAgCjB,SAAS;;IACd,MAAM,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,kBAAe,AAAD;IAE5C,OAAO,CAAA,GAAA,uIAAA,CAAA,kBAAe,AAAD;wCACnB;YACE,MAAM,WAAW,MAAM,MAAM,qCAAqC;gBAChE,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,4BAA4B,EAAE,SAAS,UAAU,EAAE;YACtE;YAEA,OAAO,SAAS,IAAI;QACtB;uCACA;QACE,SAAS;4CAAE;gBACT,sCAAsC;gBACtC,kBAAkB,gIAAA,CAAA,YAAS,CAAC,QAAQ,CAAC,UAAU;gBAC/C,kBAAkB,gIAAA,CAAA,YAAS,CAAC,QAAQ,CAAC,QAAQ;gBAC7C,QAAQ,GAAG,CAAC;YACd;;QACA,OAAO;4CAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,wBAAwB;YACxC;;IACF;AAEJ;IA9BgB;;QACgB,uIAAA,CAAA,kBAAe;QAEtC,uIAAA,CAAA,kBAAe;;;AAgCjB,SAAS;;IACd,MAAM,eAAe;IACrB,MAAM,YAAY;IAClB,MAAM,aAAa;IAEnB,OAAO;QACL,kBAAkB;QAClB;QACA;QACA;QAEA,eAAe;QACf,WAAW,aAAa,MAAM;QAC9B,gBAAgB,UAAU,MAAM;QAEhC,aAAa;QACb,WAAW,aAAa,SAAS,IAAI,UAAU,SAAS;QACxD,WAAW,aAAa,KAAK,IAAI,UAAU,KAAK;QAChD,gBAAgB,WAAW,IAAI;IACjC;AACF;IApBgB;;QACO;QACH;QACC;;;AAsBd,SAAS;;IACd,OAAO,YAAY;QACjB,QAAQ;QACR,OAAO;IACT;AACF;KALgB;;QACP;;;AASF,SAAS;;IACd,MAAM,QAAQ,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;IAEpD,OAAO,YAAY;QACjB,UAAU;QACV,QAAQ;QACR,OAAO;IACT;AACF;KARgB;;QAGP;;;AAUF,SAAS,oBAAoB,OAAe,CAAC;;IAClD,MAAM,QAAQ,IAAI;IAClB,MAAM,aAAa,IAAI,KAAK,MAAM,OAAO,KAAK,OAAO,KAAK,KAAK,KAAK;IAEpE,OAAO,YAAY;QACjB,UAAU,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAC3C,QAAQ,WAAW,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAC9C,QAAQ;QACR,OAAO;IACT;AACF;KAVgB;;QAIP"}}, {"offset": {"line": 7179, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 7185, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/types/user.ts"], "sourcesContent": ["/**\n * User Types & Interfaces\n * SystemUser management types for APISportsGame CMS\n */\n\n/**\n * System user roles\n */\nexport type SystemUserRole = 'admin' | 'editor' | 'moderator';\n\n/**\n * User status\n */\nexport type UserStatus = 'active' | 'inactive' | 'suspended';\n\n/**\n * System user interface\n */\nexport interface SystemUser {\n  id: string;\n  username: string;\n  email: string;\n  firstName?: string;\n  lastName?: string;\n  role: SystemUserRole;\n  status: UserStatus;\n  lastLogin?: string;\n  createdAt: string;\n  updatedAt: string;\n  createdBy?: string;\n  avatar?: string;\n  permissions?: string[];\n}\n\n/**\n * Create user request\n */\nexport interface CreateUserRequest {\n  username: string;\n  email: string;\n  password: string;\n  firstName?: string;\n  lastName?: string;\n  role: SystemUserRole;\n  status?: UserStatus;\n}\n\n/**\n * Update user request\n */\nexport interface UpdateUserRequest {\n  email?: string;\n  firstName?: string;\n  lastName?: string;\n  role?: SystemUserRole;\n  status?: UserStatus;\n  avatar?: string;\n}\n\n/**\n * Change password request\n */\nexport interface ChangePasswordRequest {\n  currentPassword: string;\n  newPassword: string;\n  confirmPassword: string;\n}\n\n/**\n * User list query parameters\n */\nexport interface UserListParams {\n  page?: number;\n  limit?: number;\n  search?: string;\n  role?: SystemUserRole;\n  status?: UserStatus;\n  sortBy?: 'username' | 'email' | 'role' | 'status' | 'createdAt' | 'lastLogin';\n  sortOrder?: 'asc' | 'desc';\n}\n\n/**\n * User list response\n */\nexport interface UserListResponse {\n  users: SystemUser[];\n  total: number;\n  page: number;\n  limit: number;\n  totalPages: number;\n}\n\n/**\n * User statistics\n */\nexport interface UserStatistics {\n  total: number;\n  active: number;\n  inactive: number;\n  suspended: number;\n  byRole: {\n    admin: number;\n    editor: number;\n    moderator: number;\n  };\n  recentLogins: number;\n  newThisMonth: number;\n}\n\n/**\n * User activity log\n */\nexport interface UserActivity {\n  id: string;\n  userId: string;\n  action: string;\n  description: string;\n  ipAddress?: string;\n  userAgent?: string;\n  createdAt: string;\n}\n\n/**\n * User session\n */\nexport interface UserSession {\n  id: string;\n  userId: string;\n  deviceInfo: string;\n  ipAddress: string;\n  lastActivity: string;\n  createdAt: string;\n  isActive: boolean;\n}\n\n/**\n * Role permissions mapping\n */\nexport const ROLE_PERMISSIONS: Record<SystemUserRole, string[]> = {\n  admin: [\n    'users.create',\n    'users.read',\n    'users.update',\n    'users.delete',\n    'users.manage_roles',\n    'football.create',\n    'football.read',\n    'football.update',\n    'football.delete',\n    'football.sync',\n    'broadcast.create',\n    'broadcast.read',\n    'broadcast.update',\n    'broadcast.delete',\n    'system.settings',\n    'system.logs',\n    'system.health',\n  ],\n  editor: [\n    'users.read',\n    'football.create',\n    'football.read',\n    'football.update',\n    'football.sync',\n    'broadcast.create',\n    'broadcast.read',\n    'broadcast.update',\n    'broadcast.delete',\n  ],\n  moderator: [\n    'users.read',\n    'football.read',\n    'broadcast.read',\n    'broadcast.update',\n  ],\n};\n\n/**\n * Role display names\n */\nexport const ROLE_LABELS: Record<SystemUserRole, string> = {\n  admin: 'Administrator',\n  editor: 'Editor',\n  moderator: 'Moderator',\n};\n\n/**\n * Status display names\n */\nexport const STATUS_LABELS: Record<UserStatus, string> = {\n  active: 'Active',\n  inactive: 'Inactive',\n  suspended: 'Suspended',\n};\n\n/**\n * Role colors for UI\n */\nexport const ROLE_COLORS: Record<SystemUserRole, string> = {\n  admin: '#ff4d4f',\n  editor: '#1890ff',\n  moderator: '#52c41a',\n};\n\n/**\n * Status colors for UI\n */\nexport const STATUS_COLORS: Record<UserStatus, string> = {\n  active: '#52c41a',\n  inactive: '#d9d9d9',\n  suspended: '#ff4d4f',\n};\n\n/**\n * User form validation rules\n */\nexport const USER_VALIDATION = {\n  username: {\n    min: 3,\n    max: 50,\n    pattern: /^[a-zA-Z0-9_-]+$/,\n    message: 'Username must be 3-50 characters and contain only letters, numbers, hyphens, and underscores',\n  },\n  email: {\n    pattern: /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/,\n    message: 'Please enter a valid email address',\n  },\n  password: {\n    min: 8,\n    pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]/,\n    message: 'Password must be at least 8 characters with uppercase, lowercase, number, and special character',\n  },\n  firstName: {\n    max: 50,\n    message: 'First name must not exceed 50 characters',\n  },\n  lastName: {\n    max: 50,\n    message: 'Last name must not exceed 50 characters',\n  },\n};\n\n/**\n * Default user list params\n */\nexport const DEFAULT_USER_PARAMS: UserListParams = {\n  page: 1,\n  limit: 20,\n  sortBy: 'createdAt',\n  sortOrder: 'desc',\n};\n\n/**\n * User helper functions\n */\nexport const userHelpers = {\n  /**\n   * Get user full name\n   */\n  getFullName: (user: SystemUser): string => {\n    if (user.firstName && user.lastName) {\n      return `${user.firstName} ${user.lastName}`;\n    }\n    if (user.firstName) {\n      return user.firstName;\n    }\n    if (user.lastName) {\n      return user.lastName;\n    }\n    return user.username;\n  },\n\n  /**\n   * Get user display name\n   */\n  getDisplayName: (user: SystemUser): string => {\n    const fullName = userHelpers.getFullName(user);\n    return fullName !== user.username ? `${fullName} (${user.username})` : user.username;\n  },\n\n  /**\n   * Check if user has permission\n   */\n  hasPermission: (user: SystemUser, permission: string): boolean => {\n    const rolePermissions = ROLE_PERMISSIONS[user.role] || [];\n    return rolePermissions.includes(permission);\n  },\n\n  /**\n   * Check if user is active\n   */\n  isActive: (user: SystemUser): boolean => {\n    return user.status === 'active';\n  },\n\n  /**\n   * Get user avatar URL or initials\n   */\n  getAvatarDisplay: (user: SystemUser): { type: 'url' | 'initials'; value: string } => {\n    if (user.avatar) {\n      return { type: 'url', value: user.avatar };\n    }\n    \n    const fullName = userHelpers.getFullName(user);\n    const initials = fullName\n      .split(' ')\n      .map(name => name.charAt(0).toUpperCase())\n      .slice(0, 2)\n      .join('');\n    \n    return { type: 'initials', value: initials || user.username.charAt(0).toUpperCase() };\n  },\n\n  /**\n   * Format last login time\n   */\n  formatLastLogin: (lastLogin?: string): string => {\n    if (!lastLogin) return 'Never';\n    \n    const date = new Date(lastLogin);\n    const now = new Date();\n    const diffMs = now.getTime() - date.getTime();\n    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));\n    \n    if (diffDays === 0) return 'Today';\n    if (diffDays === 1) return 'Yesterday';\n    if (diffDays < 7) return `${diffDays} days ago`;\n    if (diffDays < 30) return `${Math.floor(diffDays / 7)} weeks ago`;\n    if (diffDays < 365) return `${Math.floor(diffDays / 30)} months ago`;\n    \n    return `${Math.floor(diffDays / 365)} years ago`;\n  },\n};\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED;;CAEC;;;;;;;;;;AAmIM,MAAM,mBAAqD;IAChE,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,QAAQ;QACN;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,WAAW;QACT;QACA;QACA;QACA;KACD;AACH;AAKO,MAAM,cAA8C;IACzD,OAAO;IACP,QAAQ;IACR,WAAW;AACb;AAKO,MAAM,gBAA4C;IACvD,QAAQ;IACR,UAAU;IACV,WAAW;AACb;AAKO,MAAM,cAA8C;IACzD,OAAO;IACP,QAAQ;IACR,WAAW;AACb;AAKO,MAAM,gBAA4C;IACvD,QAAQ;IACR,UAAU;IACV,WAAW;AACb;AAKO,MAAM,kBAAkB;IAC7B,UAAU;QACR,KAAK;QACL,KAAK;QACL,SAAS;QACT,SAAS;IACX;IACA,OAAO;QACL,SAAS;QACT,SAAS;IACX;IACA,UAAU;QACR,KAAK;QACL,SAAS;QACT,SAAS;IACX;IACA,WAAW;QACT,KAAK;QACL,SAAS;IACX;IACA,UAAU;QACR,KAAK;QACL,SAAS;IACX;AACF;AAKO,MAAM,sBAAsC;IACjD,MAAM;IACN,OAAO;IACP,QAAQ;IACR,WAAW;AACb;AAKO,MAAM,cAAc;IACzB;;GAEC,GACD,aAAa,CAAC;QACZ,IAAI,KAAK,SAAS,IAAI,KAAK,QAAQ,EAAE;YACnC,OAAO,GAAG,KAAK,SAAS,CAAC,CAAC,EAAE,KAAK,QAAQ,EAAE;QAC7C;QACA,IAAI,KAAK,SAAS,EAAE;YAClB,OAAO,KAAK,SAAS;QACvB;QACA,IAAI,KAAK,QAAQ,EAAE;YACjB,OAAO,KAAK,QAAQ;QACtB;QACA,OAAO,KAAK,QAAQ;IACtB;IAEA;;GAEC,GACD,gBAAgB,CAAC;QACf,MAAM,WAAW,YAAY,WAAW,CAAC;QACzC,OAAO,aAAa,KAAK,QAAQ,GAAG,GAAG,SAAS,EAAE,EAAE,KAAK,QAAQ,CAAC,CAAC,CAAC,GAAG,KAAK,QAAQ;IACtF;IAEA;;GAEC,GACD,eAAe,CAAC,MAAkB;QAChC,MAAM,kBAAkB,gBAAgB,CAAC,KAAK,IAAI,CAAC,IAAI,EAAE;QACzD,OAAO,gBAAgB,QAAQ,CAAC;IAClC;IAEA;;GAEC,GACD,UAAU,CAAC;QACT,OAAO,KAAK,MAAM,KAAK;IACzB;IAEA;;GAEC,GACD,kBAAkB,CAAC;QACjB,IAAI,KAAK,MAAM,EAAE;YACf,OAAO;gBAAE,MAAM;gBAAO,OAAO,KAAK,MAAM;YAAC;QAC3C;QAEA,MAAM,WAAW,YAAY,WAAW,CAAC;QACzC,MAAM,WAAW,SACd,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,GAAG,WAAW,IACtC,KAAK,CAAC,GAAG,GACT,IAAI,CAAC;QAER,OAAO;YAAE,MAAM;YAAY,OAAO,YAAY,KAAK,QAAQ,CAAC,MAAM,CAAC,GAAG,WAAW;QAAG;IACtF;IAEA;;GAEC,GACD,iBAAiB,CAAC;QAChB,IAAI,CAAC,WAAW,OAAO;QAEvB,MAAM,OAAO,IAAI,KAAK;QACtB,MAAM,MAAM,IAAI;QAChB,MAAM,SAAS,IAAI,OAAO,KAAK,KAAK,OAAO;QAC3C,MAAM,WAAW,KAAK,KAAK,CAAC,SAAS,CAAC,OAAO,KAAK,KAAK,EAAE;QAEzD,IAAI,aAAa,GAAG,OAAO;QAC3B,IAAI,aAAa,GAAG,OAAO;QAC3B,IAAI,WAAW,GAAG,OAAO,GAAG,SAAS,SAAS,CAAC;QAC/C,IAAI,WAAW,IAAI,OAAO,GAAG,KAAK,KAAK,CAAC,WAAW,GAAG,UAAU,CAAC;QACjE,IAAI,WAAW,KAAK,OAAO,GAAG,KAAK,KAAK,CAAC,WAAW,IAAI,WAAW,CAAC;QAEpE,OAAO,GAAG,KAAK,KAAK,CAAC,WAAW,KAAK,UAAU,CAAC;IAClD;AACF"}}, {"offset": {"line": 7356, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 7362, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/hooks/api/users.ts"], "sourcesContent": ["/**\n * User API Hooks\n * TanStack Query hooks for SystemUser management\n */\n\nimport { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';\nimport { message } from 'antd';\nimport {\n  SystemUser,\n  CreateUserRequest,\n  UpdateUserRequest,\n  ChangePasswordRequest,\n  UserListParams,\n  UserListResponse,\n  UserStatistics,\n  UserActivity,\n  UserSession,\n  DEFAULT_USER_PARAMS,\n} from '@/types/user';\n\n/**\n * API endpoints\n */\nconst API_ENDPOINTS = {\n  users: '/api/system-auth/users',\n  userById: (id: string) => `/api/system-auth/users/${id}`,\n  userStats: '/api/system-auth/users/statistics',\n  userActivity: (id: string) => `/api/system-auth/users/${id}/activity`,\n  userSessions: (id: string) => `/api/system-auth/users/${id}/sessions`,\n  changePassword: (id: string) => `/api/system-auth/users/${id}/change-password`,\n  resetPassword: (id: string) => `/api/system-auth/users/${id}/reset-password`,\n};\n\n/**\n * Query keys\n */\nexport const userQueryKeys = {\n  all: ['users'] as const,\n  lists: () => [...userQueryKeys.all, 'list'] as const,\n  list: (params: UserListParams) => [...userQueryKeys.lists(), params] as const,\n  details: () => [...userQueryKeys.all, 'detail'] as const,\n  detail: (id: string) => [...userQueryKeys.details(), id] as const,\n  statistics: () => [...userQueryKeys.all, 'statistics'] as const,\n  activity: (id: string) => [...userQueryKeys.all, 'activity', id] as const,\n  sessions: (id: string) => [...userQueryKeys.all, 'sessions', id] as const,\n};\n\n/**\n * Mock data for development\n */\nconst mockUsers: SystemUser[] = [\n  {\n    id: '1',\n    username: 'admin',\n    email: '<EMAIL>',\n    firstName: 'System',\n    lastName: 'Administrator',\n    role: 'admin',\n    status: 'active',\n    lastLogin: new Date(Date.now() - 1000 * 60 * 30).toISOString(), // 30 minutes ago\n    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 30).toISOString(), // 30 days ago\n    updatedAt: new Date().toISOString(),\n    createdBy: 'system',\n  },\n  {\n    id: '2',\n    username: 'editor1',\n    email: '<EMAIL>',\n    firstName: 'John',\n    lastName: 'Editor',\n    role: 'editor',\n    status: 'active',\n    lastLogin: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(), // 2 hours ago\n    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 15).toISOString(), // 15 days ago\n    updatedAt: new Date().toISOString(),\n    createdBy: '1',\n  },\n  {\n    id: '3',\n    username: 'moderator1',\n    email: '<EMAIL>',\n    firstName: 'Jane',\n    lastName: 'Moderator',\n    role: 'moderator',\n    status: 'active',\n    lastLogin: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString(), // 1 day ago\n    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 7).toISOString(), // 7 days ago\n    updatedAt: new Date().toISOString(),\n    createdBy: '1',\n  },\n  {\n    id: '4',\n    username: 'inactive_user',\n    email: '<EMAIL>',\n    firstName: 'Inactive',\n    lastName: 'User',\n    role: 'editor',\n    status: 'inactive',\n    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 60).toISOString(), // 60 days ago\n    updatedAt: new Date().toISOString(),\n    createdBy: '1',\n  },\n];\n\n/**\n * Mock API functions\n */\nconst mockAPI = {\n  getUsers: async (params: UserListParams): Promise<UserListResponse> => {\n    await new Promise(resolve => setTimeout(resolve, 500)); // Simulate network delay\n    \n    let filteredUsers = [...mockUsers];\n    \n    // Apply filters\n    if (params.search) {\n      const search = params.search.toLowerCase();\n      filteredUsers = filteredUsers.filter(user =>\n        user.username.toLowerCase().includes(search) ||\n        user.email.toLowerCase().includes(search) ||\n        user.firstName?.toLowerCase().includes(search) ||\n        user.lastName?.toLowerCase().includes(search)\n      );\n    }\n    \n    if (params.role) {\n      filteredUsers = filteredUsers.filter(user => user.role === params.role);\n    }\n    \n    if (params.status) {\n      filteredUsers = filteredUsers.filter(user => user.status === params.status);\n    }\n    \n    // Apply sorting\n    if (params.sortBy) {\n      filteredUsers.sort((a, b) => {\n        const aValue = a[params.sortBy!] || '';\n        const bValue = b[params.sortBy!] || '';\n        const comparison = aValue < bValue ? -1 : aValue > bValue ? 1 : 0;\n        return params.sortOrder === 'desc' ? -comparison : comparison;\n      });\n    }\n    \n    // Apply pagination\n    const page = params.page || 1;\n    const limit = params.limit || 20;\n    const startIndex = (page - 1) * limit;\n    const endIndex = startIndex + limit;\n    const paginatedUsers = filteredUsers.slice(startIndex, endIndex);\n    \n    return {\n      users: paginatedUsers,\n      total: filteredUsers.length,\n      page,\n      limit,\n      totalPages: Math.ceil(filteredUsers.length / limit),\n    };\n  },\n\n  getUser: async (id: string): Promise<SystemUser> => {\n    await new Promise(resolve => setTimeout(resolve, 300));\n    const user = mockUsers.find(u => u.id === id);\n    if (!user) throw new Error('User not found');\n    return user;\n  },\n\n  createUser: async (data: CreateUserRequest): Promise<SystemUser> => {\n    await new Promise(resolve => setTimeout(resolve, 1000));\n    const newUser: SystemUser = {\n      id: String(mockUsers.length + 1),\n      ...data,\n      status: data.status || 'active',\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString(),\n      createdBy: '1', // Current user\n    };\n    mockUsers.push(newUser);\n    return newUser;\n  },\n\n  updateUser: async (id: string, data: UpdateUserRequest): Promise<SystemUser> => {\n    await new Promise(resolve => setTimeout(resolve, 800));\n    const userIndex = mockUsers.findIndex(u => u.id === id);\n    if (userIndex === -1) throw new Error('User not found');\n    \n    mockUsers[userIndex] = {\n      ...mockUsers[userIndex],\n      ...data,\n      updatedAt: new Date().toISOString(),\n    };\n    \n    return mockUsers[userIndex];\n  },\n\n  deleteUser: async (id: string): Promise<void> => {\n    await new Promise(resolve => setTimeout(resolve, 500));\n    const userIndex = mockUsers.findIndex(u => u.id === id);\n    if (userIndex === -1) throw new Error('User not found');\n    mockUsers.splice(userIndex, 1);\n  },\n\n  getStatistics: async (): Promise<UserStatistics> => {\n    await new Promise(resolve => setTimeout(resolve, 400));\n    \n    const total = mockUsers.length;\n    const active = mockUsers.filter(u => u.status === 'active').length;\n    const inactive = mockUsers.filter(u => u.status === 'inactive').length;\n    const suspended = mockUsers.filter(u => u.status === 'suspended').length;\n    \n    const byRole = {\n      admin: mockUsers.filter(u => u.role === 'admin').length,\n      editor: mockUsers.filter(u => u.role === 'editor').length,\n      moderator: mockUsers.filter(u => u.role === 'moderator').length,\n    };\n    \n    const recentLogins = mockUsers.filter(u => {\n      if (!u.lastLogin) return false;\n      const lastLogin = new Date(u.lastLogin);\n      const dayAgo = new Date(Date.now() - 1000 * 60 * 60 * 24);\n      return lastLogin > dayAgo;\n    }).length;\n    \n    const monthAgo = new Date(Date.now() - 1000 * 60 * 60 * 24 * 30);\n    const newThisMonth = mockUsers.filter(u => {\n      const created = new Date(u.createdAt);\n      return created > monthAgo;\n    }).length;\n    \n    return {\n      total,\n      active,\n      inactive,\n      suspended,\n      byRole,\n      recentLogins,\n      newThisMonth,\n    };\n  },\n};\n\n/**\n * Get users list\n */\nexport function useUsers(params: UserListParams = DEFAULT_USER_PARAMS) {\n  return useQuery({\n    queryKey: userQueryKeys.list(params),\n    queryFn: () => mockAPI.getUsers(params),\n    staleTime: 5 * 60 * 1000, // 5 minutes\n  });\n}\n\n/**\n * Get user by ID\n */\nexport function useUser(id: string) {\n  return useQuery({\n    queryKey: userQueryKeys.detail(id),\n    queryFn: () => mockAPI.getUser(id),\n    enabled: !!id,\n    staleTime: 5 * 60 * 1000, // 5 minutes\n  });\n}\n\n/**\n * Get user statistics\n */\nexport function useUserStatistics() {\n  return useQuery({\n    queryKey: userQueryKeys.statistics(),\n    queryFn: () => mockAPI.getStatistics(),\n    staleTime: 2 * 60 * 1000, // 2 minutes\n  });\n}\n\n/**\n * Create user mutation\n */\nexport function useCreateUser() {\n  const queryClient = useQueryClient();\n  \n  return useMutation({\n    mutationFn: (data: CreateUserRequest) => mockAPI.createUser(data),\n    onSuccess: () => {\n      queryClient.invalidateQueries({ queryKey: userQueryKeys.lists() });\n      queryClient.invalidateQueries({ queryKey: userQueryKeys.statistics() });\n      message.success('User created successfully');\n    },\n    onError: (error: Error) => {\n      message.error(`Failed to create user: ${error.message}`);\n    },\n  });\n}\n\n/**\n * Update user mutation\n */\nexport function useUpdateUser() {\n  const queryClient = useQueryClient();\n  \n  return useMutation({\n    mutationFn: ({ id, data }: { id: string; data: UpdateUserRequest }) => \n      mockAPI.updateUser(id, data),\n    onSuccess: (updatedUser) => {\n      queryClient.invalidateQueries({ queryKey: userQueryKeys.lists() });\n      queryClient.invalidateQueries({ queryKey: userQueryKeys.detail(updatedUser.id) });\n      queryClient.invalidateQueries({ queryKey: userQueryKeys.statistics() });\n      message.success('User updated successfully');\n    },\n    onError: (error: Error) => {\n      message.error(`Failed to update user: ${error.message}`);\n    },\n  });\n}\n\n/**\n * Delete user mutation\n */\nexport function useDeleteUser() {\n  const queryClient = useQueryClient();\n  \n  return useMutation({\n    mutationFn: (id: string) => mockAPI.deleteUser(id),\n    onSuccess: () => {\n      queryClient.invalidateQueries({ queryKey: userQueryKeys.lists() });\n      queryClient.invalidateQueries({ queryKey: userQueryKeys.statistics() });\n      message.success('User deleted successfully');\n    },\n    onError: (error: Error) => {\n      message.error(`Failed to delete user: ${error.message}`);\n    },\n  });\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;AAID;AAFA;AAAA;AAAA;AACA;;;;;AAcA;;CAEC,GACD,MAAM,gBAAgB;IACpB,OAAO;IACP,UAAU,CAAC,KAAe,CAAC,uBAAuB,EAAE,IAAI;IACxD,WAAW;IACX,cAAc,CAAC,KAAe,CAAC,uBAAuB,EAAE,GAAG,SAAS,CAAC;IACrE,cAAc,CAAC,KAAe,CAAC,uBAAuB,EAAE,GAAG,SAAS,CAAC;IACrE,gBAAgB,CAAC,KAAe,CAAC,uBAAuB,EAAE,GAAG,gBAAgB,CAAC;IAC9E,eAAe,CAAC,KAAe,CAAC,uBAAuB,EAAE,GAAG,eAAe,CAAC;AAC9E;AAKO,MAAM,gBAAgB;IAC3B,KAAK;QAAC;KAAQ;IACd,OAAO,IAAM;eAAI,cAAc,GAAG;YAAE;SAAO;IAC3C,MAAM,CAAC,SAA2B;eAAI,cAAc,KAAK;YAAI;SAAO;IACpE,SAAS,IAAM;eAAI,cAAc,GAAG;YAAE;SAAS;IAC/C,QAAQ,CAAC,KAAe;eAAI,cAAc,OAAO;YAAI;SAAG;IACxD,YAAY,IAAM;eAAI,cAAc,GAAG;YAAE;SAAa;IACtD,UAAU,CAAC,KAAe;eAAI,cAAc,GAAG;YAAE;YAAY;SAAG;IAChE,UAAU,CAAC,KAAe;eAAI,cAAc,GAAG;YAAE;YAAY;SAAG;AAClE;AAEA;;CAEC,GACD,MAAM,YAA0B;IAC9B;QACE,IAAI;QACJ,UAAU;QACV,OAAO;QACP,WAAW;QACX,UAAU;QACV,MAAM;QACN,QAAQ;QACR,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,OAAO,KAAK,IAAI,WAAW;QAC5D,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,OAAO,KAAK,KAAK,KAAK,IAAI,WAAW;QACtE,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW;IACb;IACA;QACE,IAAI;QACJ,UAAU;QACV,OAAO;QACP,WAAW;QACX,UAAU;QACV,MAAM;QACN,QAAQ;QACR,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,OAAO,KAAK,KAAK,GAAG,WAAW;QAChE,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,OAAO,KAAK,KAAK,KAAK,IAAI,WAAW;QACtE,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW;IACb;IACA;QACE,IAAI;QACJ,UAAU;QACV,OAAO;QACP,WAAW;QACX,UAAU;QACV,MAAM;QACN,QAAQ;QACR,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,OAAO,KAAK,KAAK,IAAI,WAAW;QACjE,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,OAAO,KAAK,KAAK,KAAK,GAAG,WAAW;QACrE,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW;IACb;IACA;QACE,IAAI;QACJ,UAAU;QACV,OAAO;QACP,WAAW;QACX,UAAU;QACV,MAAM;QACN,QAAQ;QACR,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,OAAO,KAAK,KAAK,KAAK,IAAI,WAAW;QACtE,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW;IACb;CACD;AAED;;CAEC,GACD,MAAM,UAAU;IACd,UAAU,OAAO;QACf,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS,OAAO,yBAAyB;QAEjF,IAAI,gBAAgB;eAAI;SAAU;QAElC,gBAAgB;QAChB,IAAI,OAAO,MAAM,EAAE;YACjB,MAAM,SAAS,OAAO,MAAM,CAAC,WAAW;YACxC,gBAAgB,cAAc,MAAM,CAAC,CAAA,OACnC,KAAK,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,WACrC,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAClC,KAAK,SAAS,EAAE,cAAc,SAAS,WACvC,KAAK,QAAQ,EAAE,cAAc,SAAS;QAE1C;QAEA,IAAI,OAAO,IAAI,EAAE;YACf,gBAAgB,cAAc,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,OAAO,IAAI;QACxE;QAEA,IAAI,OAAO,MAAM,EAAE;YACjB,gBAAgB,cAAc,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,OAAO,MAAM;QAC5E;QAEA,gBAAgB;QAChB,IAAI,OAAO,MAAM,EAAE;YACjB,cAAc,IAAI,CAAC,CAAC,GAAG;gBACrB,MAAM,SAAS,CAAC,CAAC,OAAO,MAAM,CAAE,IAAI;gBACpC,MAAM,SAAS,CAAC,CAAC,OAAO,MAAM,CAAE,IAAI;gBACpC,MAAM,aAAa,SAAS,SAAS,CAAC,IAAI,SAAS,SAAS,IAAI;gBAChE,OAAO,OAAO,SAAS,KAAK,SAAS,CAAC,aAAa;YACrD;QACF;QAEA,mBAAmB;QACnB,MAAM,OAAO,OAAO,IAAI,IAAI;QAC5B,MAAM,QAAQ,OAAO,KAAK,IAAI;QAC9B,MAAM,aAAa,CAAC,OAAO,CAAC,IAAI;QAChC,MAAM,WAAW,aAAa;QAC9B,MAAM,iBAAiB,cAAc,KAAK,CAAC,YAAY;QAEvD,OAAO;YACL,OAAO;YACP,OAAO,cAAc,MAAM;YAC3B;YACA;YACA,YAAY,KAAK,IAAI,CAAC,cAAc,MAAM,GAAG;QAC/C;IACF;IAEA,SAAS,OAAO;QACd,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QACjD,MAAM,OAAO,UAAU,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC1C,IAAI,CAAC,MAAM,MAAM,IAAI,MAAM;QAC3B,OAAO;IACT;IAEA,YAAY,OAAO;QACjB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QACjD,MAAM,UAAsB;YAC1B,IAAI,OAAO,UAAU,MAAM,GAAG;YAC9B,GAAG,IAAI;YACP,QAAQ,KAAK,MAAM,IAAI;YACvB,WAAW,IAAI,OAAO,WAAW;YACjC,WAAW,IAAI,OAAO,WAAW;YACjC,WAAW;QACb;QACA,UAAU,IAAI,CAAC;QACf,OAAO;IACT;IAEA,YAAY,OAAO,IAAY;QAC7B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QACjD,MAAM,YAAY,UAAU,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACpD,IAAI,cAAc,CAAC,GAAG,MAAM,IAAI,MAAM;QAEtC,SAAS,CAAC,UAAU,GAAG;YACrB,GAAG,SAAS,CAAC,UAAU;YACvB,GAAG,IAAI;YACP,WAAW,IAAI,OAAO,WAAW;QACnC;QAEA,OAAO,SAAS,CAAC,UAAU;IAC7B;IAEA,YAAY,OAAO;QACjB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QACjD,MAAM,YAAY,UAAU,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACpD,IAAI,cAAc,CAAC,GAAG,MAAM,IAAI,MAAM;QACtC,UAAU,MAAM,CAAC,WAAW;IAC9B;IAEA,eAAe;QACb,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEjD,MAAM,QAAQ,UAAU,MAAM;QAC9B,MAAM,SAAS,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,UAAU,MAAM;QAClE,MAAM,WAAW,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,YAAY,MAAM;QACtE,MAAM,YAAY,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,MAAM;QAExE,MAAM,SAAS;YACb,OAAO,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,SAAS,MAAM;YACvD,QAAQ,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,UAAU,MAAM;YACzD,WAAW,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,aAAa,MAAM;QACjE;QAEA,MAAM,eAAe,UAAU,MAAM,CAAC,CAAA;YACpC,IAAI,CAAC,EAAE,SAAS,EAAE,OAAO;YACzB,MAAM,YAAY,IAAI,KAAK,EAAE,SAAS;YACtC,MAAM,SAAS,IAAI,KAAK,KAAK,GAAG,KAAK,OAAO,KAAK,KAAK;YACtD,OAAO,YAAY;QACrB,GAAG,MAAM;QAET,MAAM,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,OAAO,KAAK,KAAK,KAAK;QAC7D,MAAM,eAAe,UAAU,MAAM,CAAC,CAAA;YACpC,MAAM,UAAU,IAAI,KAAK,EAAE,SAAS;YACpC,OAAO,UAAU;QACnB,GAAG,MAAM;QAET,OAAO;YACL;YACA;YACA;YACA;YACA;YACA;YACA;QACF;IACF;AACF;AAKO,SAAS,SAAS,SAAyB,uHAAA,CAAA,sBAAmB;;IACnE,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,cAAc,IAAI,CAAC;QAC7B,OAAO;iCAAE,IAAM,QAAQ,QAAQ,CAAC;;QAChC,WAAW,IAAI,KAAK;IACtB;AACF;GANgB;;QACP,8KAAA,CAAA,WAAQ;;;AAUV,SAAS,QAAQ,EAAU;;IAChC,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,cAAc,MAAM,CAAC;QAC/B,OAAO;gCAAE,IAAM,QAAQ,OAAO,CAAC;;QAC/B,SAAS,CAAC,CAAC;QACX,WAAW,IAAI,KAAK;IACtB;AACF;IAPgB;;QACP,8KAAA,CAAA,WAAQ;;;AAWV,SAAS;;IACd,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,cAAc,UAAU;QAClC,OAAO;0CAAE,IAAM,QAAQ,aAAa;;QACpC,WAAW,IAAI,KAAK;IACtB;AACF;IANgB;;QACP,8KAAA,CAAA,WAAQ;;;AAUV,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;yCAAE,CAAC,OAA4B,QAAQ,UAAU,CAAC;;QAC5D,SAAS;yCAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU,cAAc,KAAK;gBAAG;gBAChE,YAAY,iBAAiB,CAAC;oBAAE,UAAU,cAAc,UAAU;gBAAG;gBACrE,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;YAClB;;QACA,OAAO;yCAAE,CAAC;gBACR,uLAAA,CAAA,UAAO,CAAC,KAAK,CAAC,CAAC,uBAAuB,EAAE,MAAM,OAAO,EAAE;YACzD;;IACF;AACF;IAdgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAgBb,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;yCAAE,CAAC,EAAE,EAAE,EAAE,IAAI,EAA2C,GAChE,QAAQ,UAAU,CAAC,IAAI;;QACzB,SAAS;yCAAE,CAAC;gBACV,YAAY,iBAAiB,CAAC;oBAAE,UAAU,cAAc,KAAK;gBAAG;gBAChE,YAAY,iBAAiB,CAAC;oBAAE,UAAU,cAAc,MAAM,CAAC,YAAY,EAAE;gBAAE;gBAC/E,YAAY,iBAAiB,CAAC;oBAAE,UAAU,cAAc,UAAU;gBAAG;gBACrE,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;YAClB;;QACA,OAAO;yCAAE,CAAC;gBACR,uLAAA,CAAA,UAAO,CAAC,KAAK,CAAC,CAAC,uBAAuB,EAAE,MAAM,OAAO,EAAE;YACzD;;IACF;AACF;IAhBgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAkBb,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;yCAAE,CAAC,KAAe,QAAQ,UAAU,CAAC;;QAC/C,SAAS;yCAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU,cAAc,KAAK;gBAAG;gBAChE,YAAY,iBAAiB,CAAC;oBAAE,UAAU,cAAc,UAAU;gBAAG;gBACrE,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;YAClB;;QACA,OAAO;yCAAE,CAAC;gBACR,uLAAA,CAAA,UAAO,CAAC,KAAK,CAAC,CAAC,uBAAuB,EAAE,MAAM,OAAO,EAAE;YACzD;;IACF;AACF;IAdgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW"}}, {"offset": {"line": 7740, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 7746, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/hooks/api/broadcast-hooks.ts"], "sourcesContent": ["/**\n * Broadcast Links API Hooks\n * Hooks for broadcast links management operations\n */\n\n'use client';\n\nimport { queryKeys } from '@/lib/query-client';\nimport { BroadcastQueries } from '@/lib/query-types';\nimport { PaginatedResponse } from '@/lib/query-utils';\nimport { \n  useBaseQuery, \n  usePaginatedQuery,\n  useBaseMutation,\n  useOptimisticMutation,\n  useApiHookUtils \n} from './base-hooks';\n\n/**\n * Hook for getting broadcast links\n */\nexport function useBroadcastLinks(params?: BroadcastQueries.BroadcastLinkQueryParams) {\n  const queryParams = new URLSearchParams();\n  if (params?.page) queryParams.set('page', params.page.toString());\n  if (params?.limit) queryParams.set('limit', params.limit.toString());\n  if (params?.fixtureId) queryParams.set('fixtureId', params.fixtureId);\n  if (params?.quality) queryParams.set('quality', params.quality);\n  if (params?.language) queryParams.set('language', params.language);\n  if (params?.isActive !== undefined) queryParams.set('isActive', params.isActive.toString());\n  if (params?.query) queryParams.set('query', params.query);\n\n  return usePaginatedQuery(\n    [...queryKeys.broadcast.links(), params],\n    async (): Promise<PaginatedResponse<BroadcastQueries.BroadcastLink>> => {\n      const response = await fetch(`/api/broadcast-links?${queryParams.toString()}`);\n\n      if (!response.ok) {\n        throw new Error(`Failed to fetch broadcast links: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      staleTime: 2 * 60 * 1000, // 2 minutes\n    }\n  );\n}\n\n/**\n * Hook for getting specific broadcast link\n */\nexport function useBroadcastLink(linkId: string) {\n  return useBaseQuery(\n    queryKeys.broadcast.link(linkId),\n    async (): Promise<BroadcastQueries.BroadcastLink> => {\n      const response = await fetch(`/api/broadcast-links/${linkId}`);\n\n      if (!response.ok) {\n        throw new Error(`Failed to fetch broadcast link: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      enabled: !!linkId,\n      staleTime: 2 * 60 * 1000, // 2 minutes\n    }\n  );\n}\n\n/**\n * Hook for getting broadcast links for a specific fixture\n */\nexport function useFixtureBroadcastLinks(fixtureId: string) {\n  return useBaseQuery(\n    queryKeys.broadcast.fixture(fixtureId),\n    async (): Promise<BroadcastQueries.BroadcastLink[]> => {\n      const response = await fetch(`/api/broadcast-links/fixture/${fixtureId}`);\n\n      if (!response.ok) {\n        throw new Error(`Failed to fetch fixture broadcast links: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      enabled: !!fixtureId,\n      staleTime: 1 * 60 * 1000, // 1 minute - links for live fixtures change frequently\n    }\n  );\n}\n\n/**\n * Hook for creating broadcast link\n */\nexport function useCreateBroadcastLink() {\n  const { invalidateQueries } = useApiHookUtils();\n\n  return useOptimisticMutation<BroadcastQueries.BroadcastLink, BroadcastQueries.CreateBroadcastLinkRequest>(\n    async (data) => {\n      const response = await fetch('/api/broadcast-links', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(data),\n      });\n\n      if (!response.ok) {\n        throw new Error(`Failed to create broadcast link: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      onSuccess: (data) => {\n        // Invalidate broadcast links queries\n        invalidateQueries(queryKeys.broadcast.links());\n        invalidateQueries(queryKeys.broadcast.fixture(data.fixtureId));\n        console.log('✅ Broadcast link created successfully');\n      },\n      onError: (error) => {\n        console.error('❌ Failed to create broadcast link:', error);\n      },\n    }\n  );\n}\n\n/**\n * Hook for updating broadcast link\n */\nexport function useUpdateBroadcastLink() {\n  const { invalidateQueries } = useApiHookUtils();\n\n  return useOptimisticMutation<BroadcastQueries.BroadcastLink, { id: string; data: BroadcastQueries.UpdateBroadcastLinkRequest }>(\n    async ({ id, data }) => {\n      const response = await fetch(`/api/broadcast-links/${id}`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(data),\n      });\n\n      if (!response.ok) {\n        throw new Error(`Failed to update broadcast link: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      onSuccess: (data) => {\n        // Invalidate specific link and related queries\n        invalidateQueries(queryKeys.broadcast.link(data.id));\n        invalidateQueries(queryKeys.broadcast.links());\n        invalidateQueries(queryKeys.broadcast.fixture(data.fixtureId));\n        console.log('✅ Broadcast link updated successfully');\n      },\n      onError: (error) => {\n        console.error('❌ Failed to update broadcast link:', error);\n      },\n    }\n  );\n}\n\n/**\n * Hook for deleting broadcast link\n */\nexport function useDeleteBroadcastLink() {\n  const { invalidateQueries } = useApiHookUtils();\n\n  return useBaseMutation<{ message: string }, string>(\n    async (linkId) => {\n      const response = await fetch(`/api/broadcast-links/${linkId}`, {\n        method: 'DELETE',\n      });\n\n      if (!response.ok) {\n        throw new Error(`Failed to delete broadcast link: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      onSuccess: (_, linkId) => {\n        // Invalidate broadcast links queries\n        invalidateQueries(queryKeys.broadcast.links());\n        invalidateQueries(queryKeys.broadcast.link(linkId));\n        console.log('✅ Broadcast link deleted successfully');\n      },\n      onError: (error) => {\n        console.error('❌ Failed to delete broadcast link:', error);\n      },\n    }\n  );\n}\n\n/**\n * Hook for toggling broadcast link active status\n */\nexport function useToggleBroadcastLinkStatus() {\n  const { invalidateQueries, updateQueryData } = useApiHookUtils();\n\n  return useOptimisticMutation<BroadcastQueries.BroadcastLink, { id: string; isActive: boolean }>(\n    async ({ id, isActive }) => {\n      const response = await fetch(`/api/broadcast-links/${id}`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ isActive }),\n      });\n\n      if (!response.ok) {\n        throw new Error(`Failed to toggle broadcast link status: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      onMutate: async ({ id, isActive }) => {\n        // Optimistically update the link status\n        const linkQueryKey = queryKeys.broadcast.link(id);\n        const previousLink = updateQueryData<BroadcastQueries.BroadcastLink>(\n          linkQueryKey,\n          (old) => old ? { ...old, isActive } : old\n        );\n\n        return { previousLink, linkQueryKey };\n      },\n      onError: (error, variables, context) => {\n        // Revert optimistic update on error\n        if (context?.previousLink && context?.linkQueryKey) {\n          updateQueryData(context.linkQueryKey, () => context.previousLink);\n        }\n        console.error('❌ Failed to toggle broadcast link status:', error);\n      },\n      onSuccess: (data) => {\n        // Invalidate related queries\n        invalidateQueries(queryKeys.broadcast.links());\n        invalidateQueries(queryKeys.broadcast.fixture(data.fixtureId));\n        console.log('✅ Broadcast link status toggled successfully');\n      },\n    }\n  );\n}\n\n/**\n * Composite hook for broadcast links operations\n */\nexport function useBroadcastLinksManager() {\n  const createLink = useCreateBroadcastLink();\n  const updateLink = useUpdateBroadcastLink();\n  const deleteLink = useDeleteBroadcastLink();\n  const toggleStatus = useToggleBroadcastLinkStatus();\n\n  return {\n    // Mutations\n    createLink,\n    updateLink,\n    deleteLink,\n    toggleStatus,\n    \n    // Actions\n    createBroadcastLink: createLink.mutate,\n    updateBroadcastLink: updateLink.mutate,\n    deleteBroadcastLink: deleteLink.mutate,\n    toggleLinkStatus: toggleStatus.mutate,\n    \n    // State\n    isCreating: createLink.isPending,\n    isUpdating: updateLink.isPending,\n    isDeleting: deleteLink.isPending,\n    isToggling: toggleStatus.isPending,\n    isLoading: createLink.isPending || updateLink.isPending || deleteLink.isPending || toggleStatus.isPending,\n    \n    // Errors\n    createError: createLink.error,\n    updateError: updateLink.error,\n    deleteError: deleteLink.error,\n    toggleError: toggleStatus.error,\n  };\n}\n\n/**\n * Hook for broadcast links by quality\n */\nexport function useBroadcastLinksByQuality(quality: 'HD' | 'SD' | 'Mobile') {\n  return useBroadcastLinks({ quality, isActive: true });\n}\n\n/**\n * Hook for broadcast links by language\n */\nexport function useBroadcastLinksByLanguage(language: string) {\n  return useBroadcastLinks({ language, isActive: true });\n}\n\n/**\n * Hook for active broadcast links\n */\nexport function useActiveBroadcastLinks() {\n  return useBroadcastLinks({ isActive: true });\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;;AAID;AAGA;;AALA;;;AAgBO,SAAS,kBAAkB,MAAkD;;IAClF,MAAM,cAAc,IAAI;IACxB,IAAI,QAAQ,MAAM,YAAY,GAAG,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;IAC9D,IAAI,QAAQ,OAAO,YAAY,GAAG,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;IACjE,IAAI,QAAQ,WAAW,YAAY,GAAG,CAAC,aAAa,OAAO,SAAS;IACpE,IAAI,QAAQ,SAAS,YAAY,GAAG,CAAC,WAAW,OAAO,OAAO;IAC9D,IAAI,QAAQ,UAAU,YAAY,GAAG,CAAC,YAAY,OAAO,QAAQ;IACjE,IAAI,QAAQ,aAAa,WAAW,YAAY,GAAG,CAAC,YAAY,OAAO,QAAQ,CAAC,QAAQ;IACxF,IAAI,QAAQ,OAAO,YAAY,GAAG,CAAC,SAAS,OAAO,KAAK;IAExD,OAAO,CAAA,GAAA,uIAAA,CAAA,oBAAiB,AAAD,EACrB;WAAI,gIAAA,CAAA,YAAS,CAAC,SAAS,CAAC,KAAK;QAAI;KAAO;+CACxC;YACE,MAAM,WAAW,MAAM,MAAM,CAAC,qBAAqB,EAAE,YAAY,QAAQ,IAAI;YAE7E,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,iCAAiC,EAAE,SAAS,UAAU,EAAE;YAC3E;YAEA,OAAO,SAAS,IAAI;QACtB;8CACA;QACE,WAAW,IAAI,KAAK;IACtB;AAEJ;GAzBgB;;QAUP,uIAAA,CAAA,oBAAiB;;;AAoBnB,SAAS,iBAAiB,MAAc;;IAC7C,OAAO,CAAA,GAAA,uIAAA,CAAA,eAAY,AAAD,EAChB,gIAAA,CAAA,YAAS,CAAC,SAAS,CAAC,IAAI,CAAC;yCACzB;YACE,MAAM,WAAW,MAAM,MAAM,CAAC,qBAAqB,EAAE,QAAQ;YAE7D,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,gCAAgC,EAAE,SAAS,UAAU,EAAE;YAC1E;YAEA,OAAO,SAAS,IAAI;QACtB;wCACA;QACE,SAAS,CAAC,CAAC;QACX,WAAW,IAAI,KAAK;IACtB;AAEJ;IAjBgB;;QACP,uIAAA,CAAA,eAAY;;;AAqBd,SAAS,yBAAyB,SAAiB;;IACxD,OAAO,CAAA,GAAA,uIAAA,CAAA,eAAY,AAAD,EAChB,gIAAA,CAAA,YAAS,CAAC,SAAS,CAAC,OAAO,CAAC;iDAC5B;YACE,MAAM,WAAW,MAAM,MAAM,CAAC,6BAA6B,EAAE,WAAW;YAExE,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,yCAAyC,EAAE,SAAS,UAAU,EAAE;YACnF;YAEA,OAAO,SAAS,IAAI;QACtB;gDACA;QACE,SAAS,CAAC,CAAC;QACX,WAAW,IAAI,KAAK;IACtB;AAEJ;IAjBgB;;QACP,uIAAA,CAAA,eAAY;;;AAqBd,SAAS;;IACd,MAAM,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,kBAAe,AAAD;IAE5C,OAAO,CAAA,GAAA,uIAAA,CAAA,wBAAqB,AAAD;wDACzB,OAAO;YACL,MAAM,WAAW,MAAM,MAAM,wBAAwB;gBACnD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,iCAAiC,EAAE,SAAS,UAAU,EAAE;YAC3E;YAEA,OAAO,SAAS,IAAI;QACtB;uDACA;QACE,SAAS;4DAAE,CAAC;gBACV,qCAAqC;gBACrC,kBAAkB,gIAAA,CAAA,YAAS,CAAC,SAAS,CAAC,KAAK;gBAC3C,kBAAkB,gIAAA,CAAA,YAAS,CAAC,SAAS,CAAC,OAAO,CAAC,KAAK,SAAS;gBAC5D,QAAQ,GAAG,CAAC;YACd;;QACA,OAAO;4DAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,sCAAsC;YACtD;;IACF;AAEJ;IA/BgB;;QACgB,uIAAA,CAAA,kBAAe;QAEtC,uIAAA,CAAA,wBAAqB;;;AAiCvB,SAAS;;IACd,MAAM,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,kBAAe,AAAD;IAE5C,OAAO,CAAA,GAAA,uIAAA,CAAA,wBAAqB,AAAD;wDACzB,OAAO,EAAE,EAAE,EAAE,IAAI,EAAE;YACjB,MAAM,WAAW,MAAM,MAAM,CAAC,qBAAqB,EAAE,IAAI,EAAE;gBACzD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,iCAAiC,EAAE,SAAS,UAAU,EAAE;YAC3E;YAEA,OAAO,SAAS,IAAI;QACtB;uDACA;QACE,SAAS;4DAAE,CAAC;gBACV,+CAA+C;gBAC/C,kBAAkB,gIAAA,CAAA,YAAS,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,EAAE;gBAClD,kBAAkB,gIAAA,CAAA,YAAS,CAAC,SAAS,CAAC,KAAK;gBAC3C,kBAAkB,gIAAA,CAAA,YAAS,CAAC,SAAS,CAAC,OAAO,CAAC,KAAK,SAAS;gBAC5D,QAAQ,GAAG,CAAC;YACd;;QACA,OAAO;4DAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,sCAAsC;YACtD;;IACF;AAEJ;IAhCgB;;QACgB,uIAAA,CAAA,kBAAe;QAEtC,uIAAA,CAAA,wBAAqB;;;AAkCvB,SAAS;;IACd,MAAM,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,kBAAe,AAAD;IAE5C,OAAO,CAAA,GAAA,uIAAA,CAAA,kBAAe,AAAD;kDACnB,OAAO;YACL,MAAM,WAAW,MAAM,MAAM,CAAC,qBAAqB,EAAE,QAAQ,EAAE;gBAC7D,QAAQ;YACV;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,iCAAiC,EAAE,SAAS,UAAU,EAAE;YAC3E;YAEA,OAAO,SAAS,IAAI;QACtB;iDACA;QACE,SAAS;sDAAE,CAAC,GAAG;gBACb,qCAAqC;gBACrC,kBAAkB,gIAAA,CAAA,YAAS,CAAC,SAAS,CAAC,KAAK;gBAC3C,kBAAkB,gIAAA,CAAA,YAAS,CAAC,SAAS,CAAC,IAAI,CAAC;gBAC3C,QAAQ,GAAG,CAAC;YACd;;QACA,OAAO;sDAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,sCAAsC;YACtD;;IACF;AAEJ;IA3BgB;;QACgB,uIAAA,CAAA,kBAAe;QAEtC,uIAAA,CAAA,kBAAe;;;AA6BjB,SAAS;;IACd,MAAM,EAAE,iBAAiB,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,kBAAe,AAAD;IAE7D,OAAO,CAAA,GAAA,uIAAA,CAAA,wBAAqB,AAAD;8DACzB,OAAO,EAAE,EAAE,EAAE,QAAQ,EAAE;YACrB,MAAM,WAAW,MAAM,MAAM,CAAC,qBAAqB,EAAE,IAAI,EAAE;gBACzD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAS;YAClC;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,wCAAwC,EAAE,SAAS,UAAU,EAAE;YAClF;YAEA,OAAO,SAAS,IAAI;QACtB;6DACA;QACE,QAAQ;kEAAE,OAAO,EAAE,EAAE,EAAE,QAAQ,EAAE;gBAC/B,wCAAwC;gBACxC,MAAM,eAAe,gIAAA,CAAA,YAAS,CAAC,SAAS,CAAC,IAAI,CAAC;gBAC9C,MAAM,eAAe,gBACnB;uFACA,CAAC,MAAQ,MAAM;4BAAE,GAAG,GAAG;4BAAE;wBAAS,IAAI;;gBAGxC,OAAO;oBAAE;oBAAc;gBAAa;YACtC;;QACA,OAAO;kEAAE,CAAC,OAAO,WAAW;gBAC1B,oCAAoC;gBACpC,IAAI,SAAS,gBAAgB,SAAS,cAAc;oBAClD,gBAAgB,QAAQ,YAAY;8EAAE,IAAM,QAAQ,YAAY;;gBAClE;gBACA,QAAQ,KAAK,CAAC,6CAA6C;YAC7D;;QACA,SAAS;kEAAE,CAAC;gBACV,6BAA6B;gBAC7B,kBAAkB,gIAAA,CAAA,YAAS,CAAC,SAAS,CAAC,KAAK;gBAC3C,kBAAkB,gIAAA,CAAA,YAAS,CAAC,SAAS,CAAC,OAAO,CAAC,KAAK,SAAS;gBAC5D,QAAQ,GAAG,CAAC;YACd;;IACF;AAEJ;IA7CgB;;QACiC,uIAAA,CAAA,kBAAe;QAEvD,uIAAA,CAAA,wBAAqB;;;AA+CvB,SAAS;;IACd,MAAM,aAAa;IACnB,MAAM,aAAa;IACnB,MAAM,aAAa;IACnB,MAAM,eAAe;IAErB,OAAO;QACL,YAAY;QACZ;QACA;QACA;QACA;QAEA,UAAU;QACV,qBAAqB,WAAW,MAAM;QACtC,qBAAqB,WAAW,MAAM;QACtC,qBAAqB,WAAW,MAAM;QACtC,kBAAkB,aAAa,MAAM;QAErC,QAAQ;QACR,YAAY,WAAW,SAAS;QAChC,YAAY,WAAW,SAAS;QAChC,YAAY,WAAW,SAAS;QAChC,YAAY,aAAa,SAAS;QAClC,WAAW,WAAW,SAAS,IAAI,WAAW,SAAS,IAAI,WAAW,SAAS,IAAI,aAAa,SAAS;QAEzG,SAAS;QACT,aAAa,WAAW,KAAK;QAC7B,aAAa,WAAW,KAAK;QAC7B,aAAa,WAAW,KAAK;QAC7B,aAAa,aAAa,KAAK;IACjC;AACF;IAhCgB;;QACK;QACA;QACA;QACE;;;AAiChB,SAAS,2BAA2B,OAA+B;;IACxE,OAAO,kBAAkB;QAAE;QAAS,UAAU;IAAK;AACrD;IAFgB;;QACP;;;AAMF,SAAS,4BAA4B,QAAgB;;IAC1D,OAAO,kBAAkB;QAAE;QAAU,UAAU;IAAK;AACtD;IAFgB;;QACP;;;AAMF,SAAS;;IACd,OAAO,kBAAkB;QAAE,UAAU;IAAK;AAC5C;KAFgB;;QACP"}}, {"offset": {"line": 8091, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 8097, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/hooks/api/health-hooks.ts"], "sourcesContent": ["/**\n * Health Check API Hooks\n * Hooks for API health monitoring operations\n */\n\n'use client';\n\nimport { queryKeys } from '@/lib/query-client';\nimport { HealthQueries } from '@/lib/query-types';\nimport { useBackgroundSyncQuery, useBaseQuery } from './base-hooks';\n\n/**\n * Hook for API health check\n */\nexport function useApiHealth() {\n  return useBackgroundSyncQuery(\n    queryKeys.health.api(),\n    async (): Promise<HealthQueries.HealthResponse> => {\n      const response = await fetch('/api/health');\n\n      if (!response.ok) {\n        throw new Error(`Health check failed: ${response.statusText}`);\n      }\n\n      return response.json();\n    },\n    {\n      staleTime: 30 * 1000, // 30 seconds\n      refetchInterval: 60 * 1000, // Refetch every minute\n      retry: 3,\n      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 10000),\n    }\n  );\n}\n\n/**\n * Hook for database health check\n */\nexport function useDatabaseHealth() {\n  return useBaseQuery(\n    [...queryKeys.health.all, 'database'],\n    async (): Promise<{ status: 'up' | 'down'; responseTime: number }> => {\n      const startTime = performance.now();\n      const response = await fetch('/api/health/database');\n      const endTime = performance.now();\n\n      if (!response.ok) {\n        throw new Error(`Database health check failed: ${response.statusText}`);\n      }\n\n      const data = await response.json();\n      return {\n        ...data,\n        responseTime: endTime - startTime,\n      };\n    },\n    {\n      staleTime: 30 * 1000, // 30 seconds\n      retry: 2,\n    }\n  );\n}\n\n/**\n * Hook for external API health check\n */\nexport function useExternalApiHealth() {\n  return useBaseQuery(\n    [...queryKeys.health.all, 'external-api'],\n    async (): Promise<{ status: 'up' | 'down'; responseTime: number }> => {\n      const startTime = performance.now();\n      const response = await fetch('/api/health/external-api');\n      const endTime = performance.now();\n\n      if (!response.ok) {\n        throw new Error(`External API health check failed: ${response.statusText}`);\n      }\n\n      const data = await response.json();\n      return {\n        ...data,\n        responseTime: endTime - startTime,\n      };\n    },\n    {\n      staleTime: 60 * 1000, // 1 minute\n      retry: 2,\n    }\n  );\n}\n\n/**\n * Hook for comprehensive system health\n */\nexport function useSystemHealth() {\n  const apiHealth = useApiHealth();\n  const dbHealth = useDatabaseHealth();\n  const externalApiHealth = useExternalApiHealth();\n\n  const isLoading = apiHealth.isLoading || dbHealth.isLoading || externalApiHealth.isLoading;\n  const hasErrors = apiHealth.isError || dbHealth.isError || externalApiHealth.isError;\n\n  // Calculate overall health status\n  const getOverallStatus = (): 'healthy' | 'degraded' | 'unhealthy' => {\n    if (hasErrors) return 'unhealthy';\n    \n    const apiStatus = apiHealth.data?.status;\n    const dbStatus = dbHealth.data?.status;\n    const externalStatus = externalApiHealth.data?.status;\n\n    if (apiStatus === 'healthy' && dbStatus === 'up' && externalStatus === 'up') {\n      return 'healthy';\n    }\n    \n    if (apiStatus === 'unhealthy' || dbStatus === 'down') {\n      return 'unhealthy';\n    }\n    \n    return 'degraded';\n  };\n\n  // Calculate average response time\n  const getAverageResponseTime = (): number => {\n    const times = [\n      dbHealth.data?.responseTime,\n      externalApiHealth.data?.responseTime,\n    ].filter((time): time is number => typeof time === 'number');\n\n    if (times.length === 0) return 0;\n    return times.reduce((sum, time) => sum + time, 0) / times.length;\n  };\n\n  return {\n    // Individual health checks\n    api: apiHealth,\n    database: dbHealth,\n    externalApi: externalApiHealth,\n    \n    // Overall status\n    isLoading,\n    hasErrors,\n    overallStatus: getOverallStatus(),\n    averageResponseTime: getAverageResponseTime(),\n    \n    // Health data\n    healthData: {\n      api: apiHealth.data,\n      database: dbHealth.data,\n      externalApi: externalApiHealth.data,\n    },\n    \n    // Error information\n    errors: {\n      api: apiHealth.error,\n      database: dbHealth.error,\n      externalApi: externalApiHealth.error,\n    },\n    \n    // Refetch functions\n    refetchAll: () => {\n      apiHealth.refetch();\n      dbHealth.refetch();\n      externalApiHealth.refetch();\n    },\n  };\n}\n\n/**\n * Hook for monitoring API performance\n */\nexport function useApiPerformance() {\n  const systemHealth = useSystemHealth();\n\n  const getPerformanceMetrics = () => {\n    const { healthData } = systemHealth;\n    \n    return {\n      uptime: healthData.api?.uptime || 0,\n      responseTime: systemHealth.averageResponseTime,\n      status: systemHealth.overallStatus,\n      services: {\n        database: healthData.database?.status || 'unknown',\n        externalApi: healthData.externalApi?.status || 'unknown',\n      },\n      lastCheck: new Date().toISOString(),\n    };\n  };\n\n  return {\n    ...systemHealth,\n    performanceMetrics: getPerformanceMetrics(),\n    \n    // Performance indicators\n    isPerformanceGood: systemHealth.averageResponseTime < 1000, // Less than 1 second\n    isPerformanceFair: systemHealth.averageResponseTime < 3000, // Less than 3 seconds\n    isPerformancePoor: systemHealth.averageResponseTime >= 3000, // 3+ seconds\n  };\n}\n\n/**\n * Hook for health monitoring dashboard\n */\nexport function useHealthDashboard() {\n  const performance = useApiPerformance();\n  \n  const getDashboardData = () => {\n    const { healthData, overallStatus, averageResponseTime } = performance;\n    \n    return {\n      status: overallStatus,\n      uptime: healthData.api?.uptime || 0,\n      version: healthData.api?.version || 'unknown',\n      responseTime: averageResponseTime,\n      services: [\n        {\n          name: 'Database',\n          status: healthData.database?.status || 'unknown',\n          responseTime: healthData.database?.responseTime || 0,\n        },\n        {\n          name: 'External API',\n          status: healthData.externalApi?.status || 'unknown',\n          responseTime: healthData.externalApi?.responseTime || 0,\n        },\n      ],\n      lastUpdated: new Date().toISOString(),\n    };\n  };\n\n  return {\n    ...performance,\n    dashboardData: getDashboardData(),\n    \n    // Dashboard actions\n    refreshDashboard: performance.refetchAll,\n    \n    // Status indicators\n    statusColor: {\n      healthy: '#10b981', // green\n      degraded: '#f59e0b', // yellow\n      unhealthy: '#ef4444', // red\n    }[performance.overallStatus],\n    \n    statusIcon: {\n      healthy: '✅',\n      degraded: '⚠️',\n      unhealthy: '❌',\n    }[performance.overallStatus],\n  };\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;AAID;AAEA;;AAJA;;;AASO,SAAS;;IACd,OAAO,CAAA,GAAA,uIAAA,CAAA,yBAAsB,AAAD,EAC1B,gIAAA,CAAA,YAAS,CAAC,MAAM,CAAC,GAAG;+CACpB;YACE,MAAM,WAAW,MAAM,MAAM;YAE7B,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,qBAAqB,EAAE,SAAS,UAAU,EAAE;YAC/D;YAEA,OAAO,SAAS,IAAI;QACtB;8CACA;QACE,WAAW,KAAK;QAChB,iBAAiB,KAAK;QACtB,OAAO;QACP,UAAU;mDAAE,CAAC,eAAiB,KAAK,GAAG,CAAC,OAAO,KAAK,cAAc;;IACnE;AAEJ;GAnBgB;;QACP,uIAAA,CAAA,yBAAsB;;;AAuBxB,SAAS;;IACd,OAAO,CAAA,GAAA,uIAAA,CAAA,eAAY,AAAD,EAChB;WAAI,gIAAA,CAAA,YAAS,CAAC,MAAM,CAAC,GAAG;QAAE;KAAW;0CACrC;YACE,MAAM,YAAY,YAAY,GAAG;YACjC,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,UAAU,YAAY,GAAG;YAE/B,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,8BAA8B,EAAE,SAAS,UAAU,EAAE;YACxE;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO;gBACL,GAAG,IAAI;gBACP,cAAc,UAAU;YAC1B;QACF;yCACA;QACE,WAAW,KAAK;QAChB,OAAO;IACT;AAEJ;IAvBgB;;QACP,uIAAA,CAAA,eAAY;;;AA2Bd,SAAS;;IACd,OAAO,CAAA,GAAA,uIAAA,CAAA,eAAY,AAAD,EAChB;WAAI,gIAAA,CAAA,YAAS,CAAC,MAAM,CAAC,GAAG;QAAE;KAAe;6CACzC;YACE,MAAM,YAAY,YAAY,GAAG;YACjC,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,UAAU,YAAY,GAAG;YAE/B,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,kCAAkC,EAAE,SAAS,UAAU,EAAE;YAC5E;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO;gBACL,GAAG,IAAI;gBACP,cAAc,UAAU;YAC1B;QACF;4CACA;QACE,WAAW,KAAK;QAChB,OAAO;IACT;AAEJ;IAvBgB;;QACP,uIAAA,CAAA,eAAY;;;AA2Bd,SAAS;;IACd,MAAM,YAAY;IAClB,MAAM,WAAW;IACjB,MAAM,oBAAoB;IAE1B,MAAM,YAAY,UAAU,SAAS,IAAI,SAAS,SAAS,IAAI,kBAAkB,SAAS;IAC1F,MAAM,YAAY,UAAU,OAAO,IAAI,SAAS,OAAO,IAAI,kBAAkB,OAAO;IAEpF,kCAAkC;IAClC,MAAM,mBAAmB;QACvB,IAAI,WAAW,OAAO;QAEtB,MAAM,YAAY,UAAU,IAAI,EAAE;QAClC,MAAM,WAAW,SAAS,IAAI,EAAE;QAChC,MAAM,iBAAiB,kBAAkB,IAAI,EAAE;QAE/C,IAAI,cAAc,aAAa,aAAa,QAAQ,mBAAmB,MAAM;YAC3E,OAAO;QACT;QAEA,IAAI,cAAc,eAAe,aAAa,QAAQ;YACpD,OAAO;QACT;QAEA,OAAO;IACT;IAEA,kCAAkC;IAClC,MAAM,yBAAyB;QAC7B,MAAM,QAAQ;YACZ,SAAS,IAAI,EAAE;YACf,kBAAkB,IAAI,EAAE;SACzB,CAAC,MAAM,CAAC,CAAC,OAAyB,OAAO,SAAS;QAEnD,IAAI,MAAM,MAAM,KAAK,GAAG,OAAO;QAC/B,OAAO,MAAM,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,MAAM,KAAK,MAAM,MAAM;IAClE;IAEA,OAAO;QACL,2BAA2B;QAC3B,KAAK;QACL,UAAU;QACV,aAAa;QAEb,iBAAiB;QACjB;QACA;QACA,eAAe;QACf,qBAAqB;QAErB,cAAc;QACd,YAAY;YACV,KAAK,UAAU,IAAI;YACnB,UAAU,SAAS,IAAI;YACvB,aAAa,kBAAkB,IAAI;QACrC;QAEA,oBAAoB;QACpB,QAAQ;YACN,KAAK,UAAU,KAAK;YACpB,UAAU,SAAS,KAAK;YACxB,aAAa,kBAAkB,KAAK;QACtC;QAEA,oBAAoB;QACpB,YAAY;YACV,UAAU,OAAO;YACjB,SAAS,OAAO;YAChB,kBAAkB,OAAO;QAC3B;IACF;AACF;IAvEgB;;QACI;QACD;QACS;;;AAyErB,SAAS;;IACd,MAAM,eAAe;IAErB,MAAM,wBAAwB;QAC5B,MAAM,EAAE,UAAU,EAAE,GAAG;QAEvB,OAAO;YACL,QAAQ,WAAW,GAAG,EAAE,UAAU;YAClC,cAAc,aAAa,mBAAmB;YAC9C,QAAQ,aAAa,aAAa;YAClC,UAAU;gBACR,UAAU,WAAW,QAAQ,EAAE,UAAU;gBACzC,aAAa,WAAW,WAAW,EAAE,UAAU;YACjD;YACA,WAAW,IAAI,OAAO,WAAW;QACnC;IACF;IAEA,OAAO;QACL,GAAG,YAAY;QACf,oBAAoB;QAEpB,yBAAyB;QACzB,mBAAmB,aAAa,mBAAmB,GAAG;QACtD,mBAAmB,aAAa,mBAAmB,GAAG;QACtD,mBAAmB,aAAa,mBAAmB,IAAI;IACzD;AACF;IA3BgB;;QACO;;;AA+BhB,SAAS;;IACd,MAAM,eAAc;IAEpB,MAAM,mBAAmB;QACvB,MAAM,EAAE,UAAU,EAAE,aAAa,EAAE,mBAAmB,EAAE,GAAG;QAE3D,OAAO;YACL,QAAQ;YACR,QAAQ,WAAW,GAAG,EAAE,UAAU;YAClC,SAAS,WAAW,GAAG,EAAE,WAAW;YACpC,cAAc;YACd,UAAU;gBACR;oBACE,MAAM;oBACN,QAAQ,WAAW,QAAQ,EAAE,UAAU;oBACvC,cAAc,WAAW,QAAQ,EAAE,gBAAgB;gBACrD;gBACA;oBACE,MAAM;oBACN,QAAQ,WAAW,WAAW,EAAE,UAAU;oBAC1C,cAAc,WAAW,WAAW,EAAE,gBAAgB;gBACxD;aACD;YACD,aAAa,IAAI,OAAO,WAAW;QACrC;IACF;IAEA,OAAO;QACL,GAAG,YAAW;QACd,eAAe;QAEf,oBAAoB;QACpB,kBAAkB,aAAY,UAAU;QAExC,oBAAoB;QACpB,aAAa,CAAA;YACX,SAAS;YACT,UAAU;YACV,WAAW;QACb,CAAA,CAAC,CAAC,aAAY,aAAa,CAAC;QAE5B,YAAY,CAAA;YACV,SAAS;YACT,UAAU;YACV,WAAW;QACb,CAAA,CAAC,CAAC,aAAY,aAAa,CAAC;IAC9B;AACF;IA/CgB;;QACM"}}, {"offset": {"line": 8344, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 8350, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/hooks/api/index.ts"], "sourcesContent": ["/**\n * API Hooks Index\n * Central export for all API hooks\n */\n\n// Base hooks and utilities\nexport * from './base-hooks';\n\n// Domain-specific hooks\nexport * from './auth-hooks';\nexport * from './football-hooks';\nexport * from './users';\nexport * from './broadcast-hooks';\nexport * from './health-hooks';\n\n// Re-export TanStack Query hooks for convenience\nexport {\n  useQuery,\n  useMutation,\n  useQueryClient,\n  useInfiniteQuery,\n} from '@tanstack/react-query';\n\n/**\n * API hooks library metadata\n */\nexport const API_HOOKS_VERSION = '1.0.0';\nexport const API_HOOKS_NAME = 'APISportsGame API Hooks';\n\n/**\n * Quick setup function for API hooks\n */\nexport function setupApiHooks() {\n  console.log(`${API_HOOKS_NAME} v${API_HOOKS_VERSION} initialized`);\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,2BAA2B;;;;;;;;;;;;;AAqBpB,MAAM,oBAAoB;AAC1B,MAAM,iBAAiB;AAKvB,SAAS;IACd,QAAQ,GAAG,CAAC,GAAG,eAAe,EAAE,EAAE,kBAAkB,YAAY,CAAC;AACnE"}}, {"offset": {"line": 8374, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 8394, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/layout/app-header.tsx"], "sourcesContent": ["/**\n * App Header Component\n * Main header for the APISportsGame CMS\n */\n\n'use client';\n\nimport React from 'react';\nimport { Layout, Space, Button, Dropdown, Avatar, Badge, Tooltip, Typography } from 'antd';\nimport {\n  MenuFoldOutlined,\n  MenuUnfoldOutlined,\n  BellOutlined,\n  UserOutlined,\n  SettingOutlined,\n  LogoutOutlined,\n  SunOutlined,\n  MoonOutlined,\n  GlobalOutlined,\n} from '@ant-design/icons';\nimport { useTheme, useThemeStyles } from '@/theme';\nimport { useAuth } from '@/hooks/api';\n\nconst { Header } = Layout;\nconst { Text } = Typography;\n\n/**\n * App header props\n */\nexport interface AppHeaderProps {\n  sidebarCollapsed: boolean;\n  onSidebarToggle: () => void;\n  isMobile: boolean;\n  showSidebarToggle?: boolean;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\n/**\n * App Header component\n */\nexport function AppHeader({\n  sidebarCollapsed,\n  onSidebarToggle,\n  isMobile,\n  showSidebarToggle = true,\n  className,\n  style,\n}: AppHeaderProps) {\n  const { theme, toggleTheme, isDark } = useTheme();\n  const themeStyles = useThemeStyles();\n  const auth = useAuth();\n\n  // User menu items\n  const userMenuItems = [\n    {\n      key: 'profile',\n      icon: <UserOutlined />,\n      label: 'Profile',\n      onClick: () => console.log('Profile clicked'),\n    },\n    {\n      key: 'settings',\n      icon: <SettingOutlined />,\n      label: 'Settings',\n      onClick: () => console.log('Settings clicked'),\n    },\n    {\n      type: 'divider' as const,\n    },\n    {\n      key: 'logout',\n      icon: <LogoutOutlined />,\n      label: 'Logout',\n      onClick: () => auth.logoutUser(),\n      danger: true,\n    },\n  ];\n\n  // Notification menu items\n  const notificationItems = [\n    {\n      key: '1',\n      label: (\n        <div style={{ padding: '8px 0' }}>\n          <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>\n            New fixture sync completed\n          </div>\n          <div style={{ fontSize: '12px', color: themeStyles.getTextColor('secondary') }}>\n            2 minutes ago\n          </div>\n        </div>\n      ),\n    },\n    {\n      key: '2',\n      label: (\n        <div style={{ padding: '8px 0' }}>\n          <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>\n            User John Doe registered\n          </div>\n          <div style={{ fontSize: '12px', color: themeStyles.getTextColor('secondary') }}>\n            5 minutes ago\n          </div>\n        </div>\n      ),\n    },\n    {\n      type: 'divider' as const,\n    },\n    {\n      key: 'view-all',\n      label: (\n        <div style={{ textAlign: 'center', padding: '8px 0' }}>\n          <Button type=\"link\" size=\"small\">\n            View All Notifications\n          </Button>\n        </div>\n      ),\n    },\n  ];\n\n  const headerStyle: React.CSSProperties = {\n    position: 'fixed',\n    top: 0,\n    left: 0,\n    right: 0,\n    zIndex: 1000,\n    height: '64px',\n    padding: '0 24px',\n    backgroundColor: themeStyles.getBackgroundColor('container'),\n    borderBottom: `1px solid ${themeStyles.getBorderColor('primary')}`,\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'space-between',\n    ...style,\n  };\n\n  return (\n    <Header className={className} style={headerStyle}>\n      {/* Left section */}\n      <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>\n        {/* Sidebar toggle */}\n        {showSidebarToggle && (\n          <Button\n            type=\"text\"\n            icon={sidebarCollapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}\n            onClick={onSidebarToggle}\n            style={{\n              fontSize: '16px',\n              width: '40px',\n              height: '40px',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n            }}\n          />\n        )}\n\n        {/* Logo and title */}\n        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>\n          <div\n            style={{\n              width: '32px',\n              height: '32px',\n              backgroundColor: themeStyles.getColor('primary'),\n              borderRadius: '6px',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              color: 'white',\n              fontWeight: 'bold',\n              fontSize: '16px',\n            }}\n          >\n            ⚽\n          </div>\n          {(!isMobile || sidebarCollapsed) && (\n            <div>\n              <Text\n                style={{\n                  fontSize: '18px',\n                  fontWeight: 'bold',\n                  color: themeStyles.getTextColor('primary'),\n                }}\n              >\n                APISportsGame\n              </Text>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Right section */}\n      <Space size=\"middle\">\n        {/* Theme toggle */}\n        <Tooltip title={`Switch to ${isDark ? 'light' : 'dark'} mode`}>\n          <Button\n            type=\"text\"\n            icon={isDark ? <SunOutlined /> : <MoonOutlined />}\n            onClick={toggleTheme}\n            style={{\n              fontSize: '16px',\n              width: '40px',\n              height: '40px',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n            }}\n          />\n        </Tooltip>\n\n        {/* Language selector */}\n        <Tooltip title=\"Language\">\n          <Button\n            type=\"text\"\n            icon={<GlobalOutlined />}\n            style={{\n              fontSize: '16px',\n              width: '40px',\n              height: '40px',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n            }}\n          />\n        </Tooltip>\n\n        {/* Notifications */}\n        <Dropdown\n          menu={{ items: notificationItems }}\n          trigger={['click']}\n          placement=\"bottomRight\"\n        >\n          <Badge count={2} size=\"small\">\n            <Button\n              type=\"text\"\n              icon={<BellOutlined />}\n              style={{\n                fontSize: '16px',\n                width: '40px',\n                height: '40px',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n              }}\n            />\n          </Badge>\n        </Dropdown>\n\n        {/* User menu */}\n        <Dropdown\n          menu={{ items: userMenuItems }}\n          trigger={['click']}\n          placement=\"bottomRight\"\n        >\n          <div\n            style={{\n              display: 'flex',\n              alignItems: 'center',\n              gap: '8px',\n              cursor: 'pointer',\n              padding: '4px 8px',\n              borderRadius: '6px',\n              transition: 'background-color 0.2s ease',\n            }}\n          >\n            <Avatar\n              size=\"small\"\n              icon={<UserOutlined />}\n              style={{\n                backgroundColor: themeStyles.getColor('primary'),\n              }}\n            />\n            {!isMobile && (\n              <div>\n                <div\n                  style={{\n                    fontSize: '14px',\n                    fontWeight: 'bold',\n                    color: themeStyles.getTextColor('primary'),\n                    lineHeight: 1.2,\n                  }}\n                >\n                  {auth.user?.username || 'Admin'}\n                </div>\n                <div\n                  style={{\n                    fontSize: '12px',\n                    color: themeStyles.getTextColor('secondary'),\n                    lineHeight: 1,\n                  }}\n                >\n                  {auth.user?.role || 'Administrator'}\n                </div>\n              </div>\n            )}\n          </div>\n        </Dropdown>\n      </Space>\n    </Header>\n  );\n}\n\n/**\n * Header breadcrumb component\n */\nexport interface HeaderBreadcrumbProps {\n  items: Array<{\n    title: string;\n    href?: string;\n  }>;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\nexport function HeaderBreadcrumb({ items, className, style }: HeaderBreadcrumbProps) {\n  const themeStyles = useThemeStyles();\n\n  return (\n    <div\n      className={className}\n      style={{\n        padding: '8px 24px',\n        backgroundColor: themeStyles.getBackgroundColor('elevated'),\n        borderBottom: `1px solid ${themeStyles.getBorderColor('primary')}`,\n        ...style,\n      }}\n    >\n      <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>\n        {items.map((item, index) => (\n          <React.Fragment key={index}>\n            {index > 0 && (\n              <span style={{ color: themeStyles.getTextColor('tertiary') }}>\n                /\n              </span>\n            )}\n            {item.href ? (\n              <a\n                href={item.href}\n                style={{\n                  color: themeStyles.getColor('primary'),\n                  textDecoration: 'none',\n                  fontSize: '14px',\n                }}\n              >\n                {item.title}\n              </a>\n            ) : (\n              <span\n                style={{\n                  color: themeStyles.getTextColor('primary'),\n                  fontSize: '14px',\n                  fontWeight: index === items.length - 1 ? 'bold' : 'normal',\n                }}\n              >\n                {item.title}\n              </span>\n            )}\n          </React.Fragment>\n        ))}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AAID;AAaA;AACA;AADA;AAZA;AAAA;AAaA;AAZA;AAAA;AAAA;AADA;AACA;AAAA;AADA;AAAA;AACA;AAAA;AAAA;AADA;AAAA;AACA;AADA;;;AAHA;;;;;;AAkBA,MAAM,EAAE,MAAM,EAAE,GAAG,qLAAA,CAAA,SAAM;AACzB,MAAM,EAAE,IAAI,EAAE,GAAG,6LAAA,CAAA,aAAU;AAiBpB,SAAS,UAAU,EACxB,gBAAgB,EAChB,eAAe,EACf,QAAQ,EACR,oBAAoB,IAAI,EACxB,SAAS,EACT,KAAK,EACU;;IACf,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD;IAC9C,MAAM,cAAc,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,OAAO,CAAA,GAAA,uIAAA,CAAA,UAAO,AAAD;IAEnB,kBAAkB;IAClB,MAAM,gBAAgB;QACpB;YACE,KAAK;YACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;YACnB,OAAO;YACP,SAAS,IAAM,QAAQ,GAAG,CAAC;QAC7B;QACA;YACE,KAAK;YACL,oBAAM,6LAAC,2NAAA,CAAA,kBAAe;;;;;YACtB,OAAO;YACP,SAAS,IAAM,QAAQ,GAAG,CAAC;QAC7B;QACA;YACE,MAAM;QACR;QACA;YACE,KAAK;YACL,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;YACrB,OAAO;YACP,SAAS,IAAM,KAAK,UAAU;YAC9B,QAAQ;QACV;KACD;IAED,0BAA0B;IAC1B,MAAM,oBAAoB;QACxB;YACE,KAAK;YACL,qBACE,6LAAC;gBAAI,OAAO;oBAAE,SAAS;gBAAQ;;kCAC7B,6LAAC;wBAAI,OAAO;4BAAE,YAAY;4BAAQ,cAAc;wBAAM;kCAAG;;;;;;kCAGzD,6LAAC;wBAAI,OAAO;4BAAE,UAAU;4BAAQ,OAAO,YAAY,YAAY,CAAC;wBAAa;kCAAG;;;;;;;;;;;;QAKtF;QACA;YACE,KAAK;YACL,qBACE,6LAAC;gBAAI,OAAO;oBAAE,SAAS;gBAAQ;;kCAC7B,6LAAC;wBAAI,OAAO;4BAAE,YAAY;4BAAQ,cAAc;wBAAM;kCAAG;;;;;;kCAGzD,6LAAC;wBAAI,OAAO;4BAAE,UAAU;4BAAQ,OAAO,YAAY,YAAY,CAAC;wBAAa;kCAAG;;;;;;;;;;;;QAKtF;QACA;YACE,MAAM;QACR;QACA;YACE,KAAK;YACL,qBACE,6LAAC;gBAAI,OAAO;oBAAE,WAAW;oBAAU,SAAS;gBAAQ;0BAClD,cAAA,6LAAC,qMAAA,CAAA,SAAM;oBAAC,MAAK;oBAAO,MAAK;8BAAQ;;;;;;;;;;;QAKvC;KACD;IAED,MAAM,cAAmC;QACvC,UAAU;QACV,KAAK;QACL,MAAM;QACN,OAAO;QACP,QAAQ;QACR,QAAQ;QACR,SAAS;QACT,iBAAiB,YAAY,kBAAkB,CAAC;QAChD,cAAc,CAAC,UAAU,EAAE,YAAY,cAAc,CAAC,YAAY;QAClE,SAAS;QACT,YAAY;QACZ,gBAAgB;QAChB,GAAG,KAAK;IACV;IAEA,qBACE,6LAAC;QAAO,WAAW;QAAW,OAAO;;0BAEnC,6LAAC;gBAAI,OAAO;oBAAE,SAAS;oBAAQ,YAAY;oBAAU,KAAK;gBAAO;;oBAE9D,mCACC,6LAAC,qMAAA,CAAA,SAAM;wBACL,MAAK;wBACL,MAAM,iCAAmB,6LAAC,iOAAA,CAAA,qBAAkB;;;;mDAAM,6LAAC,6NAAA,CAAA,mBAAgB;;;;;wBACnE,SAAS;wBACT,OAAO;4BACL,UAAU;4BACV,OAAO;4BACP,QAAQ;4BACR,SAAS;4BACT,YAAY;4BACZ,gBAAgB;wBAClB;;;;;;kCAKJ,6LAAC;wBAAI,OAAO;4BAAE,SAAS;4BAAQ,YAAY;4BAAU,KAAK;wBAAO;;0CAC/D,6LAAC;gCACC,OAAO;oCACL,OAAO;oCACP,QAAQ;oCACR,iBAAiB,YAAY,QAAQ,CAAC;oCACtC,cAAc;oCACd,SAAS;oCACT,YAAY;oCACZ,gBAAgB;oCAChB,OAAO;oCACP,YAAY;oCACZ,UAAU;gCACZ;0CACD;;;;;;4BAGA,CAAC,CAAC,YAAY,gBAAgB,mBAC7B,6LAAC;0CACC,cAAA,6LAAC;oCACC,OAAO;wCACL,UAAU;wCACV,YAAY;wCACZ,OAAO,YAAY,YAAY,CAAC;oCAClC;8CACD;;;;;;;;;;;;;;;;;;;;;;;0BAST,6LAAC,mMAAA,CAAA,QAAK;gBAAC,MAAK;;kCAEV,6LAAC,uLAAA,CAAA,UAAO;wBAAC,OAAO,CAAC,UAAU,EAAE,SAAS,UAAU,OAAO,KAAK,CAAC;kCAC3D,cAAA,6LAAC,qMAAA,CAAA,SAAM;4BACL,MAAK;4BACL,MAAM,uBAAS,6LAAC,mNAAA,CAAA,cAAW;;;;uDAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;4BAC9C,SAAS;4BACT,OAAO;gCACL,UAAU;gCACV,OAAO;gCACP,QAAQ;gCACR,SAAS;gCACT,YAAY;gCACZ,gBAAgB;4BAClB;;;;;;;;;;;kCAKJ,6LAAC,uLAAA,CAAA,UAAO;wBAAC,OAAM;kCACb,cAAA,6LAAC,qMAAA,CAAA,SAAM;4BACL,MAAK;4BACL,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;4BACrB,OAAO;gCACL,UAAU;gCACV,OAAO;gCACP,QAAQ;gCACR,SAAS;gCACT,YAAY;gCACZ,gBAAgB;4BAClB;;;;;;;;;;;kCAKJ,6LAAC,yLAAA,CAAA,WAAQ;wBACP,MAAM;4BAAE,OAAO;wBAAkB;wBACjC,SAAS;4BAAC;yBAAQ;wBAClB,WAAU;kCAEV,cAAA,6LAAC,mLAAA,CAAA,QAAK;4BAAC,OAAO;4BAAG,MAAK;sCACpB,cAAA,6LAAC,qMAAA,CAAA,SAAM;gCACL,MAAK;gCACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;gCACnB,OAAO;oCACL,UAAU;oCACV,OAAO;oCACP,QAAQ;oCACR,SAAS;oCACT,YAAY;oCACZ,gBAAgB;gCAClB;;;;;;;;;;;;;;;;kCAMN,6LAAC,yLAAA,CAAA,WAAQ;wBACP,MAAM;4BAAE,OAAO;wBAAc;wBAC7B,SAAS;4BAAC;yBAAQ;wBAClB,WAAU;kCAEV,cAAA,6LAAC;4BACC,OAAO;gCACL,SAAS;gCACT,YAAY;gCACZ,KAAK;gCACL,QAAQ;gCACR,SAAS;gCACT,cAAc;gCACd,YAAY;4BACd;;8CAEA,6LAAC,qLAAA,CAAA,SAAM;oCACL,MAAK;oCACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;oCACnB,OAAO;wCACL,iBAAiB,YAAY,QAAQ,CAAC;oCACxC;;;;;;gCAED,CAAC,0BACA,6LAAC;;sDACC,6LAAC;4CACC,OAAO;gDACL,UAAU;gDACV,YAAY;gDACZ,OAAO,YAAY,YAAY,CAAC;gDAChC,YAAY;4CACd;sDAEC,KAAK,IAAI,EAAE,YAAY;;;;;;sDAE1B,6LAAC;4CACC,OAAO;gDACL,UAAU;gDACV,OAAO,YAAY,YAAY,CAAC;gDAChC,YAAY;4CACd;sDAEC,KAAK,IAAI,EAAE,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAStC;GArQgB;;QAQyB,wHAAA,CAAA,WAAQ;QAC3B,wHAAA,CAAA,iBAAc;QACrB,uIAAA,CAAA,UAAO;;;KAVN;AAmRT,SAAS,iBAAiB,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAyB;;IACjF,MAAM,cAAc,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;IAEjC,qBACE,6LAAC;QACC,WAAW;QACX,OAAO;YACL,SAAS;YACT,iBAAiB,YAAY,kBAAkB,CAAC;YAChD,cAAc,CAAC,UAAU,EAAE,YAAY,cAAc,CAAC,YAAY;YAClE,GAAG,KAAK;QACV;kBAEA,cAAA,6LAAC;YAAI,OAAO;gBAAE,SAAS;gBAAQ,YAAY;gBAAU,KAAK;YAAM;sBAC7D,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC,6JAAA,CAAA,UAAK,CAAC,QAAQ;;wBACZ,QAAQ,mBACP,6LAAC;4BAAK,OAAO;gCAAE,OAAO,YAAY,YAAY,CAAC;4BAAY;sCAAG;;;;;;wBAI/D,KAAK,IAAI,iBACR,6LAAC;4BACC,MAAM,KAAK,IAAI;4BACf,OAAO;gCACL,OAAO,YAAY,QAAQ,CAAC;gCAC5B,gBAAgB;gCAChB,UAAU;4BACZ;sCAEC,KAAK,KAAK;;;;;iDAGb,6LAAC;4BACC,OAAO;gCACL,OAAO,YAAY,YAAY,CAAC;gCAChC,UAAU;gCACV,YAAY,UAAU,MAAM,MAAM,GAAG,IAAI,SAAS;4BACpD;sCAEC,KAAK,KAAK;;;;;;;mBAzBI;;;;;;;;;;;;;;;AAiC/B;IAhDgB;;QACM,wHAAA,CAAA,iBAAc;;;MADpB"}}, {"offset": {"line": 8967, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 8973, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/layout/app-sidebar.tsx"], "sourcesContent": ["/**\n * App Sidebar Component\n * Navigation sidebar for the APISportsGame CMS\n */\n\n'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { Layout, Menu, Divider } from 'antd';\nimport {\n  DashboardOutlined,\n  UserOutlined,\n  TeamOutlined,\n  TrophyOutlined,\n  CalendarOutlined,\n  LinkOutlined,\n  SettingOutlined,\n  BarChartOutlined,\n  FileTextOutlined,\n  DatabaseOutlined,\n  ApiOutlined,\n  HeartOutlined,\n  PlusOutlined,\n  PlayCircleOutlined,\n  ExperimentOutlined,\n} from '@ant-design/icons';\nimport { useThemeStyles } from '@/theme';\nimport { useRouter, usePathname } from 'next/navigation';\n\nconst { Sider } = Layout;\n\n/**\n * App sidebar props\n */\nexport interface AppSidebarProps {\n  collapsed: boolean;\n  isMobile: boolean;\n  onCollapse: (collapsed: boolean) => void;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\n/**\n * Menu item interface\n */\ninterface MenuItem {\n  key: string;\n  icon: React.ReactNode;\n  label: string;\n  path?: string;\n  children?: MenuItem[];\n  disabled?: boolean;\n}\n\n/**\n * App Sidebar component\n */\nexport function AppSidebar({\n  collapsed,\n  isMobile,\n  onCollapse,\n  className,\n  style,\n}: AppSidebarProps) {\n  const themeStyles = useThemeStyles();\n  const router = useRouter();\n  const pathname = usePathname();\n\n  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);\n  const [openKeys, setOpenKeys] = useState<string[]>([]);\n\n  // Menu items configuration\n  const menuItems: MenuItem[] = [\n    {\n      key: 'dashboard',\n      icon: <DashboardOutlined />,\n      label: 'Dashboard',\n      path: '/',\n    },\n    {\n      key: 'divider-1',\n      icon: null,\n      label: '',\n    },\n    {\n      key: 'user-management',\n      icon: <UserOutlined />,\n      label: 'User System',\n      children: [\n        {\n          key: 'system-users',\n          icon: <TeamOutlined />,\n          label: 'System Users',\n          path: '/users/system',\n        },\n        {\n          key: 'user-roles',\n          icon: <SettingOutlined />,\n          label: 'Roles & Permissions',\n          path: '/users/roles',\n        },\n      ],\n    },\n    {\n      key: 'football-management',\n      icon: <TrophyOutlined />,\n      label: 'Football Data',\n      children: [\n        {\n          key: 'leagues',\n          icon: <TrophyOutlined />,\n          label: 'Leagues',\n          path: '/football/leagues',\n        },\n        {\n          key: 'teams',\n          icon: <TeamOutlined />,\n          label: 'Teams',\n          path: '/football/teams',\n        },\n        {\n          key: 'fixtures',\n          icon: <CalendarOutlined />,\n          label: 'Fixtures',\n          path: '/football/fixtures',\n        },\n        {\n          key: 'sync-status',\n          icon: <DatabaseOutlined />,\n          label: 'Sync Status',\n          path: '/football/sync',\n        },\n      ],\n    },\n    {\n      key: 'broadcast-management',\n      icon: <PlayCircleOutlined />,\n      label: 'Broadcast Links',\n      children: [\n        {\n          key: 'broadcast-links',\n          icon: <LinkOutlined />,\n          label: 'Manage Links',\n          path: '/broadcast-links',\n        },\n        {\n          key: 'broadcast-create',\n          icon: <PlusOutlined />,\n          label: 'Create Link',\n          path: '/broadcast-links/create',\n        },\n        {\n          key: 'broadcast-demo',\n          icon: <ExperimentOutlined />,\n          label: 'Demo & Testing',\n          path: '/broadcast-demo',\n        },\n      ],\n    },\n    {\n      key: 'divider-2',\n      icon: null,\n      label: '',\n    },\n    {\n      key: 'system',\n      icon: <SettingOutlined />,\n      label: 'System',\n      children: [\n        {\n          key: 'api-health',\n          icon: <HeartOutlined />,\n          label: 'API Health',\n          path: '/system/health',\n        },\n        {\n          key: 'api-docs',\n          icon: <ApiOutlined />,\n          label: 'API Documentation',\n          path: '/system/api-docs',\n        },\n        {\n          key: 'logs',\n          icon: <FileTextOutlined />,\n          label: 'System Logs',\n          path: '/system/logs',\n        },\n        {\n          key: 'settings',\n          icon: <SettingOutlined />,\n          label: 'Settings',\n          path: '/system/settings',\n        },\n      ],\n    },\n\n  ];\n\n  // Update selected keys based on current pathname\n  useEffect(() => {\n    const findSelectedKey = (items: MenuItem[], path: string): string | null => {\n      for (const item of items) {\n        if (item.path === path) {\n          return item.key;\n        }\n        if (item.children) {\n          const childKey = findSelectedKey(item.children, path);\n          if (childKey) {\n            return childKey;\n          }\n        }\n      }\n      return null;\n    };\n\n    const selectedKey = findSelectedKey(menuItems, pathname);\n    if (selectedKey) {\n      setSelectedKeys([selectedKey]);\n\n      // Auto-expand parent menu\n      const findParentKey = (items: MenuItem[], targetKey: string): string | null => {\n        for (const item of items) {\n          if (item.children) {\n            const hasChild = item.children.some(child => child.key === targetKey);\n            if (hasChild) {\n              return item.key;\n            }\n          }\n        }\n        return null;\n      };\n\n      const parentKey = findParentKey(menuItems, selectedKey);\n      if (parentKey && !collapsed) {\n        setOpenKeys([parentKey]);\n      }\n    }\n  }, [pathname, collapsed]);\n\n  // Handle menu click\n  const handleMenuClick = ({ key }: { key: string }) => {\n    const findMenuItem = (items: MenuItem[], targetKey: string): MenuItem | null => {\n      for (const item of items) {\n        if (item.key === targetKey) {\n          return item;\n        }\n        if (item.children) {\n          const childItem = findMenuItem(item.children, targetKey);\n          if (childItem) {\n            return childItem;\n          }\n        }\n      }\n      return null;\n    };\n\n    const menuItem = findMenuItem(menuItems, key);\n    if (menuItem?.path) {\n      router.push(menuItem.path);\n\n      // Close sidebar on mobile after navigation\n      if (isMobile) {\n        onCollapse(true);\n      }\n    }\n  };\n\n  // Handle submenu open/close\n  const handleOpenChange = (keys: string[]) => {\n    setOpenKeys(keys);\n  };\n\n  // Convert menu items to Ant Design menu format\n  const convertToAntMenuItems = (items: MenuItem[]): any[] => {\n    return items.map(item => {\n      // Handle dividers\n      if (item.key.startsWith('divider')) {\n        return {\n          type: 'divider',\n          key: item.key,\n        };\n      }\n\n      // Handle regular items\n      if (item.children) {\n        return {\n          key: item.key,\n          icon: item.icon,\n          label: item.label,\n          children: convertToAntMenuItems(item.children),\n          disabled: item.disabled,\n        };\n      }\n\n      return {\n        key: item.key,\n        icon: item.icon,\n        label: item.label,\n        disabled: item.disabled,\n      };\n    });\n  };\n\n  const siderStyle: React.CSSProperties = {\n    position: 'fixed',\n    left: 0,\n    top: '64px', // Header height\n    bottom: 0,\n    zIndex: isMobile ? 1000 : 100,\n    backgroundColor: themeStyles.getBackgroundColor('container'),\n    borderRight: `1px solid ${themeStyles.getBorderColor('primary')}`,\n    overflow: 'auto',\n    ...style,\n  };\n\n  return (\n    <Sider\n      className={className}\n      style={siderStyle}\n      collapsed={collapsed}\n      collapsible={false}\n      width={250}\n      collapsedWidth={80}\n      theme=\"light\"\n    >\n      {/* Sidebar content */}\n      <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>\n        {/* Main navigation menu */}\n        <Menu\n          mode=\"inline\"\n          selectedKeys={selectedKeys}\n          openKeys={collapsed ? [] : openKeys}\n          onOpenChange={handleOpenChange}\n          onClick={handleMenuClick}\n          items={convertToAntMenuItems(menuItems)}\n          style={{\n            flex: 1,\n            border: 'none',\n            backgroundColor: 'transparent',\n          }}\n        />\n\n        {/* Sidebar footer */}\n        {!collapsed && (\n          <div\n            style={{\n              padding: '16px',\n              borderTop: `1px solid ${themeStyles.getBorderColor('primary')}`,\n              textAlign: 'center',\n            }}\n          >\n            <div\n              style={{\n                fontSize: '12px',\n                color: themeStyles.getTextColor('tertiary'),\n                marginBottom: '4px',\n              }}\n            >\n              APISportsGame CMS\n            </div>\n            <div\n              style={{\n                fontSize: '10px',\n                color: themeStyles.getTextColor('tertiary'),\n              }}\n            >\n              v1.0.0\n            </div>\n          </div>\n        )}\n      </div>\n    </Sider>\n  );\n}\n\n/**\n * Sidebar menu item component for custom rendering\n */\nexport interface SidebarMenuItemProps {\n  icon: React.ReactNode;\n  label: string;\n  active?: boolean;\n  collapsed?: boolean;\n  onClick?: () => void;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\nexport function SidebarMenuItem({\n  icon,\n  label,\n  active = false,\n  collapsed = false,\n  onClick,\n  className,\n  style,\n}: SidebarMenuItemProps) {\n  const themeStyles = useThemeStyles();\n\n  const itemStyle: React.CSSProperties = {\n    display: 'flex',\n    alignItems: 'center',\n    gap: collapsed ? '0' : '12px',\n    padding: '12px 16px',\n    cursor: 'pointer',\n    borderRadius: '6px',\n    margin: '2px 8px',\n    transition: 'all 0.2s ease',\n    backgroundColor: active ? themeStyles.getColor('primary') + '10' : 'transparent',\n    color: active ? themeStyles.getColor('primary') : themeStyles.getTextColor('primary'),\n    ...style,\n  };\n\n  return (\n    <div\n      className={className}\n      style={itemStyle}\n      onClick={onClick}\n    >\n      <div style={{ fontSize: '16px', minWidth: '16px' }}>\n        {icon}\n      </div>\n      {!collapsed && (\n        <div style={{ fontSize: '14px', fontWeight: active ? 'bold' : 'normal' }}>\n          {label}\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AAID;AAmBA;AACA;AADA;AAlBA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AADA;;;AAHA;;;;;;AAwBA,MAAM,EAAE,KAAK,EAAE,GAAG,qLAAA,CAAA,SAAM;AA4BjB,SAAS,WAAW,EACzB,SAAS,EACT,QAAQ,EACR,UAAU,EACV,SAAS,EACT,KAAK,EACW;;IAChB,MAAM,cAAc,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC7D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAErD,2BAA2B;IAC3B,MAAM,YAAwB;QAC5B;YACE,KAAK;YACL,oBAAM,6LAAC,+NAAA,CAAA,oBAAiB;;;;;YACxB,OAAO;YACP,MAAM;QACR;QACA;YACE,KAAK;YACL,MAAM;YACN,OAAO;QACT;QACA;YACE,KAAK;YACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;YACnB,OAAO;YACP,UAAU;gBACR;oBACE,KAAK;oBACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;oBACnB,OAAO;oBACP,MAAM;gBACR;gBACA;oBACE,KAAK;oBACL,oBAAM,6LAAC,2NAAA,CAAA,kBAAe;;;;;oBACtB,OAAO;oBACP,MAAM;gBACR;aACD;QACH;QACA;YACE,KAAK;YACL,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;YACrB,OAAO;YACP,UAAU;gBACR;oBACE,KAAK;oBACL,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;oBACrB,OAAO;oBACP,MAAM;gBACR;gBACA;oBACE,KAAK;oBACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;oBACnB,OAAO;oBACP,MAAM;gBACR;gBACA;oBACE,KAAK;oBACL,oBAAM,6LAAC,6NAAA,CAAA,mBAAgB;;;;;oBACvB,OAAO;oBACP,MAAM;gBACR;gBACA;oBACE,KAAK;oBACL,oBAAM,6LAAC,6NAAA,CAAA,mBAAgB;;;;;oBACvB,OAAO;oBACP,MAAM;gBACR;aACD;QACH;QACA;YACE,KAAK;YACL,oBAAM,6LAAC,iOAAA,CAAA,qBAAkB;;;;;YACzB,OAAO;YACP,UAAU;gBACR;oBACE,KAAK;oBACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;oBACnB,OAAO;oBACP,MAAM;gBACR;gBACA;oBACE,KAAK;oBACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;oBACnB,OAAO;oBACP,MAAM;gBACR;gBACA;oBACE,KAAK;oBACL,oBAAM,6LAAC,iOAAA,CAAA,qBAAkB;;;;;oBACzB,OAAO;oBACP,MAAM;gBACR;aACD;QACH;QACA;YACE,KAAK;YACL,MAAM;YACN,OAAO;QACT;QACA;YACE,KAAK;YACL,oBAAM,6LAAC,2NAAA,CAAA,kBAAe;;;;;YACtB,OAAO;YACP,UAAU;gBACR;oBACE,KAAK;oBACL,oBAAM,6LAAC,uNAAA,CAAA,gBAAa;;;;;oBACpB,OAAO;oBACP,MAAM;gBACR;gBACA;oBACE,KAAK;oBACL,oBAAM,6LAAC,mNAAA,CAAA,cAAW;;;;;oBAClB,OAAO;oBACP,MAAM;gBACR;gBACA;oBACE,KAAK;oBACL,oBAAM,6LAAC,6NAAA,CAAA,mBAAgB;;;;;oBACvB,OAAO;oBACP,MAAM;gBACR;gBACA;oBACE,KAAK;oBACL,oBAAM,6LAAC,2NAAA,CAAA,kBAAe;;;;;oBACtB,OAAO;oBACP,MAAM;gBACR;aACD;QACH;KAED;IAED,iDAAiD;IACjD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,MAAM;wDAAkB,CAAC,OAAmB;oBAC1C,KAAK,MAAM,QAAQ,MAAO;wBACxB,IAAI,KAAK,IAAI,KAAK,MAAM;4BACtB,OAAO,KAAK,GAAG;wBACjB;wBACA,IAAI,KAAK,QAAQ,EAAE;4BACjB,MAAM,WAAW,gBAAgB,KAAK,QAAQ,EAAE;4BAChD,IAAI,UAAU;gCACZ,OAAO;4BACT;wBACF;oBACF;oBACA,OAAO;gBACT;;YAEA,MAAM,cAAc,gBAAgB,WAAW;YAC/C,IAAI,aAAa;gBACf,gBAAgB;oBAAC;iBAAY;gBAE7B,0BAA0B;gBAC1B,MAAM;0DAAgB,CAAC,OAAmB;wBACxC,KAAK,MAAM,QAAQ,MAAO;4BACxB,IAAI,KAAK,QAAQ,EAAE;gCACjB,MAAM,WAAW,KAAK,QAAQ,CAAC,IAAI;mFAAC,CAAA,QAAS,MAAM,GAAG,KAAK;;gCAC3D,IAAI,UAAU;oCACZ,OAAO,KAAK,GAAG;gCACjB;4BACF;wBACF;wBACA,OAAO;oBACT;;gBAEA,MAAM,YAAY,cAAc,WAAW;gBAC3C,IAAI,aAAa,CAAC,WAAW;oBAC3B,YAAY;wBAAC;qBAAU;gBACzB;YACF;QACF;+BAAG;QAAC;QAAU;KAAU;IAExB,oBAAoB;IACpB,MAAM,kBAAkB,CAAC,EAAE,GAAG,EAAmB;QAC/C,MAAM,eAAe,CAAC,OAAmB;YACvC,KAAK,MAAM,QAAQ,MAAO;gBACxB,IAAI,KAAK,GAAG,KAAK,WAAW;oBAC1B,OAAO;gBACT;gBACA,IAAI,KAAK,QAAQ,EAAE;oBACjB,MAAM,YAAY,aAAa,KAAK,QAAQ,EAAE;oBAC9C,IAAI,WAAW;wBACb,OAAO;oBACT;gBACF;YACF;YACA,OAAO;QACT;QAEA,MAAM,WAAW,aAAa,WAAW;QACzC,IAAI,UAAU,MAAM;YAClB,OAAO,IAAI,CAAC,SAAS,IAAI;YAEzB,2CAA2C;YAC3C,IAAI,UAAU;gBACZ,WAAW;YACb;QACF;IACF;IAEA,4BAA4B;IAC5B,MAAM,mBAAmB,CAAC;QACxB,YAAY;IACd;IAEA,+CAA+C;IAC/C,MAAM,wBAAwB,CAAC;QAC7B,OAAO,MAAM,GAAG,CAAC,CAAA;YACf,kBAAkB;YAClB,IAAI,KAAK,GAAG,CAAC,UAAU,CAAC,YAAY;gBAClC,OAAO;oBACL,MAAM;oBACN,KAAK,KAAK,GAAG;gBACf;YACF;YAEA,uBAAuB;YACvB,IAAI,KAAK,QAAQ,EAAE;gBACjB,OAAO;oBACL,KAAK,KAAK,GAAG;oBACb,MAAM,KAAK,IAAI;oBACf,OAAO,KAAK,KAAK;oBACjB,UAAU,sBAAsB,KAAK,QAAQ;oBAC7C,UAAU,KAAK,QAAQ;gBACzB;YACF;YAEA,OAAO;gBACL,KAAK,KAAK,GAAG;gBACb,MAAM,KAAK,IAAI;gBACf,OAAO,KAAK,KAAK;gBACjB,UAAU,KAAK,QAAQ;YACzB;QACF;IACF;IAEA,MAAM,aAAkC;QACtC,UAAU;QACV,MAAM;QACN,KAAK;QACL,QAAQ;QACR,QAAQ,WAAW,OAAO;QAC1B,iBAAiB,YAAY,kBAAkB,CAAC;QAChD,aAAa,CAAC,UAAU,EAAE,YAAY,cAAc,CAAC,YAAY;QACjE,UAAU;QACV,GAAG,KAAK;IACV;IAEA,qBACE,6LAAC;QACC,WAAW;QACX,OAAO;QACP,WAAW;QACX,aAAa;QACb,OAAO;QACP,gBAAgB;QAChB,OAAM;kBAGN,cAAA,6LAAC;YAAI,OAAO;gBAAE,QAAQ;gBAAQ,SAAS;gBAAQ,eAAe;YAAS;;8BAErE,6LAAC,iLAAA,CAAA,OAAI;oBACH,MAAK;oBACL,cAAc;oBACd,UAAU,YAAY,EAAE,GAAG;oBAC3B,cAAc;oBACd,SAAS;oBACT,OAAO,sBAAsB;oBAC7B,OAAO;wBACL,MAAM;wBACN,QAAQ;wBACR,iBAAiB;oBACnB;;;;;;gBAID,CAAC,2BACA,6LAAC;oBACC,OAAO;wBACL,SAAS;wBACT,WAAW,CAAC,UAAU,EAAE,YAAY,cAAc,CAAC,YAAY;wBAC/D,WAAW;oBACb;;sCAEA,6LAAC;4BACC,OAAO;gCACL,UAAU;gCACV,OAAO,YAAY,YAAY,CAAC;gCAChC,cAAc;4BAChB;sCACD;;;;;;sCAGD,6LAAC;4BACC,OAAO;gCACL,UAAU;gCACV,OAAO,YAAY,YAAY,CAAC;4BAClC;sCACD;;;;;;;;;;;;;;;;;;;;;;;AAQb;GA5TgB;;QAOM,wHAAA,CAAA,iBAAc;QACnB,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;;;KATd;AA2UT,SAAS,gBAAgB,EAC9B,IAAI,EACJ,KAAK,EACL,SAAS,KAAK,EACd,YAAY,KAAK,EACjB,OAAO,EACP,SAAS,EACT,KAAK,EACgB;;IACrB,MAAM,cAAc,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;IAEjC,MAAM,YAAiC;QACrC,SAAS;QACT,YAAY;QACZ,KAAK,YAAY,MAAM;QACvB,SAAS;QACT,QAAQ;QACR,cAAc;QACd,QAAQ;QACR,YAAY;QACZ,iBAAiB,SAAS,YAAY,QAAQ,CAAC,aAAa,OAAO;QACnE,OAAO,SAAS,YAAY,QAAQ,CAAC,aAAa,YAAY,YAAY,CAAC;QAC3E,GAAG,KAAK;IACV;IAEA,qBACE,6LAAC;QACC,WAAW;QACX,OAAO;QACP,SAAS;;0BAET,6LAAC;gBAAI,OAAO;oBAAE,UAAU;oBAAQ,UAAU;gBAAO;0BAC9C;;;;;;YAEF,CAAC,2BACA,6LAAC;gBAAI,OAAO;oBAAE,UAAU;oBAAQ,YAAY,SAAS,SAAS;gBAAS;0BACpE;;;;;;;;;;;;AAKX;IAzCgB;;QASM,wHAAA,CAAA,iBAAc;;;MATpB"}}, {"offset": {"line": 9484, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 9490, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/layout/app-footer.tsx"], "sourcesContent": ["/**\n * App Footer Component\n * Footer for the APISportsGame CMS\n */\n\n'use client';\n\nimport React from 'react';\nimport { Layout, Space, Typography, Divider } from 'antd';\nimport { \n  GithubOutlined, \n  TwitterOutlined, \n  LinkedinOutlined,\n  HeartFilled,\n  ApiOutlined,\n  GlobalOutlined,\n} from '@ant-design/icons';\nimport { useThemeStyles } from '@/theme';\n\nconst { Footer } = Layout;\nconst { Text, Link } = Typography;\n\n/**\n * App footer props\n */\nexport interface AppFooterProps {\n  className?: string;\n  style?: React.CSSProperties;\n  compact?: boolean;\n}\n\n/**\n * App Footer component\n */\nexport function AppFooter({ className, style, compact = false }: AppFooterProps) {\n  const themeStyles = useThemeStyles();\n\n  const footerStyle: React.CSSProperties = {\n    backgroundColor: themeStyles.getBackgroundColor('container'),\n    borderTop: `1px solid ${themeStyles.getBorderColor('primary')}`,\n    padding: compact ? '12px 24px' : '24px',\n    textAlign: 'center',\n    ...style,\n  };\n\n  const currentYear = new Date().getFullYear();\n\n  if (compact) {\n    return (\n      <Footer className={className} style={footerStyle}>\n        <Text\n          style={{\n            fontSize: '12px',\n            color: themeStyles.getTextColor('tertiary'),\n          }}\n        >\n          © {currentYear} APISportsGame CMS. Built with{' '}\n          <HeartFilled style={{ color: themeStyles.getColor('error') }} /> by Augment Code\n        </Text>\n      </Footer>\n    );\n  }\n\n  return (\n    <Footer className={className} style={footerStyle}>\n      <div style={{ maxWidth: '1200px', margin: '0 auto' }}>\n        {/* Main footer content */}\n        <div\n          style={{\n            display: 'grid',\n            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n            gap: '32px',\n            marginBottom: '24px',\n            textAlign: 'left',\n          }}\n        >\n          {/* About section */}\n          <div>\n            <div\n              style={{\n                display: 'flex',\n                alignItems: 'center',\n                gap: '8px',\n                marginBottom: '12px',\n              }}\n            >\n              <div\n                style={{\n                  width: '24px',\n                  height: '24px',\n                  backgroundColor: themeStyles.getColor('primary'),\n                  borderRadius: '4px',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  color: 'white',\n                  fontSize: '14px',\n                  fontWeight: 'bold',\n                }}\n              >\n                ⚽\n              </div>\n              <Text\n                style={{\n                  fontSize: '16px',\n                  fontWeight: 'bold',\n                  color: themeStyles.getTextColor('primary'),\n                }}\n              >\n                APISportsGame\n              </Text>\n            </div>\n            <Text\n              style={{\n                fontSize: '14px',\n                color: themeStyles.getTextColor('secondary'),\n                lineHeight: 1.6,\n              }}\n            >\n              A comprehensive CMS for managing football data, broadcast links, and user systems.\n              Built with modern technologies for optimal performance.\n            </Text>\n          </div>\n\n          {/* Quick links */}\n          <div>\n            <Text\n              style={{\n                fontSize: '14px',\n                fontWeight: 'bold',\n                color: themeStyles.getTextColor('primary'),\n                marginBottom: '12px',\n                display: 'block',\n              }}\n            >\n              Quick Links\n            </Text>\n            <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>\n              <Link\n                href=\"/\"\n                style={{\n                  fontSize: '13px',\n                  color: themeStyles.getTextColor('secondary'),\n                }}\n              >\n                Dashboard\n              </Link>\n              <Link\n                href=\"/football/fixtures\"\n                style={{\n                  fontSize: '13px',\n                  color: themeStyles.getTextColor('secondary'),\n                }}\n              >\n                Fixtures\n              </Link>\n              <Link\n                href=\"/broadcast/links\"\n                style={{\n                  fontSize: '13px',\n                  color: themeStyles.getTextColor('secondary'),\n                }}\n              >\n                Broadcast Links\n              </Link>\n              <Link\n                href=\"/system/health\"\n                style={{\n                  fontSize: '13px',\n                  color: themeStyles.getTextColor('secondary'),\n                }}\n              >\n                System Health\n              </Link>\n            </div>\n          </div>\n\n          {/* Resources */}\n          <div>\n            <Text\n              style={{\n                fontSize: '14px',\n                fontWeight: 'bold',\n                color: themeStyles.getTextColor('primary'),\n                marginBottom: '12px',\n                display: 'block',\n              }}\n            >\n              Resources\n            </Text>\n            <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>\n              <Link\n                href=\"/system/api-docs\"\n                style={{\n                  fontSize: '13px',\n                  color: themeStyles.getTextColor('secondary'),\n                }}\n              >\n                <ApiOutlined style={{ marginRight: '4px' }} />\n                API Documentation\n              </Link>\n              <Link\n                href=\"/components-demo\"\n                style={{\n                  fontSize: '13px',\n                  color: themeStyles.getTextColor('secondary'),\n                }}\n              >\n                Component Library\n              </Link>\n              <Link\n                href=\"/theme-demo\"\n                style={{\n                  fontSize: '13px',\n                  color: themeStyles.getTextColor('secondary'),\n                }}\n              >\n                Theme System\n              </Link>\n              <Link\n                href=\"https://github.com/apisportsgame\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                style={{\n                  fontSize: '13px',\n                  color: themeStyles.getTextColor('secondary'),\n                }}\n              >\n                <GithubOutlined style={{ marginRight: '4px' }} />\n                GitHub Repository\n              </Link>\n            </div>\n          </div>\n\n          {/* Contact */}\n          <div>\n            <Text\n              style={{\n                fontSize: '14px',\n                fontWeight: 'bold',\n                color: themeStyles.getTextColor('primary'),\n                marginBottom: '12px',\n                display: 'block',\n              }}\n            >\n              Connect\n            </Text>\n            <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>\n              <Link\n                href=\"https://github.com/apisportsgame\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                style={{\n                  fontSize: '13px',\n                  color: themeStyles.getTextColor('secondary'),\n                }}\n              >\n                <GithubOutlined style={{ marginRight: '4px' }} />\n                GitHub\n              </Link>\n              <Link\n                href=\"https://twitter.com/apisportsgame\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                style={{\n                  fontSize: '13px',\n                  color: themeStyles.getTextColor('secondary'),\n                }}\n              >\n                <TwitterOutlined style={{ marginRight: '4px' }} />\n                Twitter\n              </Link>\n              <Link\n                href=\"https://linkedin.com/company/apisportsgame\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                style={{\n                  fontSize: '13px',\n                  color: themeStyles.getTextColor('secondary'),\n                }}\n              >\n                <LinkedinOutlined style={{ marginRight: '4px' }} />\n                LinkedIn\n              </Link>\n              <Link\n                href=\"https://apisportsgame.com\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                style={{\n                  fontSize: '13px',\n                  color: themeStyles.getTextColor('secondary'),\n                }}\n              >\n                <GlobalOutlined style={{ marginRight: '4px' }} />\n                Website\n              </Link>\n            </div>\n          </div>\n        </div>\n\n        <Divider style={{ margin: '24px 0 16px 0' }} />\n\n        {/* Bottom footer */}\n        <div\n          style={{\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center',\n            flexWrap: 'wrap',\n            gap: '16px',\n          }}\n        >\n          <Text\n            style={{\n              fontSize: '13px',\n              color: themeStyles.getTextColor('tertiary'),\n            }}\n          >\n            © {currentYear} APISportsGame CMS. All rights reserved. Built with{' '}\n            <HeartFilled style={{ color: themeStyles.getColor('error') }} /> by Augment Code\n          </Text>\n\n          <Space size=\"middle\">\n            <Link\n              href=\"/privacy\"\n              style={{\n                fontSize: '13px',\n                color: themeStyles.getTextColor('tertiary'),\n              }}\n            >\n              Privacy Policy\n            </Link>\n            <Link\n              href=\"/terms\"\n              style={{\n                fontSize: '13px',\n                color: themeStyles.getTextColor('tertiary'),\n              }}\n            >\n              Terms of Service\n            </Link>\n            <Text\n              style={{\n                fontSize: '13px',\n                color: themeStyles.getTextColor('tertiary'),\n              }}\n            >\n              v1.0.0\n            </Text>\n          </Space>\n        </div>\n      </div>\n    </Footer>\n  );\n}\n\n/**\n * Simple footer for minimal layouts\n */\nexport interface SimpleFooterProps {\n  className?: string;\n  style?: React.CSSProperties;\n}\n\nexport function SimpleFooter({ className, style }: SimpleFooterProps) {\n  const themeStyles = useThemeStyles();\n  const currentYear = new Date().getFullYear();\n\n  return (\n    <div\n      className={className}\n      style={{\n        padding: '16px 24px',\n        textAlign: 'center',\n        backgroundColor: themeStyles.getBackgroundColor('container'),\n        borderTop: `1px solid ${themeStyles.getBorderColor('primary')}`,\n        ...style,\n      }}\n    >\n      <Text\n        style={{\n          fontSize: '12px',\n          color: themeStyles.getTextColor('tertiary'),\n        }}\n      >\n        © {currentYear} APISportsGame CMS. Built with{' '}\n        <HeartFilled style={{ color: themeStyles.getColor('error') }} /> by Augment Code\n      </Text>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AAcD;AAAA;AATA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AADA;AACA;AADA;;;AAHA;;;;AAcA,MAAM,EAAE,MAAM,EAAE,GAAG,qLAAA,CAAA,SAAM;AACzB,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,6LAAA,CAAA,aAAU;AAc1B,SAAS,UAAU,EAAE,SAAS,EAAE,KAAK,EAAE,UAAU,KAAK,EAAkB;;IAC7E,MAAM,cAAc,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;IAEjC,MAAM,cAAmC;QACvC,iBAAiB,YAAY,kBAAkB,CAAC;QAChD,WAAW,CAAC,UAAU,EAAE,YAAY,cAAc,CAAC,YAAY;QAC/D,SAAS,UAAU,cAAc;QACjC,WAAW;QACX,GAAG,KAAK;IACV;IAEA,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,IAAI,SAAS;QACX,qBACE,6LAAC;YAAO,WAAW;YAAW,OAAO;sBACnC,cAAA,6LAAC;gBACC,OAAO;oBACL,UAAU;oBACV,OAAO,YAAY,YAAY,CAAC;gBAClC;;oBACD;oBACI;oBAAY;oBAA+B;kCAC9C,6LAAC,mNAAA,CAAA,cAAW;wBAAC,OAAO;4BAAE,OAAO,YAAY,QAAQ,CAAC;wBAAS;;;;;;oBAAK;;;;;;;;;;;;IAIxE;IAEA,qBACE,6LAAC;QAAO,WAAW;QAAW,OAAO;kBACnC,cAAA,6LAAC;YAAI,OAAO;gBAAE,UAAU;gBAAU,QAAQ;YAAS;;8BAEjD,6LAAC;oBACC,OAAO;wBACL,SAAS;wBACT,qBAAqB;wBACrB,KAAK;wBACL,cAAc;wBACd,WAAW;oBACb;;sCAGA,6LAAC;;8CACC,6LAAC;oCACC,OAAO;wCACL,SAAS;wCACT,YAAY;wCACZ,KAAK;wCACL,cAAc;oCAChB;;sDAEA,6LAAC;4CACC,OAAO;gDACL,OAAO;gDACP,QAAQ;gDACR,iBAAiB,YAAY,QAAQ,CAAC;gDACtC,cAAc;gDACd,SAAS;gDACT,YAAY;gDACZ,gBAAgB;gDAChB,OAAO;gDACP,UAAU;gDACV,YAAY;4CACd;sDACD;;;;;;sDAGD,6LAAC;4CACC,OAAO;gDACL,UAAU;gDACV,YAAY;gDACZ,OAAO,YAAY,YAAY,CAAC;4CAClC;sDACD;;;;;;;;;;;;8CAIH,6LAAC;oCACC,OAAO;wCACL,UAAU;wCACV,OAAO,YAAY,YAAY,CAAC;wCAChC,YAAY;oCACd;8CACD;;;;;;;;;;;;sCAOH,6LAAC;;8CACC,6LAAC;oCACC,OAAO;wCACL,UAAU;wCACV,YAAY;wCACZ,OAAO,YAAY,YAAY,CAAC;wCAChC,cAAc;wCACd,SAAS;oCACX;8CACD;;;;;;8CAGD,6LAAC;oCAAI,OAAO;wCAAE,SAAS;wCAAQ,eAAe;wCAAU,KAAK;oCAAM;;sDACjE,6LAAC;4CACC,MAAK;4CACL,OAAO;gDACL,UAAU;gDACV,OAAO,YAAY,YAAY,CAAC;4CAClC;sDACD;;;;;;sDAGD,6LAAC;4CACC,MAAK;4CACL,OAAO;gDACL,UAAU;gDACV,OAAO,YAAY,YAAY,CAAC;4CAClC;sDACD;;;;;;sDAGD,6LAAC;4CACC,MAAK;4CACL,OAAO;gDACL,UAAU;gDACV,OAAO,YAAY,YAAY,CAAC;4CAClC;sDACD;;;;;;sDAGD,6LAAC;4CACC,MAAK;4CACL,OAAO;gDACL,UAAU;gDACV,OAAO,YAAY,YAAY,CAAC;4CAClC;sDACD;;;;;;;;;;;;;;;;;;sCAOL,6LAAC;;8CACC,6LAAC;oCACC,OAAO;wCACL,UAAU;wCACV,YAAY;wCACZ,OAAO,YAAY,YAAY,CAAC;wCAChC,cAAc;wCACd,SAAS;oCACX;8CACD;;;;;;8CAGD,6LAAC;oCAAI,OAAO;wCAAE,SAAS;wCAAQ,eAAe;wCAAU,KAAK;oCAAM;;sDACjE,6LAAC;4CACC,MAAK;4CACL,OAAO;gDACL,UAAU;gDACV,OAAO,YAAY,YAAY,CAAC;4CAClC;;8DAEA,6LAAC,mNAAA,CAAA,cAAW;oDAAC,OAAO;wDAAE,aAAa;oDAAM;;;;;;gDAAK;;;;;;;sDAGhD,6LAAC;4CACC,MAAK;4CACL,OAAO;gDACL,UAAU;gDACV,OAAO,YAAY,YAAY,CAAC;4CAClC;sDACD;;;;;;sDAGD,6LAAC;4CACC,MAAK;4CACL,OAAO;gDACL,UAAU;gDACV,OAAO,YAAY,YAAY,CAAC;4CAClC;sDACD;;;;;;sDAGD,6LAAC;4CACC,MAAK;4CACL,QAAO;4CACP,KAAI;4CACJ,OAAO;gDACL,UAAU;gDACV,OAAO,YAAY,YAAY,CAAC;4CAClC;;8DAEA,6LAAC,yNAAA,CAAA,iBAAc;oDAAC,OAAO;wDAAE,aAAa;oDAAM;;;;;;gDAAK;;;;;;;;;;;;;;;;;;;sCAOvD,6LAAC;;8CACC,6LAAC;oCACC,OAAO;wCACL,UAAU;wCACV,YAAY;wCACZ,OAAO,YAAY,YAAY,CAAC;wCAChC,cAAc;wCACd,SAAS;oCACX;8CACD;;;;;;8CAGD,6LAAC;oCAAI,OAAO;wCAAE,SAAS;wCAAQ,eAAe;wCAAU,KAAK;oCAAM;;sDACjE,6LAAC;4CACC,MAAK;4CACL,QAAO;4CACP,KAAI;4CACJ,OAAO;gDACL,UAAU;gDACV,OAAO,YAAY,YAAY,CAAC;4CAClC;;8DAEA,6LAAC,yNAAA,CAAA,iBAAc;oDAAC,OAAO;wDAAE,aAAa;oDAAM;;;;;;gDAAK;;;;;;;sDAGnD,6LAAC;4CACC,MAAK;4CACL,QAAO;4CACP,KAAI;4CACJ,OAAO;gDACL,UAAU;gDACV,OAAO,YAAY,YAAY,CAAC;4CAClC;;8DAEA,6LAAC,2NAAA,CAAA,kBAAe;oDAAC,OAAO;wDAAE,aAAa;oDAAM;;;;;;gDAAK;;;;;;;sDAGpD,6LAAC;4CACC,MAAK;4CACL,QAAO;4CACP,KAAI;4CACJ,OAAO;gDACL,UAAU;gDACV,OAAO,YAAY,YAAY,CAAC;4CAClC;;8DAEA,6LAAC,6NAAA,CAAA,mBAAgB;oDAAC,OAAO;wDAAE,aAAa;oDAAM;;;;;;gDAAK;;;;;;;sDAGrD,6LAAC;4CACC,MAAK;4CACL,QAAO;4CACP,KAAI;4CACJ,OAAO;gDACL,UAAU;gDACV,OAAO,YAAY,YAAY,CAAC;4CAClC;;8DAEA,6LAAC,yNAAA,CAAA,iBAAc;oDAAC,OAAO;wDAAE,aAAa;oDAAM;;;;;;gDAAK;;;;;;;;;;;;;;;;;;;;;;;;;8BAOzD,6LAAC,uLAAA,CAAA,UAAO;oBAAC,OAAO;wBAAE,QAAQ;oBAAgB;;;;;;8BAG1C,6LAAC;oBACC,OAAO;wBACL,SAAS;wBACT,gBAAgB;wBAChB,YAAY;wBACZ,UAAU;wBACV,KAAK;oBACP;;sCAEA,6LAAC;4BACC,OAAO;gCACL,UAAU;gCACV,OAAO,YAAY,YAAY,CAAC;4BAClC;;gCACD;gCACI;gCAAY;gCAAoD;8CACnE,6LAAC,mNAAA,CAAA,cAAW;oCAAC,OAAO;wCAAE,OAAO,YAAY,QAAQ,CAAC;oCAAS;;;;;;gCAAK;;;;;;;sCAGlE,6LAAC,mMAAA,CAAA,QAAK;4BAAC,MAAK;;8CACV,6LAAC;oCACC,MAAK;oCACL,OAAO;wCACL,UAAU;wCACV,OAAO,YAAY,YAAY,CAAC;oCAClC;8CACD;;;;;;8CAGD,6LAAC;oCACC,MAAK;oCACL,OAAO;wCACL,UAAU;wCACV,OAAO,YAAY,YAAY,CAAC;oCAClC;8CACD;;;;;;8CAGD,6LAAC;oCACC,OAAO;wCACL,UAAU;wCACV,OAAO,YAAY,YAAY,CAAC;oCAClC;8CACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;GAhUgB;;QACM,wHAAA,CAAA,iBAAc;;;KADpB;AA0UT,SAAS,aAAa,EAAE,SAAS,EAAE,KAAK,EAAqB;;IAClE,MAAM,cAAc,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,qBACE,6LAAC;QACC,WAAW;QACX,OAAO;YACL,SAAS;YACT,WAAW;YACX,iBAAiB,YAAY,kBAAkB,CAAC;YAChD,WAAW,CAAC,UAAU,EAAE,YAAY,cAAc,CAAC,YAAY;YAC/D,GAAG,KAAK;QACV;kBAEA,cAAA,6LAAC;YACC,OAAO;gBACL,UAAU;gBACV,OAAO,YAAY,YAAY,CAAC;YAClC;;gBACD;gBACI;gBAAY;gBAA+B;8BAC9C,6LAAC,mNAAA,CAAA,cAAW;oBAAC,OAAO;wBAAE,OAAO,YAAY,QAAQ,CAAC;oBAAS;;;;;;gBAAK;;;;;;;;;;;;AAIxE;IA1BgB;;QACM,wHAAA,CAAA,iBAAc;;;MADpB"}}, {"offset": {"line": 10148, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 10154, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/layout/app-layout.tsx"], "sourcesContent": ["/**\n * Main App Layout Component\n * Primary layout structure for the APISportsGame CMS\n */\n\n'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { Layout } from 'antd';\nimport { useThemeStyles } from '@/theme';\nimport { useAppProvider } from '@/stores';\nimport { AppHeader } from './app-header';\nimport { AppSidebar } from './app-sidebar';\nimport { AppFooter } from './app-footer';\n\nconst { Content } = Layout;\n\n/**\n * App layout props\n */\nexport interface AppLayoutProps {\n  children: React.ReactNode;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\n/**\n * Main App Layout component\n */\nexport function AppLayout({ children, className, style }: AppLayoutProps) {\n  const themeStyles = useThemeStyles();\n  const app = useAppProvider();\n\n  // Layout state\n  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);\n  const [isMobile, setIsMobile] = useState(false);\n\n  // Handle responsive behavior\n  useEffect(() => {\n    const handleResize = () => {\n      const mobile = window.innerWidth < 768;\n      setIsMobile(mobile);\n\n      // Auto-collapse sidebar on mobile\n      if (mobile && !sidebarCollapsed) {\n        setSidebarCollapsed(true);\n      }\n    };\n\n    // Initial check\n    handleResize();\n\n    // Add event listener\n    window.addEventListener('resize', handleResize);\n\n    // Cleanup\n    return () => window.removeEventListener('resize', handleResize);\n  }, [sidebarCollapsed]);\n\n  // Handle sidebar toggle\n  const handleSidebarToggle = () => {\n    setSidebarCollapsed(!sidebarCollapsed);\n  };\n\n  // Handle mobile sidebar overlay\n  const handleMobileOverlayClick = () => {\n    if (isMobile && !sidebarCollapsed) {\n      setSidebarCollapsed(true);\n    }\n  };\n\n  const layoutStyle: React.CSSProperties = {\n    minHeight: '100vh',\n    backgroundColor: themeStyles.getBackgroundColor('layout'),\n    ...style,\n  };\n\n  const contentStyle: React.CSSProperties = {\n    marginLeft: isMobile ? 0 : (sidebarCollapsed ? '80px' : '250px'),\n    transition: 'margin-left 0.2s ease',\n    minHeight: 'calc(100vh - 64px)', // Header height\n    backgroundColor: themeStyles.getBackgroundColor('layout'),\n  };\n\n  return (\n    <Layout className={className} style={layoutStyle}>\n      {/* Header */}\n      <AppHeader\n        sidebarCollapsed={sidebarCollapsed}\n        onSidebarToggle={handleSidebarToggle}\n        isMobile={isMobile}\n      />\n\n      {/* Sidebar */}\n      <AppSidebar\n        collapsed={sidebarCollapsed}\n        isMobile={isMobile}\n        onCollapse={setSidebarCollapsed}\n      />\n\n      {/* Mobile overlay */}\n      {isMobile && !sidebarCollapsed && (\n        <div\n          style={{\n            position: 'fixed',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            backgroundColor: 'rgba(0, 0, 0, 0.5)',\n            zIndex: 999,\n          }}\n          onClick={handleMobileOverlayClick}\n        />\n      )}\n\n      {/* Main content */}\n      <Layout style={contentStyle}>\n        <Content\n          style={{\n            padding: '16px 24px',\n            backgroundColor: themeStyles.getBackgroundColor('layout'),\n            overflow: 'auto',\n          }}\n        >\n          {children}\n        </Content>\n\n        {/* Footer */}\n        <AppFooter />\n      </Layout>\n    </Layout>\n  );\n}\n\n/**\n * Layout provider for layout state management\n */\nexport interface LayoutContextType {\n  sidebarCollapsed: boolean;\n  setSidebarCollapsed: (collapsed: boolean) => void;\n  isMobile: boolean;\n  toggleSidebar: () => void;\n}\n\nconst LayoutContext = React.createContext<LayoutContextType | undefined>(undefined);\n\nexport function LayoutProvider({ children }: { children: React.ReactNode }) {\n  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);\n  const [isMobile, setIsMobile] = useState(false);\n\n  useEffect(() => {\n    const handleResize = () => {\n      const mobile = window.innerWidth < 768;\n      setIsMobile(mobile);\n    };\n\n    handleResize();\n    window.addEventListener('resize', handleResize);\n    return () => window.removeEventListener('resize', handleResize);\n  }, []);\n\n  const toggleSidebar = () => {\n    setSidebarCollapsed(!sidebarCollapsed);\n  };\n\n  const value: LayoutContextType = {\n    sidebarCollapsed,\n    setSidebarCollapsed,\n    isMobile,\n    toggleSidebar,\n  };\n\n  return (\n    <LayoutContext.Provider value={value}>\n      {children}\n    </LayoutContext.Provider>\n  );\n}\n\n/**\n * Hook to use layout context\n */\nexport function useLayout() {\n  const context = React.useContext(LayoutContext);\n  if (context === undefined) {\n    throw new Error('useLayout must be used within a LayoutProvider');\n  }\n  return context;\n}\n\n/**\n * Simple layout for pages that don't need sidebar\n */\nexport interface SimpleLayoutProps {\n  children: React.ReactNode;\n  showHeader?: boolean;\n  showFooter?: boolean;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\nexport function SimpleLayout({\n  children,\n  showHeader = true,\n  showFooter = true,\n  className,\n  style,\n}: SimpleLayoutProps) {\n  const themeStyles = useThemeStyles();\n\n  const layoutStyle: React.CSSProperties = {\n    minHeight: '100vh',\n    backgroundColor: themeStyles.getBackgroundColor('layout'),\n    display: 'flex',\n    flexDirection: 'column',\n    ...style,\n  };\n\n  return (\n    <Layout className={className} style={layoutStyle}>\n      {showHeader && (\n        <AppHeader\n          sidebarCollapsed={true}\n          onSidebarToggle={() => { }}\n          isMobile={false}\n          showSidebarToggle={false}\n        />\n      )}\n\n      <Content\n        style={{\n          flex: 1,\n          padding: '24px',\n          backgroundColor: themeStyles.getBackgroundColor('layout'),\n        }}\n      >\n        {children}\n      </Content>\n\n      {showFooter && <AppFooter />}\n    </Layout>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;AAID;AAEA;AACA;AACA;AACA;AACA;AAJA;AADA;AAEA;;;AALA;;;;;;;;AAUA,MAAM,EAAE,OAAO,EAAE,GAAG,qLAAA,CAAA,SAAM;AAcnB,SAAS,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAE,KAAK,EAAkB;;IACtE,MAAM,cAAc,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,MAAM,CAAA,GAAA,qIAAA,CAAA,iBAAc,AAAD;IAEzB,eAAe;IACf,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,6BAA6B;IAC7B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,MAAM;oDAAe;oBACnB,MAAM,SAAS,OAAO,UAAU,GAAG;oBACnC,YAAY;oBAEZ,kCAAkC;oBAClC,IAAI,UAAU,CAAC,kBAAkB;wBAC/B,oBAAoB;oBACtB;gBACF;;YAEA,gBAAgB;YAChB;YAEA,qBAAqB;YACrB,OAAO,gBAAgB,CAAC,UAAU;YAElC,UAAU;YACV;uCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;8BAAG;QAAC;KAAiB;IAErB,wBAAwB;IACxB,MAAM,sBAAsB;QAC1B,oBAAoB,CAAC;IACvB;IAEA,gCAAgC;IAChC,MAAM,2BAA2B;QAC/B,IAAI,YAAY,CAAC,kBAAkB;YACjC,oBAAoB;QACtB;IACF;IAEA,MAAM,cAAmC;QACvC,WAAW;QACX,iBAAiB,YAAY,kBAAkB,CAAC;QAChD,GAAG,KAAK;IACV;IAEA,MAAM,eAAoC;QACxC,YAAY,WAAW,IAAK,mBAAmB,SAAS;QACxD,YAAY;QACZ,WAAW;QACX,iBAAiB,YAAY,kBAAkB,CAAC;IAClD;IAEA,qBACE,6LAAC,qLAAA,CAAA,SAAM;QAAC,WAAW;QAAW,OAAO;;0BAEnC,6LAAC,gJAAA,CAAA,YAAS;gBACR,kBAAkB;gBAClB,iBAAiB;gBACjB,UAAU;;;;;;0BAIZ,6LAAC,iJAAA,CAAA,aAAU;gBACT,WAAW;gBACX,UAAU;gBACV,YAAY;;;;;;YAIb,YAAY,CAAC,kCACZ,6LAAC;gBACC,OAAO;oBACL,UAAU;oBACV,KAAK;oBACL,MAAM;oBACN,OAAO;oBACP,QAAQ;oBACR,iBAAiB;oBACjB,QAAQ;gBACV;gBACA,SAAS;;;;;;0BAKb,6LAAC,qLAAA,CAAA,SAAM;gBAAC,OAAO;;kCACb,6LAAC;wBACC,OAAO;4BACL,SAAS;4BACT,iBAAiB,YAAY,kBAAkB,CAAC;4BAChD,UAAU;wBACZ;kCAEC;;;;;;kCAIH,6LAAC,gJAAA,CAAA,YAAS;;;;;;;;;;;;;;;;;AAIlB;GAxGgB;;QACM,wHAAA,CAAA,iBAAc;QACtB,qIAAA,CAAA,iBAAc;;;KAFZ;AAoHhB,MAAM,8BAAgB,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAgC;AAElE,SAAS,eAAe,EAAE,QAAQ,EAAiC;;IACxE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM;yDAAe;oBACnB,MAAM,SAAS,OAAO,UAAU,GAAG;oBACnC,YAAY;gBACd;;YAEA;YACA,OAAO,gBAAgB,CAAC,UAAU;YAClC;4CAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;mCAAG,EAAE;IAEL,MAAM,gBAAgB;QACpB,oBAAoB,CAAC;IACvB;IAEA,MAAM,QAA2B;QAC/B;QACA;QACA;QACA;IACF;IAEA,qBACE,6LAAC,cAAc,QAAQ;QAAC,OAAO;kBAC5B;;;;;;AAGP;IA/BgB;MAAA;AAoCT,SAAS;;IACd,MAAM,UAAU,6JAAA,CAAA,UAAK,CAAC,UAAU,CAAC;IACjC,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB;AAmBT,SAAS,aAAa,EAC3B,QAAQ,EACR,aAAa,IAAI,EACjB,aAAa,IAAI,EACjB,SAAS,EACT,KAAK,EACa;;IAClB,MAAM,cAAc,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;IAEjC,MAAM,cAAmC;QACvC,WAAW;QACX,iBAAiB,YAAY,kBAAkB,CAAC;QAChD,SAAS;QACT,eAAe;QACf,GAAG,KAAK;IACV;IAEA,qBACE,6LAAC,qLAAA,CAAA,SAAM;QAAC,WAAW;QAAW,OAAO;;YAClC,4BACC,6LAAC,gJAAA,CAAA,YAAS;gBACR,kBAAkB;gBAClB,iBAAiB,KAAQ;gBACzB,UAAU;gBACV,mBAAmB;;;;;;0BAIvB,6LAAC;gBACC,OAAO;oBACL,MAAM;oBACN,SAAS;oBACT,iBAAiB,YAAY,kBAAkB,CAAC;gBAClD;0BAEC;;;;;;YAGF,4BAAc,6LAAC,gJAAA,CAAA,YAAS;;;;;;;;;;;AAG/B;IAzCgB;;QAOM,wHAAA,CAAA,iBAAc;;;MAPpB"}}, {"offset": {"line": 10425, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 10431, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/layout/auth-layout.tsx"], "sourcesContent": ["/**\n * Authentication Layout Component\n * Specialized layout for authentication pages\n */\n\n'use client';\n\nimport React from 'react';\nimport { Layout, Card, Typography, Space, Divider } from 'antd';\nimport { useThemeStyles } from '@/theme';\nimport { SimpleFooter } from './app-footer';\n\nconst { Content } = Layout;\nconst { Title, Text, Link } = Typography;\n\n/**\n * Auth layout props\n */\nexport interface AuthLayoutProps {\n  children: React.ReactNode;\n  title?: string;\n  subtitle?: string;\n  showFooter?: boolean;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\n/**\n * Authentication Layout component\n */\nexport function AuthLayout({\n  children,\n  title = 'APISportsGame CMS',\n  subtitle = 'System Administration Portal',\n  showFooter = true,\n  className,\n  style,\n}: AuthLayoutProps) {\n  const themeStyles = useThemeStyles();\n\n  const layoutStyle: React.CSSProperties = {\n    minHeight: '100vh',\n    backgroundColor: themeStyles.getBackgroundColor('layout'),\n    display: 'flex',\n    flexDirection: 'column',\n    ...style,\n  };\n\n  const contentStyle: React.CSSProperties = {\n    flex: 1,\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    padding: '24px',\n    backgroundImage: `linear-gradient(135deg, ${themeStyles.getColor('primary')}10 0%, ${themeStyles.getColor('primary')}05 100%)`,\n  };\n\n  return (\n    <Layout className={className} style={layoutStyle}>\n      <Content style={contentStyle}>\n        <div\n          style={{\n            width: '100%',\n            maxWidth: '400px',\n            margin: '0 auto',\n          }}\n        >\n          {/* Header Section */}\n          <div\n            style={{\n              textAlign: 'center',\n              marginBottom: '32px',\n            }}\n          >\n            {/* Logo */}\n            <div\n              style={{\n                display: 'flex',\n                justifyContent: 'center',\n                marginBottom: '16px',\n              }}\n            >\n              <div\n                style={{\n                  width: '64px',\n                  height: '64px',\n                  backgroundColor: themeStyles.getColor('primary'),\n                  borderRadius: '12px',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  color: 'white',\n                  fontSize: '32px',\n                  fontWeight: 'bold',\n                  boxShadow: `0 8px 24px ${themeStyles.getColor('primary')}30`,\n                }}\n              >\n                ⚽\n              </div>\n            </div>\n\n            {/* Title */}\n            <Title\n              level={2}\n              style={{\n                color: themeStyles.getTextColor('primary'),\n                marginBottom: '8px',\n                fontSize: '28px',\n                fontWeight: 'bold',\n              }}\n            >\n              {title}\n            </Title>\n\n            {/* Subtitle */}\n            <Text\n              style={{\n                color: themeStyles.getTextColor('secondary'),\n                fontSize: '16px',\n              }}\n            >\n              {subtitle}\n            </Text>\n          </div>\n\n          {/* Auth Card */}\n          <Card\n            style={{\n              backgroundColor: themeStyles.getBackgroundColor('container'),\n              border: `1px solid ${themeStyles.getBorderColor('primary')}`,\n              borderRadius: '12px',\n              boxShadow: `0 8px 32px ${themeStyles.getColor('primary')}08`,\n            }}\n            bodyStyle={{\n              padding: '32px',\n            }}\n          >\n            {children}\n          </Card>\n\n          {/* Additional Links */}\n          <div\n            style={{\n              textAlign: 'center',\n              marginTop: '24px',\n            }}\n          >\n            <Space split={<Divider type=\"vertical\" />}>\n              <Link\n                href=\"/help\"\n                style={{\n                  color: themeStyles.getTextColor('secondary'),\n                  fontSize: '14px',\n                }}\n              >\n                Help & Support\n              </Link>\n              <Link\n                href=\"/privacy\"\n                style={{\n                  color: themeStyles.getTextColor('secondary'),\n                  fontSize: '14px',\n                }}\n              >\n                Privacy Policy\n              </Link>\n              <Link\n                href=\"/terms\"\n                style={{\n                  color: themeStyles.getTextColor('secondary'),\n                  fontSize: '14px',\n                }}\n              >\n                Terms of Service\n              </Link>\n            </Space>\n          </div>\n        </div>\n      </Content>\n\n      {/* Footer */}\n      {showFooter && (\n        <SimpleFooter\n          style={{\n            backgroundColor: themeStyles.getBackgroundColor('container'),\n            borderTop: `1px solid ${themeStyles.getBorderColor('primary')}`,\n          }}\n        />\n      )}\n    </Layout>\n  );\n}\n\n/**\n * Auth card wrapper for consistent styling\n */\nexport interface AuthCardProps {\n  children: React.ReactNode;\n  title?: string;\n  description?: string;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\nexport function AuthCard({\n  children,\n  title,\n  description,\n  className,\n  style,\n}: AuthCardProps) {\n  const themeStyles = useThemeStyles();\n\n  return (\n    <div className={className} style={style}>\n      {(title || description) && (\n        <div style={{ marginBottom: '24px', textAlign: 'center' }}>\n          {title && (\n            <Title\n              level={3}\n              style={{\n                color: themeStyles.getTextColor('primary'),\n                marginBottom: description ? '8px' : '0',\n                fontSize: '24px',\n                fontWeight: 'bold',\n              }}\n            >\n              {title}\n            </Title>\n          )}\n          {description && (\n            <Text\n              style={{\n                color: themeStyles.getTextColor('secondary'),\n                fontSize: '14px',\n                lineHeight: 1.5,\n              }}\n            >\n              {description}\n            </Text>\n          )}\n        </div>\n      )}\n      {children}\n    </div>\n  );\n}\n\n/**\n * Auth form wrapper with consistent spacing\n */\nexport interface AuthFormProps {\n  children: React.ReactNode;\n  onSubmit?: (e: React.FormEvent) => void;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\nexport function AuthForm({\n  children,\n  onSubmit,\n  className,\n  style,\n}: AuthFormProps) {\n  return (\n    <form\n      className={className}\n      style={{\n        width: '100%',\n        ...style,\n      }}\n      onSubmit={onSubmit}\n    >\n      <Space\n        direction=\"vertical\"\n        size=\"large\"\n        style={{ width: '100%' }}\n      >\n        {children}\n      </Space>\n    </form>\n  );\n}\n\n/**\n * Auth divider with text\n */\nexport interface AuthDividerProps {\n  text?: string;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\nexport function AuthDivider({\n  text = 'OR',\n  className,\n  style,\n}: AuthDividerProps) {\n  const themeStyles = useThemeStyles();\n\n  return (\n    <div\n      className={className}\n      style={{\n        position: 'relative',\n        textAlign: 'center',\n        margin: '24px 0',\n        ...style,\n      }}\n    >\n      <Divider\n        style={{\n          borderColor: themeStyles.getBorderColor('primary'),\n        }}\n      />\n      <span\n        style={{\n          position: 'absolute',\n          top: '50%',\n          left: '50%',\n          transform: 'translate(-50%, -50%)',\n          backgroundColor: themeStyles.getBackgroundColor('container'),\n          padding: '0 16px',\n          color: themeStyles.getTextColor('tertiary'),\n          fontSize: '12px',\n          fontWeight: 'bold',\n        }}\n      >\n        {text}\n      </span>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;AAMD;AACA;AADA;AADA;AAAA;AAAA;AAAA;AAAA;;;AAHA;;;;AAOA,MAAM,EAAE,OAAO,EAAE,GAAG,qLAAA,CAAA,SAAM;AAC1B,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,6LAAA,CAAA,aAAU;AAiBjC,SAAS,WAAW,EACzB,QAAQ,EACR,QAAQ,mBAAmB,EAC3B,WAAW,8BAA8B,EACzC,aAAa,IAAI,EACjB,SAAS,EACT,KAAK,EACW;;IAChB,MAAM,cAAc,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;IAEjC,MAAM,cAAmC;QACvC,WAAW;QACX,iBAAiB,YAAY,kBAAkB,CAAC;QAChD,SAAS;QACT,eAAe;QACf,GAAG,KAAK;IACV;IAEA,MAAM,eAAoC;QACxC,MAAM;QACN,SAAS;QACT,YAAY;QACZ,gBAAgB;QAChB,SAAS;QACT,iBAAiB,CAAC,wBAAwB,EAAE,YAAY,QAAQ,CAAC,WAAW,OAAO,EAAE,YAAY,QAAQ,CAAC,WAAW,QAAQ,CAAC;IAChI;IAEA,qBACE,6LAAC,qLAAA,CAAA,SAAM;QAAC,WAAW;QAAW,OAAO;;0BACnC,6LAAC;gBAAQ,OAAO;0BACd,cAAA,6LAAC;oBACC,OAAO;wBACL,OAAO;wBACP,UAAU;wBACV,QAAQ;oBACV;;sCAGA,6LAAC;4BACC,OAAO;gCACL,WAAW;gCACX,cAAc;4BAChB;;8CAGA,6LAAC;oCACC,OAAO;wCACL,SAAS;wCACT,gBAAgB;wCAChB,cAAc;oCAChB;8CAEA,cAAA,6LAAC;wCACC,OAAO;4CACL,OAAO;4CACP,QAAQ;4CACR,iBAAiB,YAAY,QAAQ,CAAC;4CACtC,cAAc;4CACd,SAAS;4CACT,YAAY;4CACZ,gBAAgB;4CAChB,OAAO;4CACP,UAAU;4CACV,YAAY;4CACZ,WAAW,CAAC,WAAW,EAAE,YAAY,QAAQ,CAAC,WAAW,EAAE,CAAC;wCAC9D;kDACD;;;;;;;;;;;8CAMH,6LAAC;oCACC,OAAO;oCACP,OAAO;wCACL,OAAO,YAAY,YAAY,CAAC;wCAChC,cAAc;wCACd,UAAU;wCACV,YAAY;oCACd;8CAEC;;;;;;8CAIH,6LAAC;oCACC,OAAO;wCACL,OAAO,YAAY,YAAY,CAAC;wCAChC,UAAU;oCACZ;8CAEC;;;;;;;;;;;;sCAKL,6LAAC,iLAAA,CAAA,OAAI;4BACH,OAAO;gCACL,iBAAiB,YAAY,kBAAkB,CAAC;gCAChD,QAAQ,CAAC,UAAU,EAAE,YAAY,cAAc,CAAC,YAAY;gCAC5D,cAAc;gCACd,WAAW,CAAC,WAAW,EAAE,YAAY,QAAQ,CAAC,WAAW,EAAE,CAAC;4BAC9D;4BACA,WAAW;gCACT,SAAS;4BACX;sCAEC;;;;;;sCAIH,6LAAC;4BACC,OAAO;gCACL,WAAW;gCACX,WAAW;4BACb;sCAEA,cAAA,6LAAC,mMAAA,CAAA,QAAK;gCAAC,qBAAO,6LAAC,uLAAA,CAAA,UAAO;oCAAC,MAAK;;;;;;;kDAC1B,6LAAC;wCACC,MAAK;wCACL,OAAO;4CACL,OAAO,YAAY,YAAY,CAAC;4CAChC,UAAU;wCACZ;kDACD;;;;;;kDAGD,6LAAC;wCACC,MAAK;wCACL,OAAO;4CACL,OAAO,YAAY,YAAY,CAAC;4CAChC,UAAU;wCACZ;kDACD;;;;;;kDAGD,6LAAC;wCACC,MAAK;wCACL,OAAO;4CACL,OAAO,YAAY,YAAY,CAAC;4CAChC,UAAU;wCACZ;kDACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;YASR,4BACC,6LAAC,gJAAA,CAAA,eAAY;gBACX,OAAO;oBACL,iBAAiB,YAAY,kBAAkB,CAAC;oBAChD,WAAW,CAAC,UAAU,EAAE,YAAY,cAAc,CAAC,YAAY;gBACjE;;;;;;;;;;;;AAKV;GAjKgB;;QAQM,wHAAA,CAAA,iBAAc;;;KARpB;AA8KT,SAAS,SAAS,EACvB,QAAQ,EACR,KAAK,EACL,WAAW,EACX,SAAS,EACT,KAAK,EACS;;IACd,MAAM,cAAc,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;IAEjC,qBACE,6LAAC;QAAI,WAAW;QAAW,OAAO;;YAC/B,CAAC,SAAS,WAAW,mBACpB,6LAAC;gBAAI,OAAO;oBAAE,cAAc;oBAAQ,WAAW;gBAAS;;oBACrD,uBACC,6LAAC;wBACC,OAAO;wBACP,OAAO;4BACL,OAAO,YAAY,YAAY,CAAC;4BAChC,cAAc,cAAc,QAAQ;4BACpC,UAAU;4BACV,YAAY;wBACd;kCAEC;;;;;;oBAGJ,6BACC,6LAAC;wBACC,OAAO;4BACL,OAAO,YAAY,YAAY,CAAC;4BAChC,UAAU;4BACV,YAAY;wBACd;kCAEC;;;;;;;;;;;;YAKR;;;;;;;AAGP;IA1CgB;;QAOM,wHAAA,CAAA,iBAAc;;;MAPpB;AAsDT,SAAS,SAAS,EACvB,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,KAAK,EACS;IACd,qBACE,6LAAC;QACC,WAAW;QACX,OAAO;YACL,OAAO;YACP,GAAG,KAAK;QACV;QACA,UAAU;kBAEV,cAAA,6LAAC,mMAAA,CAAA,QAAK;YACJ,WAAU;YACV,MAAK;YACL,OAAO;gBAAE,OAAO;YAAO;sBAEtB;;;;;;;;;;;AAIT;MAxBgB;AAmCT,SAAS,YAAY,EAC1B,OAAO,IAAI,EACX,SAAS,EACT,KAAK,EACY;;IACjB,MAAM,cAAc,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;IAEjC,qBACE,6LAAC;QACC,WAAW;QACX,OAAO;YACL,UAAU;YACV,WAAW;YACX,QAAQ;YACR,GAAG,KAAK;QACV;;0BAEA,6LAAC,uLAAA,CAAA,UAAO;gBACN,OAAO;oBACL,aAAa,YAAY,cAAc,CAAC;gBAC1C;;;;;;0BAEF,6LAAC;gBACC,OAAO;oBACL,UAAU;oBACV,KAAK;oBACL,MAAM;oBACN,WAAW;oBACX,iBAAiB,YAAY,kBAAkB,CAAC;oBAChD,SAAS;oBACT,OAAO,YAAY,YAAY,CAAC;oBAChC,UAAU;oBACV,YAAY;gBACd;0BAEC;;;;;;;;;;;;AAIT;IAvCgB;;QAKM,wHAAA,CAAA,iBAAc;;;MALpB"}}, {"offset": {"line": 10813, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 10819, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/layout/page-header.tsx"], "sourcesContent": ["/**\n * Page Header Component\n * Reusable page header with breadcrumbs, title, and actions\n */\n\n'use client';\n\nimport React from 'react';\nimport { PageHeader as AntPageHeader, Breadcrumb, Space, Divider } from 'antd';\nimport { HomeOutlined } from '@ant-design/icons';\nimport { useThemeStyles } from '@/theme';\n\n/**\n * Breadcrumb item interface\n */\nexport interface BreadcrumbItem {\n  title: string;\n  href?: string;\n  icon?: React.ReactNode;\n}\n\n/**\n * Page header props\n */\nexport interface PageHeaderProps {\n  title: string;\n  subtitle?: string;\n  breadcrumbs?: BreadcrumbItem[];\n  actions?: React.ReactNode[];\n  extra?: React.ReactNode;\n  children?: React.ReactNode;\n  showDivider?: boolean;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\n/**\n * Page Header component\n */\nexport function PageHeader({\n  title,\n  subtitle,\n  breadcrumbs = [],\n  actions = [],\n  extra,\n  children,\n  showDivider = true,\n  className,\n  style,\n}: PageHeaderProps) {\n  const themeStyles = useThemeStyles();\n\n  // Add home breadcrumb if not present\n  const allBreadcrumbs = breadcrumbs.length > 0 && breadcrumbs[0].href !== '/' \n    ? [{ title: 'Home', href: '/', icon: <HomeOutlined /> }, ...breadcrumbs]\n    : breadcrumbs;\n\n  return (\n    <div className={className} style={style}>\n      <div style={{\n        padding: '16px 24px',\n        backgroundColor: themeStyles.getBackgroundColor('container'),\n        borderBottom: showDivider ? `1px solid ${themeStyles.getBorderColor('primary')}` : 'none',\n      }}>\n        {/* Breadcrumbs */}\n        {allBreadcrumbs.length > 0 && (\n          <Breadcrumb style={{ marginBottom: '8px' }}>\n            {allBreadcrumbs.map((item, index) => (\n              <Breadcrumb.Item key={index} href={item.href}>\n                {item.icon && <span style={{ marginRight: '4px' }}>{item.icon}</span>}\n                {item.title}\n              </Breadcrumb.Item>\n            ))}\n          </Breadcrumb>\n        )}\n\n        {/* Header content */}\n        <div style={{\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between',\n          flexWrap: 'wrap',\n          gap: '16px',\n        }}>\n          {/* Title section */}\n          <div style={{ flex: 1, minWidth: '200px' }}>\n            <h1 style={{\n              margin: 0,\n              fontSize: '24px',\n              fontWeight: 'bold',\n              color: themeStyles.getTextColor('primary'),\n              lineHeight: 1.2,\n            }}>\n              {title}\n            </h1>\n            {subtitle && (\n              <p style={{\n                margin: '4px 0 0 0',\n                fontSize: '14px',\n                color: themeStyles.getTextColor('secondary'),\n                lineHeight: 1.4,\n              }}>\n                {subtitle}\n              </p>\n            )}\n          </div>\n\n          {/* Actions and extra content */}\n          {(actions.length > 0 || extra) && (\n            <div style={{\n              display: 'flex',\n              alignItems: 'center',\n              gap: '12px',\n              flexWrap: 'wrap',\n            }}>\n              {actions.length > 0 && (\n                <Space size=\"middle\">\n                  {actions}\n                </Space>\n              )}\n              {extra}\n            </div>\n          )}\n        </div>\n\n        {/* Children content */}\n        {children && (\n          <div style={{ marginTop: '16px' }}>\n            {children}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n\n/**\n * Simple page header for basic pages\n */\nexport interface SimplePageHeaderProps {\n  title: string;\n  subtitle?: string;\n  backButton?: boolean;\n  onBack?: () => void;\n  actions?: React.ReactNode[];\n  className?: string;\n  style?: React.CSSProperties;\n}\n\nexport function SimplePageHeader({\n  title,\n  subtitle,\n  backButton = false,\n  onBack,\n  actions = [],\n  className,\n  style,\n}: SimplePageHeaderProps) {\n  const themeStyles = useThemeStyles();\n\n  return (\n    <div \n      className={className} \n      style={{\n        padding: '16px 24px',\n        backgroundColor: themeStyles.getBackgroundColor('container'),\n        borderBottom: `1px solid ${themeStyles.getBorderColor('primary')}`,\n        ...style,\n      }}\n    >\n      <div style={{\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'space-between',\n        gap: '16px',\n      }}>\n        <div style={{ display: 'flex', alignItems: 'center', gap: '12px', flex: 1 }}>\n          {backButton && (\n            <button\n              onClick={onBack}\n              style={{\n                background: 'none',\n                border: 'none',\n                cursor: 'pointer',\n                fontSize: '16px',\n                color: themeStyles.getTextColor('secondary'),\n                padding: '4px',\n              }}\n            >\n              ←\n            </button>\n          )}\n          <div>\n            <h1 style={{\n              margin: 0,\n              fontSize: '20px',\n              fontWeight: 'bold',\n              color: themeStyles.getTextColor('primary'),\n            }}>\n              {title}\n            </h1>\n            {subtitle && (\n              <p style={{\n                margin: '2px 0 0 0',\n                fontSize: '12px',\n                color: themeStyles.getTextColor('secondary'),\n              }}>\n                {subtitle}\n              </p>\n            )}\n          </div>\n        </div>\n\n        {actions.length > 0 && (\n          <Space size=\"small\">\n            {actions}\n          </Space>\n        )}\n      </div>\n    </div>\n  );\n}\n\n/**\n * Section header for content sections\n */\nexport interface SectionHeaderProps {\n  title: string;\n  subtitle?: string;\n  actions?: React.ReactNode[];\n  divider?: boolean;\n  size?: 'small' | 'medium' | 'large';\n  className?: string;\n  style?: React.CSSProperties;\n}\n\nexport function SectionHeader({\n  title,\n  subtitle,\n  actions = [],\n  divider = false,\n  size = 'medium',\n  className,\n  style,\n}: SectionHeaderProps) {\n  const themeStyles = useThemeStyles();\n\n  const sizeMap = {\n    small: { fontSize: '16px', marginBottom: '12px' },\n    medium: { fontSize: '18px', marginBottom: '16px' },\n    large: { fontSize: '20px', marginBottom: '20px' },\n  };\n\n  return (\n    <div className={className} style={style}>\n      <div style={{\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'space-between',\n        marginBottom: sizeMap[size].marginBottom,\n      }}>\n        <div>\n          <h2 style={{\n            margin: 0,\n            fontSize: sizeMap[size].fontSize,\n            fontWeight: 'bold',\n            color: themeStyles.getTextColor('primary'),\n          }}>\n            {title}\n          </h2>\n          {subtitle && (\n            <p style={{\n              margin: '4px 0 0 0',\n              fontSize: '12px',\n              color: themeStyles.getTextColor('secondary'),\n            }}>\n              {subtitle}\n            </p>\n          )}\n        </div>\n\n        {actions.length > 0 && (\n          <Space size=\"small\">\n            {actions}\n          </Space>\n        )}\n      </div>\n\n      {divider && <Divider style={{ margin: '0 0 16px 0' }} />}\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;AAOD;AAAA;AADA;AADA;AAAA;AAAA;;;AAHA;;;;AAkCO,SAAS,WAAW,EACzB,KAAK,EACL,QAAQ,EACR,cAAc,EAAE,EAChB,UAAU,EAAE,EACZ,KAAK,EACL,QAAQ,EACR,cAAc,IAAI,EAClB,SAAS,EACT,KAAK,EACW;;IAChB,MAAM,cAAc,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;IAEjC,qCAAqC;IACrC,MAAM,iBAAiB,YAAY,MAAM,GAAG,KAAK,WAAW,CAAC,EAAE,CAAC,IAAI,KAAK,MACrE;QAAC;YAAE,OAAO;YAAQ,MAAM;YAAK,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;QAAI;WAAM;KAAY,GACtE;IAEJ,qBACE,6LAAC;QAAI,WAAW;QAAW,OAAO;kBAChC,cAAA,6LAAC;YAAI,OAAO;gBACV,SAAS;gBACT,iBAAiB,YAAY,kBAAkB,CAAC;gBAChD,cAAc,cAAc,CAAC,UAAU,EAAE,YAAY,cAAc,CAAC,YAAY,GAAG;YACrF;;gBAEG,eAAe,MAAM,GAAG,mBACvB,6LAAC,6LAAA,CAAA,aAAU;oBAAC,OAAO;wBAAE,cAAc;oBAAM;8BACtC,eAAe,GAAG,CAAC,CAAC,MAAM,sBACzB,6LAAC,6LAAA,CAAA,aAAU,CAAC,IAAI;4BAAa,MAAM,KAAK,IAAI;;gCACzC,KAAK,IAAI,kBAAI,6LAAC;oCAAK,OAAO;wCAAE,aAAa;oCAAM;8CAAI,KAAK,IAAI;;;;;;gCAC5D,KAAK,KAAK;;2BAFS;;;;;;;;;;8BAS5B,6LAAC;oBAAI,OAAO;wBACV,SAAS;wBACT,YAAY;wBACZ,gBAAgB;wBAChB,UAAU;wBACV,KAAK;oBACP;;sCAEE,6LAAC;4BAAI,OAAO;gCAAE,MAAM;gCAAG,UAAU;4BAAQ;;8CACvC,6LAAC;oCAAG,OAAO;wCACT,QAAQ;wCACR,UAAU;wCACV,YAAY;wCACZ,OAAO,YAAY,YAAY,CAAC;wCAChC,YAAY;oCACd;8CACG;;;;;;gCAEF,0BACC,6LAAC;oCAAE,OAAO;wCACR,QAAQ;wCACR,UAAU;wCACV,OAAO,YAAY,YAAY,CAAC;wCAChC,YAAY;oCACd;8CACG;;;;;;;;;;;;wBAMN,CAAC,QAAQ,MAAM,GAAG,KAAK,KAAK,mBAC3B,6LAAC;4BAAI,OAAO;gCACV,SAAS;gCACT,YAAY;gCACZ,KAAK;gCACL,UAAU;4BACZ;;gCACG,QAAQ,MAAM,GAAG,mBAChB,6LAAC,mMAAA,CAAA,QAAK;oCAAC,MAAK;8CACT;;;;;;gCAGJ;;;;;;;;;;;;;gBAMN,0BACC,6LAAC;oBAAI,OAAO;wBAAE,WAAW;oBAAO;8BAC7B;;;;;;;;;;;;;;;;;AAMb;GA/FgB;;QAWM,wHAAA,CAAA,iBAAc;;;KAXpB;AA8GT,SAAS,iBAAiB,EAC/B,KAAK,EACL,QAAQ,EACR,aAAa,KAAK,EAClB,MAAM,EACN,UAAU,EAAE,EACZ,SAAS,EACT,KAAK,EACiB;;IACtB,MAAM,cAAc,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;IAEjC,qBACE,6LAAC;QACC,WAAW;QACX,OAAO;YACL,SAAS;YACT,iBAAiB,YAAY,kBAAkB,CAAC;YAChD,cAAc,CAAC,UAAU,EAAE,YAAY,cAAc,CAAC,YAAY;YAClE,GAAG,KAAK;QACV;kBAEA,cAAA,6LAAC;YAAI,OAAO;gBACV,SAAS;gBACT,YAAY;gBACZ,gBAAgB;gBAChB,KAAK;YACP;;8BACE,6LAAC;oBAAI,OAAO;wBAAE,SAAS;wBAAQ,YAAY;wBAAU,KAAK;wBAAQ,MAAM;oBAAE;;wBACvE,4BACC,6LAAC;4BACC,SAAS;4BACT,OAAO;gCACL,YAAY;gCACZ,QAAQ;gCACR,QAAQ;gCACR,UAAU;gCACV,OAAO,YAAY,YAAY,CAAC;gCAChC,SAAS;4BACX;sCACD;;;;;;sCAIH,6LAAC;;8CACC,6LAAC;oCAAG,OAAO;wCACT,QAAQ;wCACR,UAAU;wCACV,YAAY;wCACZ,OAAO,YAAY,YAAY,CAAC;oCAClC;8CACG;;;;;;gCAEF,0BACC,6LAAC;oCAAE,OAAO;wCACR,QAAQ;wCACR,UAAU;wCACV,OAAO,YAAY,YAAY,CAAC;oCAClC;8CACG;;;;;;;;;;;;;;;;;;gBAMR,QAAQ,MAAM,GAAG,mBAChB,6LAAC,mMAAA,CAAA,QAAK;oBAAC,MAAK;8BACT;;;;;;;;;;;;;;;;;AAMb;IAxEgB;;QASM,wHAAA,CAAA,iBAAc;;;MATpB;AAuFT,SAAS,cAAc,EAC5B,KAAK,EACL,QAAQ,EACR,UAAU,EAAE,EACZ,UAAU,KAAK,EACf,OAAO,QAAQ,EACf,SAAS,EACT,KAAK,EACc;;IACnB,MAAM,cAAc,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;IAEjC,MAAM,UAAU;QACd,OAAO;YAAE,UAAU;YAAQ,cAAc;QAAO;QAChD,QAAQ;YAAE,UAAU;YAAQ,cAAc;QAAO;QACjD,OAAO;YAAE,UAAU;YAAQ,cAAc;QAAO;IAClD;IAEA,qBACE,6LAAC;QAAI,WAAW;QAAW,OAAO;;0BAChC,6LAAC;gBAAI,OAAO;oBACV,SAAS;oBACT,YAAY;oBACZ,gBAAgB;oBAChB,cAAc,OAAO,CAAC,KAAK,CAAC,YAAY;gBAC1C;;kCACE,6LAAC;;0CACC,6LAAC;gCAAG,OAAO;oCACT,QAAQ;oCACR,UAAU,OAAO,CAAC,KAAK,CAAC,QAAQ;oCAChC,YAAY;oCACZ,OAAO,YAAY,YAAY,CAAC;gCAClC;0CACG;;;;;;4BAEF,0BACC,6LAAC;gCAAE,OAAO;oCACR,QAAQ;oCACR,UAAU;oCACV,OAAO,YAAY,YAAY,CAAC;gCAClC;0CACG;;;;;;;;;;;;oBAKN,QAAQ,MAAM,GAAG,mBAChB,6LAAC,mMAAA,CAAA,QAAK;wBAAC,MAAK;kCACT;;;;;;;;;;;;YAKN,yBAAW,6LAAC,uLAAA,CAAA,UAAO;gBAAC,OAAO;oBAAE,QAAQ;gBAAa;;;;;;;;;;;;AAGzD;IAvDgB;;QASM,wHAAA,CAAA,iBAAc;;;MATpB"}}, {"offset": {"line": 11213, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 11219, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/layout/content-layout.tsx"], "sourcesContent": ["/**\n * Content Layout Components\n * Reusable layout components for content organization\n */\n\n'use client';\n\nimport React from 'react';\nimport { Layout, Row, Col } from 'antd';\nimport { useThemeStyles } from '@/theme';\n\nconst { Content } = Layout;\n\n/**\n * Main content layout props\n */\nexport interface ContentLayoutProps {\n  children: React.ReactNode;\n  maxWidth?: number | string;\n  padding?: 'none' | 'small' | 'medium' | 'large';\n  centered?: boolean;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\n/**\n * Main content layout component\n */\nexport function ContentLayout({\n  children,\n  maxWidth = '1200px',\n  padding = 'medium',\n  centered = true,\n  className,\n  style,\n}: ContentLayoutProps) {\n  const themeStyles = useThemeStyles();\n\n  const paddingMap = {\n    none: '0',\n    small: '12px',\n    medium: '24px',\n    large: '32px',\n  };\n\n  const contentStyle: React.CSSProperties = {\n    maxWidth: typeof maxWidth === 'number' ? `${maxWidth}px` : maxWidth,\n    margin: centered ? '0 auto' : '0',\n    padding: paddingMap[padding],\n    backgroundColor: themeStyles.getBackgroundColor('layout'),\n    minHeight: 'calc(100vh - 64px)', // Assuming 64px header height\n    ...style,\n  };\n\n  return (\n    <Content className={className} style={contentStyle}>\n      {children}\n    </Content>\n  );\n}\n\n/**\n * Two column layout props\n */\nexport interface TwoColumnLayoutProps {\n  leftContent: React.ReactNode;\n  rightContent: React.ReactNode;\n  leftSpan?: number;\n  rightSpan?: number;\n  gutter?: number | [number, number];\n  responsive?: boolean;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\n/**\n * Two column layout component\n */\nexport function TwoColumnLayout({\n  leftContent,\n  rightContent,\n  leftSpan = 16,\n  rightSpan = 8,\n  gutter = 24,\n  responsive = true,\n  className,\n  style,\n}: TwoColumnLayoutProps) {\n  const responsiveProps = responsive ? {\n    xs: 24,\n    sm: 24,\n    md: leftSpan,\n    lg: leftSpan,\n    xl: leftSpan,\n  } : { span: leftSpan };\n\n  const rightResponsiveProps = responsive ? {\n    xs: 24,\n    sm: 24,\n    md: rightSpan,\n    lg: rightSpan,\n    xl: rightSpan,\n  } : { span: rightSpan };\n\n  return (\n    <Row gutter={gutter} className={className} style={style}>\n      <Col {...responsiveProps}>\n        {leftContent}\n      </Col>\n      <Col {...rightResponsiveProps}>\n        {rightContent}\n      </Col>\n    </Row>\n  );\n}\n\n/**\n * Three column layout props\n */\nexport interface ThreeColumnLayoutProps {\n  leftContent: React.ReactNode;\n  centerContent: React.ReactNode;\n  rightContent: React.ReactNode;\n  leftSpan?: number;\n  centerSpan?: number;\n  rightSpan?: number;\n  gutter?: number | [number, number];\n  responsive?: boolean;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\n/**\n * Three column layout component\n */\nexport function ThreeColumnLayout({\n  leftContent,\n  centerContent,\n  rightContent,\n  leftSpan = 6,\n  centerSpan = 12,\n  rightSpan = 6,\n  gutter = 24,\n  responsive = true,\n  className,\n  style,\n}: ThreeColumnLayoutProps) {\n  const getResponsiveProps = (span: number) => responsive ? {\n    xs: 24,\n    sm: 24,\n    md: span,\n    lg: span,\n    xl: span,\n  } : { span };\n\n  return (\n    <Row gutter={gutter} className={className} style={style}>\n      <Col {...getResponsiveProps(leftSpan)}>\n        {leftContent}\n      </Col>\n      <Col {...getResponsiveProps(centerSpan)}>\n        {centerContent}\n      </Col>\n      <Col {...getResponsiveProps(rightSpan)}>\n        {rightContent}\n      </Col>\n    </Row>\n  );\n}\n\n/**\n * Grid layout props\n */\nexport interface GridLayoutProps {\n  children: React.ReactNode;\n  columns?: number;\n  gutter?: number | [number, number];\n  responsive?: {\n    xs?: number;\n    sm?: number;\n    md?: number;\n    lg?: number;\n    xl?: number;\n    xxl?: number;\n  };\n  className?: string;\n  style?: React.CSSProperties;\n}\n\n/**\n * Grid layout component\n */\nexport function GridLayout({\n  children,\n  columns = 3,\n  gutter = 24,\n  responsive,\n  className,\n  style,\n}: GridLayoutProps) {\n  const defaultResponsive = {\n    xs: 1,\n    sm: 1,\n    md: 2,\n    lg: columns,\n    xl: columns,\n    xxl: columns,\n  };\n\n  const responsiveConfig = responsive || defaultResponsive;\n  const span = 24 / columns;\n\n  return (\n    <Row gutter={gutter} className={className} style={style}>\n      {React.Children.map(children, (child, index) => (\n        <Col\n          key={index}\n          xs={24 / responsiveConfig.xs}\n          sm={24 / responsiveConfig.sm}\n          md={24 / responsiveConfig.md}\n          lg={24 / responsiveConfig.lg}\n          xl={24 / responsiveConfig.xl}\n          xxl={24 / responsiveConfig.xxl}\n        >\n          {child}\n        </Col>\n      ))}\n    </Row>\n  );\n}\n\n/**\n * Sidebar layout props\n */\nexport interface SidebarLayoutProps {\n  sidebar: React.ReactNode;\n  content: React.ReactNode;\n  sidebarWidth?: number;\n  sidebarPosition?: 'left' | 'right';\n  collapsible?: boolean;\n  collapsed?: boolean;\n  onCollapse?: (collapsed: boolean) => void;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\n/**\n * Sidebar layout component\n */\nexport function SidebarLayout({\n  sidebar,\n  content,\n  sidebarWidth = 250,\n  sidebarPosition = 'left',\n  collapsible = false,\n  collapsed = false,\n  onCollapse,\n  className,\n  style,\n}: SidebarLayoutProps) {\n  const themeStyles = useThemeStyles();\n\n  const sidebarStyle: React.CSSProperties = {\n    width: collapsed ? '80px' : `${sidebarWidth}px`,\n    backgroundColor: themeStyles.getBackgroundColor('container'),\n    borderRight: sidebarPosition === 'left' ? `1px solid ${themeStyles.getBorderColor('primary')}` : 'none',\n    borderLeft: sidebarPosition === 'right' ? `1px solid ${themeStyles.getBorderColor('primary')}` : 'none',\n    transition: 'width 0.2s ease',\n    overflow: 'hidden',\n  };\n\n  const contentStyle: React.CSSProperties = {\n    flex: 1,\n    backgroundColor: themeStyles.getBackgroundColor('layout'),\n    overflow: 'auto',\n  };\n\n  const layoutStyle: React.CSSProperties = {\n    display: 'flex',\n    flexDirection: sidebarPosition === 'left' ? 'row' : 'row-reverse',\n    height: '100%',\n    ...style,\n  };\n\n  return (\n    <div className={className} style={layoutStyle}>\n      <div style={sidebarStyle}>\n        {collapsible && (\n          <div style={{\n            padding: '8px',\n            borderBottom: `1px solid ${themeStyles.getBorderColor('primary')}`,\n            textAlign: 'center',\n          }}>\n            <button\n              onClick={() => onCollapse?.(!collapsed)}\n              style={{\n                background: 'none',\n                border: 'none',\n                cursor: 'pointer',\n                fontSize: '16px',\n                color: themeStyles.getTextColor('secondary'),\n              }}\n            >\n              {collapsed ? '→' : '←'}\n            </button>\n          </div>\n        )}\n        {sidebar}\n      </div>\n      <div style={contentStyle}>\n        {content}\n      </div>\n    </div>\n  );\n}\n\n/**\n * Container component for consistent spacing\n */\nexport interface ContainerProps {\n  children: React.ReactNode;\n  size?: 'small' | 'medium' | 'large' | 'full';\n  padding?: boolean;\n  centered?: boolean;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\nexport function Container({\n  children,\n  size = 'large',\n  padding = true,\n  centered = true,\n  className,\n  style,\n}: ContainerProps) {\n  const sizeMap = {\n    small: '600px',\n    medium: '900px',\n    large: '1200px',\n    full: '100%',\n  };\n\n  const containerStyle: React.CSSProperties = {\n    maxWidth: sizeMap[size],\n    margin: centered ? '0 auto' : '0',\n    padding: padding ? '0 24px' : '0',\n    width: '100%',\n    ...style,\n  };\n\n  return (\n    <div className={className} style={containerStyle}>\n      {children}\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;AAID;AAEA;AADA;AACA;AADA;AAAA;;;AAHA;;;;AAMA,MAAM,EAAE,OAAO,EAAE,GAAG,qLAAA,CAAA,SAAM;AAiBnB,SAAS,cAAc,EAC5B,QAAQ,EACR,WAAW,QAAQ,EACnB,UAAU,QAAQ,EAClB,WAAW,IAAI,EACf,SAAS,EACT,KAAK,EACc;;IACnB,MAAM,cAAc,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;IAEjC,MAAM,aAAa;QACjB,MAAM;QACN,OAAO;QACP,QAAQ;QACR,OAAO;IACT;IAEA,MAAM,eAAoC;QACxC,UAAU,OAAO,aAAa,WAAW,GAAG,SAAS,EAAE,CAAC,GAAG;QAC3D,QAAQ,WAAW,WAAW;QAC9B,SAAS,UAAU,CAAC,QAAQ;QAC5B,iBAAiB,YAAY,kBAAkB,CAAC;QAChD,WAAW;QACX,GAAG,KAAK;IACV;IAEA,qBACE,6LAAC;QAAQ,WAAW;QAAW,OAAO;kBACnC;;;;;;AAGP;GA/BgB;;QAQM,wHAAA,CAAA,iBAAc;;;KARpB;AAkDT,SAAS,gBAAgB,EAC9B,WAAW,EACX,YAAY,EACZ,WAAW,EAAE,EACb,YAAY,CAAC,EACb,SAAS,EAAE,EACX,aAAa,IAAI,EACjB,SAAS,EACT,KAAK,EACgB;IACrB,MAAM,kBAAkB,aAAa;QACnC,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN,IAAI;QAAE,MAAM;IAAS;IAErB,MAAM,uBAAuB,aAAa;QACxC,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN,IAAI;QAAE,MAAM;IAAU;IAEtB,qBACE,6LAAC,+KAAA,CAAA,MAAG;QAAC,QAAQ;QAAQ,WAAW;QAAW,OAAO;;0BAChD,6LAAC,+KAAA,CAAA,MAAG;gBAAE,GAAG,eAAe;0BACrB;;;;;;0BAEH,6LAAC,+KAAA,CAAA,MAAG;gBAAE,GAAG,oBAAoB;0BAC1B;;;;;;;;;;;;AAIT;MApCgB;AAyDT,SAAS,kBAAkB,EAChC,WAAW,EACX,aAAa,EACb,YAAY,EACZ,WAAW,CAAC,EACZ,aAAa,EAAE,EACf,YAAY,CAAC,EACb,SAAS,EAAE,EACX,aAAa,IAAI,EACjB,SAAS,EACT,KAAK,EACkB;IACvB,MAAM,qBAAqB,CAAC,OAAiB,aAAa;YACxD,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN,IAAI;YAAE;QAAK;IAEX,qBACE,6LAAC,+KAAA,CAAA,MAAG;QAAC,QAAQ;QAAQ,WAAW;QAAW,OAAO;;0BAChD,6LAAC,+KAAA,CAAA,MAAG;gBAAE,GAAG,mBAAmB,SAAS;0BAClC;;;;;;0BAEH,6LAAC,+KAAA,CAAA,MAAG;gBAAE,GAAG,mBAAmB,WAAW;0BACpC;;;;;;0BAEH,6LAAC,+KAAA,CAAA,MAAG;gBAAE,GAAG,mBAAmB,UAAU;0BACnC;;;;;;;;;;;;AAIT;MAjCgB;AAyDT,SAAS,WAAW,EACzB,QAAQ,EACR,UAAU,CAAC,EACX,SAAS,EAAE,EACX,UAAU,EACV,SAAS,EACT,KAAK,EACW;IAChB,MAAM,oBAAoB;QACxB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,KAAK;IACP;IAEA,MAAM,mBAAmB,cAAc;IACvC,MAAM,OAAO,KAAK;IAElB,qBACE,6LAAC,+KAAA,CAAA,MAAG;QAAC,QAAQ;QAAQ,WAAW;QAAW,OAAO;kBAC/C,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,UAAU,CAAC,OAAO,sBACpC,6LAAC,+KAAA,CAAA,MAAG;gBAEF,IAAI,KAAK,iBAAiB,EAAE;gBAC5B,IAAI,KAAK,iBAAiB,EAAE;gBAC5B,IAAI,KAAK,iBAAiB,EAAE;gBAC5B,IAAI,KAAK,iBAAiB,EAAE;gBAC5B,IAAI,KAAK,iBAAiB,EAAE;gBAC5B,KAAK,KAAK,iBAAiB,GAAG;0BAE7B;eARI;;;;;;;;;;AAaf;MArCgB;AAyDT,SAAS,cAAc,EAC5B,OAAO,EACP,OAAO,EACP,eAAe,GAAG,EAClB,kBAAkB,MAAM,EACxB,cAAc,KAAK,EACnB,YAAY,KAAK,EACjB,UAAU,EACV,SAAS,EACT,KAAK,EACc;;IACnB,MAAM,cAAc,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;IAEjC,MAAM,eAAoC;QACxC,OAAO,YAAY,SAAS,GAAG,aAAa,EAAE,CAAC;QAC/C,iBAAiB,YAAY,kBAAkB,CAAC;QAChD,aAAa,oBAAoB,SAAS,CAAC,UAAU,EAAE,YAAY,cAAc,CAAC,YAAY,GAAG;QACjG,YAAY,oBAAoB,UAAU,CAAC,UAAU,EAAE,YAAY,cAAc,CAAC,YAAY,GAAG;QACjG,YAAY;QACZ,UAAU;IACZ;IAEA,MAAM,eAAoC;QACxC,MAAM;QACN,iBAAiB,YAAY,kBAAkB,CAAC;QAChD,UAAU;IACZ;IAEA,MAAM,cAAmC;QACvC,SAAS;QACT,eAAe,oBAAoB,SAAS,QAAQ;QACpD,QAAQ;QACR,GAAG,KAAK;IACV;IAEA,qBACE,6LAAC;QAAI,WAAW;QAAW,OAAO;;0BAChC,6LAAC;gBAAI,OAAO;;oBACT,6BACC,6LAAC;wBAAI,OAAO;4BACV,SAAS;4BACT,cAAc,CAAC,UAAU,EAAE,YAAY,cAAc,CAAC,YAAY;4BAClE,WAAW;wBACb;kCACE,cAAA,6LAAC;4BACC,SAAS,IAAM,aAAa,CAAC;4BAC7B,OAAO;gCACL,YAAY;gCACZ,QAAQ;gCACR,QAAQ;gCACR,UAAU;gCACV,OAAO,YAAY,YAAY,CAAC;4BAClC;sCAEC,YAAY,MAAM;;;;;;;;;;;oBAIxB;;;;;;;0BAEH,6LAAC;gBAAI,OAAO;0BACT;;;;;;;;;;;;AAIT;IAjEgB;;QAWM,wHAAA,CAAA,iBAAc;;;MAXpB;AA+ET,SAAS,UAAU,EACxB,QAAQ,EACR,OAAO,OAAO,EACd,UAAU,IAAI,EACd,WAAW,IAAI,EACf,SAAS,EACT,KAAK,EACU;IACf,MAAM,UAAU;QACd,OAAO;QACP,QAAQ;QACR,OAAO;QACP,MAAM;IACR;IAEA,MAAM,iBAAsC;QAC1C,UAAU,OAAO,CAAC,KAAK;QACvB,QAAQ,WAAW,WAAW;QAC9B,SAAS,UAAU,WAAW;QAC9B,OAAO;QACP,GAAG,KAAK;IACV;IAEA,qBACE,6LAAC;QAAI,WAAW;QAAW,OAAO;kBAC/B;;;;;;AAGP;MA5BgB"}}, {"offset": {"line": 11525, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}