{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[next]/internal/font/google/geist_e531dabc.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_export_value__({\n  \"className\": \"geist_e531dabc-module__QGiZLq__className\",\n  \"variable\": \"geist_e531dabc-module__QGiZLq__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 10, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 16, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[next]/internal/font/google/geist_e531dabc.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Geist%22,%22arguments%22:[{%22variable%22:%22--font-geist-sans%22,%22subsets%22:[%22latin%22]}],%22variableName%22:%22geistSans%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Geist', 'Geist Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,wJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,wJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,wJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0]}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[next]/internal/font/google/geist_mono_68a01160.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_export_value__({\n  \"className\": \"geist_mono_68a01160-module__YLcDdW__className\",\n  \"variable\": \"geist_mono_68a01160-module__YLcDdW__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 41, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[next]/internal/font/google/geist_mono_68a01160.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Geist_Mono%22,%22arguments%22:[{%22variable%22:%22--font-geist-mono%22,%22subsets%22:[%22latin%22]}],%22variableName%22:%22geistMono%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Geist Mono', 'Geist Mono Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,6JAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,6JAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,6JAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0]}}, {"offset": {"line": 63, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 69, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/providers/app-provider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AppProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call AppProvider() from the server but AppProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/providers/app-provider.tsx <module evaluation>\",\n    \"AppProvider\",\n);\nexport const AppProviderErrorBoundary = registerClientReference(\n    function() { throw new Error(\"Attempted to call AppProviderErrorBoundary() from the server but AppProviderErrorBoundary is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/providers/app-provider.tsx <module evaluation>\",\n    \"AppProviderErrorBoundary\",\n);\nexport const withAppProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call with<PERSON><PERSON><PERSON><PERSON><PERSON>() from the server but withA<PERSON><PERSON><PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/providers/app-provider.tsx <module evaluation>\",\n    \"withAppProvider\",\n);\n"], "names": [], "mappings": ";;;;;AAAA;;AACO,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,gEACA;AAEG,MAAM,2BAA2B,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1D;IAAa,MAAM,IAAI,MAAM;AAAgQ,GAC7R,gEACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,gEACA"}}, {"offset": {"line": 85, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 91, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/providers/app-provider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AppProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call AppProvider() from the server but AppProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/providers/app-provider.tsx\",\n    \"AppProvider\",\n);\nexport const AppProviderErrorBoundary = registerClientReference(\n    function() { throw new Error(\"Attempted to call AppProviderErrorBoundary() from the server but AppProviderErrorBoundary is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/providers/app-provider.tsx\",\n    \"AppProviderErrorBoundary\",\n);\nexport const withAppProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call withA<PERSON><PERSON><PERSON><PERSON>() from the server but with<PERSON>ppProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/providers/app-provider.tsx\",\n    \"withAppProvider\",\n);\n"], "names": [], "mappings": ";;;;;AAAA;;AACO,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,4CACA;AAEG,MAAM,2BAA2B,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1D;IAAa,MAAM,IAAI,MAAM;AAAgQ,GAC7R,4CACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,4CACA"}}, {"offset": {"line": 107, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 113, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 117, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 123, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/stores/types.ts"], "sourcesContent": ["/**\n * Store Types and Interfaces\n * Defines TypeScript types for all store modules\n */\n\n// ============================================================================\n// Base Store Types\n// ============================================================================\n\n/**\n * Base store state interface\n * All stores should extend this interface\n */\nexport interface BaseStoreState {\n  // Hydration state for SSR compatibility\n  _hasHydrated: boolean;\n  setHasHydrated: (hasHydrated: boolean) => void;\n}\n\n/**\n * Store action interface\n * Defines the structure for store actions\n */\nexport interface StoreAction<T = any> {\n  type: string;\n  payload?: T;\n}\n\n/**\n * Store slice interface\n * For modular store composition\n */\nexport interface StoreSlice<T> {\n  (...args: any[]): T;\n}\n\n// ============================================================================\n// User and Authentication Types\n// ============================================================================\n\n/**\n * System user roles\n */\nexport type SystemUserRole = 'Admin' | 'Editor' | 'Moderator';\n\n/**\n * System user interface\n */\nexport interface SystemUser {\n  id: string;\n  email: string;\n  name: string;\n  role: SystemUserRole;\n  isActive: boolean;\n  createdAt: string;\n  updatedAt: string;\n  lastLoginAt?: string;\n}\n\n/**\n * Authentication tokens\n */\nexport interface AuthTokens {\n  accessToken: string;\n  refreshToken: string;\n  expiresAt: number; // Unix timestamp\n}\n\n/**\n * Authentication state\n */\nexport interface AuthState {\n  // User data\n  user: SystemUser | null;\n  tokens: AuthTokens | null;\n  \n  // Authentication status\n  isAuthenticated: boolean;\n  isLoading: boolean;\n  \n  // Error handling\n  error: string | null;\n  \n  // Session management\n  lastActivity: number; // Unix timestamp\n  sessionTimeout: number; // Minutes\n}\n\n/**\n * Authentication actions\n */\nexport interface AuthActions {\n  // Login/logout\n  login: (email: string, password: string) => Promise<void>;\n  logout: () => Promise<void>;\n  logoutAll: () => Promise<void>;\n  \n  // User management\n  updateProfile: (data: Partial<SystemUser>) => Promise<void>;\n  refreshTokens: () => Promise<void>;\n  \n  // State management\n  setUser: (user: SystemUser | null) => void;\n  setTokens: (tokens: AuthTokens | null) => void;\n  setLoading: (loading: boolean) => void;\n  setError: (error: string | null) => void;\n  clearError: () => void;\n  \n  // Session management\n  updateLastActivity: () => void;\n  checkSession: () => boolean;\n  \n  // Hydration\n  hydrate: () => void;\n}\n\n// ============================================================================\n// Application State Types\n// ============================================================================\n\n/**\n * Theme configuration\n */\nexport interface ThemeConfig {\n  mode: 'light' | 'dark';\n  primaryColor: string;\n  borderRadius: number;\n  compactMode: boolean;\n}\n\n/**\n * Navigation state\n */\nexport interface NavigationState {\n  currentPath: string;\n  breadcrumbs: Array<{\n    title: string;\n    path: string;\n  }>;\n  sidebarCollapsed: boolean;\n  activeMenuKey: string;\n}\n\n/**\n * Global UI state\n */\nexport interface UIState {\n  // Loading states\n  globalLoading: boolean;\n  loadingMessage: string;\n  \n  // Error states\n  globalError: string | null;\n  errorDetails: any;\n  \n  // Notification state\n  notifications: Array<{\n    id: string;\n    type: 'success' | 'error' | 'warning' | 'info';\n    title: string;\n    message: string;\n    duration?: number;\n    timestamp: number;\n  }>;\n  \n  // Modal state\n  modals: Record<string, {\n    visible: boolean;\n    data?: any;\n  }>;\n}\n\n/**\n * Application settings\n */\nexport interface AppSettings {\n  // Language and localization\n  language: 'en' | 'vi';\n  timezone: string;\n  dateFormat: string;\n  \n  // Data preferences\n  pageSize: number;\n  autoRefresh: boolean;\n  refreshInterval: number; // Seconds\n  \n  // Feature flags\n  features: {\n    darkMode: boolean;\n    notifications: boolean;\n    autoSave: boolean;\n    advancedFilters: boolean;\n  };\n}\n\n/**\n * Application state\n */\nexport interface AppState {\n  // Configuration\n  theme: ThemeConfig;\n  settings: AppSettings;\n  \n  // Navigation\n  navigation: NavigationState;\n  \n  // UI state\n  ui: UIState;\n  \n  // System info\n  version: string;\n  buildTime: string;\n  environment: 'development' | 'staging' | 'production';\n}\n\n/**\n * Application actions\n */\nexport interface AppActions {\n  // Theme management\n  setTheme: (theme: Partial<ThemeConfig>) => void;\n  toggleTheme: () => void;\n  \n  // Settings management\n  updateSettings: (settings: Partial<AppSettings>) => void;\n  resetSettings: () => void;\n  \n  // Navigation\n  setCurrentPath: (path: string) => void;\n  setBreadcrumbs: (breadcrumbs: NavigationState['breadcrumbs']) => void;\n  toggleSidebar: () => void;\n  setActiveMenu: (key: string) => void;\n  \n  // UI state management\n  setGlobalLoading: (loading: boolean, message?: string) => void;\n  setGlobalError: (error: string | null, details?: any) => void;\n  clearGlobalError: () => void;\n  \n  // Notifications\n  addNotification: (notification: Omit<UIState['notifications'][0], 'id' | 'timestamp'>) => void;\n  removeNotification: (id: string) => void;\n  clearNotifications: () => void;\n  \n  // Modals\n  showModal: (key: string, data?: any) => void;\n  hideModal: (key: string) => void;\n  hideAllModals: () => void;\n}\n\n// ============================================================================\n// Combined Store Types\n// ============================================================================\n\n/**\n * Complete authentication store\n */\nexport interface AuthStore extends BaseStoreState, AuthState, AuthActions {}\n\n/**\n * Complete application store\n */\nexport interface AppStore extends BaseStoreState, AppState, AppActions {}\n\n// ============================================================================\n// Store Configuration Types\n// ============================================================================\n\n/**\n * Store persistence configuration\n */\nexport interface StorePersistConfig {\n  name: string;\n  version: number;\n  partialize?: (state: any) => any;\n  skipHydration?: boolean;\n}\n\n/**\n * Store devtools configuration\n */\nexport interface StoreDevtoolsConfig {\n  name: string;\n  enabled: boolean;\n}\n\n/**\n * Store configuration\n */\nexport interface StoreConfig {\n  persist?: StorePersistConfig;\n  devtools?: StoreDevtoolsConfig;\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,+EAA+E;AAC/E,mBAAmB;AACnB,+EAA+E;AAE/E;;;CAGC"}}, {"offset": {"line": 134, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 140, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/stores/utils.ts"], "sourcesContent": ["/**\n * Store Utilities\n * Helper functions and utilities for store management\n */\n\nimport { StateCreator } from 'zustand';\nimport { persist, devtools, createJSONStorage } from 'zustand/middleware';\nimport type {\n  BaseStoreState,\n  StoreConfig,\n  StorePersistConfig,\n  StoreDevtoolsConfig\n} from './types';\n\n// ============================================================================\n// Store Creation Utilities\n// ============================================================================\n\n/**\n * Create a store with middleware support\n * Provides a consistent way to create stores with persistence and devtools\n */\nexport function createStoreWithMiddleware<T extends BaseStoreState>(\n  storeCreator: StateCreator<T>,\n  config: StoreConfig\n): StateCreator<T> {\n  let store: any = storeCreator;\n\n  // Apply persistence middleware if configured\n  if (config.persist) {\n    store = persist(\n      store,\n      {\n        name: config.persist.name,\n        version: config.persist.version,\n        storage: createJSONStorage(() => localStorage),\n        partialize: config.persist.partialize || ((state) => state),\n        skipHydration: config.persist.skipHydration || false,\n        onRehydrateStorage: () => (state: any) => {\n          if (state) {\n            state.setHasHydrated(true);\n          }\n        },\n      }\n    );\n  }\n\n  // Apply devtools middleware if configured\n  if (config.devtools) {\n    store = devtools(\n      store,\n      {\n        name: config.devtools.name,\n        enabled: config.devtools.enabled && process.env.NODE_ENV === 'development',\n      }\n    );\n  }\n\n  return store as StateCreator<T>;\n}\n\n// ============================================================================\n// Base Store State Utilities\n// ============================================================================\n\n/**\n * Create base store state\n * Provides common state properties for all stores\n */\nexport function createBaseStoreState(): BaseStoreState {\n  return {\n    _hasHydrated: false,\n    setHasHydrated: (hasHydrated: boolean) => {\n      // This will be implemented by the actual store\n    },\n  };\n}\n\n/**\n * Create base store actions\n * Provides common actions for all stores\n */\nexport function createBaseStoreActions<T extends BaseStoreState>(\n  set: (partial: Partial<T>) => void\n): Pick<BaseStoreState, 'setHasHydrated'> {\n  return {\n    setHasHydrated: (hasHydrated: boolean) => {\n      set({ _hasHydrated: hasHydrated } as Partial<T>);\n    },\n  };\n}\n\n// ============================================================================\n// Token Management Utilities\n// ============================================================================\n\n/**\n * Check if token is expired\n */\nexport function isTokenExpired(expiresAt: number): boolean {\n  return Date.now() >= expiresAt;\n}\n\n/**\n * Get token expiration time\n * Calculates expiration time from JWT token or sets default\n */\nexport function getTokenExpiration(token: string, defaultMinutes: number = 60): number {\n  try {\n    // Try to decode JWT token to get expiration\n    const payload = JSON.parse(atob(token.split('.')[1]));\n    if (payload.exp) {\n      return payload.exp * 1000; // Convert to milliseconds\n    }\n  } catch (error) {\n    // If JWT parsing fails, use default expiration\n  }\n\n  // Default expiration: current time + defaultMinutes\n  return Date.now() + (defaultMinutes * 60 * 1000);\n}\n\n/**\n * Clear all stored tokens\n */\nexport function clearStoredTokens(): void {\n  if (typeof window !== 'undefined') {\n    localStorage.removeItem('auth-storage');\n    sessionStorage.removeItem('auth-storage');\n  }\n}\n\n// ============================================================================\n// Session Management Utilities\n// ============================================================================\n\n/**\n * Check if session is valid\n */\nexport function isSessionValid(\n  lastActivity: number,\n  sessionTimeout: number\n): boolean {\n  const now = Date.now();\n  const timeoutMs = sessionTimeout * 60 * 1000; // Convert minutes to milliseconds\n  return (now - lastActivity) < timeoutMs;\n}\n\n/**\n * Get session remaining time in minutes\n */\nexport function getSessionRemainingTime(\n  lastActivity: number,\n  sessionTimeout: number\n): number {\n  const now = Date.now();\n  const timeoutMs = sessionTimeout * 60 * 1000;\n  const elapsed = now - lastActivity;\n  const remaining = timeoutMs - elapsed;\n  return Math.max(0, Math.floor(remaining / (60 * 1000)));\n}\n\n// ============================================================================\n// Error Handling Utilities\n// ============================================================================\n\n/**\n * Extract error message from various error types\n */\nexport function extractErrorMessage(error: any): string {\n  if (typeof error === 'string') {\n    return error;\n  }\n\n  if (error?.response?.data?.message) {\n    return error.response.data.message;\n  }\n\n  if (error?.message) {\n    return error.message;\n  }\n\n  if (error?.error) {\n    return error.error;\n  }\n\n  return 'An unexpected error occurred';\n}\n\n/**\n * Create error object with details\n */\nexport function createErrorObject(\n  message: string,\n  details?: any\n): { message: string; details: any } {\n  return {\n    message,\n    details: details || null,\n  };\n}\n\n// ============================================================================\n// Notification Utilities\n// ============================================================================\n\n/**\n * Generate unique notification ID\n */\nexport function generateNotificationId(): string {\n  return `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n}\n\n/**\n * Get default notification duration based on type\n */\nexport function getDefaultNotificationDuration(\n  type: 'success' | 'error' | 'warning' | 'info'\n): number {\n  switch (type) {\n    case 'success':\n      return 3000; // 3 seconds\n    case 'error':\n      return 5000; // 5 seconds\n    case 'warning':\n      return 4000; // 4 seconds\n    case 'info':\n      return 3000; // 3 seconds\n    default:\n      return 3000;\n  }\n}\n\n// ============================================================================\n// Local Storage Utilities\n// ============================================================================\n\n/**\n * Safe localStorage operations\n */\nexport const storage = {\n  get: (key: string): any => {\n    if (typeof window === 'undefined') return null;\n\n    try {\n      const item = localStorage.getItem(key);\n      return item ? JSON.parse(item) : null;\n    } catch (error) {\n      console.warn(`Error reading from localStorage key \"${key}\":`, error);\n      return null;\n    }\n  },\n\n  set: (key: string, value: any): void => {\n    if (typeof window === 'undefined') return;\n\n    try {\n      localStorage.setItem(key, JSON.stringify(value));\n    } catch (error) {\n      console.warn(`Error writing to localStorage key \"${key}\":`, error);\n    }\n  },\n\n  remove: (key: string): void => {\n    if (typeof window === 'undefined') return;\n\n    try {\n      localStorage.removeItem(key);\n    } catch (error) {\n      console.warn(`Error removing localStorage key \"${key}\":`, error);\n    }\n  },\n\n  clear: (): void => {\n    if (typeof window === 'undefined') return;\n\n    try {\n      localStorage.clear();\n    } catch (error) {\n      console.warn('Error clearing localStorage:', error);\n    }\n  },\n};\n\n// ============================================================================\n// Development Utilities\n// ============================================================================\n\n/**\n * Log store action for debugging\n */\nexport function logStoreAction(\n  storeName: string,\n  actionName: string,\n  payload?: any\n): void {\n  if (process.env.NODE_ENV === 'development') {\n    console.group(`🐻 [${storeName}] ${actionName}`);\n    if (payload !== undefined) {\n      console.log('Payload:', payload);\n    }\n    console.log('Timestamp:', new Date().toISOString());\n    console.groupEnd();\n  }\n}\n\n/**\n * Validate store state structure\n */\nexport function validateStoreState<T extends BaseStoreState>(\n  state: T,\n  requiredKeys: (keyof T)[]\n): boolean {\n  for (const key of requiredKeys) {\n    if (!(key in state)) {\n      console.error(`Missing required store state key: ${String(key)}`);\n      return false;\n    }\n  }\n  return true;\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;;;;;;AAGD;;AAgBO,SAAS,0BACd,YAA6B,EAC7B,MAAmB;IAEnB,IAAI,QAAa;IAEjB,6CAA6C;IAC7C,IAAI,OAAO,OAAO,EAAE;QAClB,QAAQ,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EACZ,OACA;YACE,MAAM,OAAO,OAAO,CAAC,IAAI;YACzB,SAAS,OAAO,OAAO,CAAC,OAAO;YAC/B,SAAS,CAAA,GAAA,6IAAA,CAAA,oBAAiB,AAAD,EAAE,IAAM;YACjC,YAAY,OAAO,OAAO,CAAC,UAAU,IAAI,CAAC,CAAC,QAAU,KAAK;YAC1D,eAAe,OAAO,OAAO,CAAC,aAAa,IAAI;YAC/C,oBAAoB,IAAM,CAAC;oBACzB,IAAI,OAAO;wBACT,MAAM,cAAc,CAAC;oBACvB;gBACF;QACF;IAEJ;IAEA,0CAA0C;IAC1C,IAAI,OAAO,QAAQ,EAAE;QACnB,QAAQ,CAAA,GAAA,6IAAA,CAAA,WAAQ,AAAD,EACb,OACA;YACE,MAAM,OAAO,QAAQ,CAAC,IAAI;YAC1B,SAAS,OAAO,QAAQ,CAAC,OAAO,IAAI,oDAAyB;QAC/D;IAEJ;IAEA,OAAO;AACT;AAUO,SAAS;IACd,OAAO;QACL,cAAc;QACd,gBAAgB,CAAC;QACf,+CAA+C;QACjD;IACF;AACF;AAMO,SAAS,uBACd,GAAkC;IAElC,OAAO;QACL,gBAAgB,CAAC;YACf,IAAI;gBAAE,cAAc;YAAY;QAClC;IACF;AACF;AASO,SAAS,eAAe,SAAiB;IAC9C,OAAO,KAAK,GAAG,MAAM;AACvB;AAMO,SAAS,mBAAmB,KAAa,EAAE,iBAAyB,EAAE;IAC3E,IAAI;QACF,4CAA4C;QAC5C,MAAM,UAAU,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,CAAC,IAAI,CAAC,EAAE;QACnD,IAAI,QAAQ,GAAG,EAAE;YACf,OAAO,QAAQ,GAAG,GAAG,MAAM,0BAA0B;QACvD;IACF,EAAE,OAAO,OAAO;IACd,+CAA+C;IACjD;IAEA,oDAAoD;IACpD,OAAO,KAAK,GAAG,KAAM,iBAAiB,KAAK;AAC7C;AAKO,SAAS;IACd,uCAAmC;;IAGnC;AACF;AASO,SAAS,eACd,YAAoB,EACpB,cAAsB;IAEtB,MAAM,MAAM,KAAK,GAAG;IACpB,MAAM,YAAY,iBAAiB,KAAK,MAAM,kCAAkC;IAChF,OAAO,AAAC,MAAM,eAAgB;AAChC;AAKO,SAAS,wBACd,YAAoB,EACpB,cAAsB;IAEtB,MAAM,MAAM,KAAK,GAAG;IACpB,MAAM,YAAY,iBAAiB,KAAK;IACxC,MAAM,UAAU,MAAM;IACtB,MAAM,YAAY,YAAY;IAC9B,OAAO,KAAK,GAAG,CAAC,GAAG,KAAK,KAAK,CAAC,YAAY,CAAC,KAAK,IAAI;AACtD;AASO,SAAS,oBAAoB,KAAU;IAC5C,IAAI,OAAO,UAAU,UAAU;QAC7B,OAAO;IACT;IAEA,IAAI,OAAO,UAAU,MAAM,SAAS;QAClC,OAAO,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO;IACpC;IAEA,IAAI,OAAO,SAAS;QAClB,OAAO,MAAM,OAAO;IACtB;IAEA,IAAI,OAAO,OAAO;QAChB,OAAO,MAAM,KAAK;IACpB;IAEA,OAAO;AACT;AAKO,SAAS,kBACd,OAAe,EACf,OAAa;IAEb,OAAO;QACL;QACA,SAAS,WAAW;IACtB;AACF;AASO,SAAS;IACd,OAAO,CAAC,aAAa,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;AAChF;AAKO,SAAS,+BACd,IAA8C;IAE9C,OAAQ;QACN,KAAK;YACH,OAAO,MAAM,YAAY;QAC3B,KAAK;YACH,OAAO,MAAM,YAAY;QAC3B,KAAK;YACH,OAAO,MAAM,YAAY;QAC3B,KAAK;YACH,OAAO,MAAM,YAAY;QAC3B;YACE,OAAO;IACX;AACF;AASO,MAAM,UAAU;IACrB,KAAK,CAAC;QACJ,wCAAmC,OAAO;;IAS5C;IAEA,KAAK,CAAC,KAAa;QACjB,wCAAmC;;IAOrC;IAEA,QAAQ,CAAC;QACP,wCAAmC;;IAOrC;IAEA,OAAO;QACL,wCAAmC;;IAOrC;AACF;AASO,SAAS,eACd,SAAiB,EACjB,UAAkB,EAClB,OAAa;IAEb,wCAA4C;QAC1C,QAAQ,KAAK,CAAC,CAAC,IAAI,EAAE,UAAU,EAAE,EAAE,YAAY;QAC/C,IAAI,YAAY,WAAW;YACzB,QAAQ,GAAG,CAAC,YAAY;QAC1B;QACA,QAAQ,GAAG,CAAC,cAAc,IAAI,OAAO,WAAW;QAChD,QAAQ,QAAQ;IAClB;AACF;AAKO,SAAS,mBACd,KAAQ,EACR,YAAyB;IAEzB,KAAK,MAAM,OAAO,aAAc;QAC9B,IAAI,CAAC,CAAC,OAAO,KAAK,GAAG;YACnB,QAAQ,KAAK,CAAC,CAAC,kCAAkC,EAAE,OAAO,MAAM;YAChE,OAAO;QACT;IACF;IACA,OAAO;AACT"}}, {"offset": {"line": 313, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 319, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/stores/constants.ts"], "sourcesContent": ["/**\n * Store Constants\n * Defines constants used across all stores\n */\n\n// ============================================================================\n// Store Names\n// ============================================================================\n\nexport const STORE_NAMES = {\n  AUTH: 'auth-store',\n  APP: 'app-store',\n} as const;\n\n// ============================================================================\n// Storage Keys\n// ============================================================================\n\nexport const STORAGE_KEYS = {\n  AUTH: 'auth-storage',\n  APP: 'app-storage',\n  THEME: 'theme-storage',\n  SETTINGS: 'settings-storage',\n} as const;\n\n// ============================================================================\n// Default Values\n// ============================================================================\n\n/**\n * Default theme configuration\n */\nexport const DEFAULT_THEME = {\n  mode: 'light' as const,\n  primaryColor: '#1890ff',\n  borderRadius: 6,\n  compactMode: false,\n};\n\n/**\n * Default application settings\n */\nexport const DEFAULT_APP_SETTINGS = {\n  language: 'en' as const,\n  timezone: 'UTC',\n  dateFormat: 'YYYY-MM-DD',\n  pageSize: 20,\n  autoRefresh: true,\n  refreshInterval: 30, // seconds\n  features: {\n    darkMode: true,\n    notifications: true,\n    autoSave: true,\n    advancedFilters: true,\n  },\n};\n\n/**\n * Default navigation state\n */\nexport const DEFAULT_NAVIGATION = {\n  currentPath: '/',\n  breadcrumbs: [],\n  sidebarCollapsed: false,\n  activeMenuKey: 'dashboard',\n};\n\n/**\n * Default UI state\n */\nexport const DEFAULT_UI_STATE = {\n  globalLoading: false,\n  loadingMessage: '',\n  globalError: null,\n  errorDetails: null,\n  notifications: [],\n  modals: {},\n};\n\n// ============================================================================\n// Session Configuration\n// ============================================================================\n\n/**\n * Session timeout in minutes\n */\nexport const SESSION_TIMEOUT = 60; // 1 hour\n\n/**\n * Token refresh threshold in minutes\n * Refresh token when it expires in less than this time\n */\nexport const TOKEN_REFRESH_THRESHOLD = 5; // 5 minutes\n\n/**\n * Activity tracking interval in milliseconds\n */\nexport const ACTIVITY_TRACKING_INTERVAL = 60000; // 1 minute\n\n// ============================================================================\n// API Configuration\n// ============================================================================\n\n/**\n * API endpoints for store operations\n */\nexport const STORE_API_ENDPOINTS = {\n  AUTH: {\n    LOGIN: '/api/system-auth/login',\n    LOGOUT: '/api/system-auth/logout',\n    LOGOUT_ALL: '/api/system-auth/logout-all',\n    PROFILE: '/api/system-auth/profile',\n    REFRESH: '/api/system-auth/refresh',\n  },\n} as const;\n\n// ============================================================================\n// Error Messages\n// ============================================================================\n\nexport const ERROR_MESSAGES = {\n  AUTH: {\n    LOGIN_FAILED: 'Login failed. Please check your credentials.',\n    LOGOUT_FAILED: 'Logout failed. Please try again.',\n    SESSION_EXPIRED: 'Your session has expired. Please log in again.',\n    TOKEN_REFRESH_FAILED: 'Failed to refresh authentication token.',\n    PROFILE_UPDATE_FAILED: 'Failed to update profile. Please try again.',\n    UNAUTHORIZED: 'You are not authorized to perform this action.',\n  },\n  APP: {\n    SETTINGS_SAVE_FAILED: 'Failed to save settings. Please try again.',\n    THEME_LOAD_FAILED: 'Failed to load theme configuration.',\n    NETWORK_ERROR: 'Network error. Please check your connection.',\n    UNKNOWN_ERROR: 'An unexpected error occurred. Please try again.',\n  },\n} as const;\n\n// ============================================================================\n// Success Messages\n// ============================================================================\n\nexport const SUCCESS_MESSAGES = {\n  AUTH: {\n    LOGIN_SUCCESS: 'Successfully logged in.',\n    LOGOUT_SUCCESS: 'Successfully logged out.',\n    PROFILE_UPDATED: 'Profile updated successfully.',\n  },\n  APP: {\n    SETTINGS_SAVED: 'Settings saved successfully.',\n    THEME_UPDATED: 'Theme updated successfully.',\n  },\n} as const;\n\n// ============================================================================\n// Store Versions\n// ============================================================================\n\n/**\n * Store versions for migration support\n */\nexport const STORE_VERSIONS = {\n  AUTH: 1,\n  APP: 1,\n} as const;\n\n// ============================================================================\n// Development Configuration\n// ============================================================================\n\n/**\n * Development mode configuration\n */\nexport const DEV_CONFIG = {\n  ENABLE_DEVTOOLS: process.env.NODE_ENV === 'development',\n  ENABLE_LOGGING: process.env.NODE_ENV === 'development',\n  MOCK_API_DELAY: 1000, // milliseconds\n} as const;\n\n// ============================================================================\n// Feature Flags\n// ============================================================================\n\n/**\n * Feature flags for conditional functionality\n */\nexport const FEATURE_FLAGS = {\n  ENABLE_DARK_MODE: true,\n  ENABLE_NOTIFICATIONS: true,\n  ENABLE_AUTO_SAVE: true,\n  ENABLE_ADVANCED_FILTERS: true,\n  ENABLE_REAL_TIME_UPDATES: false, // Future feature\n  ENABLE_OFFLINE_MODE: false, // Future feature\n} as const;\n\n// ============================================================================\n// Validation Rules\n// ============================================================================\n\n/**\n * Validation rules for store data\n */\nexport const VALIDATION_RULES = {\n  EMAIL: /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/,\n  PASSWORD_MIN_LENGTH: 8,\n  NAME_MIN_LENGTH: 2,\n  NAME_MAX_LENGTH: 50,\n  PAGE_SIZE_MIN: 5,\n  PAGE_SIZE_MAX: 100,\n  REFRESH_INTERVAL_MIN: 10, // seconds\n  REFRESH_INTERVAL_MAX: 300, // seconds\n} as const;\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,+EAA+E;AAC/E,cAAc;AACd,+EAA+E;;;;;;;;;;;;;;;;;;;AAExE,MAAM,cAAc;IACzB,MAAM;IACN,KAAK;AACP;AAMO,MAAM,eAAe;IAC1B,MAAM;IACN,KAAK;IACL,OAAO;IACP,UAAU;AACZ;AASO,MAAM,gBAAgB;IAC3B,MAAM;IACN,cAAc;IACd,cAAc;IACd,aAAa;AACf;AAKO,MAAM,uBAAuB;IAClC,UAAU;IACV,UAAU;IACV,YAAY;IACZ,UAAU;IACV,aAAa;IACb,iBAAiB;IACjB,UAAU;QACR,UAAU;QACV,eAAe;QACf,UAAU;QACV,iBAAiB;IACnB;AACF;AAKO,MAAM,qBAAqB;IAChC,aAAa;IACb,aAAa,EAAE;IACf,kBAAkB;IAClB,eAAe;AACjB;AAKO,MAAM,mBAAmB;IAC9B,eAAe;IACf,gBAAgB;IAChB,aAAa;IACb,cAAc;IACd,eAAe,EAAE;IACjB,QAAQ,CAAC;AACX;AASO,MAAM,kBAAkB,IAAI,SAAS;AAMrC,MAAM,0BAA0B,GAAG,YAAY;AAK/C,MAAM,6BAA6B,OAAO,WAAW;AASrD,MAAM,sBAAsB;IACjC,MAAM;QACJ,OAAO;QACP,QAAQ;QACR,YAAY;QACZ,SAAS;QACT,SAAS;IACX;AACF;AAMO,MAAM,iBAAiB;IAC5B,MAAM;QACJ,cAAc;QACd,eAAe;QACf,iBAAiB;QACjB,sBAAsB;QACtB,uBAAuB;QACvB,cAAc;IAChB;IACA,KAAK;QACH,sBAAsB;QACtB,mBAAmB;QACnB,eAAe;QACf,eAAe;IACjB;AACF;AAMO,MAAM,mBAAmB;IAC9B,MAAM;QACJ,eAAe;QACf,gBAAgB;QAChB,iBAAiB;IACnB;IACA,KAAK;QACH,gBAAgB;QAChB,eAAe;IACjB;AACF;AASO,MAAM,iBAAiB;IAC5B,MAAM;IACN,KAAK;AACP;AASO,MAAM,aAAa;IACxB,iBAAiB,oDAAyB;IAC1C,gBAAgB,oDAAyB;IACzC,gBAAgB;AAClB;AASO,MAAM,gBAAgB;IAC3B,kBAAkB;IAClB,sBAAsB;IACtB,kBAAkB;IAClB,yBAAyB;IACzB,0BAA0B;IAC1B,qBAAqB;AACvB;AASO,MAAM,mBAAmB;IAC9B,OAAO;IACP,qBAAqB;IACrB,iBAAiB;IACjB,iBAAiB;IACjB,eAAe;IACf,eAAe;IACf,sBAAsB;IACtB,sBAAsB;AACxB"}}, {"offset": {"line": 453, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 459, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/stores/auth-store.ts"], "sourcesContent": ["/**\n * Authentication Store\n * Manages user authentication state, tokens, and session\n */\n\nimport { create } from 'zustand';\nimport {\n  AuthStore,\n  SystemUser,\n  AuthTokens,\n  SystemUserRole\n} from './types';\nimport {\n  createStoreWithMiddleware,\n  createBaseStoreActions,\n  extractErrorMessage,\n  isTokenExpired,\n  getTokenExpiration,\n  clearStoredTokens,\n  isSessionValid,\n  logStoreAction\n} from './utils';\nimport {\n  STORE_NAMES,\n  STORAGE_KEYS,\n  SESSION_TIMEOUT,\n  TOKEN_REFRESH_THRESHOLD,\n  STORE_API_ENDPOINTS,\n  ERROR_MESSAGES,\n  SUCCESS_MESSAGES,\n  STORE_VERSIONS\n} from './constants';\n\n// ============================================================================\n// Initial State\n// ============================================================================\n\nconst initialAuthState = {\n  // Base store state\n  _hasHydrated: false,\n\n  // User data\n  user: null,\n  tokens: null,\n\n  // Authentication status\n  isAuthenticated: false,\n  isLoading: false,\n\n  // Error handling\n  error: null,\n\n  // Session management\n  lastActivity: Date.now(),\n  sessionTimeout: SESSION_TIMEOUT,\n};\n\n// ============================================================================\n// Store Implementation\n// ============================================================================\n\n/**\n * Authentication Store Creator\n */\nconst createAuthStore = () => {\n  return create<AuthStore>()(\n    createStoreWithMiddleware<AuthStore>(\n      (set, get) => ({\n        ...initialAuthState,\n\n        // Base store actions\n        ...createBaseStoreActions<AuthStore>(set),\n\n        // ========================================================================\n        // Authentication Actions\n        // ========================================================================\n\n        /**\n         * Login user with email and password\n         */\n        login: async (email: string, password: string) => {\n          logStoreAction(STORE_NAMES.AUTH, 'login', { email });\n\n          set({ isLoading: true, error: null });\n\n          try {\n            const response = await fetch(STORE_API_ENDPOINTS.AUTH.LOGIN, {\n              method: 'POST',\n              headers: {\n                'Content-Type': 'application/json',\n              },\n              body: JSON.stringify({ email, password }),\n            });\n\n            if (!response.ok) {\n              const errorData = await response.json().catch(() => ({}));\n              throw new Error(errorData.message || ERROR_MESSAGES.AUTH.LOGIN_FAILED);\n            }\n\n            const data = await response.json();\n\n            if (data.success && data.data) {\n              const { user, accessToken, refreshToken } = data.data;\n\n              // Create tokens object\n              const tokens: AuthTokens = {\n                accessToken,\n                refreshToken,\n                expiresAt: getTokenExpiration(accessToken),\n              };\n\n              // Update store state\n              set({\n                user,\n                tokens,\n                isAuthenticated: true,\n                isLoading: false,\n                error: null,\n                lastActivity: Date.now(),\n              });\n\n              logStoreAction(STORE_NAMES.AUTH, 'login_success', { userId: user.id });\n            } else {\n              throw new Error(data.message || ERROR_MESSAGES.AUTH.LOGIN_FAILED);\n            }\n          } catch (error) {\n            const errorMessage = extractErrorMessage(error);\n            logStoreAction(STORE_NAMES.AUTH, 'login_error', { error: errorMessage });\n\n            set({\n              isLoading: false,\n              error: errorMessage,\n              isAuthenticated: false,\n              user: null,\n              tokens: null,\n            });\n\n            throw error;\n          }\n        },\n\n        /**\n         * Logout user from current session\n         */\n        logout: async () => {\n          logStoreAction(STORE_NAMES.AUTH, 'logout');\n\n          const { tokens } = get();\n\n          try {\n            // Call logout API if we have tokens\n            if (tokens?.accessToken) {\n              await fetch(STORE_API_ENDPOINTS.AUTH.LOGOUT, {\n                method: 'POST',\n                headers: {\n                  'Content-Type': 'application/json',\n                  'Authorization': `Bearer ${tokens.accessToken}`,\n                },\n              });\n            }\n          } catch (error) {\n            // Log error but don't prevent logout\n            logStoreAction(STORE_NAMES.AUTH, 'logout_api_error', { error: extractErrorMessage(error) });\n          }\n\n          // Clear local state regardless of API call result\n          clearStoredTokens();\n          set({\n            user: null,\n            tokens: null,\n            isAuthenticated: false,\n            error: null,\n            lastActivity: Date.now(),\n          });\n\n          logStoreAction(STORE_NAMES.AUTH, 'logout_success');\n        },\n\n        /**\n         * Logout user from all devices\n         */\n        logoutAll: async () => {\n          logStoreAction(STORE_NAMES.AUTH, 'logout_all');\n\n          const { tokens } = get();\n\n          try {\n            if (tokens?.accessToken) {\n              await fetch(STORE_API_ENDPOINTS.AUTH.LOGOUT_ALL, {\n                method: 'POST',\n                headers: {\n                  'Content-Type': 'application/json',\n                  'Authorization': `Bearer ${tokens.accessToken}`,\n                },\n              });\n            }\n          } catch (error) {\n            logStoreAction(STORE_NAMES.AUTH, 'logout_all_api_error', { error: extractErrorMessage(error) });\n          }\n\n          // Clear local state\n          clearStoredTokens();\n          set({\n            user: null,\n            tokens: null,\n            isAuthenticated: false,\n            error: null,\n            lastActivity: Date.now(),\n          });\n\n          logStoreAction(STORE_NAMES.AUTH, 'logout_all_success');\n        },\n\n        // ========================================================================\n        // User Profile Actions\n        // ========================================================================\n\n        /**\n         * Update user profile\n         */\n        updateProfile: async (data: Partial<SystemUser>) => {\n          logStoreAction(STORE_NAMES.AUTH, 'update_profile', data);\n\n          const { tokens, user } = get();\n\n          if (!tokens?.accessToken || !user) {\n            throw new Error(ERROR_MESSAGES.AUTH.UNAUTHORIZED);\n          }\n\n          set({ isLoading: true, error: null });\n\n          try {\n            const response = await fetch(STORE_API_ENDPOINTS.AUTH.PROFILE, {\n              method: 'PUT',\n              headers: {\n                'Content-Type': 'application/json',\n                'Authorization': `Bearer ${tokens.accessToken}`,\n              },\n              body: JSON.stringify(data),\n            });\n\n            if (!response.ok) {\n              const errorData = await response.json().catch(() => ({}));\n              throw new Error(errorData.message || ERROR_MESSAGES.AUTH.PROFILE_UPDATE_FAILED);\n            }\n\n            const responseData = await response.json();\n\n            if (responseData.success && responseData.data) {\n              set({\n                user: { ...user, ...responseData.data },\n                isLoading: false,\n                error: null,\n                lastActivity: Date.now(),\n              });\n\n              logStoreAction(STORE_NAMES.AUTH, 'update_profile_success');\n            } else {\n              throw new Error(responseData.message || ERROR_MESSAGES.AUTH.PROFILE_UPDATE_FAILED);\n            }\n          } catch (error) {\n            const errorMessage = extractErrorMessage(error);\n            logStoreAction(STORE_NAMES.AUTH, 'update_profile_error', { error: errorMessage });\n\n            set({\n              isLoading: false,\n              error: errorMessage,\n            });\n\n            throw error;\n          }\n        },\n\n        /**\n         * Refresh authentication tokens\n         */\n        refreshTokens: async () => {\n          logStoreAction(STORE_NAMES.AUTH, 'refresh_tokens');\n\n          const { tokens } = get();\n\n          if (!tokens?.refreshToken) {\n            throw new Error(ERROR_MESSAGES.AUTH.TOKEN_REFRESH_FAILED);\n          }\n\n          try {\n            const response = await fetch(STORE_API_ENDPOINTS.AUTH.REFRESH, {\n              method: 'POST',\n              headers: {\n                'Content-Type': 'application/json',\n              },\n              body: JSON.stringify({ refreshToken: tokens.refreshToken }),\n            });\n\n            if (!response.ok) {\n              throw new Error(ERROR_MESSAGES.AUTH.TOKEN_REFRESH_FAILED);\n            }\n\n            const data = await response.json();\n\n            if (data.success && data.data) {\n              const { accessToken, refreshToken } = data.data;\n\n              const newTokens: AuthTokens = {\n                accessToken,\n                refreshToken,\n                expiresAt: getTokenExpiration(accessToken),\n              };\n\n              set({\n                tokens: newTokens,\n                lastActivity: Date.now(),\n              });\n\n              logStoreAction(STORE_NAMES.AUTH, 'refresh_tokens_success');\n            } else {\n              throw new Error(data.message || ERROR_MESSAGES.AUTH.TOKEN_REFRESH_FAILED);\n            }\n          } catch (error) {\n            const errorMessage = extractErrorMessage(error);\n            logStoreAction(STORE_NAMES.AUTH, 'refresh_tokens_error', { error: errorMessage });\n\n            // If refresh fails, logout user\n            get().logout();\n            throw error;\n          }\n        },\n\n        // ========================================================================\n        // State Management Actions\n        // ========================================================================\n\n        /**\n         * Set user data\n         */\n        setUser: (user: SystemUser | null) => {\n          set({ user, isAuthenticated: !!user });\n          logStoreAction(STORE_NAMES.AUTH, 'set_user', { userId: user?.id });\n        },\n\n        /**\n         * Set authentication tokens\n         */\n        setTokens: (tokens: AuthTokens | null) => {\n          set({ tokens });\n          logStoreAction(STORE_NAMES.AUTH, 'set_tokens', { hasTokens: !!tokens });\n        },\n\n        /**\n         * Set loading state\n         */\n        setLoading: (loading: boolean) => {\n          set({ isLoading: loading });\n        },\n\n        /**\n         * Set error message\n         */\n        setError: (error: string | null) => {\n          set({ error });\n          if (error) {\n            logStoreAction(STORE_NAMES.AUTH, 'set_error', { error });\n          }\n        },\n\n        /**\n         * Clear error message\n         */\n        clearError: () => {\n          set({ error: null });\n        },\n\n        // ========================================================================\n        // Session Management Actions\n        // ========================================================================\n\n        /**\n         * Update last activity timestamp\n         */\n        updateLastActivity: () => {\n          set({ lastActivity: Date.now() });\n        },\n\n        /**\n         * Check if current session is valid\n         */\n        checkSession: () => {\n          const { lastActivity, sessionTimeout, tokens } = get();\n\n          // Check session timeout\n          if (!isSessionValid(lastActivity, sessionTimeout)) {\n            logStoreAction(STORE_NAMES.AUTH, 'session_expired');\n            get().logout();\n            return false;\n          }\n\n          // Check token expiration\n          if (tokens && isTokenExpired(tokens.expiresAt)) {\n            logStoreAction(STORE_NAMES.AUTH, 'token_expired');\n\n            // Try to refresh tokens\n            get().refreshTokens().catch(() => {\n              // If refresh fails, logout will be called automatically\n            });\n\n            return false;\n          }\n\n          return true;\n        },\n\n        /**\n         * Hydrate store from persisted state\n         */\n        hydrate: () => {\n          const state = get();\n\n          // Validate persisted session\n          if (state.isAuthenticated && state.user && state.tokens) {\n            const isValid = state.checkSession();\n            if (!isValid) {\n              // Session is invalid, clear state\n              set({\n                user: null,\n                tokens: null,\n                isAuthenticated: false,\n                error: null,\n              });\n            }\n          }\n\n          set({ _hasHydrated: true });\n          logStoreAction(STORE_NAMES.AUTH, 'hydrated');\n        },\n      }),\n      {\n        persist: {\n          name: STORAGE_KEYS.AUTH,\n          version: STORE_VERSIONS.AUTH,\n          partialize: (state) => ({\n            user: state.user,\n            tokens: state.tokens,\n            isAuthenticated: state.isAuthenticated,\n            lastActivity: state.lastActivity,\n            sessionTimeout: state.sessionTimeout,\n          }),\n        },\n        devtools: {\n          name: STORE_NAMES.AUTH,\n          enabled: process.env.NODE_ENV === 'development',\n        },\n      }\n    )\n  );\n};\n\n// ============================================================================\n// Export Store\n// ============================================================================\n\nexport const useAuthStore = createAuthStore();\n\n// Export store for testing and advanced usage\nexport { createAuthStore };\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AASD;AAUA;AAjBA;;;;AA4BA,+EAA+E;AAC/E,gBAAgB;AAChB,+EAA+E;AAE/E,MAAM,mBAAmB;IACvB,mBAAmB;IACnB,cAAc;IAEd,YAAY;IACZ,MAAM;IACN,QAAQ;IAER,wBAAwB;IACxB,iBAAiB;IACjB,WAAW;IAEX,iBAAiB;IACjB,OAAO;IAEP,qBAAqB;IACrB,cAAc,KAAK,GAAG;IACtB,gBAAgB,0HAAA,CAAA,kBAAe;AACjC;AAEA,+EAA+E;AAC/E,uBAAuB;AACvB,+EAA+E;AAE/E;;CAEC,GACD,MAAM,kBAAkB;IACtB,OAAO,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,IACV,CAAA,GAAA,sHAAA,CAAA,4BAAyB,AAAD,EACtB,CAAC,KAAK,MAAQ,CAAC;YACb,GAAG,gBAAgB;YAEnB,qBAAqB;YACrB,GAAG,CAAA,GAAA,sHAAA,CAAA,yBAAsB,AAAD,EAAa,IAAI;YAEzC,2EAA2E;YAC3E,yBAAyB;YACzB,2EAA2E;YAE3E;;SAEC,GACD,OAAO,OAAO,OAAe;gBAC3B,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,0HAAA,CAAA,cAAW,CAAC,IAAI,EAAE,SAAS;oBAAE;gBAAM;gBAElD,IAAI;oBAAE,WAAW;oBAAM,OAAO;gBAAK;gBAEnC,IAAI;oBACF,MAAM,WAAW,MAAM,MAAM,0HAAA,CAAA,sBAAmB,CAAC,IAAI,CAAC,KAAK,EAAE;wBAC3D,QAAQ;wBACR,SAAS;4BACP,gBAAgB;wBAClB;wBACA,MAAM,KAAK,SAAS,CAAC;4BAAE;4BAAO;wBAAS;oBACzC;oBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;wBAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;wBACvD,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,0HAAA,CAAA,iBAAc,CAAC,IAAI,CAAC,YAAY;oBACvE;oBAEA,MAAM,OAAO,MAAM,SAAS,IAAI;oBAEhC,IAAI,KAAK,OAAO,IAAI,KAAK,IAAI,EAAE;wBAC7B,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,YAAY,EAAE,GAAG,KAAK,IAAI;wBAErD,uBAAuB;wBACvB,MAAM,SAAqB;4BACzB;4BACA;4BACA,WAAW,CAAA,GAAA,sHAAA,CAAA,qBAAkB,AAAD,EAAE;wBAChC;wBAEA,qBAAqB;wBACrB,IAAI;4BACF;4BACA;4BACA,iBAAiB;4BACjB,WAAW;4BACX,OAAO;4BACP,cAAc,KAAK,GAAG;wBACxB;wBAEA,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,0HAAA,CAAA,cAAW,CAAC,IAAI,EAAE,iBAAiB;4BAAE,QAAQ,KAAK,EAAE;wBAAC;oBACtE,OAAO;wBACL,MAAM,IAAI,MAAM,KAAK,OAAO,IAAI,0HAAA,CAAA,iBAAc,CAAC,IAAI,CAAC,YAAY;oBAClE;gBACF,EAAE,OAAO,OAAO;oBACd,MAAM,eAAe,CAAA,GAAA,sHAAA,CAAA,sBAAmB,AAAD,EAAE;oBACzC,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,0HAAA,CAAA,cAAW,CAAC,IAAI,EAAE,eAAe;wBAAE,OAAO;oBAAa;oBAEtE,IAAI;wBACF,WAAW;wBACX,OAAO;wBACP,iBAAiB;wBACjB,MAAM;wBACN,QAAQ;oBACV;oBAEA,MAAM;gBACR;YACF;YAEA;;SAEC,GACD,QAAQ;gBACN,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,0HAAA,CAAA,cAAW,CAAC,IAAI,EAAE;gBAEjC,MAAM,EAAE,MAAM,EAAE,GAAG;gBAEnB,IAAI;oBACF,oCAAoC;oBACpC,IAAI,QAAQ,aAAa;wBACvB,MAAM,MAAM,0HAAA,CAAA,sBAAmB,CAAC,IAAI,CAAC,MAAM,EAAE;4BAC3C,QAAQ;4BACR,SAAS;gCACP,gBAAgB;gCAChB,iBAAiB,CAAC,OAAO,EAAE,OAAO,WAAW,EAAE;4BACjD;wBACF;oBACF;gBACF,EAAE,OAAO,OAAO;oBACd,qCAAqC;oBACrC,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,0HAAA,CAAA,cAAW,CAAC,IAAI,EAAE,oBAAoB;wBAAE,OAAO,CAAA,GAAA,sHAAA,CAAA,sBAAmB,AAAD,EAAE;oBAAO;gBAC3F;gBAEA,kDAAkD;gBAClD,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD;gBAChB,IAAI;oBACF,MAAM;oBACN,QAAQ;oBACR,iBAAiB;oBACjB,OAAO;oBACP,cAAc,KAAK,GAAG;gBACxB;gBAEA,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,0HAAA,CAAA,cAAW,CAAC,IAAI,EAAE;YACnC;YAEA;;SAEC,GACD,WAAW;gBACT,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,0HAAA,CAAA,cAAW,CAAC,IAAI,EAAE;gBAEjC,MAAM,EAAE,MAAM,EAAE,GAAG;gBAEnB,IAAI;oBACF,IAAI,QAAQ,aAAa;wBACvB,MAAM,MAAM,0HAAA,CAAA,sBAAmB,CAAC,IAAI,CAAC,UAAU,EAAE;4BAC/C,QAAQ;4BACR,SAAS;gCACP,gBAAgB;gCAChB,iBAAiB,CAAC,OAAO,EAAE,OAAO,WAAW,EAAE;4BACjD;wBACF;oBACF;gBACF,EAAE,OAAO,OAAO;oBACd,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,0HAAA,CAAA,cAAW,CAAC,IAAI,EAAE,wBAAwB;wBAAE,OAAO,CAAA,GAAA,sHAAA,CAAA,sBAAmB,AAAD,EAAE;oBAAO;gBAC/F;gBAEA,oBAAoB;gBACpB,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD;gBAChB,IAAI;oBACF,MAAM;oBACN,QAAQ;oBACR,iBAAiB;oBACjB,OAAO;oBACP,cAAc,KAAK,GAAG;gBACxB;gBAEA,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,0HAAA,CAAA,cAAW,CAAC,IAAI,EAAE;YACnC;YAEA,2EAA2E;YAC3E,uBAAuB;YACvB,2EAA2E;YAE3E;;SAEC,GACD,eAAe,OAAO;gBACpB,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,0HAAA,CAAA,cAAW,CAAC,IAAI,EAAE,kBAAkB;gBAEnD,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG;gBAEzB,IAAI,CAAC,QAAQ,eAAe,CAAC,MAAM;oBACjC,MAAM,IAAI,MAAM,0HAAA,CAAA,iBAAc,CAAC,IAAI,CAAC,YAAY;gBAClD;gBAEA,IAAI;oBAAE,WAAW;oBAAM,OAAO;gBAAK;gBAEnC,IAAI;oBACF,MAAM,WAAW,MAAM,MAAM,0HAAA,CAAA,sBAAmB,CAAC,IAAI,CAAC,OAAO,EAAE;wBAC7D,QAAQ;wBACR,SAAS;4BACP,gBAAgB;4BAChB,iBAAiB,CAAC,OAAO,EAAE,OAAO,WAAW,EAAE;wBACjD;wBACA,MAAM,KAAK,SAAS,CAAC;oBACvB;oBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;wBAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;wBACvD,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,0HAAA,CAAA,iBAAc,CAAC,IAAI,CAAC,qBAAqB;oBAChF;oBAEA,MAAM,eAAe,MAAM,SAAS,IAAI;oBAExC,IAAI,aAAa,OAAO,IAAI,aAAa,IAAI,EAAE;wBAC7C,IAAI;4BACF,MAAM;gCAAE,GAAG,IAAI;gCAAE,GAAG,aAAa,IAAI;4BAAC;4BACtC,WAAW;4BACX,OAAO;4BACP,cAAc,KAAK,GAAG;wBACxB;wBAEA,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,0HAAA,CAAA,cAAW,CAAC,IAAI,EAAE;oBACnC,OAAO;wBACL,MAAM,IAAI,MAAM,aAAa,OAAO,IAAI,0HAAA,CAAA,iBAAc,CAAC,IAAI,CAAC,qBAAqB;oBACnF;gBACF,EAAE,OAAO,OAAO;oBACd,MAAM,eAAe,CAAA,GAAA,sHAAA,CAAA,sBAAmB,AAAD,EAAE;oBACzC,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,0HAAA,CAAA,cAAW,CAAC,IAAI,EAAE,wBAAwB;wBAAE,OAAO;oBAAa;oBAE/E,IAAI;wBACF,WAAW;wBACX,OAAO;oBACT;oBAEA,MAAM;gBACR;YACF;YAEA;;SAEC,GACD,eAAe;gBACb,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,0HAAA,CAAA,cAAW,CAAC,IAAI,EAAE;gBAEjC,MAAM,EAAE,MAAM,EAAE,GAAG;gBAEnB,IAAI,CAAC,QAAQ,cAAc;oBACzB,MAAM,IAAI,MAAM,0HAAA,CAAA,iBAAc,CAAC,IAAI,CAAC,oBAAoB;gBAC1D;gBAEA,IAAI;oBACF,MAAM,WAAW,MAAM,MAAM,0HAAA,CAAA,sBAAmB,CAAC,IAAI,CAAC,OAAO,EAAE;wBAC7D,QAAQ;wBACR,SAAS;4BACP,gBAAgB;wBAClB;wBACA,MAAM,KAAK,SAAS,CAAC;4BAAE,cAAc,OAAO,YAAY;wBAAC;oBAC3D;oBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;wBAChB,MAAM,IAAI,MAAM,0HAAA,CAAA,iBAAc,CAAC,IAAI,CAAC,oBAAoB;oBAC1D;oBAEA,MAAM,OAAO,MAAM,SAAS,IAAI;oBAEhC,IAAI,KAAK,OAAO,IAAI,KAAK,IAAI,EAAE;wBAC7B,MAAM,EAAE,WAAW,EAAE,YAAY,EAAE,GAAG,KAAK,IAAI;wBAE/C,MAAM,YAAwB;4BAC5B;4BACA;4BACA,WAAW,CAAA,GAAA,sHAAA,CAAA,qBAAkB,AAAD,EAAE;wBAChC;wBAEA,IAAI;4BACF,QAAQ;4BACR,cAAc,KAAK,GAAG;wBACxB;wBAEA,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,0HAAA,CAAA,cAAW,CAAC,IAAI,EAAE;oBACnC,OAAO;wBACL,MAAM,IAAI,MAAM,KAAK,OAAO,IAAI,0HAAA,CAAA,iBAAc,CAAC,IAAI,CAAC,oBAAoB;oBAC1E;gBACF,EAAE,OAAO,OAAO;oBACd,MAAM,eAAe,CAAA,GAAA,sHAAA,CAAA,sBAAmB,AAAD,EAAE;oBACzC,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,0HAAA,CAAA,cAAW,CAAC,IAAI,EAAE,wBAAwB;wBAAE,OAAO;oBAAa;oBAE/E,gCAAgC;oBAChC,MAAM,MAAM;oBACZ,MAAM;gBACR;YACF;YAEA,2EAA2E;YAC3E,2BAA2B;YAC3B,2EAA2E;YAE3E;;SAEC,GACD,SAAS,CAAC;gBACR,IAAI;oBAAE;oBAAM,iBAAiB,CAAC,CAAC;gBAAK;gBACpC,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,0HAAA,CAAA,cAAW,CAAC,IAAI,EAAE,YAAY;oBAAE,QAAQ,MAAM;gBAAG;YAClE;YAEA;;SAEC,GACD,WAAW,CAAC;gBACV,IAAI;oBAAE;gBAAO;gBACb,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,0HAAA,CAAA,cAAW,CAAC,IAAI,EAAE,cAAc;oBAAE,WAAW,CAAC,CAAC;gBAAO;YACvE;YAEA;;SAEC,GACD,YAAY,CAAC;gBACX,IAAI;oBAAE,WAAW;gBAAQ;YAC3B;YAEA;;SAEC,GACD,UAAU,CAAC;gBACT,IAAI;oBAAE;gBAAM;gBACZ,IAAI,OAAO;oBACT,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,0HAAA,CAAA,cAAW,CAAC,IAAI,EAAE,aAAa;wBAAE;oBAAM;gBACxD;YACF;YAEA;;SAEC,GACD,YAAY;gBACV,IAAI;oBAAE,OAAO;gBAAK;YACpB;YAEA,2EAA2E;YAC3E,6BAA6B;YAC7B,2EAA2E;YAE3E;;SAEC,GACD,oBAAoB;gBAClB,IAAI;oBAAE,cAAc,KAAK,GAAG;gBAAG;YACjC;YAEA;;SAEC,GACD,cAAc;gBACZ,MAAM,EAAE,YAAY,EAAE,cAAc,EAAE,MAAM,EAAE,GAAG;gBAEjD,wBAAwB;gBACxB,IAAI,CAAC,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,cAAc,iBAAiB;oBACjD,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,0HAAA,CAAA,cAAW,CAAC,IAAI,EAAE;oBACjC,MAAM,MAAM;oBACZ,OAAO;gBACT;gBAEA,yBAAyB;gBACzB,IAAI,UAAU,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,SAAS,GAAG;oBAC9C,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,0HAAA,CAAA,cAAW,CAAC,IAAI,EAAE;oBAEjC,wBAAwB;oBACxB,MAAM,aAAa,GAAG,KAAK,CAAC;oBAC1B,wDAAwD;oBAC1D;oBAEA,OAAO;gBACT;gBAEA,OAAO;YACT;YAEA;;SAEC,GACD,SAAS;gBACP,MAAM,QAAQ;gBAEd,6BAA6B;gBAC7B,IAAI,MAAM,eAAe,IAAI,MAAM,IAAI,IAAI,MAAM,MAAM,EAAE;oBACvD,MAAM,UAAU,MAAM,YAAY;oBAClC,IAAI,CAAC,SAAS;wBACZ,kCAAkC;wBAClC,IAAI;4BACF,MAAM;4BACN,QAAQ;4BACR,iBAAiB;4BACjB,OAAO;wBACT;oBACF;gBACF;gBAEA,IAAI;oBAAE,cAAc;gBAAK;gBACzB,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,0HAAA,CAAA,cAAW,CAAC,IAAI,EAAE;YACnC;QACF,CAAC,GACD;QACE,SAAS;YACP,MAAM,0HAAA,CAAA,eAAY,CAAC,IAAI;YACvB,SAAS,0HAAA,CAAA,iBAAc,CAAC,IAAI;YAC5B,YAAY,CAAC,QAAU,CAAC;oBACtB,MAAM,MAAM,IAAI;oBAChB,QAAQ,MAAM,MAAM;oBACpB,iBAAiB,MAAM,eAAe;oBACtC,cAAc,MAAM,YAAY;oBAChC,gBAAgB,MAAM,cAAc;gBACtC,CAAC;QACH;QACA,UAAU;YACR,MAAM,0HAAA,CAAA,cAAW,CAAC,IAAI;YACtB,SAAS,oDAAyB;QACpC;IACF;AAGN;AAMO,MAAM,eAAe"}}, {"offset": {"line": 856, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 862, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/stores/auth-hooks.ts/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const useAuth = registerClientReference(\n    function() { throw new Error(\"Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/auth-hooks.ts <module evaluation>\",\n    \"useAuth\",\n);\nexport const useAuthDebug = registerClientReference(\n    function() { throw new Error(\"Attempted to call useAuthDebug() from the server but useAuthDebug is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/auth-hooks.ts <module evaluation>\",\n    \"useAuthDebug\",\n);\nexport const useAuthError = registerClientReference(\n    function() { throw new Error(\"Attempted to call useAuthError() from the server but useAuthError is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/auth-hooks.ts <module evaluation>\",\n    \"useAuthError\",\n);\nexport const useAuthLoading = registerClientReference(\n    function() { throw new Error(\"Attempted to call useAuthLoading() from the server but useAuthLoading is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/auth-hooks.ts <module evaluation>\",\n    \"useAuthLoading\",\n);\nexport const useAuthTokens = registerClientReference(\n    function() { throw new Error(\"Attempted to call useAuthTokens() from the server but useAuthTokens is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/auth-hooks.ts <module evaluation>\",\n    \"useAuthTokens\",\n);\nexport const useAuthWithSession = registerClientReference(\n    function() { throw new Error(\"Attempted to call useAuthWithSession() from the server but useAuthWithSession is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/auth-hooks.ts <module evaluation>\",\n    \"useAuthWithSession\",\n);\nexport const useCanAdmin = registerClientReference(\n    function() { throw new Error(\"Attempted to call useCanAdmin() from the server but useCanAdmin is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/auth-hooks.ts <module evaluation>\",\n    \"useCanAdmin\",\n);\nexport const useCanEdit = registerClientReference(\n    function() { throw new Error(\"Attempted to call useCanEdit() from the server but useCanEdit is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/auth-hooks.ts <module evaluation>\",\n    \"useCanEdit\",\n);\nexport const useCheckSession = registerClientReference(\n    function() { throw new Error(\"Attempted to call useCheckSession() from the server but useCheckSession is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/auth-hooks.ts <module evaluation>\",\n    \"useCheckSession\",\n);\nexport const useClearAuthError = registerClientReference(\n    function() { throw new Error(\"Attempted to call useClearAuthError() from the server but useClearAuthError is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/auth-hooks.ts <module evaluation>\",\n    \"useClearAuthError\",\n);\nexport const useHasRole = registerClientReference(\n    function() { throw new Error(\"Attempted to call useHasRole() from the server but useHasRole is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/auth-hooks.ts <module evaluation>\",\n    \"useHasRole\",\n);\nexport const useIsAdmin = registerClientReference(\n    function() { throw new Error(\"Attempted to call useIsAdmin() from the server but useIsAdmin is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/auth-hooks.ts <module evaluation>\",\n    \"useIsAdmin\",\n);\nexport const useIsAuthenticated = registerClientReference(\n    function() { throw new Error(\"Attempted to call useIsAuthenticated() from the server but useIsAuthenticated is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/auth-hooks.ts <module evaluation>\",\n    \"useIsAuthenticated\",\n);\nexport const useIsEditor = registerClientReference(\n    function() { throw new Error(\"Attempted to call useIsEditor() from the server but useIsEditor is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/auth-hooks.ts <module evaluation>\",\n    \"useIsEditor\",\n);\nexport const useIsModerator = registerClientReference(\n    function() { throw new Error(\"Attempted to call useIsModerator() from the server but useIsModerator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/auth-hooks.ts <module evaluation>\",\n    \"useIsModerator\",\n);\nexport const useLogin = registerClientReference(\n    function() { throw new Error(\"Attempted to call useLogin() from the server but useLogin is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/auth-hooks.ts <module evaluation>\",\n    \"useLogin\",\n);\nexport const useLogout = registerClientReference(\n    function() { throw new Error(\"Attempted to call useLogout() from the server but useLogout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/auth-hooks.ts <module evaluation>\",\n    \"useLogout\",\n);\nexport const useLogoutAll = registerClientReference(\n    function() { throw new Error(\"Attempted to call useLogoutAll() from the server but useLogoutAll is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/auth-hooks.ts <module evaluation>\",\n    \"useLogoutAll\",\n);\nexport const usePermissions = registerClientReference(\n    function() { throw new Error(\"Attempted to call usePermissions() from the server but usePermissions is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/auth-hooks.ts <module evaluation>\",\n    \"usePermissions\",\n);\nexport const useRefreshTokens = registerClientReference(\n    function() { throw new Error(\"Attempted to call useRefreshTokens() from the server but useRefreshTokens is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/auth-hooks.ts <module evaluation>\",\n    \"useRefreshTokens\",\n);\nexport const useRouteProtection = registerClientReference(\n    function() { throw new Error(\"Attempted to call useRouteProtection() from the server but useRouteProtection is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/auth-hooks.ts <module evaluation>\",\n    \"useRouteProtection\",\n);\nexport const useUpdateActivity = registerClientReference(\n    function() { throw new Error(\"Attempted to call useUpdateActivity() from the server but useUpdateActivity is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/auth-hooks.ts <module evaluation>\",\n    \"useUpdateActivity\",\n);\nexport const useUpdateProfile = registerClientReference(\n    function() { throw new Error(\"Attempted to call useUpdateProfile() from the server but useUpdateProfile is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/auth-hooks.ts <module evaluation>\",\n    \"useUpdateProfile\",\n);\nexport const useUser = registerClientReference(\n    function() { throw new Error(\"Attempted to call useUser() from the server but useUser is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/auth-hooks.ts <module evaluation>\",\n    \"useUser\",\n);\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;AACO,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,0DACA;AAEG,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,0DACA;AAEG,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,0DACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,0DACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,0DACA;AAEG,MAAM,qBAAqB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,0DACA;AAEG,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,0DACA;AAEG,MAAM,aAAa,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,0DACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,0DACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,0DACA;AAEG,MAAM,aAAa,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,0DACA;AAEG,MAAM,aAAa,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,0DACA;AAEG,MAAM,qBAAqB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,0DACA;AAEG,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,0DACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,0DACA;AAEG,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,0DACA;AAEG,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,0DACA;AAEG,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,0DACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,0DACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,0DACA;AAEG,MAAM,qBAAqB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,0DACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,0DACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,0DACA;AAEG,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,0DACA"}}, {"offset": {"line": 962, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 968, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/stores/auth-hooks.ts/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const useAuth = registerClientReference(\n    function() { throw new Error(\"Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/auth-hooks.ts\",\n    \"useAuth\",\n);\nexport const useAuthDebug = registerClientReference(\n    function() { throw new Error(\"Attempted to call useAuthDebug() from the server but useAuthDebug is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/auth-hooks.ts\",\n    \"useAuthDebug\",\n);\nexport const useAuthError = registerClientReference(\n    function() { throw new Error(\"Attempted to call useAuthError() from the server but useAuthError is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/auth-hooks.ts\",\n    \"useAuthError\",\n);\nexport const useAuthLoading = registerClientReference(\n    function() { throw new Error(\"Attempted to call useAuthLoading() from the server but useAuthLoading is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/auth-hooks.ts\",\n    \"useAuthLoading\",\n);\nexport const useAuthTokens = registerClientReference(\n    function() { throw new Error(\"Attempted to call useAuthTokens() from the server but useAuthTokens is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/auth-hooks.ts\",\n    \"useAuthTokens\",\n);\nexport const useAuthWithSession = registerClientReference(\n    function() { throw new Error(\"Attempted to call useAuthWithSession() from the server but useAuthWithSession is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/auth-hooks.ts\",\n    \"useAuthWithSession\",\n);\nexport const useCanAdmin = registerClientReference(\n    function() { throw new Error(\"Attempted to call useCanAdmin() from the server but useCanAdmin is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/auth-hooks.ts\",\n    \"useCanAdmin\",\n);\nexport const useCanEdit = registerClientReference(\n    function() { throw new Error(\"Attempted to call useCanEdit() from the server but useCanEdit is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/auth-hooks.ts\",\n    \"useCanEdit\",\n);\nexport const useCheckSession = registerClientReference(\n    function() { throw new Error(\"Attempted to call useCheckSession() from the server but useCheckSession is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/auth-hooks.ts\",\n    \"useCheckSession\",\n);\nexport const useClearAuthError = registerClientReference(\n    function() { throw new Error(\"Attempted to call useClearAuthError() from the server but useClearAuthError is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/auth-hooks.ts\",\n    \"useClearAuthError\",\n);\nexport const useHasRole = registerClientReference(\n    function() { throw new Error(\"Attempted to call useHasRole() from the server but useHasRole is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/auth-hooks.ts\",\n    \"useHasRole\",\n);\nexport const useIsAdmin = registerClientReference(\n    function() { throw new Error(\"Attempted to call useIsAdmin() from the server but useIsAdmin is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/auth-hooks.ts\",\n    \"useIsAdmin\",\n);\nexport const useIsAuthenticated = registerClientReference(\n    function() { throw new Error(\"Attempted to call useIsAuthenticated() from the server but useIsAuthenticated is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/auth-hooks.ts\",\n    \"useIsAuthenticated\",\n);\nexport const useIsEditor = registerClientReference(\n    function() { throw new Error(\"Attempted to call useIsEditor() from the server but useIsEditor is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/auth-hooks.ts\",\n    \"useIsEditor\",\n);\nexport const useIsModerator = registerClientReference(\n    function() { throw new Error(\"Attempted to call useIsModerator() from the server but useIsModerator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/auth-hooks.ts\",\n    \"useIsModerator\",\n);\nexport const useLogin = registerClientReference(\n    function() { throw new Error(\"Attempted to call useLogin() from the server but useLogin is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/auth-hooks.ts\",\n    \"useLogin\",\n);\nexport const useLogout = registerClientReference(\n    function() { throw new Error(\"Attempted to call useLogout() from the server but useLogout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/auth-hooks.ts\",\n    \"useLogout\",\n);\nexport const useLogoutAll = registerClientReference(\n    function() { throw new Error(\"Attempted to call useLogoutAll() from the server but useLogoutAll is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/auth-hooks.ts\",\n    \"useLogoutAll\",\n);\nexport const usePermissions = registerClientReference(\n    function() { throw new Error(\"Attempted to call usePermissions() from the server but usePermissions is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/auth-hooks.ts\",\n    \"usePermissions\",\n);\nexport const useRefreshTokens = registerClientReference(\n    function() { throw new Error(\"Attempted to call useRefreshTokens() from the server but useRefreshTokens is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/auth-hooks.ts\",\n    \"useRefreshTokens\",\n);\nexport const useRouteProtection = registerClientReference(\n    function() { throw new Error(\"Attempted to call useRouteProtection() from the server but useRouteProtection is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/auth-hooks.ts\",\n    \"useRouteProtection\",\n);\nexport const useUpdateActivity = registerClientReference(\n    function() { throw new Error(\"Attempted to call useUpdateActivity() from the server but useUpdateActivity is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/auth-hooks.ts\",\n    \"useUpdateActivity\",\n);\nexport const useUpdateProfile = registerClientReference(\n    function() { throw new Error(\"Attempted to call useUpdateProfile() from the server but useUpdateProfile is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/auth-hooks.ts\",\n    \"useUpdateProfile\",\n);\nexport const useUser = registerClientReference(\n    function() { throw new Error(\"Attempted to call useUser() from the server but useUser is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/auth-hooks.ts\",\n    \"useUser\",\n);\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;AACO,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,sCACA;AAEG,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,sCACA;AAEG,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,sCACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,sCACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,sCACA;AAEG,MAAM,qBAAqB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,sCACA;AAEG,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,sCACA;AAEG,MAAM,aAAa,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,sCACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,sCACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,sCACA;AAEG,MAAM,aAAa,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,sCACA;AAEG,MAAM,aAAa,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,sCACA;AAEG,MAAM,qBAAqB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,sCACA;AAEG,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,sCACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,sCACA;AAEG,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,sCACA;AAEG,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,sCACA;AAEG,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,sCACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,sCACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,sCACA;AAEG,MAAM,qBAAqB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,sCACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,sCACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,sCACA;AAEG,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,sCACA"}}, {"offset": {"line": 1068, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1074, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 1078, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1084, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/stores/auth-utils.ts"], "sourcesContent": ["/**\n * Authentication Utilities\n * Helper functions for authentication operations\n */\n\nimport type { SystemUser, SystemUserRole, AuthTokens } from './types';\nimport { useAuthStore } from './auth-store';\n\n// ============================================================================\n// Token Utilities\n// ============================================================================\n\n/**\n * Get current access token\n */\nexport const getAccessToken = (): string | null => {\n  const tokens = useAuthStore.getState().tokens;\n  return tokens?.accessToken || null;\n};\n\n/**\n * Get current refresh token\n */\nexport const getRefreshToken = (): string | null => {\n  const tokens = useAuthStore.getState().tokens;\n  return tokens?.refreshToken || null;\n};\n\n/**\n * Get authorization header for API requests\n */\nexport const getAuthHeader = (): Record<string, string> => {\n  const token = getAccessToken();\n  return token ? { Authorization: `Bearer ${token}` } : {};\n};\n\n/**\n * Check if user is currently authenticated\n */\nexport const isAuthenticated = (): boolean => {\n  return useAuthStore.getState().isAuthenticated;\n};\n\n/**\n * Get current user\n */\nexport const getCurrentUser = (): SystemUser | null => {\n  return useAuthStore.getState().user;\n};\n\n/**\n * Get current user role\n */\nexport const getCurrentUserRole = (): SystemUserRole | null => {\n  const user = getCurrentUser();\n  return user?.role || null;\n};\n\n// ============================================================================\n// Permission Utilities\n// ============================================================================\n\n/**\n * Check if current user has specific role\n */\nexport const hasRole = (role: SystemUserRole): boolean => {\n  const currentRole = getCurrentUserRole();\n  return currentRole === role;\n};\n\n/**\n * Check if current user has any of the specified roles\n */\nexport const hasAnyRole = (roles: SystemUserRole[]): boolean => {\n  const currentRole = getCurrentUserRole();\n  return currentRole ? roles.includes(currentRole) : false;\n};\n\n/**\n * Check if current user is admin\n */\nexport const isAdmin = (): boolean => {\n  return hasRole('Admin');\n};\n\n/**\n * Check if current user is editor\n */\nexport const isEditor = (): boolean => {\n  return hasRole('Editor');\n};\n\n/**\n * Check if current user is moderator\n */\nexport const isModerator = (): boolean => {\n  return hasRole('Moderator');\n};\n\n/**\n * Check if current user can edit content\n */\nexport const canEdit = (): boolean => {\n  return hasAnyRole(['Admin', 'Editor']);\n};\n\n/**\n * Check if current user can perform admin actions\n */\nexport const canAdmin = (): boolean => {\n  return isAdmin();\n};\n\n/**\n * Check if current user can moderate content\n */\nexport const canModerate = (): boolean => {\n  return hasAnyRole(['Admin', 'Editor', 'Moderator']);\n};\n\n// ============================================================================\n// Session Utilities\n// ============================================================================\n\n/**\n * Force session check\n */\nexport const checkSession = (): boolean => {\n  return useAuthStore.getState().checkSession();\n};\n\n/**\n * Update user activity timestamp\n */\nexport const updateActivity = (): void => {\n  useAuthStore.getState().updateLastActivity();\n};\n\n/**\n * Get session remaining time in minutes\n */\nexport const getAuthSessionRemainingTime = (): number => {\n  const { lastActivity, sessionTimeout } = useAuthStore.getState();\n  const now = Date.now();\n  const timeoutMs = sessionTimeout * 60 * 1000;\n  const elapsed = now - lastActivity;\n  const remaining = timeoutMs - elapsed;\n  return Math.max(0, Math.floor(remaining / (60 * 1000)));\n};\n\n/**\n * Check if session will expire soon (within 5 minutes)\n */\nexport const isSessionExpiringSoon = (): boolean => {\n  return getAuthSessionRemainingTime() <= 5;\n};\n\n// ============================================================================\n// Authentication Actions\n// ============================================================================\n\n/**\n * Login with credentials\n */\nexport const login = async (email: string, password: string): Promise<void> => {\n  return useAuthStore.getState().login(email, password);\n};\n\n/**\n * Logout current user\n */\nexport const logout = async (): Promise<void> => {\n  return useAuthStore.getState().logout();\n};\n\n/**\n * Logout from all devices\n */\nexport const logoutAll = async (): Promise<void> => {\n  return useAuthStore.getState().logoutAll();\n};\n\n/**\n * Update user profile\n */\nexport const updateProfile = async (data: Partial<SystemUser>): Promise<void> => {\n  return useAuthStore.getState().updateProfile(data);\n};\n\n/**\n * Refresh authentication tokens\n */\nexport const refreshTokens = async (): Promise<void> => {\n  return useAuthStore.getState().refreshTokens();\n};\n\n// ============================================================================\n// Error Handling Utilities\n// ============================================================================\n\n/**\n * Get current authentication error\n */\nexport const getAuthError = (): string | null => {\n  return useAuthStore.getState().error;\n};\n\n/**\n * Clear authentication error\n */\nexport const clearAuthError = (): void => {\n  useAuthStore.getState().clearError();\n};\n\n/**\n * Check if there's an authentication error\n */\nexport const hasAuthError = (): boolean => {\n  return !!getAuthError();\n};\n\n// ============================================================================\n// API Request Utilities\n// ============================================================================\n\n/**\n * Create authenticated fetch request\n */\nexport const authenticatedFetch = async (\n  url: string,\n  options: RequestInit = {}\n): Promise<Response> => {\n  const authHeaders = getAuthHeader();\n\n  const config: RequestInit = {\n    ...options,\n    headers: {\n      'Content-Type': 'application/json',\n      ...authHeaders,\n      ...options.headers,\n    },\n  };\n\n  const response = await fetch(url, config);\n\n  // Handle token expiration\n  if (response.status === 401) {\n    const isAuth = isAuthenticated();\n    if (isAuth) {\n      // Try to refresh tokens\n      try {\n        await refreshTokens();\n        // Retry the request with new token\n        const newAuthHeaders = getAuthHeader();\n        const retryConfig: RequestInit = {\n          ...config,\n          headers: {\n            ...config.headers,\n            ...newAuthHeaders,\n          },\n        };\n        return fetch(url, retryConfig);\n      } catch (error) {\n        // Refresh failed, logout user\n        await logout();\n        throw new Error('Authentication expired. Please log in again.');\n      }\n    }\n  }\n\n  return response;\n};\n\n/**\n * Create authenticated API request with JSON response\n */\nexport const authenticatedApiRequest = async <T = any>(\n  url: string,\n  options: RequestInit = {}\n): Promise<T> => {\n  const response = await authenticatedFetch(url, options);\n\n  if (!response.ok) {\n    const errorData = await response.json().catch(() => ({}));\n    throw new Error(errorData.message || `Request failed with status ${response.status}`);\n  }\n\n  return response.json();\n};\n\n// ============================================================================\n// Route Protection Utilities\n// ============================================================================\n\n/**\n * Check if user can access route with required roles\n */\nexport const canAccessRoute = (requiredRoles?: SystemUserRole[]): boolean => {\n  if (!isAuthenticated()) return false;\n  if (!requiredRoles || requiredRoles.length === 0) return true;\n\n  return hasAnyRole(requiredRoles);\n};\n\n/**\n * Get redirect path for unauthenticated users\n */\nexport const getLoginRedirectPath = (currentPath?: string): string => {\n  const loginPath = '/login';\n  if (currentPath && currentPath !== '/') {\n    return `${loginPath}?redirect=${encodeURIComponent(currentPath)}`;\n  }\n  return loginPath;\n};\n\n/**\n * Get redirect path after successful login\n */\nexport const getPostLoginRedirectPath = (searchParams?: URLSearchParams): string => {\n  const redirectParam = searchParams?.get('redirect');\n  return redirectParam || '/dashboard';\n};\n\n// ============================================================================\n// Development Utilities\n// ============================================================================\n\n/**\n * Get full authentication state (development only)\n */\nexport const getAuthState = () => {\n  if (process.env.NODE_ENV !== 'development') {\n    return null;\n  }\n\n  return useAuthStore.getState();\n};\n\n/**\n * Mock login for development/testing\n */\nexport const mockLogin = (user: SystemUser, tokens: AuthTokens): void => {\n  if (process.env.NODE_ENV !== 'development') {\n    return;\n  }\n\n  useAuthStore.getState().setUser(user);\n  useAuthStore.getState().setTokens(tokens);\n};\n\n/**\n * Reset authentication state (development/testing only)\n */\nexport const resetAuthState = (): void => {\n  if (process.env.NODE_ENV !== 'development') {\n    return;\n  }\n\n  useAuthStore.getState().logout();\n};\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGD;;AASO,MAAM,iBAAiB;IAC5B,MAAM,SAAS,8HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,MAAM;IAC7C,OAAO,QAAQ,eAAe;AAChC;AAKO,MAAM,kBAAkB;IAC7B,MAAM,SAAS,8HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,MAAM;IAC7C,OAAO,QAAQ,gBAAgB;AACjC;AAKO,MAAM,gBAAgB;IAC3B,MAAM,QAAQ;IACd,OAAO,QAAQ;QAAE,eAAe,CAAC,OAAO,EAAE,OAAO;IAAC,IAAI,CAAC;AACzD;AAKO,MAAM,kBAAkB;IAC7B,OAAO,8HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,eAAe;AAChD;AAKO,MAAM,iBAAiB;IAC5B,OAAO,8HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,IAAI;AACrC;AAKO,MAAM,qBAAqB;IAChC,MAAM,OAAO;IACb,OAAO,MAAM,QAAQ;AACvB;AASO,MAAM,UAAU,CAAC;IACtB,MAAM,cAAc;IACpB,OAAO,gBAAgB;AACzB;AAKO,MAAM,aAAa,CAAC;IACzB,MAAM,cAAc;IACpB,OAAO,cAAc,MAAM,QAAQ,CAAC,eAAe;AACrD;AAKO,MAAM,UAAU;IACrB,OAAO,QAAQ;AACjB;AAKO,MAAM,WAAW;IACtB,OAAO,QAAQ;AACjB;AAKO,MAAM,cAAc;IACzB,OAAO,QAAQ;AACjB;AAKO,MAAM,UAAU;IACrB,OAAO,WAAW;QAAC;QAAS;KAAS;AACvC;AAKO,MAAM,WAAW;IACtB,OAAO;AACT;AAKO,MAAM,cAAc;IACzB,OAAO,WAAW;QAAC;QAAS;QAAU;KAAY;AACpD;AASO,MAAM,eAAe;IAC1B,OAAO,8HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,YAAY;AAC7C;AAKO,MAAM,iBAAiB;IAC5B,8HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,kBAAkB;AAC5C;AAKO,MAAM,8BAA8B;IACzC,MAAM,EAAE,YAAY,EAAE,cAAc,EAAE,GAAG,8HAAA,CAAA,eAAY,CAAC,QAAQ;IAC9D,MAAM,MAAM,KAAK,GAAG;IACpB,MAAM,YAAY,iBAAiB,KAAK;IACxC,MAAM,UAAU,MAAM;IACtB,MAAM,YAAY,YAAY;IAC9B,OAAO,KAAK,GAAG,CAAC,GAAG,KAAK,KAAK,CAAC,YAAY,CAAC,KAAK,IAAI;AACtD;AAKO,MAAM,wBAAwB;IACnC,OAAO,iCAAiC;AAC1C;AASO,MAAM,QAAQ,OAAO,OAAe;IACzC,OAAO,8HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,KAAK,CAAC,OAAO;AAC9C;AAKO,MAAM,SAAS;IACpB,OAAO,8HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,MAAM;AACvC;AAKO,MAAM,YAAY;IACvB,OAAO,8HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,SAAS;AAC1C;AAKO,MAAM,gBAAgB,OAAO;IAClC,OAAO,8HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,aAAa,CAAC;AAC/C;AAKO,MAAM,gBAAgB;IAC3B,OAAO,8HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,aAAa;AAC9C;AASO,MAAM,eAAe;IAC1B,OAAO,8HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,KAAK;AACtC;AAKO,MAAM,iBAAiB;IAC5B,8HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,UAAU;AACpC;AAKO,MAAM,eAAe;IAC1B,OAAO,CAAC,CAAC;AACX;AASO,MAAM,qBAAqB,OAChC,KACA,UAAuB,CAAC,CAAC;IAEzB,MAAM,cAAc;IAEpB,MAAM,SAAsB;QAC1B,GAAG,OAAO;QACV,SAAS;YACP,gBAAgB;YAChB,GAAG,WAAW;YACd,GAAG,QAAQ,OAAO;QACpB;IACF;IAEA,MAAM,WAAW,MAAM,MAAM,KAAK;IAElC,0BAA0B;IAC1B,IAAI,SAAS,MAAM,KAAK,KAAK;QAC3B,MAAM,SAAS;QACf,IAAI,QAAQ;YACV,wBAAwB;YACxB,IAAI;gBACF,MAAM;gBACN,mCAAmC;gBACnC,MAAM,iBAAiB;gBACvB,MAAM,cAA2B;oBAC/B,GAAG,MAAM;oBACT,SAAS;wBACP,GAAG,OAAO,OAAO;wBACjB,GAAG,cAAc;oBACnB;gBACF;gBACA,OAAO,MAAM,KAAK;YACpB,EAAE,OAAO,OAAO;gBACd,8BAA8B;gBAC9B,MAAM;gBACN,MAAM,IAAI,MAAM;YAClB;QACF;IACF;IAEA,OAAO;AACT;AAKO,MAAM,0BAA0B,OACrC,KACA,UAAuB,CAAC,CAAC;IAEzB,MAAM,WAAW,MAAM,mBAAmB,KAAK;IAE/C,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;QACvD,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,CAAC,2BAA2B,EAAE,SAAS,MAAM,EAAE;IACtF;IAEA,OAAO,SAAS,IAAI;AACtB;AASO,MAAM,iBAAiB,CAAC;IAC7B,IAAI,CAAC,mBAAmB,OAAO;IAC/B,IAAI,CAAC,iBAAiB,cAAc,MAAM,KAAK,GAAG,OAAO;IAEzD,OAAO,WAAW;AACpB;AAKO,MAAM,uBAAuB,CAAC;IACnC,MAAM,YAAY;IAClB,IAAI,eAAe,gBAAgB,KAAK;QACtC,OAAO,GAAG,UAAU,UAAU,EAAE,mBAAmB,cAAc;IACnE;IACA,OAAO;AACT;AAKO,MAAM,2BAA2B,CAAC;IACvC,MAAM,gBAAgB,cAAc,IAAI;IACxC,OAAO,iBAAiB;AAC1B;AASO,MAAM,eAAe;IAC1B,uCAA4C;;IAE5C;IAEA,OAAO,8HAAA,CAAA,eAAY,CAAC,QAAQ;AAC9B;AAKO,MAAM,YAAY,CAAC,MAAkB;IAC1C,uCAA4C;;IAE5C;IAEA,8HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,OAAO,CAAC;IAChC,8HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,SAAS,CAAC;AACpC;AAKO,MAAM,iBAAiB;IAC5B,uCAA4C;;IAE5C;IAEA,8HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,MAAM;AAChC"}}, {"offset": {"line": 1303, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1309, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/stores/app-store.ts"], "sourcesContent": ["/**\n * Application Store\n * Manages application state, theme, settings, navigation, and UI state\n */\n\nimport { create } from 'zustand';\nimport { \n  AppStore, \n  ThemeConfig,\n  AppSettings,\n  NavigationState,\n  UIState\n} from './types';\nimport { \n  createStoreWithMiddleware,\n  createBaseStoreActions,\n  generateNotificationId,\n  getDefaultNotificationDuration,\n  logStoreAction\n} from './utils';\nimport { \n  STORE_NAMES,\n  STORAGE_KEYS,\n  DEFAULT_THEME,\n  DEFAULT_APP_SETTINGS,\n  DEFAULT_NAVIGATION,\n  DEFAULT_UI_STATE,\n  SUCCESS_MESSAGES,\n  ERROR_MESSAGES,\n  STORE_VERSIONS\n} from './constants';\n\n// ============================================================================\n// Initial State\n// ============================================================================\n\nconst initialAppState = {\n  // Base store state\n  _hasHydrated: false,\n  \n  // Configuration\n  theme: DEFAULT_THEME,\n  settings: DEFAULT_APP_SETTINGS,\n  \n  // Navigation\n  navigation: DEFAULT_NAVIGATION,\n  \n  // UI state\n  ui: DEFAULT_UI_STATE,\n  \n  // System info\n  version: process.env.NEXT_PUBLIC_APP_VERSION || '1.0.0',\n  buildTime: process.env.NEXT_PUBLIC_BUILD_TIME || new Date().toISOString(),\n  environment: (process.env.NODE_ENV as 'development' | 'staging' | 'production') || 'development',\n};\n\n// ============================================================================\n// Store Implementation\n// ============================================================================\n\n/**\n * Application Store Creator\n */\nconst createAppStore = () => {\n  return create<AppStore>()(\n    createStoreWithMiddleware<AppStore>(\n      (set, get) => ({\n        ...initialAppState,\n        \n        // Base store actions\n        ...createBaseStoreActions<AppStore>(set),\n        \n        // ========================================================================\n        // Theme Management Actions\n        // ========================================================================\n        \n        /**\n         * Set theme configuration\n         */\n        setTheme: (themeUpdate: Partial<ThemeConfig>) => {\n          const currentTheme = get().theme;\n          const newTheme = { ...currentTheme, ...themeUpdate };\n          \n          set({ theme: newTheme });\n          logStoreAction(STORE_NAMES.APP, 'set_theme', themeUpdate);\n        },\n        \n        /**\n         * Toggle between light and dark mode\n         */\n        toggleTheme: () => {\n          const currentMode = get().theme.mode;\n          const newMode = currentMode === 'light' ? 'dark' : 'light';\n          \n          get().setTheme({ mode: newMode });\n          logStoreAction(STORE_NAMES.APP, 'toggle_theme', { newMode });\n        },\n        \n        // ========================================================================\n        // Settings Management Actions\n        // ========================================================================\n        \n        /**\n         * Update application settings\n         */\n        updateSettings: (settingsUpdate: Partial<AppSettings>) => {\n          const currentSettings = get().settings;\n          const newSettings = { ...currentSettings, ...settingsUpdate };\n          \n          set({ settings: newSettings });\n          logStoreAction(STORE_NAMES.APP, 'update_settings', settingsUpdate);\n        },\n        \n        /**\n         * Reset settings to default values\n         */\n        resetSettings: () => {\n          set({ settings: DEFAULT_APP_SETTINGS });\n          logStoreAction(STORE_NAMES.APP, 'reset_settings');\n        },\n        \n        // ========================================================================\n        // Navigation Actions\n        // ========================================================================\n        \n        /**\n         * Set current path\n         */\n        setCurrentPath: (path: string) => {\n          const currentNavigation = get().navigation;\n          const newNavigation = { ...currentNavigation, currentPath: path };\n          \n          set({ navigation: newNavigation });\n          logStoreAction(STORE_NAMES.APP, 'set_current_path', { path });\n        },\n        \n        /**\n         * Set breadcrumbs\n         */\n        setBreadcrumbs: (breadcrumbs: NavigationState['breadcrumbs']) => {\n          const currentNavigation = get().navigation;\n          const newNavigation = { ...currentNavigation, breadcrumbs };\n          \n          set({ navigation: newNavigation });\n          logStoreAction(STORE_NAMES.APP, 'set_breadcrumbs', { count: breadcrumbs.length });\n        },\n        \n        /**\n         * Toggle sidebar collapsed state\n         */\n        toggleSidebar: () => {\n          const currentNavigation = get().navigation;\n          const newCollapsed = !currentNavigation.sidebarCollapsed;\n          const newNavigation = { ...currentNavigation, sidebarCollapsed: newCollapsed };\n          \n          set({ navigation: newNavigation });\n          logStoreAction(STORE_NAMES.APP, 'toggle_sidebar', { collapsed: newCollapsed });\n        },\n        \n        /**\n         * Set active menu key\n         */\n        setActiveMenu: (key: string) => {\n          const currentNavigation = get().navigation;\n          const newNavigation = { ...currentNavigation, activeMenuKey: key };\n          \n          set({ navigation: newNavigation });\n          logStoreAction(STORE_NAMES.APP, 'set_active_menu', { key });\n        },\n        \n        // ========================================================================\n        // UI State Management Actions\n        // ========================================================================\n        \n        /**\n         * Set global loading state\n         */\n        setGlobalLoading: (loading: boolean, message?: string) => {\n          const currentUI = get().ui;\n          const newUI = { \n            ...currentUI, \n            globalLoading: loading,\n            loadingMessage: message || ''\n          };\n          \n          set({ ui: newUI });\n          logStoreAction(STORE_NAMES.APP, 'set_global_loading', { loading, message });\n        },\n        \n        /**\n         * Set global error\n         */\n        setGlobalError: (error: string | null, details?: any) => {\n          const currentUI = get().ui;\n          const newUI = { \n            ...currentUI, \n            globalError: error,\n            errorDetails: details || null\n          };\n          \n          set({ ui: newUI });\n          logStoreAction(STORE_NAMES.APP, 'set_global_error', { error, hasDetails: !!details });\n        },\n        \n        /**\n         * Clear global error\n         */\n        clearGlobalError: () => {\n          const currentUI = get().ui;\n          const newUI = { \n            ...currentUI, \n            globalError: null,\n            errorDetails: null\n          };\n          \n          set({ ui: newUI });\n          logStoreAction(STORE_NAMES.APP, 'clear_global_error');\n        },\n        \n        // ========================================================================\n        // Notifications Actions\n        // ========================================================================\n        \n        /**\n         * Add notification\n         */\n        addNotification: (notification: Omit<UIState['notifications'][0], 'id' | 'timestamp'>) => {\n          const id = generateNotificationId();\n          const timestamp = Date.now();\n          const duration = notification.duration || getDefaultNotificationDuration(notification.type);\n          \n          const newNotification = {\n            ...notification,\n            id,\n            timestamp,\n            duration,\n          };\n          \n          const currentUI = get().ui;\n          const newNotifications = [...currentUI.notifications, newNotification];\n          const newUI = { ...currentUI, notifications: newNotifications };\n          \n          set({ ui: newUI });\n          logStoreAction(STORE_NAMES.APP, 'add_notification', { type: notification.type, id });\n          \n          // Auto-remove notification after duration\n          if (duration > 0) {\n            setTimeout(() => {\n              get().removeNotification(id);\n            }, duration);\n          }\n        },\n        \n        /**\n         * Remove notification\n         */\n        removeNotification: (id: string) => {\n          const currentUI = get().ui;\n          const newNotifications = currentUI.notifications.filter(n => n.id !== id);\n          const newUI = { ...currentUI, notifications: newNotifications };\n          \n          set({ ui: newUI });\n          logStoreAction(STORE_NAMES.APP, 'remove_notification', { id });\n        },\n        \n        /**\n         * Clear all notifications\n         */\n        clearNotifications: () => {\n          const currentUI = get().ui;\n          const newUI = { ...currentUI, notifications: [] };\n          \n          set({ ui: newUI });\n          logStoreAction(STORE_NAMES.APP, 'clear_notifications');\n        },\n        \n        // ========================================================================\n        // Modals Actions\n        // ========================================================================\n        \n        /**\n         * Show modal\n         */\n        showModal: (key: string, data?: any) => {\n          const currentUI = get().ui;\n          const newModals = { \n            ...currentUI.modals, \n            [key]: { visible: true, data } \n          };\n          const newUI = { ...currentUI, modals: newModals };\n          \n          set({ ui: newUI });\n          logStoreAction(STORE_NAMES.APP, 'show_modal', { key, hasData: !!data });\n        },\n        \n        /**\n         * Hide modal\n         */\n        hideModal: (key: string) => {\n          const currentUI = get().ui;\n          const newModals = { \n            ...currentUI.modals, \n            [key]: { visible: false, data: undefined } \n          };\n          const newUI = { ...currentUI, modals: newModals };\n          \n          set({ ui: newUI });\n          logStoreAction(STORE_NAMES.APP, 'hide_modal', { key });\n        },\n        \n        /**\n         * Hide all modals\n         */\n        hideAllModals: () => {\n          const currentUI = get().ui;\n          const newModals: Record<string, { visible: boolean; data?: any }> = {};\n          \n          // Set all modals to hidden\n          Object.keys(currentUI.modals).forEach(key => {\n            newModals[key] = { visible: false, data: undefined };\n          });\n          \n          const newUI = { ...currentUI, modals: newModals };\n          \n          set({ ui: newUI });\n          logStoreAction(STORE_NAMES.APP, 'hide_all_modals');\n        },\n      }),\n      {\n        persist: {\n          name: STORAGE_KEYS.APP,\n          version: STORE_VERSIONS.APP,\n          partialize: (state) => ({\n            theme: state.theme,\n            settings: state.settings,\n            navigation: {\n              sidebarCollapsed: state.navigation.sidebarCollapsed,\n              activeMenuKey: state.navigation.activeMenuKey,\n            },\n          }),\n        },\n        devtools: {\n          name: STORE_NAMES.APP,\n          enabled: process.env.NODE_ENV === 'development',\n        },\n      }\n    )\n  );\n};\n\n// ============================================================================\n// Export Store\n// ============================================================================\n\nexport const useAppStore = createAppStore();\n\n// Export store for testing and advanced usage\nexport { createAppStore };\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAUD;AAOA;AAfA;;;;AA2BA,+EAA+E;AAC/E,gBAAgB;AAChB,+EAA+E;AAE/E,MAAM,kBAAkB;IACtB,mBAAmB;IACnB,cAAc;IAEd,gBAAgB;IAChB,OAAO,0HAAA,CAAA,gBAAa;IACpB,UAAU,0HAAA,CAAA,uBAAoB;IAE9B,aAAa;IACb,YAAY,0HAAA,CAAA,qBAAkB;IAE9B,WAAW;IACX,IAAI,0HAAA,CAAA,mBAAgB;IAEpB,cAAc;IACd,SAAS,QAAQ,GAAG,CAAC,uBAAuB,IAAI;IAChD,WAAW,QAAQ,GAAG,CAAC,sBAAsB,IAAI,IAAI,OAAO,WAAW;IACvE,aAAa,mDAAsE;AACrF;AAEA,+EAA+E;AAC/E,uBAAuB;AACvB,+EAA+E;AAE/E;;CAEC,GACD,MAAM,iBAAiB;IACrB,OAAO,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,IACV,CAAA,GAAA,sHAAA,CAAA,4BAAyB,AAAD,EACtB,CAAC,KAAK,MAAQ,CAAC;YACb,GAAG,eAAe;YAElB,qBAAqB;YACrB,GAAG,CAAA,GAAA,sHAAA,CAAA,yBAAsB,AAAD,EAAY,IAAI;YAExC,2EAA2E;YAC3E,2BAA2B;YAC3B,2EAA2E;YAE3E;;SAEC,GACD,UAAU,CAAC;gBACT,MAAM,eAAe,MAAM,KAAK;gBAChC,MAAM,WAAW;oBAAE,GAAG,YAAY;oBAAE,GAAG,WAAW;gBAAC;gBAEnD,IAAI;oBAAE,OAAO;gBAAS;gBACtB,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,0HAAA,CAAA,cAAW,CAAC,GAAG,EAAE,aAAa;YAC/C;YAEA;;SAEC,GACD,aAAa;gBACX,MAAM,cAAc,MAAM,KAAK,CAAC,IAAI;gBACpC,MAAM,UAAU,gBAAgB,UAAU,SAAS;gBAEnD,MAAM,QAAQ,CAAC;oBAAE,MAAM;gBAAQ;gBAC/B,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,0HAAA,CAAA,cAAW,CAAC,GAAG,EAAE,gBAAgB;oBAAE;gBAAQ;YAC5D;YAEA,2EAA2E;YAC3E,8BAA8B;YAC9B,2EAA2E;YAE3E;;SAEC,GACD,gBAAgB,CAAC;gBACf,MAAM,kBAAkB,MAAM,QAAQ;gBACtC,MAAM,cAAc;oBAAE,GAAG,eAAe;oBAAE,GAAG,cAAc;gBAAC;gBAE5D,IAAI;oBAAE,UAAU;gBAAY;gBAC5B,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,0HAAA,CAAA,cAAW,CAAC,GAAG,EAAE,mBAAmB;YACrD;YAEA;;SAEC,GACD,eAAe;gBACb,IAAI;oBAAE,UAAU,0HAAA,CAAA,uBAAoB;gBAAC;gBACrC,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,0HAAA,CAAA,cAAW,CAAC,GAAG,EAAE;YAClC;YAEA,2EAA2E;YAC3E,qBAAqB;YACrB,2EAA2E;YAE3E;;SAEC,GACD,gBAAgB,CAAC;gBACf,MAAM,oBAAoB,MAAM,UAAU;gBAC1C,MAAM,gBAAgB;oBAAE,GAAG,iBAAiB;oBAAE,aAAa;gBAAK;gBAEhE,IAAI;oBAAE,YAAY;gBAAc;gBAChC,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,0HAAA,CAAA,cAAW,CAAC,GAAG,EAAE,oBAAoB;oBAAE;gBAAK;YAC7D;YAEA;;SAEC,GACD,gBAAgB,CAAC;gBACf,MAAM,oBAAoB,MAAM,UAAU;gBAC1C,MAAM,gBAAgB;oBAAE,GAAG,iBAAiB;oBAAE;gBAAY;gBAE1D,IAAI;oBAAE,YAAY;gBAAc;gBAChC,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,0HAAA,CAAA,cAAW,CAAC,GAAG,EAAE,mBAAmB;oBAAE,OAAO,YAAY,MAAM;gBAAC;YACjF;YAEA;;SAEC,GACD,eAAe;gBACb,MAAM,oBAAoB,MAAM,UAAU;gBAC1C,MAAM,eAAe,CAAC,kBAAkB,gBAAgB;gBACxD,MAAM,gBAAgB;oBAAE,GAAG,iBAAiB;oBAAE,kBAAkB;gBAAa;gBAE7E,IAAI;oBAAE,YAAY;gBAAc;gBAChC,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,0HAAA,CAAA,cAAW,CAAC,GAAG,EAAE,kBAAkB;oBAAE,WAAW;gBAAa;YAC9E;YAEA;;SAEC,GACD,eAAe,CAAC;gBACd,MAAM,oBAAoB,MAAM,UAAU;gBAC1C,MAAM,gBAAgB;oBAAE,GAAG,iBAAiB;oBAAE,eAAe;gBAAI;gBAEjE,IAAI;oBAAE,YAAY;gBAAc;gBAChC,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,0HAAA,CAAA,cAAW,CAAC,GAAG,EAAE,mBAAmB;oBAAE;gBAAI;YAC3D;YAEA,2EAA2E;YAC3E,8BAA8B;YAC9B,2EAA2E;YAE3E;;SAEC,GACD,kBAAkB,CAAC,SAAkB;gBACnC,MAAM,YAAY,MAAM,EAAE;gBAC1B,MAAM,QAAQ;oBACZ,GAAG,SAAS;oBACZ,eAAe;oBACf,gBAAgB,WAAW;gBAC7B;gBAEA,IAAI;oBAAE,IAAI;gBAAM;gBAChB,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,0HAAA,CAAA,cAAW,CAAC,GAAG,EAAE,sBAAsB;oBAAE;oBAAS;gBAAQ;YAC3E;YAEA;;SAEC,GACD,gBAAgB,CAAC,OAAsB;gBACrC,MAAM,YAAY,MAAM,EAAE;gBAC1B,MAAM,QAAQ;oBACZ,GAAG,SAAS;oBACZ,aAAa;oBACb,cAAc,WAAW;gBAC3B;gBAEA,IAAI;oBAAE,IAAI;gBAAM;gBAChB,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,0HAAA,CAAA,cAAW,CAAC,GAAG,EAAE,oBAAoB;oBAAE;oBAAO,YAAY,CAAC,CAAC;gBAAQ;YACrF;YAEA;;SAEC,GACD,kBAAkB;gBAChB,MAAM,YAAY,MAAM,EAAE;gBAC1B,MAAM,QAAQ;oBACZ,GAAG,SAAS;oBACZ,aAAa;oBACb,cAAc;gBAChB;gBAEA,IAAI;oBAAE,IAAI;gBAAM;gBAChB,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,0HAAA,CAAA,cAAW,CAAC,GAAG,EAAE;YAClC;YAEA,2EAA2E;YAC3E,wBAAwB;YACxB,2EAA2E;YAE3E;;SAEC,GACD,iBAAiB,CAAC;gBAChB,MAAM,KAAK,CAAA,GAAA,sHAAA,CAAA,yBAAsB,AAAD;gBAChC,MAAM,YAAY,KAAK,GAAG;gBAC1B,MAAM,WAAW,aAAa,QAAQ,IAAI,CAAA,GAAA,sHAAA,CAAA,iCAA8B,AAAD,EAAE,aAAa,IAAI;gBAE1F,MAAM,kBAAkB;oBACtB,GAAG,YAAY;oBACf;oBACA;oBACA;gBACF;gBAEA,MAAM,YAAY,MAAM,EAAE;gBAC1B,MAAM,mBAAmB;uBAAI,UAAU,aAAa;oBAAE;iBAAgB;gBACtE,MAAM,QAAQ;oBAAE,GAAG,SAAS;oBAAE,eAAe;gBAAiB;gBAE9D,IAAI;oBAAE,IAAI;gBAAM;gBAChB,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,0HAAA,CAAA,cAAW,CAAC,GAAG,EAAE,oBAAoB;oBAAE,MAAM,aAAa,IAAI;oBAAE;gBAAG;gBAElF,0CAA0C;gBAC1C,IAAI,WAAW,GAAG;oBAChB,WAAW;wBACT,MAAM,kBAAkB,CAAC;oBAC3B,GAAG;gBACL;YACF;YAEA;;SAEC,GACD,oBAAoB,CAAC;gBACnB,MAAM,YAAY,MAAM,EAAE;gBAC1B,MAAM,mBAAmB,UAAU,aAAa,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBACtE,MAAM,QAAQ;oBAAE,GAAG,SAAS;oBAAE,eAAe;gBAAiB;gBAE9D,IAAI;oBAAE,IAAI;gBAAM;gBAChB,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,0HAAA,CAAA,cAAW,CAAC,GAAG,EAAE,uBAAuB;oBAAE;gBAAG;YAC9D;YAEA;;SAEC,GACD,oBAAoB;gBAClB,MAAM,YAAY,MAAM,EAAE;gBAC1B,MAAM,QAAQ;oBAAE,GAAG,SAAS;oBAAE,eAAe,EAAE;gBAAC;gBAEhD,IAAI;oBAAE,IAAI;gBAAM;gBAChB,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,0HAAA,CAAA,cAAW,CAAC,GAAG,EAAE;YAClC;YAEA,2EAA2E;YAC3E,iBAAiB;YACjB,2EAA2E;YAE3E;;SAEC,GACD,WAAW,CAAC,KAAa;gBACvB,MAAM,YAAY,MAAM,EAAE;gBAC1B,MAAM,YAAY;oBAChB,GAAG,UAAU,MAAM;oBACnB,CAAC,IAAI,EAAE;wBAAE,SAAS;wBAAM;oBAAK;gBAC/B;gBACA,MAAM,QAAQ;oBAAE,GAAG,SAAS;oBAAE,QAAQ;gBAAU;gBAEhD,IAAI;oBAAE,IAAI;gBAAM;gBAChB,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,0HAAA,CAAA,cAAW,CAAC,GAAG,EAAE,cAAc;oBAAE;oBAAK,SAAS,CAAC,CAAC;gBAAK;YACvE;YAEA;;SAEC,GACD,WAAW,CAAC;gBACV,MAAM,YAAY,MAAM,EAAE;gBAC1B,MAAM,YAAY;oBAChB,GAAG,UAAU,MAAM;oBACnB,CAAC,IAAI,EAAE;wBAAE,SAAS;wBAAO,MAAM;oBAAU;gBAC3C;gBACA,MAAM,QAAQ;oBAAE,GAAG,SAAS;oBAAE,QAAQ;gBAAU;gBAEhD,IAAI;oBAAE,IAAI;gBAAM;gBAChB,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,0HAAA,CAAA,cAAW,CAAC,GAAG,EAAE,cAAc;oBAAE;gBAAI;YACtD;YAEA;;SAEC,GACD,eAAe;gBACb,MAAM,YAAY,MAAM,EAAE;gBAC1B,MAAM,YAA8D,CAAC;gBAErE,2BAA2B;gBAC3B,OAAO,IAAI,CAAC,UAAU,MAAM,EAAE,OAAO,CAAC,CAAA;oBACpC,SAAS,CAAC,IAAI,GAAG;wBAAE,SAAS;wBAAO,MAAM;oBAAU;gBACrD;gBAEA,MAAM,QAAQ;oBAAE,GAAG,SAAS;oBAAE,QAAQ;gBAAU;gBAEhD,IAAI;oBAAE,IAAI;gBAAM;gBAChB,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,0HAAA,CAAA,cAAW,CAAC,GAAG,EAAE;YAClC;QACF,CAAC,GACD;QACE,SAAS;YACP,MAAM,0HAAA,CAAA,eAAY,CAAC,GAAG;YACtB,SAAS,0HAAA,CAAA,iBAAc,CAAC,GAAG;YAC3B,YAAY,CAAC,QAAU,CAAC;oBACtB,OAAO,MAAM,KAAK;oBAClB,UAAU,MAAM,QAAQ;oBACxB,YAAY;wBACV,kBAAkB,MAAM,UAAU,CAAC,gBAAgB;wBACnD,eAAe,MAAM,UAAU,CAAC,aAAa;oBAC/C;gBACF,CAAC;QACH;QACA,UAAU;YACR,MAAM,0HAAA,CAAA,cAAW,CAAC,GAAG;YACrB,SAAS,oDAAyB;QACpC;IACF;AAGN;AAMO,MAAM,cAAc"}}, {"offset": {"line": 1674, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1680, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/stores/app-hooks.ts/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const useActiveMenu = registerClientReference(\n    function() { throw new Error(\"Attempted to call useActiveMenu() from the server but useActiveMenu is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/app-hooks.ts <module evaluation>\",\n    \"useActiveMenu\",\n);\nexport const useApp = registerClientReference(\n    function() { throw new Error(\"Attempted to call useApp() from the server but useApp is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/app-hooks.ts <module evaluation>\",\n    \"useApp\",\n);\nexport const useAppSettings = registerClientReference(\n    function() { throw new Error(\"Attempted to call useAppSettings() from the server but useAppSettings is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/app-hooks.ts <module evaluation>\",\n    \"useAppSettings\",\n);\nexport const useAppVersion = registerClientReference(\n    function() { throw new Error(\"Attempted to call useAppVersion() from the server but useAppVersion is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/app-hooks.ts <module evaluation>\",\n    \"useAppVersion\",\n);\nexport const useBreadcrumbs = registerClientReference(\n    function() { throw new Error(\"Attempted to call useBreadcrumbs() from the server but useBreadcrumbs is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/app-hooks.ts <module evaluation>\",\n    \"useBreadcrumbs\",\n);\nexport const useBuildTime = registerClientReference(\n    function() { throw new Error(\"Attempted to call useBuildTime() from the server but useBuildTime is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/app-hooks.ts <module evaluation>\",\n    \"useBuildTime\",\n);\nexport const useCurrentPath = registerClientReference(\n    function() { throw new Error(\"Attempted to call useCurrentPath() from the server but useCurrentPath is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/app-hooks.ts <module evaluation>\",\n    \"useCurrentPath\",\n);\nexport const useEnvironment = registerClientReference(\n    function() { throw new Error(\"Attempted to call useEnvironment() from the server but useEnvironment is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/app-hooks.ts <module evaluation>\",\n    \"useEnvironment\",\n);\nexport const useGlobalError = registerClientReference(\n    function() { throw new Error(\"Attempted to call useGlobalError() from the server but useGlobalError is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/app-hooks.ts <module evaluation>\",\n    \"useGlobalError\",\n);\nexport const useGlobalLoading = registerClientReference(\n    function() { throw new Error(\"Attempted to call useGlobalLoading() from the server but useGlobalLoading is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/app-hooks.ts <module evaluation>\",\n    \"useGlobalLoading\",\n);\nexport const useIsDarkMode = registerClientReference(\n    function() { throw new Error(\"Attempted to call useIsDarkMode() from the server but useIsDarkMode is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/app-hooks.ts <module evaluation>\",\n    \"useIsDarkMode\",\n);\nexport const useModal = registerClientReference(\n    function() { throw new Error(\"Attempted to call useModal() from the server but useModal is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/app-hooks.ts <module evaluation>\",\n    \"useModal\",\n);\nexport const useModalActions = registerClientReference(\n    function() { throw new Error(\"Attempted to call useModalActions() from the server but useModalActions is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/app-hooks.ts <module evaluation>\",\n    \"useModalActions\",\n);\nexport const useModals = registerClientReference(\n    function() { throw new Error(\"Attempted to call useModals() from the server but useModals is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/app-hooks.ts <module evaluation>\",\n    \"useModals\",\n);\nexport const useNavigation = registerClientReference(\n    function() { throw new Error(\"Attempted to call useNavigation() from the server but useNavigation is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/app-hooks.ts <module evaluation>\",\n    \"useNavigation\",\n);\nexport const useNavigationActions = registerClientReference(\n    function() { throw new Error(\"Attempted to call useNavigationActions() from the server but useNavigationActions is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/app-hooks.ts <module evaluation>\",\n    \"useNavigationActions\",\n);\nexport const useNotificationActions = registerClientReference(\n    function() { throw new Error(\"Attempted to call useNotificationActions() from the server but useNotificationActions is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/app-hooks.ts <module evaluation>\",\n    \"useNotificationActions\",\n);\nexport const useNotifications = registerClientReference(\n    function() { throw new Error(\"Attempted to call useNotifications() from the server but useNotifications is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/app-hooks.ts <module evaluation>\",\n    \"useNotifications\",\n);\nexport const useNotify = registerClientReference(\n    function() { throw new Error(\"Attempted to call useNotify() from the server but useNotify is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/app-hooks.ts <module evaluation>\",\n    \"useNotify\",\n);\nexport const useResponsive = registerClientReference(\n    function() { throw new Error(\"Attempted to call useResponsive() from the server but useResponsive is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/app-hooks.ts <module evaluation>\",\n    \"useResponsive\",\n);\nexport const useSetting = registerClientReference(\n    function() { throw new Error(\"Attempted to call useSetting() from the server but useSetting is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/app-hooks.ts <module evaluation>\",\n    \"useSetting\",\n);\nexport const useSettingsActions = registerClientReference(\n    function() { throw new Error(\"Attempted to call useSettingsActions() from the server but useSettingsActions is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/app-hooks.ts <module evaluation>\",\n    \"useSettingsActions\",\n);\nexport const useSidebarState = registerClientReference(\n    function() { throw new Error(\"Attempted to call useSidebarState() from the server but useSidebarState is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/app-hooks.ts <module evaluation>\",\n    \"useSidebarState\",\n);\nexport const useSystemInfo = registerClientReference(\n    function() { throw new Error(\"Attempted to call useSystemInfo() from the server but useSystemInfo is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/app-hooks.ts <module evaluation>\",\n    \"useSystemInfo\",\n);\nexport const useTheme = registerClientReference(\n    function() { throw new Error(\"Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/app-hooks.ts <module evaluation>\",\n    \"useTheme\",\n);\nexport const useThemeActions = registerClientReference(\n    function() { throw new Error(\"Attempted to call useThemeActions() from the server but useThemeActions is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/app-hooks.ts <module evaluation>\",\n    \"useThemeActions\",\n);\nexport const useThemeMode = registerClientReference(\n    function() { throw new Error(\"Attempted to call useThemeMode() from the server but useThemeMode is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/app-hooks.ts <module evaluation>\",\n    \"useThemeMode\",\n);\nexport const useUIActions = registerClientReference(\n    function() { throw new Error(\"Attempted to call useUIActions() from the server but useUIActions is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/app-hooks.ts <module evaluation>\",\n    \"useUIActions\",\n);\nexport const useUIState = registerClientReference(\n    function() { throw new Error(\"Attempted to call useUIState() from the server but useUIState is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/app-hooks.ts <module evaluation>\",\n    \"useUIState\",\n);\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,yDACA;AAEG,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,yDACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,yDACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,yDACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,yDACA;AAEG,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,yDACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,yDACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,yDACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,yDACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,yDACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,yDACA;AAEG,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,yDACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,yDACA;AAEG,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,yDACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,yDACA;AAEG,MAAM,uBAAuB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACtD;IAAa,MAAM,IAAI,MAAM;AAAwP,GACrR,yDACA;AAEG,MAAM,yBAAyB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxD;IAAa,MAAM,IAAI,MAAM;AAA4P,GACzR,yDACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,yDACA;AAEG,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,yDACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,yDACA;AAEG,MAAM,aAAa,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,yDACA;AAEG,MAAM,qBAAqB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,yDACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,yDACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,yDACA;AAEG,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,yDACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,yDACA;AAEG,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,yDACA;AAEG,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,yDACA;AAEG,MAAM,aAAa,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,yDACA"}}, {"offset": {"line": 1800, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1806, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/stores/app-hooks.ts/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const useActiveMenu = registerClientReference(\n    function() { throw new Error(\"Attempted to call useActiveMenu() from the server but useActiveMenu is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/app-hooks.ts\",\n    \"useActiveMenu\",\n);\nexport const useApp = registerClientReference(\n    function() { throw new Error(\"Attempted to call useApp() from the server but useApp is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/app-hooks.ts\",\n    \"useApp\",\n);\nexport const useAppSettings = registerClientReference(\n    function() { throw new Error(\"Attempted to call useAppSettings() from the server but useAppSettings is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/app-hooks.ts\",\n    \"useAppSettings\",\n);\nexport const useAppVersion = registerClientReference(\n    function() { throw new Error(\"Attempted to call useAppVersion() from the server but useAppVersion is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/app-hooks.ts\",\n    \"useAppVersion\",\n);\nexport const useBreadcrumbs = registerClientReference(\n    function() { throw new Error(\"Attempted to call useBreadcrumbs() from the server but useBreadcrumbs is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/app-hooks.ts\",\n    \"useBreadcrumbs\",\n);\nexport const useBuildTime = registerClientReference(\n    function() { throw new Error(\"Attempted to call useBuildTime() from the server but useBuildTime is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/app-hooks.ts\",\n    \"useBuildTime\",\n);\nexport const useCurrentPath = registerClientReference(\n    function() { throw new Error(\"Attempted to call useCurrentPath() from the server but useCurrentPath is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/app-hooks.ts\",\n    \"useCurrentPath\",\n);\nexport const useEnvironment = registerClientReference(\n    function() { throw new Error(\"Attempted to call useEnvironment() from the server but useEnvironment is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/app-hooks.ts\",\n    \"useEnvironment\",\n);\nexport const useGlobalError = registerClientReference(\n    function() { throw new Error(\"Attempted to call useGlobalError() from the server but useGlobalError is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/app-hooks.ts\",\n    \"useGlobalError\",\n);\nexport const useGlobalLoading = registerClientReference(\n    function() { throw new Error(\"Attempted to call useGlobalLoading() from the server but useGlobalLoading is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/app-hooks.ts\",\n    \"useGlobalLoading\",\n);\nexport const useIsDarkMode = registerClientReference(\n    function() { throw new Error(\"Attempted to call useIsDarkMode() from the server but useIsDarkMode is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/app-hooks.ts\",\n    \"useIsDarkMode\",\n);\nexport const useModal = registerClientReference(\n    function() { throw new Error(\"Attempted to call useModal() from the server but useModal is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/app-hooks.ts\",\n    \"useModal\",\n);\nexport const useModalActions = registerClientReference(\n    function() { throw new Error(\"Attempted to call useModalActions() from the server but useModalActions is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/app-hooks.ts\",\n    \"useModalActions\",\n);\nexport const useModals = registerClientReference(\n    function() { throw new Error(\"Attempted to call useModals() from the server but useModals is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/app-hooks.ts\",\n    \"useModals\",\n);\nexport const useNavigation = registerClientReference(\n    function() { throw new Error(\"Attempted to call useNavigation() from the server but useNavigation is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/app-hooks.ts\",\n    \"useNavigation\",\n);\nexport const useNavigationActions = registerClientReference(\n    function() { throw new Error(\"Attempted to call useNavigationActions() from the server but useNavigationActions is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/app-hooks.ts\",\n    \"useNavigationActions\",\n);\nexport const useNotificationActions = registerClientReference(\n    function() { throw new Error(\"Attempted to call useNotificationActions() from the server but useNotificationActions is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/app-hooks.ts\",\n    \"useNotificationActions\",\n);\nexport const useNotifications = registerClientReference(\n    function() { throw new Error(\"Attempted to call useNotifications() from the server but useNotifications is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/app-hooks.ts\",\n    \"useNotifications\",\n);\nexport const useNotify = registerClientReference(\n    function() { throw new Error(\"Attempted to call useNotify() from the server but useNotify is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/app-hooks.ts\",\n    \"useNotify\",\n);\nexport const useResponsive = registerClientReference(\n    function() { throw new Error(\"Attempted to call useResponsive() from the server but useResponsive is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/app-hooks.ts\",\n    \"useResponsive\",\n);\nexport const useSetting = registerClientReference(\n    function() { throw new Error(\"Attempted to call useSetting() from the server but useSetting is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/app-hooks.ts\",\n    \"useSetting\",\n);\nexport const useSettingsActions = registerClientReference(\n    function() { throw new Error(\"Attempted to call useSettingsActions() from the server but useSettingsActions is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/app-hooks.ts\",\n    \"useSettingsActions\",\n);\nexport const useSidebarState = registerClientReference(\n    function() { throw new Error(\"Attempted to call useSidebarState() from the server but useSidebarState is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/app-hooks.ts\",\n    \"useSidebarState\",\n);\nexport const useSystemInfo = registerClientReference(\n    function() { throw new Error(\"Attempted to call useSystemInfo() from the server but useSystemInfo is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/app-hooks.ts\",\n    \"useSystemInfo\",\n);\nexport const useTheme = registerClientReference(\n    function() { throw new Error(\"Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/app-hooks.ts\",\n    \"useTheme\",\n);\nexport const useThemeActions = registerClientReference(\n    function() { throw new Error(\"Attempted to call useThemeActions() from the server but useThemeActions is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/app-hooks.ts\",\n    \"useThemeActions\",\n);\nexport const useThemeMode = registerClientReference(\n    function() { throw new Error(\"Attempted to call useThemeMode() from the server but useThemeMode is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/app-hooks.ts\",\n    \"useThemeMode\",\n);\nexport const useUIActions = registerClientReference(\n    function() { throw new Error(\"Attempted to call useUIActions() from the server but useUIActions is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/app-hooks.ts\",\n    \"useUIActions\",\n);\nexport const useUIState = registerClientReference(\n    function() { throw new Error(\"Attempted to call useUIState() from the server but useUIState is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/app-hooks.ts\",\n    \"useUIState\",\n);\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,qCACA;AAEG,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,qCACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,qCACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,qCACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,qCACA;AAEG,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,qCACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,qCACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,qCACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,qCACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,qCACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,qCACA;AAEG,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,qCACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,qCACA;AAEG,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,qCACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,qCACA;AAEG,MAAM,uBAAuB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACtD;IAAa,MAAM,IAAI,MAAM;AAAwP,GACrR,qCACA;AAEG,MAAM,yBAAyB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxD;IAAa,MAAM,IAAI,MAAM;AAA4P,GACzR,qCACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,qCACA;AAEG,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,qCACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,qCACA;AAEG,MAAM,aAAa,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,qCACA;AAEG,MAAM,qBAAqB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,qCACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,qCACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,qCACA;AAEG,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,qCACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,qCACA;AAEG,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,qCACA;AAEG,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,qCACA;AAEG,MAAM,aAAa,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,qCACA"}}, {"offset": {"line": 1926, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1932, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 1936, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1942, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/stores/app-utils.ts"], "sourcesContent": ["/**\n * Application Utilities\n * Helper functions for application operations\n */\n\nimport type { ThemeConfig, AppSettings, NavigationState, UIState } from './types';\nimport { useAppStore } from './app-store';\n\n// ============================================================================\n// Theme Utilities\n// ============================================================================\n\n/**\n * Get current theme configuration\n */\nexport const getCurrentTheme = (): ThemeConfig => {\n  return useAppStore.getState().theme;\n};\n\n/**\n * Get current theme mode\n */\nexport const getCurrentThemeMode = (): 'light' | 'dark' => {\n  return useAppStore.getState().theme.mode;\n};\n\n/**\n * Check if dark mode is active\n */\nexport const isDarkMode = (): boolean => {\n  return getCurrentThemeMode() === 'dark';\n};\n\n/**\n * Toggle theme mode\n */\nexport const toggleTheme = (): void => {\n  useAppStore.getState().toggleTheme();\n};\n\n/**\n * Set theme mode\n */\nexport const setThemeMode = (mode: 'light' | 'dark'): void => {\n  useAppStore.getState().setTheme({ mode });\n};\n\n/**\n * Apply theme to document\n */\nexport const applyThemeToDocument = (): void => {\n  if (typeof document === 'undefined') return;\n\n  const theme = getCurrentTheme();\n  const { mode, primaryColor, borderRadius } = theme;\n\n  // Apply theme class to body\n  document.body.className = document.body.className.replace(/theme-\\w+/g, '');\n  document.body.classList.add(`theme-${mode}`);\n\n  // Apply CSS custom properties\n  const root = document.documentElement;\n  root.style.setProperty('--primary-color', primaryColor);\n  root.style.setProperty('--border-radius', `${borderRadius}px`);\n};\n\n// ============================================================================\n// Settings Utilities\n// ============================================================================\n\n/**\n * Get current application settings\n */\nexport const getCurrentSettings = (): AppSettings => {\n  return useAppStore.getState().settings;\n};\n\n/**\n * Get specific setting value\n */\nexport const getSetting = <K extends keyof AppSettings>(key: K): AppSettings[K] => {\n  return useAppStore.getState().settings[key];\n};\n\n/**\n * Update application settings\n */\nexport const updateSettings = (settings: Partial<AppSettings>): void => {\n  useAppStore.getState().updateSettings(settings);\n};\n\n/**\n * Reset settings to default\n */\nexport const resetSettings = (): void => {\n  useAppStore.getState().resetSettings();\n};\n\n// ============================================================================\n// Navigation Utilities\n// ============================================================================\n\n/**\n * Get current navigation state\n */\nexport const getCurrentNavigation = (): NavigationState => {\n  return useAppStore.getState().navigation;\n};\n\n/**\n * Get current path\n */\nexport const getCurrentPath = (): string => {\n  return useAppStore.getState().navigation.currentPath;\n};\n\n/**\n * Set current path\n */\nexport const setCurrentPath = (path: string): void => {\n  useAppStore.getState().setCurrentPath(path);\n};\n\n/**\n * Get breadcrumbs\n */\nexport const getBreadcrumbs = (): NavigationState['breadcrumbs'] => {\n  return useAppStore.getState().navigation.breadcrumbs;\n};\n\n/**\n * Set breadcrumbs\n */\nexport const setBreadcrumbs = (breadcrumbs: NavigationState['breadcrumbs']): void => {\n  useAppStore.getState().setBreadcrumbs(breadcrumbs);\n};\n\n/**\n * Check if sidebar is collapsed\n */\nexport const isSidebarCollapsed = (): boolean => {\n  return useAppStore.getState().navigation.sidebarCollapsed;\n};\n\n/**\n * Toggle sidebar\n */\nexport const toggleSidebar = (): void => {\n  useAppStore.getState().toggleSidebar();\n};\n\n/**\n * Get active menu key\n */\nexport const getActiveMenuKey = (): string => {\n  return useAppStore.getState().navigation.activeMenuKey;\n};\n\n/**\n * Set active menu key\n */\nexport const setActiveMenu = (key: string): void => {\n  useAppStore.getState().setActiveMenu(key);\n};\n\n// ============================================================================\n// UI State Utilities\n// ============================================================================\n\n/**\n * Get current UI state\n */\nexport const getCurrentUIState = (): UIState => {\n  return useAppStore.getState().ui;\n};\n\n/**\n * Check if global loading is active\n */\nexport const isGlobalLoading = (): boolean => {\n  return useAppStore.getState().ui.globalLoading;\n};\n\n/**\n * Set global loading state\n */\nexport const setGlobalLoading = (loading: boolean, message?: string): void => {\n  useAppStore.getState().setGlobalLoading(loading, message);\n};\n\n/**\n * Get global error\n */\nexport const getGlobalError = (): string | null => {\n  return useAppStore.getState().ui.globalError;\n};\n\n/**\n * Set global error\n */\nexport const setGlobalError = (error: string | null, details?: any): void => {\n  useAppStore.getState().setGlobalError(error, details);\n};\n\n/**\n * Clear global error\n */\nexport const clearGlobalError = (): void => {\n  useAppStore.getState().clearGlobalError();\n};\n\n// ============================================================================\n// Notifications Utilities\n// ============================================================================\n\n/**\n * Get current notifications\n */\nexport const getNotifications = (): UIState['notifications'] => {\n  return useAppStore.getState().ui.notifications;\n};\n\n/**\n * Add notification\n */\nexport const addNotification = (\n  notification: Omit<UIState['notifications'][0], 'id' | 'timestamp'>\n): void => {\n  useAppStore.getState().addNotification(notification);\n};\n\n/**\n * Remove notification\n */\nexport const removeNotification = (id: string): void => {\n  useAppStore.getState().removeNotification(id);\n};\n\n/**\n * Clear all notifications\n */\nexport const clearNotifications = (): void => {\n  useAppStore.getState().clearNotifications();\n};\n\n/**\n * Show success notification\n */\nexport const notifySuccess = (message: string, title?: string): void => {\n  addNotification({\n    type: 'success',\n    title: title || 'Success',\n    message,\n  });\n};\n\n/**\n * Show error notification\n */\nexport const notifyError = (message: string, title?: string): void => {\n  addNotification({\n    type: 'error',\n    title: title || 'Error',\n    message,\n  });\n};\n\n/**\n * Show warning notification\n */\nexport const notifyWarning = (message: string, title?: string): void => {\n  addNotification({\n    type: 'warning',\n    title: title || 'Warning',\n    message,\n  });\n};\n\n/**\n * Show info notification\n */\nexport const notifyInfo = (message: string, title?: string): void => {\n  addNotification({\n    type: 'info',\n    title: title || 'Info',\n    message,\n  });\n};\n\n// ============================================================================\n// Modals Utilities\n// ============================================================================\n\n/**\n * Get modals state\n */\nexport const getModalsState = (): UIState['modals'] => {\n  return useAppStore.getState().ui.modals;\n};\n\n/**\n * Check if modal is visible\n */\nexport const isModalVisible = (key: string): boolean => {\n  const modal = useAppStore.getState().ui.modals[key];\n  return modal?.visible || false;\n};\n\n/**\n * Get modal data\n */\nexport const getModalData = (key: string): any => {\n  const modal = useAppStore.getState().ui.modals[key];\n  return modal?.data;\n};\n\n/**\n * Show modal\n */\nexport const showModal = (key: string, data?: any): void => {\n  useAppStore.getState().showModal(key, data);\n};\n\n/**\n * Hide modal\n */\nexport const hideModal = (key: string): void => {\n  useAppStore.getState().hideModal(key);\n};\n\n/**\n * Hide all modals\n */\nexport const hideAllModals = (): void => {\n  useAppStore.getState().hideAllModals();\n};\n\n// ============================================================================\n// System Info Utilities\n// ============================================================================\n\n/**\n * Get application version\n */\nexport const getAppVersion = (): string => {\n  return useAppStore.getState().version;\n};\n\n/**\n * Get build time\n */\nexport const getBuildTime = (): string => {\n  return useAppStore.getState().buildTime;\n};\n\n/**\n * Get environment\n */\nexport const getEnvironment = (): 'development' | 'staging' | 'production' => {\n  return useAppStore.getState().environment;\n};\n\n/**\n * Check if development environment\n */\nexport const isDevelopment = (): boolean => {\n  return getEnvironment() === 'development';\n};\n\n/**\n * Check if production environment\n */\nexport const isProduction = (): boolean => {\n  return getEnvironment() === 'production';\n};\n\n// ============================================================================\n// Responsive Utilities\n// ============================================================================\n\n/**\n * Check if current viewport is mobile\n */\nexport const isMobile = (): boolean => {\n  if (typeof window === 'undefined') return false;\n  return window.innerWidth < 768;\n};\n\n/**\n * Check if current viewport is tablet\n */\nexport const isTablet = (): boolean => {\n  if (typeof window === 'undefined') return false;\n  return window.innerWidth >= 768 && window.innerWidth < 1024;\n};\n\n/**\n * Check if current viewport is desktop\n */\nexport const isDesktop = (): boolean => {\n  if (typeof window === 'undefined') return false;\n  return window.innerWidth >= 1024;\n};\n\n/**\n * Get current breakpoint\n */\nexport const getCurrentBreakpoint = (): 'mobile' | 'tablet' | 'desktop' => {\n  if (isMobile()) return 'mobile';\n  if (isTablet()) return 'tablet';\n  return 'desktop';\n};\n\n// ============================================================================\n// Development Utilities\n// ============================================================================\n\n/**\n * Get full application state (development only)\n */\nexport const getAppState = () => {\n  if (process.env.NODE_ENV !== 'development') {\n    return null;\n  }\n\n  return useAppStore.getState();\n};\n\n/**\n * Reset application state (development/testing only)\n */\nexport const resetAppState = (): void => {\n  if (process.env.NODE_ENV !== 'development') {\n    return;\n  }\n\n  const store = useAppStore.getState();\n  store.resetSettings();\n  store.clearNotifications();\n  store.hideAllModals();\n  store.clearGlobalError();\n  store.setGlobalLoading(false);\n};\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGD;;AASO,MAAM,kBAAkB;IAC7B,OAAO,6HAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,KAAK;AACrC;AAKO,MAAM,sBAAsB;IACjC,OAAO,6HAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,KAAK,CAAC,IAAI;AAC1C;AAKO,MAAM,aAAa;IACxB,OAAO,0BAA0B;AACnC;AAKO,MAAM,cAAc;IACzB,6HAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,WAAW;AACpC;AAKO,MAAM,eAAe,CAAC;IAC3B,6HAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAAE;IAAK;AACzC;AAKO,MAAM,uBAAuB;IAClC,IAAI,OAAO,aAAa,aAAa;IAErC,MAAM,QAAQ;IACd,MAAM,EAAE,IAAI,EAAE,YAAY,EAAE,YAAY,EAAE,GAAG;IAE7C,4BAA4B;IAC5B,SAAS,IAAI,CAAC,SAAS,GAAG,SAAS,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,cAAc;IACxE,SAAS,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,MAAM;IAE3C,8BAA8B;IAC9B,MAAM,OAAO,SAAS,eAAe;IACrC,KAAK,KAAK,CAAC,WAAW,CAAC,mBAAmB;IAC1C,KAAK,KAAK,CAAC,WAAW,CAAC,mBAAmB,GAAG,aAAa,EAAE,CAAC;AAC/D;AASO,MAAM,qBAAqB;IAChC,OAAO,6HAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,QAAQ;AACxC;AAKO,MAAM,aAAa,CAA8B;IACtD,OAAO,6HAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,QAAQ,CAAC,IAAI;AAC7C;AAKO,MAAM,iBAAiB,CAAC;IAC7B,6HAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,cAAc,CAAC;AACxC;AAKO,MAAM,gBAAgB;IAC3B,6HAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,aAAa;AACtC;AASO,MAAM,uBAAuB;IAClC,OAAO,6HAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,UAAU;AAC1C;AAKO,MAAM,iBAAiB;IAC5B,OAAO,6HAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,UAAU,CAAC,WAAW;AACtD;AAKO,MAAM,iBAAiB,CAAC;IAC7B,6HAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,cAAc,CAAC;AACxC;AAKO,MAAM,iBAAiB;IAC5B,OAAO,6HAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,UAAU,CAAC,WAAW;AACtD;AAKO,MAAM,iBAAiB,CAAC;IAC7B,6HAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,cAAc,CAAC;AACxC;AAKO,MAAM,qBAAqB;IAChC,OAAO,6HAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,UAAU,CAAC,gBAAgB;AAC3D;AAKO,MAAM,gBAAgB;IAC3B,6HAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,aAAa;AACtC;AAKO,MAAM,mBAAmB;IAC9B,OAAO,6HAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,UAAU,CAAC,aAAa;AACxD;AAKO,MAAM,gBAAgB,CAAC;IAC5B,6HAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,aAAa,CAAC;AACvC;AASO,MAAM,oBAAoB;IAC/B,OAAO,6HAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,EAAE;AAClC;AAKO,MAAM,kBAAkB;IAC7B,OAAO,6HAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,EAAE,CAAC,aAAa;AAChD;AAKO,MAAM,mBAAmB,CAAC,SAAkB;IACjD,6HAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,gBAAgB,CAAC,SAAS;AACnD;AAKO,MAAM,iBAAiB;IAC5B,OAAO,6HAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,EAAE,CAAC,WAAW;AAC9C;AAKO,MAAM,iBAAiB,CAAC,OAAsB;IACnD,6HAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,cAAc,CAAC,OAAO;AAC/C;AAKO,MAAM,mBAAmB;IAC9B,6HAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,gBAAgB;AACzC;AASO,MAAM,mBAAmB;IAC9B,OAAO,6HAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,EAAE,CAAC,aAAa;AAChD;AAKO,MAAM,kBAAkB,CAC7B;IAEA,6HAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,eAAe,CAAC;AACzC;AAKO,MAAM,qBAAqB,CAAC;IACjC,6HAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,kBAAkB,CAAC;AAC5C;AAKO,MAAM,qBAAqB;IAChC,6HAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,kBAAkB;AAC3C;AAKO,MAAM,gBAAgB,CAAC,SAAiB;IAC7C,gBAAgB;QACd,MAAM;QACN,OAAO,SAAS;QAChB;IACF;AACF;AAKO,MAAM,cAAc,CAAC,SAAiB;IAC3C,gBAAgB;QACd,MAAM;QACN,OAAO,SAAS;QAChB;IACF;AACF;AAKO,MAAM,gBAAgB,CAAC,SAAiB;IAC7C,gBAAgB;QACd,MAAM;QACN,OAAO,SAAS;QAChB;IACF;AACF;AAKO,MAAM,aAAa,CAAC,SAAiB;IAC1C,gBAAgB;QACd,MAAM;QACN,OAAO,SAAS;QAChB;IACF;AACF;AASO,MAAM,iBAAiB;IAC5B,OAAO,6HAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,EAAE,CAAC,MAAM;AACzC;AAKO,MAAM,iBAAiB,CAAC;IAC7B,MAAM,QAAQ,6HAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,EAAE,CAAC,MAAM,CAAC,IAAI;IACnD,OAAO,OAAO,WAAW;AAC3B;AAKO,MAAM,eAAe,CAAC;IAC3B,MAAM,QAAQ,6HAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,EAAE,CAAC,MAAM,CAAC,IAAI;IACnD,OAAO,OAAO;AAChB;AAKO,MAAM,YAAY,CAAC,KAAa;IACrC,6HAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,SAAS,CAAC,KAAK;AACxC;AAKO,MAAM,YAAY,CAAC;IACxB,6HAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,SAAS,CAAC;AACnC;AAKO,MAAM,gBAAgB;IAC3B,6HAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,aAAa;AACtC;AASO,MAAM,gBAAgB;IAC3B,OAAO,6HAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,OAAO;AACvC;AAKO,MAAM,eAAe;IAC1B,OAAO,6HAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,SAAS;AACzC;AAKO,MAAM,iBAAiB;IAC5B,OAAO,6HAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,WAAW;AAC3C;AAKO,MAAM,gBAAgB;IAC3B,OAAO,qBAAqB;AAC9B;AAKO,MAAM,eAAe;IAC1B,OAAO,qBAAqB;AAC9B;AASO,MAAM,WAAW;IACtB,wCAAmC,OAAO;;AAE5C;AAKO,MAAM,WAAW;IACtB,wCAAmC,OAAO;;AAE5C;AAKO,MAAM,YAAY;IACvB,wCAAmC,OAAO;;AAE5C;AAKO,MAAM,uBAAuB;IAClC,IAAI,YAAY,OAAO;IACvB,IAAI,YAAY,OAAO;IACvB,OAAO;AACT;AASO,MAAM,cAAc;IACzB,uCAA4C;;IAE5C;IAEA,OAAO,6HAAA,CAAA,cAAW,CAAC,QAAQ;AAC7B;AAKO,MAAM,gBAAgB;IAC3B,uCAA4C;;IAE5C;IAEA,MAAM,QAAQ,6HAAA,CAAA,cAAW,CAAC,QAAQ;IAClC,MAAM,aAAa;IACnB,MAAM,kBAAkB;IACxB,MAAM,aAAa;IACnB,MAAM,gBAAgB;IACtB,MAAM,gBAAgB,CAAC;AACzB"}}, {"offset": {"line": 2194, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2200, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/stores/store-provider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const StoreProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call StoreProvider() from the server but StoreProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/store-provider.tsx <module evaluation>\",\n    \"StoreProvider\",\n);\nexport const StoreProviderUtils = registerClientReference(\n    function() { throw new Error(\"Attempted to call StoreProviderUtils() from the server but StoreProviderUtils is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/store-provider.tsx <module evaluation>\",\n    \"StoreProviderUtils\",\n);\nexport const withStoreProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call withStoreProvider() from the server but withStoreProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/store-provider.tsx <module evaluation>\",\n    \"withStoreProvider\",\n);\n"], "names": [], "mappings": ";;;;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,+DACA;AAEG,MAAM,qBAAqB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,+DACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,+DACA"}}, {"offset": {"line": 2216, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2222, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/stores/store-provider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const StoreProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call StoreProvider() from the server but StoreProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/store-provider.tsx\",\n    \"StoreProvider\",\n);\nexport const StoreProviderUtils = registerClientReference(\n    function() { throw new Error(\"Attempted to call StoreProviderUtils() from the server but StoreProviderUtils is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/store-provider.tsx\",\n    \"StoreProviderUtils\",\n);\nexport const withStoreProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call withStoreProvider() from the server but withStoreProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/store-provider.tsx\",\n    \"withStoreProvider\",\n);\n"], "names": [], "mappings": ";;;;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,2CACA;AAEG,MAAM,qBAAqB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,2CACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,2CACA"}}, {"offset": {"line": 2238, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2244, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 2248, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2254, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/stores/store-context.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const StoreContextProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call StoreContextProvider() from the server but StoreContextProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/store-context.tsx <module evaluation>\",\n    \"StoreContextProvider\",\n);\nexport const useAppStoreContext = registerClientReference(\n    function() { throw new Error(\"Attempted to call useAppStoreContext() from the server but useAppStoreContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/store-context.tsx <module evaluation>\",\n    \"useAppStoreContext\",\n);\nexport const useAuthStoreContext = registerClientReference(\n    function() { throw new Error(\"Attempted to call useAuthStoreContext() from the server but useAuthStoreContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/store-context.tsx <module evaluation>\",\n    \"useAuthStoreContext\",\n);\nexport const useIsStoreContextAvailable = registerClientReference(\n    function() { throw new Error(\"Attempted to call useIsStoreContextAvailable() from the server but useIsStoreContextAvailable is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/store-context.tsx <module evaluation>\",\n    \"useIsStoreContextAvailable\",\n);\nexport const useStoreContext = registerClientReference(\n    function() { throw new Error(\"Attempted to call useStoreContext() from the server but useStoreContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/store-context.tsx <module evaluation>\",\n    \"useStoreContext\",\n);\n"], "names": [], "mappings": ";;;;;;;AAAA;;AACO,MAAM,uBAAuB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACtD;IAAa,MAAM,IAAI,MAAM;AAAwP,GACrR,8DACA;AAEG,MAAM,qBAAqB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,8DACA;AAEG,MAAM,sBAAsB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACrD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,8DACA;AAEG,MAAM,6BAA6B,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC5D;IAAa,MAAM,IAAI,MAAM;AAAoQ,GACjS,8DACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,8DACA"}}, {"offset": {"line": 2278, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2284, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/stores/store-context.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const StoreContextProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call StoreContextProvider() from the server but StoreContextProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/store-context.tsx\",\n    \"StoreContextProvider\",\n);\nexport const useAppStoreContext = registerClientReference(\n    function() { throw new Error(\"Attempted to call useAppStoreContext() from the server but useAppStoreContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/store-context.tsx\",\n    \"useAppStoreContext\",\n);\nexport const useAuthStoreContext = registerClientReference(\n    function() { throw new Error(\"Attempted to call useAuthStoreContext() from the server but useAuthStoreContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/store-context.tsx\",\n    \"useAuthStoreContext\",\n);\nexport const useIsStoreContextAvailable = registerClientReference(\n    function() { throw new Error(\"Attempted to call useIsStoreContextAvailable() from the server but useIsStoreContextAvailable is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/store-context.tsx\",\n    \"useIsStoreContextAvailable\",\n);\nexport const useStoreContext = registerClientReference(\n    function() { throw new Error(\"Attempted to call useStoreContext() from the server but useStoreContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/store-context.tsx\",\n    \"useStoreContext\",\n);\n"], "names": [], "mappings": ";;;;;;;AAAA;;AACO,MAAM,uBAAuB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACtD;IAAa,MAAM,IAAI,MAAM;AAAwP,GACrR,0CACA;AAEG,MAAM,qBAAqB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,0CACA;AAEG,MAAM,sBAAsB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACrD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,0CACA;AAEG,MAAM,6BAA6B,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC5D;IAAa,MAAM,IAAI,MAAM;AAAoQ,GACjS,0CACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,0CACA"}}, {"offset": {"line": 2308, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2314, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 2318, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2324, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/stores/provider-hooks.ts/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const useAppProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call useAppProvider() from the server but useAppProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/provider-hooks.ts <module evaluation>\",\n    \"useAppProvider\",\n);\nexport const useAuthProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call useAuthProvider() from the server but useAuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/provider-hooks.ts <module evaluation>\",\n    \"useAuthProvider\",\n);\nexport const useStoreAvailability = registerClientReference(\n    function() { throw new Error(\"Attempted to call useStoreAvailability() from the server but useStoreAvailability is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/provider-hooks.ts <module evaluation>\",\n    \"useStoreAvailability\",\n);\nexport const useStoreDebug = registerClientReference(\n    function() { throw new Error(\"Attempted to call useStoreDebug() from the server but useStoreDebug is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/provider-hooks.ts <module evaluation>\",\n    \"useStoreDebug\",\n);\nexport const useStores = registerClientReference(\n    function() { throw new Error(\"Attempted to call useStores() from the server but useStores is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/provider-hooks.ts <module evaluation>\",\n    \"useStores\",\n);\n"], "names": [], "mappings": ";;;;;;;AAAA;;AACO,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,8DACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,8DACA;AAEG,MAAM,uBAAuB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACtD;IAAa,MAAM,IAAI,MAAM;AAAwP,GACrR,8DACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,8DACA;AAEG,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,8DACA"}}, {"offset": {"line": 2348, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2354, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/stores/provider-hooks.ts/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const useAppProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call useAppProvider() from the server but useAppProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/provider-hooks.ts\",\n    \"useAppProvider\",\n);\nexport const useAuthProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call useAuthProvider() from the server but useAuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/provider-hooks.ts\",\n    \"useAuthProvider\",\n);\nexport const useStoreAvailability = registerClientReference(\n    function() { throw new Error(\"Attempted to call useStoreAvailability() from the server but useStoreAvailability is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/provider-hooks.ts\",\n    \"useStoreAvailability\",\n);\nexport const useStoreDebug = registerClientReference(\n    function() { throw new Error(\"Attempted to call useStoreDebug() from the server but useStoreDebug is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/provider-hooks.ts\",\n    \"useStoreDebug\",\n);\nexport const useStores = registerClientReference(\n    function() { throw new Error(\"Attempted to call useStores() from the server but useStores is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/stores/provider-hooks.ts\",\n    \"useStores\",\n);\n"], "names": [], "mappings": ";;;;;;;AAAA;;AACO,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,0CACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,0CACA;AAEG,MAAM,uBAAuB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACtD;IAAa,MAAM,IAAI,MAAM;AAAwP,GACrR,0CACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,0CACA;AAEG,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,0CACA"}}, {"offset": {"line": 2378, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2384, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 2388, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2394, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/stores/index.ts"], "sourcesContent": ["/**\n * Store Index - Central export for all stores\n * Provides barrel exports for all store modules\n */\n\n// Store types and interfaces\nexport * from './types';\n\n// Store utilities\nexport * from './utils';\n\n// Individual stores\nexport * from './auth-store';\nexport * from './auth-hooks';\nexport * from './auth-utils';\nexport * from './app-store';\nexport * from './app-hooks';\nexport * from './app-utils';\n\n// Store providers and context\nexport * from './store-provider';\nexport * from './store-context';\nexport * from './provider-hooks';\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,6BAA6B"}}, {"offset": {"line": 2410, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2435, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/lib/query-provider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const QueryProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call QueryProvider() from the server but QueryProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/lib/query-provider.tsx <module evaluation>\",\n    \"QueryProvider\",\n);\nexport const QueryProviderUtils = registerClientReference(\n    function() { throw new Error(\"Attempted to call QueryProviderUtils() from the server but QueryProviderUtils is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/lib/query-provider.tsx <module evaluation>\",\n    \"QueryProviderUtils\",\n);\nexport const QueryProviderWithErrorBoundary = registerClientReference(\n    function() { throw new Error(\"Attempted to call QueryProviderWithErrorBoundary() from the server but QueryProviderWithErrorBoundary is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/lib/query-provider.tsx <module evaluation>\",\n    \"QueryProviderWithErrorBoundary\",\n);\nexport const withQueryProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call withQueryProvider() from the server but withQueryProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/lib/query-provider.tsx <module evaluation>\",\n    \"withQueryProvider\",\n);\n"], "names": [], "mappings": ";;;;;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,4DACA;AAEG,MAAM,qBAAqB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,4DACA;AAEG,MAAM,iCAAiC,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChE;IAAa,MAAM,IAAI,MAAM;AAA4Q,GACzS,4DACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,4DACA"}}, {"offset": {"line": 2455, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2461, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/lib/query-provider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const QueryProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call QueryProvider() from the server but QueryProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/lib/query-provider.tsx\",\n    \"QueryProvider\",\n);\nexport const QueryProviderUtils = registerClientReference(\n    function() { throw new Error(\"Attempted to call QueryProviderUtils() from the server but QueryProviderUtils is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/lib/query-provider.tsx\",\n    \"QueryProviderUtils\",\n);\nexport const QueryProviderWithErrorBoundary = registerClientReference(\n    function() { throw new Error(\"Attempted to call QueryProviderWithErrorBoundary() from the server but QueryProviderWithErrorBoundary is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/lib/query-provider.tsx\",\n    \"QueryProviderWithErrorBoundary\",\n);\nexport const withQueryProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call withQueryProvider() from the server but withQueryProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/lib/query-provider.tsx\",\n    \"withQueryProvider\",\n);\n"], "names": [], "mappings": ";;;;;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,wCACA;AAEG,MAAM,qBAAqB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,wCACA;AAEG,MAAM,iCAAiC,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChE;IAAa,MAAM,IAAI,MAAM;AAA4Q,GACzS,wCACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,wCACA"}}, {"offset": {"line": 2481, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2487, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 2491, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2497, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/providers/index.ts"], "sourcesContent": ["/**\n * Providers Index\n * Central export for all provider components\n */\n\n// App provider (combined)\nexport * from './app-provider';\n\n// Individual providers (re-exported for convenience)\nexport { StoreProvider, StoreProviderUtils } from '@/stores';\nexport {\n  QueryProvider,\n  QueryProviderWithErrorBoundary,\n  QueryProviderUtils\n} from '@/lib/query-provider';\n\n// Import for internal use\nimport { QueryProviderUtils as QProviderUtils } from '@/lib/query-provider';\n\n/**\n * Provider utilities\n */\nexport const ProviderUtils = {\n  /**\n   * Check if all providers are properly initialized\n   */\n  checkProviderStatus: () => {\n    const storeAvailable = true; // StoreProvider is always available when rendered\n    const queryAvailable = QProviderUtils.isQueryClientAvailable();\n\n    return {\n      store: storeAvailable,\n      query: queryAvailable,\n      all: storeAvailable && queryAvailable,\n    };\n  },\n\n  /**\n   * Reset all providers (development only)\n   */\n  resetAllProviders: () => {\n    if (process.env.NODE_ENV === 'development') {\n      QProviderUtils.resetQueryClient();\n      console.log('[Dev] All providers reset');\n    }\n  },\n} as const;\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,0BAA0B;;;;AAW1B,0BAA0B;AAC1B;;;;;AAKO,MAAM,gBAAgB;IAC3B;;GAEC,GACD,qBAAqB;QACnB,MAAM,iBAAiB,MAAM,kDAAkD;QAC/E,MAAM,iBAAiB,gIAAA,CAAA,qBAAc,CAAC,sBAAsB;QAE5D,OAAO;YACL,OAAO;YACP,OAAO;YACP,KAAK,kBAAkB;QACzB;IACF;IAEA;;GAEC,GACD,mBAAmB;QACjB,wCAA4C;YAC1C,gIAAA,CAAA,qBAAc,CAAC,gBAAgB;YAC/B,QAAQ,GAAG,CAAC;QACd;IACF;AACF"}}, {"offset": {"line": 2531, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2548, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/layout/app-layout.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AppLayout = registerClientReference(\n    function() { throw new Error(\"Attempted to call AppLayout() from the server but AppLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/app-layout.tsx <module evaluation>\",\n    \"AppLayout\",\n);\nexport const LayoutProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call LayoutProvider() from the server but LayoutProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/app-layout.tsx <module evaluation>\",\n    \"LayoutProvider\",\n);\nexport const SimpleLayout = registerClientReference(\n    function() { throw new Error(\"Attempted to call SimpleLayout() from the server but SimpleLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/app-layout.tsx <module evaluation>\",\n    \"SimpleLayout\",\n);\nexport const useLayout = registerClientReference(\n    function() { throw new Error(\"Attempted to call useLayout() from the server but useLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/app-layout.tsx <module evaluation>\",\n    \"useLayout\",\n);\n"], "names": [], "mappings": ";;;;;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,sEACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,sEACA;AAEG,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,sEACA;AAEG,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,sEACA"}}, {"offset": {"line": 2568, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2574, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/layout/app-layout.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AppLayout = registerClientReference(\n    function() { throw new Error(\"Attempted to call AppLayout() from the server but AppLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/app-layout.tsx\",\n    \"AppLayout\",\n);\nexport const LayoutProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call LayoutProvider() from the server but LayoutProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/app-layout.tsx\",\n    \"LayoutProvider\",\n);\nexport const SimpleLayout = registerClientReference(\n    function() { throw new Error(\"Attempted to call SimpleLayout() from the server but SimpleLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/app-layout.tsx\",\n    \"SimpleLayout\",\n);\nexport const useLayout = registerClientReference(\n    function() { throw new Error(\"Attempted to call useLayout() from the server but useLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/app-layout.tsx\",\n    \"useLayout\",\n);\n"], "names": [], "mappings": ";;;;;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,kDACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,kDACA;AAEG,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,kDACA;AAEG,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,kDACA"}}, {"offset": {"line": 2594, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2600, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 2604, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2610, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/layout/app-header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AppHeader = registerClientReference(\n    function() { throw new Error(\"Attempted to call AppHeader() from the server but AppHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/app-header.tsx <module evaluation>\",\n    \"AppHeader\",\n);\nexport const HeaderBreadcrumb = registerClientReference(\n    function() { throw new Error(\"Attempted to call HeaderBreadcrumb() from the server but HeaderBreadcrumb is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/app-header.tsx <module evaluation>\",\n    \"HeaderBreadcrumb\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,sEACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,sEACA"}}, {"offset": {"line": 2622, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2628, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/layout/app-header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AppHeader = registerClientReference(\n    function() { throw new Error(\"Attempted to call AppHeader() from the server but AppHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/app-header.tsx\",\n    \"AppHeader\",\n);\nexport const HeaderBreadcrumb = registerClientReference(\n    function() { throw new Error(\"Attempted to call HeaderBreadcrumb() from the server but HeaderBreadcrumb is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/app-header.tsx\",\n    \"HeaderBreadcrumb\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,kDACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,kDACA"}}, {"offset": {"line": 2640, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2646, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 2650, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2656, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/layout/app-sidebar.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AppSidebar = registerClientReference(\n    function() { throw new Error(\"Attempted to call AppSidebar() from the server but AppSidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/app-sidebar.tsx <module evaluation>\",\n    \"AppSidebar\",\n);\nexport const SidebarMenuItem = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenuItem() from the server but SidebarMenuItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/app-sidebar.tsx <module evaluation>\",\n    \"SidebarMenuItem\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,aAAa,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,uEACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,uEACA"}}, {"offset": {"line": 2668, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2674, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/layout/app-sidebar.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AppSidebar = registerClientReference(\n    function() { throw new Error(\"Attempted to call AppSidebar() from the server but AppSidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/app-sidebar.tsx\",\n    \"AppSidebar\",\n);\nexport const SidebarMenuItem = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenuItem() from the server but SidebarMenuItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/app-sidebar.tsx\",\n    \"SidebarMenuItem\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,aAAa,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,mDACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,mDACA"}}, {"offset": {"line": 2686, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2692, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 2696, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2702, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/layout/app-footer.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AppFooter = registerClientReference(\n    function() { throw new Error(\"Attempted to call AppFooter() from the server but AppFooter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/app-footer.tsx <module evaluation>\",\n    \"AppFooter\",\n);\nexport const SimpleFooter = registerClientReference(\n    function() { throw new Error(\"Attempted to call SimpleFooter() from the server but <PERSON>Footer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/app-footer.tsx <module evaluation>\",\n    \"SimpleFooter\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,sEACA;AAEG,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,sEACA"}}, {"offset": {"line": 2714, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2720, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/layout/app-footer.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AppFooter = registerClientReference(\n    function() { throw new Error(\"Attempted to call AppFooter() from the server but AppFooter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/app-footer.tsx\",\n    \"AppFooter\",\n);\nexport const SimpleFooter = registerClientReference(\n    function() { throw new Error(\"Attempted to call SimpleFooter() from the server but <PERSON>Footer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/app-footer.tsx\",\n    \"SimpleFooter\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,kDACA;AAEG,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,kDACA"}}, {"offset": {"line": 2732, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2738, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 2742, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2748, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/layout/auth-layout.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AuthCard = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuthCard() from the server but AuthCard is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/auth-layout.tsx <module evaluation>\",\n    \"AuthCard\",\n);\nexport const AuthDivider = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuthDivider() from the server but AuthDivider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/auth-layout.tsx <module evaluation>\",\n    \"AuthDivider\",\n);\nexport const AuthForm = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuthForm() from the server but AuthForm is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/auth-layout.tsx <module evaluation>\",\n    \"AuthForm\",\n);\nexport const AuthLayout = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuthLayout() from the server but AuthLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/auth-layout.tsx <module evaluation>\",\n    \"AuthLayout\",\n);\n"], "names": [], "mappings": ";;;;;;AAAA;;AACO,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,uEACA;AAEG,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,uEACA;AAEG,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,uEACA;AAEG,MAAM,aAAa,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,uEACA"}}, {"offset": {"line": 2768, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2774, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/layout/auth-layout.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AuthCard = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuthCard() from the server but AuthCard is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/auth-layout.tsx\",\n    \"AuthCard\",\n);\nexport const AuthDivider = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuthDivider() from the server but AuthDivider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/auth-layout.tsx\",\n    \"AuthDivider\",\n);\nexport const AuthForm = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuthForm() from the server but AuthForm is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/auth-layout.tsx\",\n    \"AuthForm\",\n);\nexport const AuthLayout = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuthLayout() from the server but AuthLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/auth-layout.tsx\",\n    \"AuthLayout\",\n);\n"], "names": [], "mappings": ";;;;;;AAAA;;AACO,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,mDACA;AAEG,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,mDACA;AAEG,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,mDACA;AAEG,MAAM,aAAa,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,mDACA"}}, {"offset": {"line": 2794, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2800, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 2804, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2810, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/layout/page-header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const PageHeader = registerClientReference(\n    function() { throw new Error(\"Attempted to call PageHeader() from the server but PageHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/page-header.tsx <module evaluation>\",\n    \"PageHeader\",\n);\nexport const SectionHeader = registerClientReference(\n    function() { throw new Error(\"Attempted to call SectionHeader() from the server but SectionHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/page-header.tsx <module evaluation>\",\n    \"SectionHeader\",\n);\nexport const SimplePageHeader = registerClientReference(\n    function() { throw new Error(\"Attempted to call <PERSON><PERSON>ageHeader() from the server but SimplePageHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/page-header.tsx <module evaluation>\",\n    \"SimplePageHeader\",\n);\n"], "names": [], "mappings": ";;;;;AAAA;;AACO,MAAM,aAAa,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,uEACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,uEACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,uEACA"}}, {"offset": {"line": 2826, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2832, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/layout/page-header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const PageHeader = registerClientReference(\n    function() { throw new Error(\"Attempted to call PageHeader() from the server but PageHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/page-header.tsx\",\n    \"PageHeader\",\n);\nexport const SectionHeader = registerClientReference(\n    function() { throw new Error(\"Attempted to call SectionHeader() from the server but SectionHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/page-header.tsx\",\n    \"SectionHeader\",\n);\nexport const SimplePageHeader = registerClientReference(\n    function() { throw new Error(\"Attempted to call SimplePageHeader() from the server but SimplePageHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/page-header.tsx\",\n    \"SimplePageHeader\",\n);\n"], "names": [], "mappings": ";;;;;AAAA;;AACO,MAAM,aAAa,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,mDACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,mDACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,mDACA"}}, {"offset": {"line": 2848, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2854, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 2858, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2864, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/layout/content-layout.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Container = registerClientReference(\n    function() { throw new Error(\"Attempted to call Container() from the server but Container is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/content-layout.tsx <module evaluation>\",\n    \"Container\",\n);\nexport const ContentLayout = registerClientReference(\n    function() { throw new Error(\"Attempted to call ContentLayout() from the server but ContentLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/content-layout.tsx <module evaluation>\",\n    \"ContentLayout\",\n);\nexport const GridLayout = registerClientReference(\n    function() { throw new Error(\"Attempted to call GridLayout() from the server but GridLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/content-layout.tsx <module evaluation>\",\n    \"GridLayout\",\n);\nexport const SidebarLayout = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarLayout() from the server but SidebarLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/content-layout.tsx <module evaluation>\",\n    \"SidebarLayout\",\n);\nexport const ThreeColumnLayout = registerClientReference(\n    function() { throw new Error(\"Attempted to call ThreeColumnLayout() from the server but ThreeColumnLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/content-layout.tsx <module evaluation>\",\n    \"ThreeColumnLayout\",\n);\nexport const TwoColumnLayout = registerClientReference(\n    function() { throw new Error(\"Attempted to call TwoColumnLayout() from the server but TwoColumnLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/content-layout.tsx <module evaluation>\",\n    \"TwoColumnLayout\",\n);\n"], "names": [], "mappings": ";;;;;;;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,0EACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,0EACA;AAEG,MAAM,aAAa,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,0EACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,0EACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,0EACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,0EACA"}}, {"offset": {"line": 2892, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2898, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/layout/content-layout.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Container = registerClientReference(\n    function() { throw new Error(\"Attempted to call Container() from the server but Container is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/content-layout.tsx\",\n    \"Container\",\n);\nexport const ContentLayout = registerClientReference(\n    function() { throw new Error(\"Attempted to call ContentLayout() from the server but ContentLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/content-layout.tsx\",\n    \"ContentLayout\",\n);\nexport const GridLayout = registerClientReference(\n    function() { throw new Error(\"Attempted to call GridLayout() from the server but GridLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/content-layout.tsx\",\n    \"GridLayout\",\n);\nexport const SidebarLayout = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarLayout() from the server but SidebarLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/content-layout.tsx\",\n    \"SidebarLayout\",\n);\nexport const ThreeColumnLayout = registerClientReference(\n    function() { throw new Error(\"Attempted to call ThreeColumnLayout() from the server but ThreeColumnLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/content-layout.tsx\",\n    \"ThreeColumnLayout\",\n);\nexport const TwoColumnLayout = registerClientReference(\n    function() { throw new Error(\"Attempted to call TwoColumnLayout() from the server but TwoColumnLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/content-layout.tsx\",\n    \"TwoColumnLayout\",\n);\n"], "names": [], "mappings": ";;;;;;;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,sDACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,sDACA;AAEG,MAAM,aAAa,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,sDACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,sDACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,sDACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,sDACA"}}, {"offset": {"line": 2926, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2932, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 2936, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2942, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/layout/index.ts"], "sourcesContent": ["/**\n * Layout Components Index\n * Export all layout components\n */\n\n// Main app layout components\nexport * from './app-layout';\nexport * from './app-header';\nexport * from './app-sidebar';\nexport * from './app-footer';\n\n// Authentication layout components\nexport * from './auth-layout';\n\n// Page header components\nexport * from './page-header';\n\n// Content layout components\nexport * from './content-layout';\n\n// Re-export Ant Design layout components for convenience\nexport { Layout } from 'antd';\n\n// Export specific layout components\nimport { Layout } from 'antd';\nconst { Header, Footer, Sider, Content } = Layout;\nexport { Header, Footer, Sider, Content };\n\n/**\n * Layout components metadata\n */\nexport const LAYOUT_COMPONENTS_VERSION = '1.0.0';\nexport const LAYOUT_COMPONENTS_NAME = 'APISportsGame Layout Components';\n\n/**\n * Setup function for layout components\n */\nexport function setupLayoutComponents() {\n  console.log(`${LAYOUT_COMPONENTS_NAME} v${LAYOUT_COMPONENTS_VERSION} initialized`);\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,6BAA6B;;;;;;;;;;AAkB7B,oCAAoC;AACpC;;;;;;;;;;AACA,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,mIAAA,CAAA,SAAM;;AAM1C,MAAM,4BAA4B;AAClC,MAAM,yBAAyB;AAK/B,SAAS;IACd,QAAQ,GAAG,CAAC,GAAG,uBAAuB,EAAE,EAAE,0BAA0B,YAAY,CAAC;AACnF"}}, {"offset": {"line": 2973, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3020, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/app/layout.tsx"], "sourcesContent": ["import type { Metada<PERSON> } from \"next\";\nimport { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from \"next/font/google\";\nimport \"./globals.css\";\nimport { AppProvider } from \"@/providers\";\nimport { AppLayout } from \"@/components/layout\";\n\nconst geistSans = Geist({\n  variable: \"--font-geist-sans\",\n  subsets: [\"latin\"],\n});\n\nconst geistMono = Geist_Mono({\n  variable: \"--font-geist-mono\",\n  subsets: [\"latin\"],\n});\n\nexport const metadata: Metadata = {\n  title: \"APISportsGame CMS\",\n  description: \"Frontend CMS for APISportsGame - Manage tournaments, matches, teams, and users\",\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"en\">\n      <body\n        className={`${geistSans.variable} ${geistMono.variable} antialiased`}\n      >\n        <AppProvider>\n          <AppLayout>\n            {children}\n          </AppLayout>\n        </AppProvider>\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;AAGA;AACA;AADA;AACA;;;;;;;AAYO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;AACf;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,8OAAC;QAAK,MAAK;kBACT,cAAA,8OAAC;YACC,WAAW,GAAG,yIAAA,CAAA,UAAS,CAAC,QAAQ,CAAC,CAAC,EAAE,8IAAA,CAAA,UAAS,CAAC,QAAQ,CAAC,YAAY,CAAC;sBAEpE,cAAA,8OAAC,oIAAA,CAAA,cAAW;0BACV,cAAA,8OAAC,qJAAA,CAAA,YAAS;8BACP;;;;;;;;;;;;;;;;;;;;;AAMb"}}, {"offset": {"line": 3070, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}