{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/types/broadcast.ts"], "sourcesContent": ["/**\n * Broadcast Links Types and Interfaces\n * Comprehensive type definitions for broadcast link management\n */\n\n// Re-export from query types for consistency\nexport type {\n  BroadcastQueries as BroadcastTypes\n} from '@/lib/query-types';\n\n/**\n * Broadcast link quality options\n */\nexport const BROADCAST_QUALITIES = ['HD', 'SD', 'Mobile'] as const;\nexport type BroadcastQuality = typeof BROADCAST_QUALITIES[number];\n\n/**\n * Common broadcast languages\n */\nexport const BROADCAST_LANGUAGES = [\n  'English',\n  'Spanish', \n  'French',\n  'German',\n  'Italian',\n  'Portuguese',\n  'Arabic',\n  'Russian',\n  'Chinese',\n  'Japanese',\n  'Korean',\n  'Other'\n] as const;\nexport type BroadcastLanguage = typeof BROADCAST_LANGUAGES[number];\n\n/**\n * Broadcast link status\n */\nexport const BROADCAST_STATUS = ['active', 'inactive', 'pending', 'blocked'] as const;\nexport type BroadcastStatus = typeof BROADCAST_STATUS[number];\n\n/**\n * Extended broadcast link interface\n */\nexport interface BroadcastLink {\n  id: string;\n  fixtureId: string;\n  fixture?: {\n    id: string;\n    homeTeam: string;\n    awayTeam: string;\n    date: string;\n    league: string;\n    status: string;\n  };\n  url: string;\n  title?: string;\n  description?: string;\n  quality: BroadcastQuality;\n  language: string;\n  isActive: boolean;\n  status: BroadcastStatus;\n  viewCount?: number;\n  rating?: number;\n  createdBy: string;\n  createdAt: string;\n  updatedAt: string;\n  tags?: string[];\n}\n\n/**\n * Broadcast link creation request\n */\nexport interface CreateBroadcastLinkRequest {\n  fixtureId: string;\n  url: string;\n  title?: string;\n  description?: string;\n  quality: BroadcastQuality;\n  language: string;\n  tags?: string[];\n}\n\n/**\n * Broadcast link update request\n */\nexport interface UpdateBroadcastLinkRequest {\n  url?: string;\n  title?: string;\n  description?: string;\n  quality?: BroadcastQuality;\n  language?: string;\n  isActive?: boolean;\n  status?: BroadcastStatus;\n  tags?: string[];\n}\n\n/**\n * Broadcast link query parameters\n */\nexport interface BroadcastLinkQueryParams {\n  page?: number;\n  limit?: number;\n  search?: string;\n  fixtureId?: string;\n  quality?: BroadcastQuality;\n  language?: string;\n  isActive?: boolean;\n  status?: BroadcastStatus;\n  createdBy?: string;\n  sortBy?: 'createdAt' | 'updatedAt' | 'viewCount' | 'rating';\n  sortOrder?: 'asc' | 'desc';\n}\n\n/**\n * Broadcast link list response\n */\nexport interface BroadcastLinkListResponse {\n  data: BroadcastLink[];\n  total: number;\n  page: number;\n  limit: number;\n  totalPages: number;\n}\n\n/**\n * Broadcast link statistics\n */\nexport interface BroadcastLinkStatistics {\n  total: number;\n  active: number;\n  inactive: number;\n  pending: number;\n  blocked: number;\n  byQuality: Record<BroadcastQuality, number>;\n  byLanguage: Record<string, number>;\n  totalViews: number;\n  averageRating: number;\n  recentlyAdded: number; // Last 24 hours\n  topFixtures: Array<{\n    fixtureId: string;\n    fixture: string;\n    linkCount: number;\n  }>;\n}\n\n/**\n * Broadcast link form data\n */\nexport interface BroadcastLinkFormData {\n  fixtureId: string;\n  url: string;\n  title: string;\n  description: string;\n  quality: BroadcastQuality;\n  language: string;\n  tags: string[];\n}\n\n/**\n * Broadcast link validation rules\n */\nexport const BROADCAST_VALIDATION = {\n  url: {\n    required: true,\n    pattern: /^https?:\\/\\/.+/,\n    message: 'Please enter a valid URL starting with http:// or https://'\n  },\n  title: {\n    required: false,\n    minLength: 3,\n    maxLength: 100,\n    message: 'Title must be between 3 and 100 characters'\n  },\n  description: {\n    required: false,\n    maxLength: 500,\n    message: 'Description must not exceed 500 characters'\n  },\n  quality: {\n    required: true,\n    options: BROADCAST_QUALITIES,\n    message: 'Please select a valid quality option'\n  },\n  language: {\n    required: true,\n    message: 'Please select a language'\n  },\n  fixtureId: {\n    required: true,\n    message: 'Please select a fixture'\n  }\n} as const;\n\n/**\n * Broadcast link helper functions\n */\nexport const BroadcastHelpers = {\n  /**\n   * Validate broadcast link URL\n   */\n  isValidUrl: (url: string): boolean => {\n    return BROADCAST_VALIDATION.url.pattern.test(url);\n  },\n\n  /**\n   * Get quality badge color\n   */\n  getQualityColor: (quality: BroadcastQuality): string => {\n    const colors = {\n      HD: 'success',\n      SD: 'warning', \n      Mobile: 'default'\n    };\n    return colors[quality];\n  },\n\n  /**\n   * Get status badge color\n   */\n  getStatusColor: (status: BroadcastStatus): string => {\n    const colors = {\n      active: 'success',\n      inactive: 'default',\n      pending: 'processing',\n      blocked: 'error'\n    };\n    return colors[status];\n  },\n\n  /**\n   * Format view count\n   */\n  formatViewCount: (count: number): string => {\n    if (count >= 1000000) {\n      return `${(count / 1000000).toFixed(1)}M`;\n    }\n    if (count >= 1000) {\n      return `${(count / 1000).toFixed(1)}K`;\n    }\n    return count.toString();\n  },\n\n  /**\n   * Get language display name\n   */\n  getLanguageDisplayName: (language: string): string => {\n    const languageMap: Record<string, string> = {\n      'en': 'English',\n      'es': 'Spanish',\n      'fr': 'French',\n      'de': 'German',\n      'it': 'Italian',\n      'pt': 'Portuguese',\n      'ar': 'Arabic',\n      'ru': 'Russian',\n      'zh': 'Chinese',\n      'ja': 'Japanese',\n      'ko': 'Korean'\n    };\n    return languageMap[language] || language;\n  },\n\n  /**\n   * Generate broadcast link title from fixture\n   */\n  generateTitle: (fixture: { homeTeam: string; awayTeam: string; league: string }, quality: BroadcastQuality): string => {\n    return `${fixture.homeTeam} vs ${fixture.awayTeam} - ${quality} Stream`;\n  },\n\n  /**\n   * Check if broadcast link is live\n   */\n  isLive: (fixture: { date: string; status: string }): boolean => {\n    return fixture.status === 'LIVE' || fixture.status === 'IN_PLAY';\n  },\n\n  /**\n   * Get fixture display text\n   */\n  getFixtureDisplayText: (fixture: { homeTeam: string; awayTeam: string; date: string }): string => {\n    const date = new Date(fixture.date).toLocaleDateString();\n    return `${fixture.homeTeam} vs ${fixture.awayTeam} (${date})`;\n  }\n};\n\n/**\n * Mock data for development\n */\nexport const MOCK_BROADCAST_LINKS: BroadcastLink[] = [\n  {\n    id: '1',\n    fixtureId: 'fixture-1',\n    fixture: {\n      id: 'fixture-1',\n      homeTeam: 'Manchester United',\n      awayTeam: 'Liverpool',\n      date: '2024-05-26T15:00:00Z',\n      league: 'Premier League',\n      status: 'SCHEDULED'\n    },\n    url: 'https://stream1.example.com/match1',\n    title: 'Manchester United vs Liverpool - HD Stream',\n    description: 'High quality stream for Premier League match',\n    quality: 'HD',\n    language: 'English',\n    isActive: true,\n    status: 'active',\n    viewCount: 15420,\n    rating: 4.5,\n    createdBy: 'admin',\n    createdAt: '2024-05-25T10:00:00Z',\n    updatedAt: '2024-05-25T10:00:00Z',\n    tags: ['premier-league', 'hd', 'english']\n  },\n  {\n    id: '2',\n    fixtureId: 'fixture-1',\n    fixture: {\n      id: 'fixture-1',\n      homeTeam: 'Manchester United',\n      awayTeam: 'Liverpool',\n      date: '2024-05-26T15:00:00Z',\n      league: 'Premier League',\n      status: 'SCHEDULED'\n    },\n    url: 'https://stream2.example.com/match1-mobile',\n    title: 'Manchester United vs Liverpool - Mobile Stream',\n    description: 'Mobile optimized stream',\n    quality: 'Mobile',\n    language: 'English',\n    isActive: true,\n    status: 'active',\n    viewCount: 8930,\n    rating: 4.2,\n    createdBy: 'editor1',\n    createdAt: '2024-05-25T11:00:00Z',\n    updatedAt: '2024-05-25T11:00:00Z',\n    tags: ['premier-league', 'mobile', 'english']\n  },\n  {\n    id: '3',\n    fixtureId: 'fixture-2',\n    fixture: {\n      id: 'fixture-2',\n      homeTeam: 'Barcelona',\n      awayTeam: 'Real Madrid',\n      date: '2024-05-27T20:00:00Z',\n      league: 'La Liga',\n      status: 'SCHEDULED'\n    },\n    url: 'https://stream3.example.com/clasico',\n    title: 'El Clasico - HD Stream',\n    description: 'Barcelona vs Real Madrid in HD',\n    quality: 'HD',\n    language: 'Spanish',\n    isActive: false,\n    status: 'pending',\n    viewCount: 0,\n    rating: 0,\n    createdBy: 'editor2',\n    createdAt: '2024-05-25T12:00:00Z',\n    updatedAt: '2024-05-25T12:00:00Z',\n    tags: ['la-liga', 'clasico', 'spanish']\n  }\n];\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,6CAA6C;;;;;;;;;AAQtC,MAAM,sBAAsB;IAAC;IAAM;IAAM;CAAS;AAMlD,MAAM,sBAAsB;IACjC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAMM,MAAM,mBAAmB;IAAC;IAAU;IAAY;IAAW;CAAU;AA4HrE,MAAM,uBAAuB;IAClC,KAAK;QACH,UAAU;QACV,SAAS;QACT,SAAS;IACX;IACA,OAAO;QACL,UAAU;QACV,WAAW;QACX,WAAW;QACX,SAAS;IACX;IACA,aAAa;QACX,UAAU;QACV,WAAW;QACX,SAAS;IACX;IACA,SAAS;QACP,UAAU;QACV,SAAS;QACT,SAAS;IACX;IACA,UAAU;QACR,UAAU;QACV,SAAS;IACX;IACA,WAAW;QACT,UAAU;QACV,SAAS;IACX;AACF;AAKO,MAAM,mBAAmB;IAC9B;;GAEC,GACD,YAAY,CAAC;QACX,OAAO,qBAAqB,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC;IAC/C;IAEA;;GAEC,GACD,iBAAiB,CAAC;QAChB,MAAM,SAAS;YACb,IAAI;YACJ,IAAI;YACJ,QAAQ;QACV;QACA,OAAO,MAAM,CAAC,QAAQ;IACxB;IAEA;;GAEC,GACD,gBAAgB,CAAC;QACf,MAAM,SAAS;YACb,QAAQ;YACR,UAAU;YACV,SAAS;YACT,SAAS;QACX;QACA,OAAO,MAAM,CAAC,OAAO;IACvB;IAEA;;GAEC,GACD,iBAAiB,CAAC;QAChB,IAAI,SAAS,SAAS;YACpB,OAAO,GAAG,CAAC,QAAQ,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QAC3C;QACA,IAAI,SAAS,MAAM;YACjB,OAAO,GAAG,CAAC,QAAQ,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QACxC;QACA,OAAO,MAAM,QAAQ;IACvB;IAEA;;GAEC,GACD,wBAAwB,CAAC;QACvB,MAAM,cAAsC;YAC1C,MAAM;YACN,MAAM;YACN,MAAM;YACN,MAAM;YACN,MAAM;YACN,MAAM;YACN,MAAM;YACN,MAAM;YACN,MAAM;YACN,MAAM;YACN,MAAM;QACR;QACA,OAAO,WAAW,CAAC,SAAS,IAAI;IAClC;IAEA;;GAEC,GACD,eAAe,CAAC,SAAiE;QAC/E,OAAO,GAAG,QAAQ,QAAQ,CAAC,IAAI,EAAE,QAAQ,QAAQ,CAAC,GAAG,EAAE,QAAQ,OAAO,CAAC;IACzE;IAEA;;GAEC,GACD,QAAQ,CAAC;QACP,OAAO,QAAQ,MAAM,KAAK,UAAU,QAAQ,MAAM,KAAK;IACzD;IAEA;;GAEC,GACD,uBAAuB,CAAC;QACtB,MAAM,OAAO,IAAI,KAAK,QAAQ,IAAI,EAAE,kBAAkB;QACtD,OAAO,GAAG,QAAQ,QAAQ,CAAC,IAAI,EAAE,QAAQ,QAAQ,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;IAC/D;AACF;AAKO,MAAM,uBAAwC;IACnD;QACE,IAAI;QACJ,WAAW;QACX,SAAS;YACP,IAAI;YACJ,UAAU;YACV,UAAU;YACV,MAAM;YACN,QAAQ;YACR,QAAQ;QACV;QACA,KAAK;QACL,OAAO;QACP,aAAa;QACb,SAAS;QACT,UAAU;QACV,UAAU;QACV,QAAQ;QACR,WAAW;QACX,QAAQ;QACR,WAAW;QACX,WAAW;QACX,WAAW;QACX,MAAM;YAAC;YAAkB;YAAM;SAAU;IAC3C;IACA;QACE,IAAI;QACJ,WAAW;QACX,SAAS;YACP,IAAI;YACJ,UAAU;YACV,UAAU;YACV,MAAM;YACN,QAAQ;YACR,QAAQ;QACV;QACA,KAAK;QACL,OAAO;QACP,aAAa;QACb,SAAS;QACT,UAAU;QACV,UAAU;QACV,QAAQ;QACR,WAAW;QACX,QAAQ;QACR,WAAW;QACX,WAAW;QACX,WAAW;QACX,MAAM;YAAC;YAAkB;YAAU;SAAU;IAC/C;IACA;QACE,IAAI;QACJ,WAAW;QACX,SAAS;YACP,IAAI;YACJ,UAAU;YACV,UAAU;YACV,MAAM;YACN,QAAQ;YACR,QAAQ;QACV;QACA,KAAK;QACL,OAAO;QACP,aAAa;QACb,SAAS;QACT,UAAU;QACV,UAAU;QACV,QAAQ;QACR,WAAW;QACX,QAAQ;QACR,WAAW;QACX,WAAW;QACX,WAAW;QACX,MAAM;YAAC;YAAW;YAAW;SAAU;IACzC;CACD"}}, {"offset": {"line": 237, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 243, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/app/broadcast-links/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport {\n  Card,\n  Table,\n  Button,\n  Input,\n  Select,\n  Space,\n  Tag,\n  Tooltip,\n  Popconfirm,\n  Row,\n  Col,\n  Statistic,\n  Typography,\n  message,\n  Badge,\n  Avatar,\n  Dropdown,\n  MenuProps\n} from 'antd';\nimport {\n  PlusOutlined,\n  SearchOutlined,\n  FilterOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  EyeOutlined,\n  <PERSON>Outlined,\n  PlayCircleOutlined,\n  GlobalOutlined,\n  MoreOutlined,\n  ReloadOutlined,\n  ExportOutlined\n} from '@ant-design/icons';\nimport { useRouter } from 'next/navigation';\nimport { ColumnsType } from 'antd/es/table';\nimport {\n  BroadcastLink,\n  BroadcastLinkQueryParams,\n  BROADCAST_QUALITIES,\n  BROADCAST_LANGUAGES,\n  BroadcastHelpers,\n  MOCK_BROADCAST_LINKS\n} from '@/types/broadcast';\nimport { useBroadcastLinks, useDeleteBroadcastLink, useAvailableFixtures } from '@/hooks/api/broadcast-hooks';\nimport { FootballQueries } from '@/lib/query-types';\nimport dayjs from 'dayjs';\n\nconst { Title, Text } = Typography;\nconst { Option } = Select;\n\nexport default function BroadcastLinksPage() {\n  const router = useRouter();\n  const [queryParams, setQueryParams] = useState<BroadcastLinkQueryParams>({\n    page: 1,\n    limit: 10,\n    sortBy: 'createdAt',\n    sortOrder: 'desc'\n  });\n\n  // Get fixture ID from URL params if coming from fixtures page\n  const [preSelectedFixtureId, setPreSelectedFixtureId] = React.useState<string | null>(null);\n\n  React.useEffect(() => {\n    if (typeof window !== 'undefined') {\n      const urlParams = new URLSearchParams(window.location.search);\n      setPreSelectedFixtureId(urlParams.get('fixtureId'));\n    }\n  }, []);\n\n  // For development, use mock data\n  const broadcastLinksQuery = {\n    data: {\n      data: MOCK_BROADCAST_LINKS,\n      total: MOCK_BROADCAST_LINKS.length,\n      page: 1,\n      limit: 10,\n      totalPages: 1\n    },\n    isLoading: false,\n    error: null,\n    refetch: () => Promise.resolve()\n  };\n\n  // Fetch available fixtures for filtering\n  const { data: fixturesData, isLoading: fixturesLoading } = useAvailableFixtures();\n  const deleteLink = useDeleteBroadcastLink();\n\n  // Statistics calculation\n  const statistics = React.useMemo(() => {\n    const links = MOCK_BROADCAST_LINKS;\n    return {\n      total: links.length,\n      active: links.filter(l => l.isActive).length,\n      inactive: links.filter(l => !l.isActive).length,\n      hd: links.filter(l => l.quality === 'HD').length,\n      totalViews: links.reduce((sum, l) => sum + (l.viewCount || 0), 0)\n    };\n  }, []);\n\n  // Handle search\n  const handleSearch = (value: string) => {\n    setQueryParams(prev => ({ ...prev, search: value, page: 1 }));\n  };\n\n  // Handle filter change\n  const handleFilterChange = (key: keyof BroadcastLinkQueryParams, value: any) => {\n    setQueryParams(prev => ({ ...prev, [key]: value, page: 1 }));\n  };\n\n  // Handle delete\n  const handleDelete = async (id: string) => {\n    try {\n      await deleteLink.mutateAsync(id);\n      message.success('Broadcast link deleted successfully');\n    } catch (error) {\n      message.error('Failed to delete broadcast link');\n    }\n  };\n\n  // Handle table change\n  const handleTableChange = (pagination: any, filters: any, sorter: any) => {\n    setQueryParams(prev => ({\n      ...prev,\n      page: pagination.current,\n      limit: pagination.pageSize,\n      sortBy: sorter.field || 'createdAt',\n      sortOrder: sorter.order === 'ascend' ? 'asc' : 'desc'\n    }));\n  };\n\n  // Table columns\n  const columns: ColumnsType<BroadcastLink> = [\n    {\n      title: 'Fixture',\n      key: 'fixture',\n      render: (_, record) => {\n        // Find fixture data from available fixtures\n        const fixture = fixturesData?.find(f => f.externalId === record.fixtureId);\n\n        if (!fixture) {\n          return (\n            <div>\n              <Text type=\"secondary\">Fixture not found</Text>\n              <br />\n              <Text type=\"secondary\" className=\"text-xs\">ID: {record.fixtureId}</Text>\n            </div>\n          );\n        }\n\n        return (\n          <div>\n            <Text strong className=\"block\">\n              {fixture.homeTeam.name} vs {fixture.awayTeam.name}\n            </Text>\n            <div className=\"flex items-center gap-2 text-sm\">\n              <Text type=\"secondary\">{fixture.league.name}</Text>\n              {fixture.status === 'live' && (\n                <Tag color=\"red\" size=\"small\">LIVE</Tag>\n              )}\n              {fixture.status === 'scheduled' && (\n                <Tag color=\"blue\" size=\"small\">\n                  {dayjs(fixture.date).format('MMM DD, HH:mm')}\n                </Tag>\n              )}\n            </div>\n          </div>\n        );\n      },\n      width: 250,\n    },\n    {\n      title: 'Stream Info',\n      key: 'streamInfo',\n      render: (_, record) => (\n        <div>\n          <div className=\"flex items-center gap-2 mb-1\">\n            <LinkOutlined />\n            <Text strong>{record.title || 'Untitled Stream'}</Text>\n          </div>\n          <div className=\"flex items-center gap-2\">\n            <Tag color={BroadcastHelpers.getQualityColor(record.quality)}>\n              {record.quality}\n            </Tag>\n            <Tag icon={<GlobalOutlined />}>\n              {record.language}\n            </Tag>\n          </div>\n        </div>\n      ),\n      width: 200,\n    },\n    {\n      title: 'Status',\n      dataIndex: 'isActive',\n      key: 'status',\n      render: (isActive: boolean, record) => (\n        <div>\n          <Badge\n            status={isActive ? 'success' : 'default'}\n            text={isActive ? 'Active' : 'Inactive'}\n          />\n          <br />\n          <Tag color={BroadcastHelpers.getStatusColor(record.status)} className=\"mt-1\">\n            {record.status.toUpperCase()}\n          </Tag>\n        </div>\n      ),\n      width: 100,\n    },\n    {\n      title: 'Performance',\n      key: 'performance',\n      render: (_, record) => (\n        <div>\n          <div className=\"flex items-center gap-2\">\n            <EyeOutlined />\n            <Text>{BroadcastHelpers.formatViewCount(record.viewCount || 0)} views</Text>\n          </div>\n          {record.rating && (\n            <div className=\"flex items-center gap-2 mt-1\">\n              <Text type=\"secondary\">★ {record.rating.toFixed(1)}</Text>\n            </div>\n          )}\n        </div>\n      ),\n      width: 120,\n    },\n    {\n      title: 'Created',\n      key: 'created',\n      render: (_, record) => (\n        <div>\n          <Text className=\"block\">{new Date(record.createdAt).toLocaleDateString()}</Text>\n          <Text type=\"secondary\" className=\"text-sm\">by {record.createdBy}</Text>\n        </div>\n      ),\n      width: 120,\n      sorter: true,\n    },\n    {\n      title: 'Actions',\n      key: 'actions',\n      render: (_, record) => {\n        const menuItems: MenuProps['items'] = [\n          {\n            key: 'view',\n            label: 'View Details',\n            icon: <EyeOutlined />,\n            onClick: () => router.push(`/broadcast-links/${record.id}`)\n          },\n          {\n            key: 'edit',\n            label: 'Edit',\n            icon: <EditOutlined />,\n            onClick: () => router.push(`/broadcast-links/${record.id}/edit`)\n          },\n          {\n            type: 'divider'\n          },\n          {\n            key: 'delete',\n            label: 'Delete',\n            icon: <DeleteOutlined />,\n            danger: true,\n            onClick: () => handleDelete(record.id)\n          }\n        ];\n\n        return (\n          <Dropdown menu={{ items: menuItems }} trigger={['click']}>\n            <Button icon={<MoreOutlined />} />\n          </Dropdown>\n        );\n      },\n      width: 80,\n      fixed: 'right',\n    },\n  ];\n\n  return (\n    <div>\n      {/* Page Header */}\n      <div className=\"mb-6\">\n        <Title level={2}>\n          <PlayCircleOutlined className=\"mr-2\" />\n          Broadcast Links Management\n        </Title>\n        <Text type=\"secondary\">\n          Manage broadcast links for football fixtures with quality control and language support\n        </Text>\n      </div>\n\n      {/* Statistics Cards */}\n      <Row gutter={16} className=\"mb-6\">\n        <Col xs={12} sm={6}>\n          <Card>\n            <Statistic\n              title=\"Total Links\"\n              value={statistics.total}\n              prefix={<LinkOutlined />}\n            />\n          </Card>\n        </Col>\n        <Col xs={12} sm={6}>\n          <Card>\n            <Statistic\n              title=\"Active Links\"\n              value={statistics.active}\n              prefix={<PlayCircleOutlined />}\n              valueStyle={{ color: '#3f8600' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={12} sm={6}>\n          <Card>\n            <Statistic\n              title=\"HD Quality\"\n              value={statistics.hd}\n              suffix={`/ ${statistics.total}`}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={12} sm={6}>\n          <Card>\n            <Statistic\n              title=\"Total Views\"\n              value={BroadcastHelpers.formatViewCount(statistics.totalViews)}\n              prefix={<EyeOutlined />}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      {/* Filters and Actions */}\n      <Card className=\"mb-4\">\n        <Row gutter={16} align=\"middle\">\n          <Col xs={24} sm={8} md={6}>\n            <Input\n              placeholder=\"Search broadcast links...\"\n              prefix={<SearchOutlined />}\n              onChange={(e) => handleSearch(e.target.value)}\n              allowClear\n            />\n          </Col>\n          <Col xs={12} sm={4} md={3}>\n            <Select\n              placeholder=\"Quality\"\n              allowClear\n              onChange={(value) => handleFilterChange('quality', value)}\n              className=\"w-full\"\n            >\n              {BROADCAST_QUALITIES.map(quality => (\n                <Option key={quality} value={quality}>\n                  <Tag color={BroadcastHelpers.getQualityColor(quality)}>\n                    {quality}\n                  </Tag>\n                </Option>\n              ))}\n            </Select>\n          </Col>\n          <Col xs={12} sm={4} md={3}>\n            <Select\n              placeholder=\"Language\"\n              allowClear\n              onChange={(value) => handleFilterChange('language', value)}\n              className=\"w-full\"\n            >\n              {BROADCAST_LANGUAGES.slice(0, 8).map(language => (\n                <Option key={language} value={language}>\n                  {language}\n                </Option>\n              ))}\n            </Select>\n          </Col>\n          <Col xs={12} sm={4} md={3}>\n            <Select\n              placeholder=\"Fixture\"\n              allowClear\n              loading={fixturesLoading}\n              onChange={(value) => handleFilterChange('fixtureId', value)}\n              className=\"w-full\"\n              showSearch\n              filterOption={(input, option) =>\n                option?.children?.toString().toLowerCase().includes(input.toLowerCase()) ?? false\n              }\n              defaultValue={preSelectedFixtureId}\n            >\n              {fixturesData?.map(fixture => (\n                <Option key={fixture.externalId} value={fixture.externalId}>\n                  {fixture.homeTeam.name} vs {fixture.awayTeam.name}\n                </Option>\n              ))}\n            </Select>\n          </Col>\n          <Col xs={12} sm={4} md={3}>\n            <Select\n              placeholder=\"Status\"\n              allowClear\n              onChange={(value) => handleFilterChange('isActive', value)}\n              className=\"w-full\"\n            >\n              <Option value={true}>Active</Option>\n              <Option value={false}>Inactive</Option>\n            </Select>\n          </Col>\n          <Col xs={12} sm={4} md={6} className=\"text-right\">\n            <Space>\n              <Button\n                icon={<ReloadOutlined />}\n                onClick={() => broadcastLinksQuery.refetch()}\n                loading={broadcastLinksQuery.isLoading}\n              >\n                Refresh\n              </Button>\n              <Button\n                icon={<ExportOutlined />}\n                onClick={() => message.info('Export functionality coming soon')}\n              >\n                Export\n              </Button>\n              <Button\n                type=\"primary\"\n                icon={<PlusOutlined />}\n                onClick={() => router.push('/broadcast-links/create')}\n              >\n                Add Broadcast Link\n              </Button>\n            </Space>\n          </Col>\n        </Row>\n      </Card>\n\n      {/* Broadcast Links Table */}\n      <Card>\n        <Table\n          columns={columns}\n          dataSource={broadcastLinksQuery.data?.data || []}\n          rowKey=\"id\"\n          loading={broadcastLinksQuery.isLoading}\n          pagination={{\n            current: queryParams.page,\n            pageSize: queryParams.limit,\n            total: broadcastLinksQuery.data?.total || 0,\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total, range) =>\n              `${range[0]}-${range[1]} of ${total} broadcast links`,\n          }}\n          onChange={handleTableChange}\n          scroll={{ x: 1200 }}\n        />\n      </Card>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAmCA;AAEA;AAQA;AAEA;AA9CA;AAAA;AAAA;AAAA;AAoBA;AAAA;AApBA;AAoBA;AAAA;AAAA;AApBA;AAAA;AAoBA;AAAA;AApBA;AAAA;AAAA;AAAA;AAAA;AAoBA;AApBA;AAoBA;AAAA;AAAA;AApBA;AAHA;;;;;;;;;AAmDA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,0LAAA,CAAA,aAAU;AAClC,MAAM,EAAE,MAAM,EAAE,GAAG,kLAAA,CAAA,SAAM;AAEV,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA4B;QACvE,MAAM;QACN,OAAO;QACP,QAAQ;QACR,WAAW;IACb;IAEA,8DAA8D;IAC9D,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAAgB;IAEtF,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,uCAAmC;;QAGnC;IACF,GAAG,EAAE;IAEL,iCAAiC;IACjC,MAAM,sBAAsB;QAC1B,MAAM;YACJ,MAAM,yHAAA,CAAA,uBAAoB;YAC1B,OAAO,yHAAA,CAAA,uBAAoB,CAAC,MAAM;YAClC,MAAM;YACN,OAAO;YACP,YAAY;QACd;QACA,WAAW;QACX,OAAO;QACP,SAAS,IAAM,QAAQ,OAAO;IAChC;IAEA,yCAAyC;IACzC,MAAM,EAAE,MAAM,YAAY,EAAE,WAAW,eAAe,EAAE,GAAG,CAAA,GAAA,yIAAA,CAAA,uBAAoB,AAAD;IAC9E,MAAM,aAAa,CAAA,GAAA,yIAAA,CAAA,yBAAsB,AAAD;IAExC,yBAAyB;IACzB,MAAM,aAAa,qMAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QAC/B,MAAM,QAAQ,yHAAA,CAAA,uBAAoB;QAClC,OAAO;YACL,OAAO,MAAM,MAAM;YACnB,QAAQ,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,EAAE,MAAM;YAC5C,UAAU,MAAM,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,QAAQ,EAAE,MAAM;YAC/C,IAAI,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,OAAO,KAAK,MAAM,MAAM;YAChD,YAAY,MAAM,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,CAAC,EAAE,SAAS,IAAI,CAAC,GAAG;QACjE;IACF,GAAG,EAAE;IAEL,gBAAgB;IAChB,MAAM,eAAe,CAAC;QACpB,eAAe,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,QAAQ;gBAAO,MAAM;YAAE,CAAC;IAC7D;IAEA,uBAAuB;IACvB,MAAM,qBAAqB,CAAC,KAAqC;QAC/D,eAAe,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,IAAI,EAAE;gBAAO,MAAM;YAAE,CAAC;IAC5D;IAEA,gBAAgB;IAChB,MAAM,eAAe,OAAO;QAC1B,IAAI;YACF,MAAM,WAAW,WAAW,CAAC;YAC7B,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;QAClB,EAAE,OAAO,OAAO;YACd,oLAAA,CAAA,UAAO,CAAC,KAAK,CAAC;QAChB;IACF;IAEA,sBAAsB;IACtB,MAAM,oBAAoB,CAAC,YAAiB,SAAc;QACxD,eAAe,CAAA,OAAQ,CAAC;gBACtB,GAAG,IAAI;gBACP,MAAM,WAAW,OAAO;gBACxB,OAAO,WAAW,QAAQ;gBAC1B,QAAQ,OAAO,KAAK,IAAI;gBACxB,WAAW,OAAO,KAAK,KAAK,WAAW,QAAQ;YACjD,CAAC;IACH;IAEA,gBAAgB;IAChB,MAAM,UAAsC;QAC1C;YACE,OAAO;YACP,KAAK;YACL,QAAQ,CAAC,GAAG;gBACV,4CAA4C;gBAC5C,MAAM,UAAU,cAAc,KAAK,CAAA,IAAK,EAAE,UAAU,KAAK,OAAO,SAAS;gBAEzE,IAAI,CAAC,SAAS;oBACZ,qBACE,8OAAC;;0CACC,8OAAC;gCAAK,MAAK;0CAAY;;;;;;0CACvB,8OAAC;;;;;0CACD,8OAAC;gCAAK,MAAK;gCAAY,WAAU;;oCAAU;oCAAK,OAAO,SAAS;;;;;;;;;;;;;gBAGtE;gBAEA,qBACE,8OAAC;;sCACC,8OAAC;4BAAK,MAAM;4BAAC,WAAU;;gCACpB,QAAQ,QAAQ,CAAC,IAAI;gCAAC;gCAAK,QAAQ,QAAQ,CAAC,IAAI;;;;;;;sCAEnD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,MAAK;8CAAa,QAAQ,MAAM,CAAC,IAAI;;;;;;gCAC1C,QAAQ,MAAM,KAAK,wBAClB,8OAAC,4KAAA,CAAA,MAAG;oCAAC,OAAM;oCAAM,MAAK;8CAAQ;;;;;;gCAE/B,QAAQ,MAAM,KAAK,6BAClB,8OAAC,4KAAA,CAAA,MAAG;oCAAC,OAAM;oCAAO,MAAK;8CACpB,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD,EAAE,QAAQ,IAAI,EAAE,MAAM,CAAC;;;;;;;;;;;;;;;;;;YAMxC;YACA,OAAO;QACT;QACA;YACE,OAAO;YACP,KAAK;YACL,QAAQ,CAAC,GAAG,uBACV,8OAAC;;sCACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kNAAA,CAAA,eAAY;;;;;8CACb,8OAAC;oCAAK,MAAM;8CAAE,OAAO,KAAK,IAAI;;;;;;;;;;;;sCAEhC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4KAAA,CAAA,MAAG;oCAAC,OAAO,yHAAA,CAAA,mBAAgB,CAAC,eAAe,CAAC,OAAO,OAAO;8CACxD,OAAO,OAAO;;;;;;8CAEjB,8OAAC,4KAAA,CAAA,MAAG;oCAAC,oBAAM,8OAAC,sNAAA,CAAA,iBAAc;;;;;8CACvB,OAAO,QAAQ;;;;;;;;;;;;;;;;;;YAKxB,OAAO;QACT;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,QAAQ,CAAC,UAAmB,uBAC1B,8OAAC;;sCACC,8OAAC,gLAAA,CAAA,QAAK;4BACJ,QAAQ,WAAW,YAAY;4BAC/B,MAAM,WAAW,WAAW;;;;;;sCAE9B,8OAAC;;;;;sCACD,8OAAC,4KAAA,CAAA,MAAG;4BAAC,OAAO,yHAAA,CAAA,mBAAgB,CAAC,cAAc,CAAC,OAAO,MAAM;4BAAG,WAAU;sCACnE,OAAO,MAAM,CAAC,WAAW;;;;;;;;;;;;YAIhC,OAAO;QACT;QACA;YACE,OAAO;YACP,KAAK;YACL,QAAQ,CAAC,GAAG,uBACV,8OAAC;;sCACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,gNAAA,CAAA,cAAW;;;;;8CACZ,8OAAC;;wCAAM,yHAAA,CAAA,mBAAgB,CAAC,eAAe,CAAC,OAAO,SAAS,IAAI;wCAAG;;;;;;;;;;;;;wBAEhE,OAAO,MAAM,kBACZ,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAK,MAAK;;oCAAY;oCAAG,OAAO,MAAM,CAAC,OAAO,CAAC;;;;;;;;;;;;;;;;;;YAKxD,OAAO;QACT;QACA;YACE,OAAO;YACP,KAAK;YACL,QAAQ,CAAC,GAAG,uBACV,8OAAC;;sCACC,8OAAC;4BAAK,WAAU;sCAAS,IAAI,KAAK,OAAO,SAAS,EAAE,kBAAkB;;;;;;sCACtE,8OAAC;4BAAK,MAAK;4BAAY,WAAU;;gCAAU;gCAAI,OAAO,SAAS;;;;;;;;;;;;;YAGnE,OAAO;YACP,QAAQ;QACV;QACA;YACE,OAAO;YACP,KAAK;YACL,QAAQ,CAAC,GAAG;gBACV,MAAM,YAAgC;oBACpC;wBACE,KAAK;wBACL,OAAO;wBACP,oBAAM,8OAAC,gNAAA,CAAA,cAAW;;;;;wBAClB,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,iBAAiB,EAAE,OAAO,EAAE,EAAE;oBAC5D;oBACA;wBACE,KAAK;wBACL,OAAO;wBACP,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;wBACnB,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,iBAAiB,EAAE,OAAO,EAAE,CAAC,KAAK,CAAC;oBACjE;oBACA;wBACE,MAAM;oBACR;oBACA;wBACE,KAAK;wBACL,OAAO;wBACP,oBAAM,8OAAC,sNAAA,CAAA,iBAAc;;;;;wBACrB,QAAQ;wBACR,SAAS,IAAM,aAAa,OAAO,EAAE;oBACvC;iBACD;gBAED,qBACE,8OAAC,sLAAA,CAAA,WAAQ;oBAAC,MAAM;wBAAE,OAAO;oBAAU;oBAAG,SAAS;wBAAC;qBAAQ;8BACtD,cAAA,8OAAC,kMAAA,CAAA,SAAM;wBAAC,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;;;;;;;;;;;YAGjC;YACA,OAAO;YACP,OAAO;QACT;KACD;IAED,qBACE,8OAAC;;0BAEC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAM,OAAO;;0CACZ,8OAAC,8NAAA,CAAA,qBAAkB;gCAAC,WAAU;;;;;;4BAAS;;;;;;;kCAGzC,8OAAC;wBAAK,MAAK;kCAAY;;;;;;;;;;;;0BAMzB,8OAAC,4KAAA,CAAA,MAAG;gBAAC,QAAQ;gBAAI,WAAU;;kCACzB,8OAAC,4KAAA,CAAA,MAAG;wBAAC,IAAI;wBAAI,IAAI;kCACf,cAAA,8OAAC,8KAAA,CAAA,OAAI;sCACH,cAAA,8OAAC,wLAAA,CAAA,YAAS;gCACR,OAAM;gCACN,OAAO,WAAW,KAAK;gCACvB,sBAAQ,8OAAC,kNAAA,CAAA,eAAY;;;;;;;;;;;;;;;;;;;;kCAI3B,8OAAC,4KAAA,CAAA,MAAG;wBAAC,IAAI;wBAAI,IAAI;kCACf,cAAA,8OAAC,8KAAA,CAAA,OAAI;sCACH,cAAA,8OAAC,wLAAA,CAAA,YAAS;gCACR,OAAM;gCACN,OAAO,WAAW,MAAM;gCACxB,sBAAQ,8OAAC,8NAAA,CAAA,qBAAkB;;;;;gCAC3B,YAAY;oCAAE,OAAO;gCAAU;;;;;;;;;;;;;;;;kCAIrC,8OAAC,4KAAA,CAAA,MAAG;wBAAC,IAAI;wBAAI,IAAI;kCACf,cAAA,8OAAC,8KAAA,CAAA,OAAI;sCACH,cAAA,8OAAC,wLAAA,CAAA,YAAS;gCACR,OAAM;gCACN,OAAO,WAAW,EAAE;gCACpB,QAAQ,CAAC,EAAE,EAAE,WAAW,KAAK,EAAE;gCAC/B,YAAY;oCAAE,OAAO;gCAAU;;;;;;;;;;;;;;;;kCAIrC,8OAAC,4KAAA,CAAA,MAAG;wBAAC,IAAI;wBAAI,IAAI;kCACf,cAAA,8OAAC,8KAAA,CAAA,OAAI;sCACH,cAAA,8OAAC,wLAAA,CAAA,YAAS;gCACR,OAAM;gCACN,OAAO,yHAAA,CAAA,mBAAgB,CAAC,eAAe,CAAC,WAAW,UAAU;gCAC7D,sBAAQ,8OAAC,gNAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO5B,8OAAC,8KAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,MAAG;oBAAC,QAAQ;oBAAI,OAAM;;sCACrB,8OAAC,4KAAA,CAAA,MAAG;4BAAC,IAAI;4BAAI,IAAI;4BAAG,IAAI;sCACtB,cAAA,8OAAC,gLAAA,CAAA,QAAK;gCACJ,aAAY;gCACZ,sBAAQ,8OAAC,sNAAA,CAAA,iBAAc;;;;;gCACvB,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;gCAC5C,UAAU;;;;;;;;;;;sCAGd,8OAAC,4KAAA,CAAA,MAAG;4BAAC,IAAI;4BAAI,IAAI;4BAAG,IAAI;sCACtB,cAAA,8OAAC,kLAAA,CAAA,SAAM;gCACL,aAAY;gCACZ,UAAU;gCACV,UAAU,CAAC,QAAU,mBAAmB,WAAW;gCACnD,WAAU;0CAET,yHAAA,CAAA,sBAAmB,CAAC,GAAG,CAAC,CAAA,wBACvB,8OAAC;wCAAqB,OAAO;kDAC3B,cAAA,8OAAC,4KAAA,CAAA,MAAG;4CAAC,OAAO,yHAAA,CAAA,mBAAgB,CAAC,eAAe,CAAC;sDAC1C;;;;;;uCAFQ;;;;;;;;;;;;;;;sCAQnB,8OAAC,4KAAA,CAAA,MAAG;4BAAC,IAAI;4BAAI,IAAI;4BAAG,IAAI;sCACtB,cAAA,8OAAC,kLAAA,CAAA,SAAM;gCACL,aAAY;gCACZ,UAAU;gCACV,UAAU,CAAC,QAAU,mBAAmB,YAAY;gCACpD,WAAU;0CAET,yHAAA,CAAA,sBAAmB,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA,yBACnC,8OAAC;wCAAsB,OAAO;kDAC3B;uCADU;;;;;;;;;;;;;;;sCAMnB,8OAAC,4KAAA,CAAA,MAAG;4BAAC,IAAI;4BAAI,IAAI;4BAAG,IAAI;sCACtB,cAAA,8OAAC,kLAAA,CAAA,SAAM;gCACL,aAAY;gCACZ,UAAU;gCACV,SAAS;gCACT,UAAU,CAAC,QAAU,mBAAmB,aAAa;gCACrD,WAAU;gCACV,UAAU;gCACV,cAAc,CAAC,OAAO,SACpB,QAAQ,UAAU,WAAW,cAAc,SAAS,MAAM,WAAW,OAAO;gCAE9E,cAAc;0CAEb,cAAc,IAAI,CAAA,wBACjB,8OAAC;wCAAgC,OAAO,QAAQ,UAAU;;4CACvD,QAAQ,QAAQ,CAAC,IAAI;4CAAC;4CAAK,QAAQ,QAAQ,CAAC,IAAI;;uCADtC,QAAQ,UAAU;;;;;;;;;;;;;;;sCAMrC,8OAAC,4KAAA,CAAA,MAAG;4BAAC,IAAI;4BAAI,IAAI;4BAAG,IAAI;sCACtB,cAAA,8OAAC,kLAAA,CAAA,SAAM;gCACL,aAAY;gCACZ,UAAU;gCACV,UAAU,CAAC,QAAU,mBAAmB,YAAY;gCACpD,WAAU;;kDAEV,8OAAC;wCAAO,OAAO;kDAAM;;;;;;kDACrB,8OAAC;wCAAO,OAAO;kDAAO;;;;;;;;;;;;;;;;;sCAG1B,8OAAC,4KAAA,CAAA,MAAG;4BAAC,IAAI;4BAAI,IAAI;4BAAG,IAAI;4BAAG,WAAU;sCACnC,cAAA,8OAAC,gMAAA,CAAA,QAAK;;kDACJ,8OAAC,kMAAA,CAAA,SAAM;wCACL,oBAAM,8OAAC,sNAAA,CAAA,iBAAc;;;;;wCACrB,SAAS,IAAM,oBAAoB,OAAO;wCAC1C,SAAS,oBAAoB,SAAS;kDACvC;;;;;;kDAGD,8OAAC,kMAAA,CAAA,SAAM;wCACL,oBAAM,8OAAC,sNAAA,CAAA,iBAAc;;;;;wCACrB,SAAS,IAAM,oLAAA,CAAA,UAAO,CAAC,IAAI,CAAC;kDAC7B;;;;;;kDAGD,8OAAC,kMAAA,CAAA,SAAM;wCACL,MAAK;wCACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;wCACnB,SAAS,IAAM,OAAO,IAAI,CAAC;kDAC5B;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAST,8OAAC,8KAAA,CAAA,OAAI;0BACH,cAAA,8OAAC,gLAAA,CAAA,QAAK;oBACJ,SAAS;oBACT,YAAY,oBAAoB,IAAI,EAAE,QAAQ,EAAE;oBAChD,QAAO;oBACP,SAAS,oBAAoB,SAAS;oBACtC,YAAY;wBACV,SAAS,YAAY,IAAI;wBACzB,UAAU,YAAY,KAAK;wBAC3B,OAAO,oBAAoB,IAAI,EAAE,SAAS;wBAC1C,iBAAiB;wBACjB,iBAAiB;wBACjB,WAAW,CAAC,OAAO,QACjB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC,IAAI,EAAE,MAAM,gBAAgB,CAAC;oBACzD;oBACA,UAAU;oBACV,QAAQ;wBAAE,GAAG;oBAAK;;;;;;;;;;;;;;;;;AAK5B"}}, {"offset": {"line": 1147, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}