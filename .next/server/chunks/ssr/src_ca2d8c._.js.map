{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/football/league-form.tsx"], "sourcesContent": ["/**\n * League Form Component\n * Form for creating and editing football leagues\n */\n\n'use client';\n\nimport React from 'react';\nimport {\n  Form,\n  Input,\n  Select,\n  Switch,\n  Button,\n  Card,\n  Row,\n  Col,\n  Typography,\n  Space,\n  Upload,\n  message\n} from 'antd';\nimport {\n  TrophyOutlined,\n  SaveOutlined,\n  CloseOutlined,\n  UploadOutlined,\n  GlobalOutlined\n} from '@ant-design/icons';\nimport { FootballQueries } from '@/lib/query-types';\n\nconst { Title, Text } = Typography;\nconst { Option } = Select;\n\ninterface LeagueFormProps {\n  initialValues?: Partial<FootballQueries.League>;\n  onSubmit: (values: FootballQueries.CreateLeagueRequest | FootballQueries.UpdateLeagueRequest) => void;\n  onCancel: () => void;\n  loading?: boolean;\n  mode: 'create' | 'edit';\n}\n\n// Common countries for football leagues\nconst COUNTRIES = [\n  'England', 'Spain', 'Germany', 'Italy', 'France', 'Netherlands', 'Portugal',\n  'Brazil', 'Argentina', 'Mexico', 'United States', 'Turkey', 'Russia',\n  'Belgium', 'Scotland', 'Austria', 'Switzerland', 'Greece', 'Ukraine',\n  'Poland', 'Czech Republic', 'Croatia', 'Serbia', 'Denmark', 'Sweden',\n  'Norway', 'Romania', 'Bulgaria', 'Hungary', 'Slovakia', 'Slovenia'\n];\n\n// Current and recent seasons\nconst SEASONS = [\n  '2024/25', '2023/24', '2022/23', '2021/22', '2020/21', '2019/20'\n];\n\nexport default function LeagueForm({\n  initialValues,\n  onSubmit,\n  onCancel,\n  loading = false,\n  mode\n}: LeagueFormProps) {\n  const [form] = Form.useForm();\n\n  const handleSubmit = (values: any) => {\n    const formData = {\n      ...values,\n      isActive: values.isActive ?? true\n    };\n    onSubmit(formData);\n  };\n\n  const handleLogoUpload = (info: any) => {\n    if (info.file.status === 'done') {\n      message.success(`${info.file.name} file uploaded successfully`);\n      form.setFieldsValue({ logo: info.file.response?.url });\n    } else if (info.file.status === 'error') {\n      message.error(`${info.file.name} file upload failed.`);\n    }\n  };\n\n  return (\n    <Card>\n      <div className=\"mb-6\">\n        <Title level={3}>\n          <TrophyOutlined className=\"mr-2\" />\n          {mode === 'create' ? 'Create New League' : 'Edit League'}\n        </Title>\n        <Text type=\"secondary\">\n          {mode === 'create' \n            ? 'Add a new football league to the system'\n            : 'Update league information and settings'\n          }\n        </Text>\n      </div>\n\n      <Form\n        form={form}\n        layout=\"vertical\"\n        initialValues={initialValues}\n        onFinish={handleSubmit}\n        size=\"large\"\n      >\n        <Row gutter={24}>\n          <Col xs={24} md={12}>\n            <Form.Item\n              name=\"name\"\n              label=\"League Name\"\n              rules={[\n                { required: true, message: 'Please enter league name' },\n                { min: 2, message: 'League name must be at least 2 characters' },\n                { max: 100, message: 'League name must not exceed 100 characters' }\n              ]}\n            >\n              <Input\n                placeholder=\"e.g., Premier League, La Liga, Bundesliga\"\n                prefix={<TrophyOutlined />}\n              />\n            </Form.Item>\n          </Col>\n\n          <Col xs={24} md={12}>\n            <Form.Item\n              name=\"country\"\n              label=\"Country\"\n              rules={[\n                { required: true, message: 'Please select country' }\n              ]}\n            >\n              <Select\n                placeholder=\"Select country\"\n                showSearch\n                filterOption={(input, option) =>\n                  (option?.children as string)?.toLowerCase().includes(input.toLowerCase())\n                }\n                prefix={<GlobalOutlined />}\n              >\n                {COUNTRIES.map(country => (\n                  <Option key={country} value={country}>\n                    {country}\n                  </Option>\n                ))}\n              </Select>\n            </Form.Item>\n          </Col>\n        </Row>\n\n        <Row gutter={24}>\n          <Col xs={24} md={12}>\n            <Form.Item\n              name=\"season\"\n              label=\"Season\"\n              rules={[\n                { required: true, message: 'Please select season' }\n              ]}\n            >\n              <Select placeholder=\"Select season\">\n                {SEASONS.map(season => (\n                  <Option key={season} value={season}>\n                    {season}\n                  </Option>\n                ))}\n              </Select>\n            </Form.Item>\n          </Col>\n\n          <Col xs={24} md={12}>\n            <Form.Item\n              name=\"isActive\"\n              label=\"Status\"\n              valuePropName=\"checked\"\n            >\n              <Switch\n                checkedChildren=\"Active\"\n                unCheckedChildren=\"Inactive\"\n                defaultChecked\n              />\n            </Form.Item>\n          </Col>\n        </Row>\n\n        <Row gutter={24}>\n          <Col xs={24}>\n            <Form.Item\n              name=\"logo\"\n              label=\"League Logo URL\"\n              rules={[\n                { type: 'url', message: 'Please enter a valid URL' }\n              ]}\n            >\n              <Input\n                placeholder=\"https://example.com/logo.png\"\n                prefix={<UploadOutlined />}\n              />\n            </Form.Item>\n          </Col>\n        </Row>\n\n        <Form.Item className=\"mb-0\">\n          <Space>\n            <Button\n              type=\"primary\"\n              htmlType=\"submit\"\n              loading={loading}\n              icon={<SaveOutlined />}\n              size=\"large\"\n            >\n              {mode === 'create' ? 'Create League' : 'Update League'}\n            </Button>\n            <Button\n              onClick={onCancel}\n              icon={<CloseOutlined />}\n              size=\"large\"\n            >\n              Cancel\n            </Button>\n          </Space>\n        </Form.Item>\n      </Form>\n    </Card>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAKD;AAAA;AAAA;AAAA;AAAA;AAcA;AAdA;AAAA;AAAA;AAcA;AAdA;AAcA;AAdA;AAAA;AAcA;AAAA;AAjBA;;;;AA0BA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,0LAAA,CAAA,aAAU;AAClC,MAAM,EAAE,MAAM,EAAE,GAAG,kLAAA,CAAA,SAAM;AAUzB,wCAAwC;AACxC,MAAM,YAAY;IAChB;IAAW;IAAS;IAAW;IAAS;IAAU;IAAe;IACjE;IAAU;IAAa;IAAU;IAAiB;IAAU;IAC5D;IAAW;IAAY;IAAW;IAAe;IAAU;IAC3D;IAAU;IAAkB;IAAW;IAAU;IAAW;IAC5D;IAAU;IAAW;IAAY;IAAW;IAAY;CACzD;AAED,6BAA6B;AAC7B,MAAM,UAAU;IACd;IAAW;IAAW;IAAW;IAAW;IAAW;CACxD;AAEc,SAAS,WAAW,EACjC,aAAa,EACb,QAAQ,EACR,QAAQ,EACR,UAAU,KAAK,EACf,IAAI,EACY;IAChB,MAAM,CAAC,KAAK,GAAG,8KAAA,CAAA,OAAI,CAAC,OAAO;IAE3B,MAAM,eAAe,CAAC;QACpB,MAAM,WAAW;YACf,GAAG,MAAM;YACT,UAAU,OAAO,QAAQ,IAAI;QAC/B;QACA,SAAS;IACX;IAEA,MAAM,mBAAmB,CAAC;QACxB,IAAI,KAAK,IAAI,CAAC,MAAM,KAAK,QAAQ;YAC/B,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC,GAAG,KAAK,IAAI,CAAC,IAAI,CAAC,2BAA2B,CAAC;YAC9D,KAAK,cAAc,CAAC;gBAAE,MAAM,KAAK,IAAI,CAAC,QAAQ,EAAE;YAAI;QACtD,OAAO,IAAI,KAAK,IAAI,CAAC,MAAM,KAAK,SAAS;YACvC,oLAAA,CAAA,UAAO,CAAC,KAAK,CAAC,GAAG,KAAK,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC;QACvD;IACF;IAEA,qBACE,8OAAC,8KAAA,CAAA,OAAI;;0BACH,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAM,OAAO;;0CACZ,8OAAC,sNAAA,CAAA,iBAAc;gCAAC,WAAU;;;;;;4BACzB,SAAS,WAAW,sBAAsB;;;;;;;kCAE7C,8OAAC;wBAAK,MAAK;kCACR,SAAS,WACN,4CACA;;;;;;;;;;;;0BAKR,8OAAC,8KAAA,CAAA,OAAI;gBACH,MAAM;gBACN,QAAO;gBACP,eAAe;gBACf,UAAU;gBACV,MAAK;;kCAEL,8OAAC,4KAAA,CAAA,MAAG;wBAAC,QAAQ;;0CACX,8OAAC,4KAAA,CAAA,MAAG;gCAAC,IAAI;gCAAI,IAAI;0CACf,cAAA,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCACL;4CAAE,UAAU;4CAAM,SAAS;wCAA2B;wCACtD;4CAAE,KAAK;4CAAG,SAAS;wCAA4C;wCAC/D;4CAAE,KAAK;4CAAK,SAAS;wCAA6C;qCACnE;8CAED,cAAA,8OAAC,gLAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,sBAAQ,8OAAC,sNAAA,CAAA,iBAAc;;;;;;;;;;;;;;;;;;;;0CAK7B,8OAAC,4KAAA,CAAA,MAAG;gCAAC,IAAI;gCAAI,IAAI;0CACf,cAAA,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCACL;4CAAE,UAAU;4CAAM,SAAS;wCAAwB;qCACpD;8CAED,cAAA,8OAAC,kLAAA,CAAA,SAAM;wCACL,aAAY;wCACZ,UAAU;wCACV,cAAc,CAAC,OAAO,SACnB,QAAQ,UAAqB,cAAc,SAAS,MAAM,WAAW;wCAExE,sBAAQ,8OAAC,sNAAA,CAAA,iBAAc;;;;;kDAEtB,UAAU,GAAG,CAAC,CAAA,wBACb,8OAAC;gDAAqB,OAAO;0DAC1B;+CADU;;;;;;;;;;;;;;;;;;;;;;;;;;kCASvB,8OAAC,4KAAA,CAAA,MAAG;wBAAC,QAAQ;;0CACX,8OAAC,4KAAA,CAAA,MAAG;gCAAC,IAAI;gCAAI,IAAI;0CACf,cAAA,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCACL;4CAAE,UAAU;4CAAM,SAAS;wCAAuB;qCACnD;8CAED,cAAA,8OAAC,kLAAA,CAAA,SAAM;wCAAC,aAAY;kDACjB,QAAQ,GAAG,CAAC,CAAA,uBACX,8OAAC;gDAAoB,OAAO;0DACzB;+CADU;;;;;;;;;;;;;;;;;;;;0CAQrB,8OAAC,4KAAA,CAAA,MAAG;gCAAC,IAAI;gCAAI,IAAI;0CACf,cAAA,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,eAAc;8CAEd,cAAA,8OAAC,kLAAA,CAAA,SAAM;wCACL,iBAAgB;wCAChB,mBAAkB;wCAClB,cAAc;;;;;;;;;;;;;;;;;;;;;;kCAMtB,8OAAC,4KAAA,CAAA,MAAG;wBAAC,QAAQ;kCACX,cAAA,8OAAC,4KAAA,CAAA,MAAG;4BAAC,IAAI;sCACP,cAAA,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;gCACR,MAAK;gCACL,OAAM;gCACN,OAAO;oCACL;wCAAE,MAAM;wCAAO,SAAS;oCAA2B;iCACpD;0CAED,cAAA,8OAAC,gLAAA,CAAA,QAAK;oCACJ,aAAY;oCACZ,sBAAQ,8OAAC,sNAAA,CAAA,iBAAc;;;;;;;;;;;;;;;;;;;;;;;;;kCAM/B,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;wBAAC,WAAU;kCACnB,cAAA,8OAAC,gMAAA,CAAA,QAAK;;8CACJ,8OAAC,kMAAA,CAAA,SAAM;oCACL,MAAK;oCACL,UAAS;oCACT,SAAS;oCACT,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;oCACnB,MAAK;8CAEJ,SAAS,WAAW,kBAAkB;;;;;;8CAEzC,8OAAC,kMAAA,CAAA,SAAM;oCACL,SAAS;oCACT,oBAAM,8OAAC,oNAAA,CAAA,gBAAa;;;;;oCACpB,MAAK;8CACN;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb"}}, {"offset": {"line": 406, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 412, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/football/team-form.tsx"], "sourcesContent": ["/**\n * Team Form Component\n * Form for creating and editing football teams\n */\n\n'use client';\n\nimport React from 'react';\nimport {\n  Form,\n  Input,\n  Select,\n  Switch,\n  Button,\n  Card,\n  Row,\n  Col,\n  Typography,\n  Space,\n  InputNumber,\n  message\n} from 'antd';\nimport {\n  TeamOutlined,\n  SaveOutlined,\n  CloseOutlined,\n  UploadOutlined,\n  GlobalOutlined,\n  TrophyOutlined,\n  HomeOutlined,\n  CalendarOutlined\n} from '@ant-design/icons';\nimport { FootballQueries } from '@/lib/query-types';\nimport { useLeagues } from '@/hooks/api/football-hooks';\n\nconst { Title, Text } = Typography;\nconst { Option } = Select;\n\ninterface TeamFormProps {\n  initialValues?: Partial<FootballQueries.Team>;\n  onSubmit: (values: FootballQueries.CreateTeamRequest | FootballQueries.UpdateTeamRequest) => void;\n  onCancel: () => void;\n  loading?: boolean;\n  mode: 'create' | 'edit';\n}\n\n// Common countries for football teams\nconst COUNTRIES = [\n  'England', 'Spain', 'Germany', 'Italy', 'France', 'Netherlands', 'Portugal',\n  'Brazil', 'Argentina', 'Mexico', 'United States', 'Turkey', 'Russia',\n  'Belgium', 'Scotland', 'Austria', 'Switzerland', 'Greece', 'Ukraine',\n  'Poland', 'Czech Republic', 'Croatia', 'Serbia', 'Denmark', 'Sweden',\n  'Norway', 'Romania', 'Bulgaria', 'Hungary', 'Slovakia', 'Slovenia'\n];\n\nexport default function TeamForm({\n  initialValues,\n  onSubmit,\n  onCancel,\n  loading = false,\n  mode\n}: TeamFormProps) {\n  const [form] = Form.useForm();\n  \n  // Fetch leagues for dropdown\n  const { data: leaguesData, isLoading: leaguesLoading } = useLeagues({ limit: 100 });\n\n  const handleSubmit = (values: any) => {\n    const formData = {\n      ...values,\n      isActive: values.isActive ?? true,\n      founded: values.founded ? parseInt(values.founded) : undefined\n    };\n    onSubmit(formData);\n  };\n\n  return (\n    <Card>\n      <div className=\"mb-6\">\n        <Title level={3}>\n          <TeamOutlined className=\"mr-2\" />\n          {mode === 'create' ? 'Create New Team' : 'Edit Team'}\n        </Title>\n        <Text type=\"secondary\">\n          {mode === 'create' \n            ? 'Add a new football team to the system'\n            : 'Update team information and settings'\n          }\n        </Text>\n      </div>\n\n      <Form\n        form={form}\n        layout=\"vertical\"\n        initialValues={initialValues}\n        onFinish={handleSubmit}\n        size=\"large\"\n      >\n        <Row gutter={24}>\n          <Col xs={24} md={12}>\n            <Form.Item\n              name=\"name\"\n              label=\"Team Name\"\n              rules={[\n                { required: true, message: 'Please enter team name' },\n                { min: 2, message: 'Team name must be at least 2 characters' },\n                { max: 100, message: 'Team name must not exceed 100 characters' }\n              ]}\n            >\n              <Input\n                placeholder=\"e.g., Manchester United, Real Madrid, Bayern Munich\"\n                prefix={<TeamOutlined />}\n              />\n            </Form.Item>\n          </Col>\n\n          <Col xs={24} md={12}>\n            <Form.Item\n              name=\"country\"\n              label=\"Country\"\n              rules={[\n                { required: true, message: 'Please select country' }\n              ]}\n            >\n              <Select\n                placeholder=\"Select country\"\n                showSearch\n                filterOption={(input, option) =>\n                  (option?.children as string)?.toLowerCase().includes(input.toLowerCase())\n                }\n              >\n                {COUNTRIES.map(country => (\n                  <Option key={country} value={country}>\n                    <GlobalOutlined className=\"mr-2\" />\n                    {country}\n                  </Option>\n                ))}\n              </Select>\n            </Form.Item>\n          </Col>\n        </Row>\n\n        <Row gutter={24}>\n          <Col xs={24} md={12}>\n            <Form.Item\n              name=\"leagueId\"\n              label=\"League\"\n              rules={[\n                { required: true, message: 'Please select league' }\n              ]}\n            >\n              <Select\n                placeholder=\"Select league\"\n                loading={leaguesLoading}\n                showSearch\n                filterOption={(input, option) =>\n                  (option?.children as string)?.toLowerCase().includes(input.toLowerCase())\n                }\n              >\n                {leaguesData?.data?.map(league => (\n                  <Option key={league.id} value={league.id}>\n                    <TrophyOutlined className=\"mr-2\" />\n                    {league.name} ({league.country})\n                  </Option>\n                ))}\n              </Select>\n            </Form.Item>\n          </Col>\n\n          <Col xs={24} md={12}>\n            <Form.Item\n              name=\"founded\"\n              label=\"Founded Year\"\n              rules={[\n                { type: 'number', min: 1800, max: new Date().getFullYear(), message: 'Please enter a valid year' }\n              ]}\n            >\n              <InputNumber\n                placeholder=\"e.g., 1878, 1902, 1900\"\n                prefix={<CalendarOutlined />}\n                style={{ width: '100%' }}\n                min={1800}\n                max={new Date().getFullYear()}\n              />\n            </Form.Item>\n          </Col>\n        </Row>\n\n        <Row gutter={24}>\n          <Col xs={24} md={12}>\n            <Form.Item\n              name=\"venue\"\n              label=\"Home Venue\"\n              rules={[\n                { max: 200, message: 'Venue name must not exceed 200 characters' }\n              ]}\n            >\n              <Input\n                placeholder=\"e.g., Old Trafford, Santiago Bernabéu, Allianz Arena\"\n                prefix={<HomeOutlined />}\n              />\n            </Form.Item>\n          </Col>\n\n          <Col xs={24} md={12}>\n            <Form.Item\n              name=\"isActive\"\n              label=\"Status\"\n              valuePropName=\"checked\"\n            >\n              <Switch\n                checkedChildren=\"Active\"\n                unCheckedChildren=\"Inactive\"\n                defaultChecked\n              />\n            </Form.Item>\n          </Col>\n        </Row>\n\n        <Row gutter={24}>\n          <Col xs={24}>\n            <Form.Item\n              name=\"logo\"\n              label=\"Team Logo URL\"\n              rules={[\n                { type: 'url', message: 'Please enter a valid URL' }\n              ]}\n            >\n              <Input\n                placeholder=\"https://example.com/team-logo.png\"\n                prefix={<UploadOutlined />}\n              />\n            </Form.Item>\n          </Col>\n        </Row>\n\n        <Form.Item className=\"mb-0\">\n          <Space>\n            <Button\n              type=\"primary\"\n              htmlType=\"submit\"\n              loading={loading}\n              icon={<SaveOutlined />}\n              size=\"large\"\n            >\n              {mode === 'create' ? 'Create Team' : 'Update Team'}\n            </Button>\n            <Button\n              onClick={onCancel}\n              icon={<CloseOutlined />}\n              size=\"large\"\n            >\n              Cancel\n            </Button>\n          </Space>\n        </Form.Item>\n      </Form>\n    </Card>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AA8BD;AAzBA;AAAA;AAAA;AAAA;AAcA;AAdA;AAAA;AAAA;AAcA;AAAA;AAdA;AAcA;AAAA;AAdA;AAcA;AAdA;AAAA;AAcA;AAAA;AAjBA;;;;;AA8BA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,0LAAA,CAAA,aAAU;AAClC,MAAM,EAAE,MAAM,EAAE,GAAG,kLAAA,CAAA,SAAM;AAUzB,sCAAsC;AACtC,MAAM,YAAY;IAChB;IAAW;IAAS;IAAW;IAAS;IAAU;IAAe;IACjE;IAAU;IAAa;IAAU;IAAiB;IAAU;IAC5D;IAAW;IAAY;IAAW;IAAe;IAAU;IAC3D;IAAU;IAAkB;IAAW;IAAU;IAAW;IAC5D;IAAU;IAAW;IAAY;IAAW;IAAY;CACzD;AAEc,SAAS,SAAS,EAC/B,aAAa,EACb,QAAQ,EACR,QAAQ,EACR,UAAU,KAAK,EACf,IAAI,EACU;IACd,MAAM,CAAC,KAAK,GAAG,8KAAA,CAAA,OAAI,CAAC,OAAO;IAE3B,6BAA6B;IAC7B,MAAM,EAAE,MAAM,WAAW,EAAE,WAAW,cAAc,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,aAAU,AAAD,EAAE;QAAE,OAAO;IAAI;IAEjF,MAAM,eAAe,CAAC;QACpB,MAAM,WAAW;YACf,GAAG,MAAM;YACT,UAAU,OAAO,QAAQ,IAAI;YAC7B,SAAS,OAAO,OAAO,GAAG,SAAS,OAAO,OAAO,IAAI;QACvD;QACA,SAAS;IACX;IAEA,qBACE,8OAAC,8KAAA,CAAA,OAAI;;0BACH,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAM,OAAO;;0CACZ,8OAAC,kNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;4BACvB,SAAS,WAAW,oBAAoB;;;;;;;kCAE3C,8OAAC;wBAAK,MAAK;kCACR,SAAS,WACN,0CACA;;;;;;;;;;;;0BAKR,8OAAC,8KAAA,CAAA,OAAI;gBACH,MAAM;gBACN,QAAO;gBACP,eAAe;gBACf,UAAU;gBACV,MAAK;;kCAEL,8OAAC,4KAAA,CAAA,MAAG;wBAAC,QAAQ;;0CACX,8OAAC,4KAAA,CAAA,MAAG;gCAAC,IAAI;gCAAI,IAAI;0CACf,cAAA,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCACL;4CAAE,UAAU;4CAAM,SAAS;wCAAyB;wCACpD;4CAAE,KAAK;4CAAG,SAAS;wCAA0C;wCAC7D;4CAAE,KAAK;4CAAK,SAAS;wCAA2C;qCACjE;8CAED,cAAA,8OAAC,gLAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,sBAAQ,8OAAC,kNAAA,CAAA,eAAY;;;;;;;;;;;;;;;;;;;;0CAK3B,8OAAC,4KAAA,CAAA,MAAG;gCAAC,IAAI;gCAAI,IAAI;0CACf,cAAA,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCACL;4CAAE,UAAU;4CAAM,SAAS;wCAAwB;qCACpD;8CAED,cAAA,8OAAC,kLAAA,CAAA,SAAM;wCACL,aAAY;wCACZ,UAAU;wCACV,cAAc,CAAC,OAAO,SACnB,QAAQ,UAAqB,cAAc,SAAS,MAAM,WAAW;kDAGvE,UAAU,GAAG,CAAC,CAAA,wBACb,8OAAC;gDAAqB,OAAO;;kEAC3B,8OAAC,sNAAA,CAAA,iBAAc;wDAAC,WAAU;;;;;;oDACzB;;+CAFU;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUvB,8OAAC,4KAAA,CAAA,MAAG;wBAAC,QAAQ;;0CACX,8OAAC,4KAAA,CAAA,MAAG;gCAAC,IAAI;gCAAI,IAAI;0CACf,cAAA,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCACL;4CAAE,UAAU;4CAAM,SAAS;wCAAuB;qCACnD;8CAED,cAAA,8OAAC,kLAAA,CAAA,SAAM;wCACL,aAAY;wCACZ,SAAS;wCACT,UAAU;wCACV,cAAc,CAAC,OAAO,SACnB,QAAQ,UAAqB,cAAc,SAAS,MAAM,WAAW;kDAGvE,aAAa,MAAM,IAAI,CAAA,uBACtB,8OAAC;gDAAuB,OAAO,OAAO,EAAE;;kEACtC,8OAAC,sNAAA,CAAA,iBAAc;wDAAC,WAAU;;;;;;oDACzB,OAAO,IAAI;oDAAC;oDAAG,OAAO,OAAO;oDAAC;;+CAFpB,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;0CAS9B,8OAAC,4KAAA,CAAA,MAAG;gCAAC,IAAI;gCAAI,IAAI;0CACf,cAAA,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCACL;4CAAE,MAAM;4CAAU,KAAK;4CAAM,KAAK,IAAI,OAAO,WAAW;4CAAI,SAAS;wCAA4B;qCAClG;8CAED,cAAA,8OAAC,gMAAA,CAAA,cAAW;wCACV,aAAY;wCACZ,sBAAQ,8OAAC,0NAAA,CAAA,mBAAgB;;;;;wCACzB,OAAO;4CAAE,OAAO;wCAAO;wCACvB,KAAK;wCACL,KAAK,IAAI,OAAO,WAAW;;;;;;;;;;;;;;;;;;;;;;kCAMnC,8OAAC,4KAAA,CAAA,MAAG;wBAAC,QAAQ;;0CACX,8OAAC,4KAAA,CAAA,MAAG;gCAAC,IAAI;gCAAI,IAAI;0CACf,cAAA,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCACL;4CAAE,KAAK;4CAAK,SAAS;wCAA4C;qCAClE;8CAED,cAAA,8OAAC,gLAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,sBAAQ,8OAAC,kNAAA,CAAA,eAAY;;;;;;;;;;;;;;;;;;;;0CAK3B,8OAAC,4KAAA,CAAA,MAAG;gCAAC,IAAI;gCAAI,IAAI;0CACf,cAAA,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,eAAc;8CAEd,cAAA,8OAAC,kLAAA,CAAA,SAAM;wCACL,iBAAgB;wCAChB,mBAAkB;wCAClB,cAAc;;;;;;;;;;;;;;;;;;;;;;kCAMtB,8OAAC,4KAAA,CAAA,MAAG;wBAAC,QAAQ;kCACX,cAAA,8OAAC,4KAAA,CAAA,MAAG;4BAAC,IAAI;sCACP,cAAA,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;gCACR,MAAK;gCACL,OAAM;gCACN,OAAO;oCACL;wCAAE,MAAM;wCAAO,SAAS;oCAA2B;iCACpD;0CAED,cAAA,8OAAC,gLAAA,CAAA,QAAK;oCACJ,aAAY;oCACZ,sBAAQ,8OAAC,sNAAA,CAAA,iBAAc;;;;;;;;;;;;;;;;;;;;;;;;;kCAM/B,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;wBAAC,WAAU;kCACnB,cAAA,8OAAC,gMAAA,CAAA,QAAK;;8CACJ,8OAAC,kMAAA,CAAA,SAAM;oCACL,MAAK;oCACL,UAAS;oCACT,SAAS;oCACT,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;oCACnB,MAAK;8CAEJ,SAAS,WAAW,gBAAgB;;;;;;8CAEvC,8OAAC,kMAAA,CAAA,SAAM;oCACL,SAAS;oCACT,oBAAM,8OAAC,oNAAA,CAAA,gBAAa;;;;;oCACpB,MAAK;8CACN;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb"}}, {"offset": {"line": 905, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 911, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/football/fixture-form.tsx"], "sourcesContent": ["/**\n * Fixture Form Component\n * Form for creating and editing football fixtures\n */\n\n'use client';\n\nimport React from 'react';\nimport {\n  Form,\n  Input,\n  Select,\n  Button,\n  Card,\n  Row,\n  Col,\n  Typography,\n  Space,\n  DatePicker,\n  InputNumber,\n  message\n} from 'antd';\nimport {\n  CalendarOutlined,\n  SaveOutlined,\n  CloseOutlined,\n  TeamOutlined,\n  TrophyOutlined,\n  HomeOutlined,\n  NumberOutlined\n} from '@ant-design/icons';\nimport { FootballQueries } from '@/lib/query-types';\nimport { useLeagues, useTeams } from '@/hooks/api/football-hooks';\nimport dayjs from 'dayjs';\n\nconst { Title, Text } = Typography;\nconst { Option } = Select;\n\ninterface FixtureFormProps {\n  initialValues?: Partial<FootballQueries.Fixture>;\n  onSubmit: (values: FootballQueries.CreateFixtureRequest | FootballQueries.UpdateFixtureRequest) => void;\n  onCancel: () => void;\n  loading?: boolean;\n  mode: 'create' | 'edit';\n}\n\n// Fixture status options\nconst FIXTURE_STATUSES = [\n  { value: 'scheduled', label: 'Scheduled', color: 'blue' },\n  { value: 'live', label: 'Live', color: 'red' },\n  { value: 'finished', label: 'Finished', color: 'green' },\n  { value: 'postponed', label: 'Postponed', color: 'orange' },\n  { value: 'cancelled', label: 'Cancelled', color: 'gray' },\n  { value: 'suspended', label: 'Suspended', color: 'purple' }\n];\n\nexport default function FixtureForm({\n  initialValues,\n  onSubmit,\n  onCancel,\n  loading = false,\n  mode\n}: FixtureFormProps) {\n  const [form] = Form.useForm();\n  \n  // Fetch leagues and teams for dropdowns\n  const { data: leaguesData, isLoading: leaguesLoading } = useLeagues({ limit: 100 });\n  const { data: teamsData, isLoading: teamsLoading } = useTeams({ limit: 200 });\n\n  const handleSubmit = (values: any) => {\n    const formData = {\n      ...values,\n      date: values.date ? values.date.toISOString() : undefined,\n      homeScore: values.homeScore || undefined,\n      awayScore: values.awayScore || undefined\n    };\n    onSubmit(formData);\n  };\n\n  // Filter teams by selected league\n  const [selectedLeague, setSelectedLeague] = React.useState<string | undefined>(\n    initialValues?.leagueId\n  );\n\n  const filteredTeams = React.useMemo(() => {\n    if (!teamsData?.data || !selectedLeague) return teamsData?.data || [];\n    return teamsData.data.filter(team => team.leagueId === selectedLeague);\n  }, [teamsData?.data, selectedLeague]);\n\n  return (\n    <Card>\n      <div className=\"mb-6\">\n        <Title level={3}>\n          <CalendarOutlined className=\"mr-2\" />\n          {mode === 'create' ? 'Create New Fixture' : 'Edit Fixture'}\n        </Title>\n        <Text type=\"secondary\">\n          {mode === 'create' \n            ? 'Add a new football fixture to the system'\n            : 'Update fixture information and results'\n          }\n        </Text>\n      </div>\n\n      <Form\n        form={form}\n        layout=\"vertical\"\n        initialValues={{\n          ...initialValues,\n          date: initialValues?.date ? dayjs(initialValues.date) : undefined\n        }}\n        onFinish={handleSubmit}\n        size=\"large\"\n      >\n        <Row gutter={24}>\n          <Col xs={24} md={12}>\n            <Form.Item\n              name=\"externalId\"\n              label=\"External ID\"\n              rules={[\n                { required: mode === 'create', message: 'Please enter external ID' },\n                { min: 1, message: 'External ID must be at least 1 character' }\n              ]}\n            >\n              <Input\n                placeholder=\"e.g., 12345, ext_001\"\n                prefix={<NumberOutlined />}\n                disabled={mode === 'edit'}\n              />\n            </Form.Item>\n          </Col>\n\n          <Col xs={24} md={12}>\n            <Form.Item\n              name=\"leagueId\"\n              label=\"League\"\n              rules={[\n                { required: true, message: 'Please select league' }\n              ]}\n            >\n              <Select\n                placeholder=\"Select league\"\n                loading={leaguesLoading}\n                showSearch\n                filterOption={(input, option) =>\n                  (option?.children as string)?.toLowerCase().includes(input.toLowerCase())\n                }\n                onChange={(value) => {\n                  setSelectedLeague(value);\n                  // Clear team selections when league changes\n                  form.setFieldsValue({ homeTeamId: undefined, awayTeamId: undefined });\n                }}\n              >\n                {leaguesData?.data?.map(league => (\n                  <Option key={league.id} value={league.id}>\n                    <TrophyOutlined className=\"mr-2\" />\n                    {league.name} ({league.country})\n                  </Option>\n                ))}\n              </Select>\n            </Form.Item>\n          </Col>\n        </Row>\n\n        <Row gutter={24}>\n          <Col xs={24} md={12}>\n            <Form.Item\n              name=\"homeTeamId\"\n              label=\"Home Team\"\n              rules={[\n                { required: true, message: 'Please select home team' }\n              ]}\n            >\n              <Select\n                placeholder=\"Select home team\"\n                loading={teamsLoading}\n                showSearch\n                filterOption={(input, option) =>\n                  (option?.children as string)?.toLowerCase().includes(input.toLowerCase())\n                }\n                disabled={!selectedLeague}\n              >\n                {filteredTeams.map(team => (\n                  <Option key={team.id} value={team.id}>\n                    <TeamOutlined className=\"mr-2\" />\n                    {team.name}\n                  </Option>\n                ))}\n              </Select>\n            </Form.Item>\n          </Col>\n\n          <Col xs={24} md={12}>\n            <Form.Item\n              name=\"awayTeamId\"\n              label=\"Away Team\"\n              rules={[\n                { required: true, message: 'Please select away team' }\n              ]}\n            >\n              <Select\n                placeholder=\"Select away team\"\n                loading={teamsLoading}\n                showSearch\n                filterOption={(input, option) =>\n                  (option?.children as string)?.toLowerCase().includes(input.toLowerCase())\n                }\n                disabled={!selectedLeague}\n              >\n                {filteredTeams.map(team => (\n                  <Option key={team.id} value={team.id}>\n                    <TeamOutlined className=\"mr-2\" />\n                    {team.name}\n                  </Option>\n                ))}\n              </Select>\n            </Form.Item>\n          </Col>\n        </Row>\n\n        <Row gutter={24}>\n          <Col xs={24} md={12}>\n            <Form.Item\n              name=\"date\"\n              label=\"Match Date & Time\"\n              rules={[\n                { required: true, message: 'Please select match date and time' }\n              ]}\n            >\n              <DatePicker\n                showTime\n                format=\"YYYY-MM-DD HH:mm\"\n                placeholder=\"Select date and time\"\n                style={{ width: '100%' }}\n              />\n            </Form.Item>\n          </Col>\n\n          <Col xs={24} md={12}>\n            <Form.Item\n              name=\"status\"\n              label=\"Status\"\n              rules={[\n                { required: true, message: 'Please select status' }\n              ]}\n            >\n              <Select placeholder=\"Select status\">\n                {FIXTURE_STATUSES.map(status => (\n                  <Option key={status.value} value={status.value}>\n                    <span style={{ color: status.color }}>●</span>\n                    <span className=\"ml-2\">{status.label}</span>\n                  </Option>\n                ))}\n              </Select>\n            </Form.Item>\n          </Col>\n        </Row>\n\n        <Row gutter={24}>\n          <Col xs={24} md={8}>\n            <Form.Item\n              name=\"venue\"\n              label=\"Venue\"\n            >\n              <Input\n                placeholder=\"e.g., Old Trafford, Wembley Stadium\"\n                prefix={<HomeOutlined />}\n              />\n            </Form.Item>\n          </Col>\n\n          <Col xs={24} md={8}>\n            <Form.Item\n              name=\"round\"\n              label=\"Round/Matchday\"\n            >\n              <Input\n                placeholder=\"e.g., Matchday 15, Round 16\"\n              />\n            </Form.Item>\n          </Col>\n\n          <Col xs={24} md={8}>\n            <div className=\"grid grid-cols-2 gap-2\">\n              <Form.Item\n                name=\"homeScore\"\n                label=\"Home Score\"\n              >\n                <InputNumber\n                  placeholder=\"0\"\n                  min={0}\n                  max={20}\n                  style={{ width: '100%' }}\n                />\n              </Form.Item>\n              <Form.Item\n                name=\"awayScore\"\n                label=\"Away Score\"\n              >\n                <InputNumber\n                  placeholder=\"0\"\n                  min={0}\n                  max={20}\n                  style={{ width: '100%' }}\n                />\n              </Form.Item>\n            </div>\n          </Col>\n        </Row>\n\n        <Form.Item className=\"mb-0\">\n          <Space>\n            <Button\n              type=\"primary\"\n              htmlType=\"submit\"\n              loading={loading}\n              icon={<SaveOutlined />}\n              size=\"large\"\n            >\n              {mode === 'create' ? 'Create Fixture' : 'Update Fixture'}\n            </Button>\n            <Button\n              onClick={onCancel}\n              icon={<CloseOutlined />}\n              size=\"large\"\n            >\n              Cancel\n            </Button>\n          </Space>\n        </Form.Item>\n      </Form>\n    </Card>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAID;AAyBA;AACA;AAzBA;AAAA;AAAA;AAAA;AAcA;AAdA;AAAA;AAAA;AAcA;AAAA;AAAA;AAdA;AAcA;AAdA;AAAA;AAAA;AAcA;AAAA;AAjBA;;;;;;;AA8BA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,0LAAA,CAAA,aAAU;AAClC,MAAM,EAAE,MAAM,EAAE,GAAG,kLAAA,CAAA,SAAM;AAUzB,yBAAyB;AACzB,MAAM,mBAAmB;IACvB;QAAE,OAAO;QAAa,OAAO;QAAa,OAAO;IAAO;IACxD;QAAE,OAAO;QAAQ,OAAO;QAAQ,OAAO;IAAM;IAC7C;QAAE,OAAO;QAAY,OAAO;QAAY,OAAO;IAAQ;IACvD;QAAE,OAAO;QAAa,OAAO;QAAa,OAAO;IAAS;IAC1D;QAAE,OAAO;QAAa,OAAO;QAAa,OAAO;IAAO;IACxD;QAAE,OAAO;QAAa,OAAO;QAAa,OAAO;IAAS;CAC3D;AAEc,SAAS,YAAY,EAClC,aAAa,EACb,QAAQ,EACR,QAAQ,EACR,UAAU,KAAK,EACf,IAAI,EACa;IACjB,MAAM,CAAC,KAAK,GAAG,8KAAA,CAAA,OAAI,CAAC,OAAO;IAE3B,wCAAwC;IACxC,MAAM,EAAE,MAAM,WAAW,EAAE,WAAW,cAAc,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,aAAU,AAAD,EAAE;QAAE,OAAO;IAAI;IACjF,MAAM,EAAE,MAAM,SAAS,EAAE,WAAW,YAAY,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,OAAO;IAAI;IAE3E,MAAM,eAAe,CAAC;QACpB,MAAM,WAAW;YACf,GAAG,MAAM;YACT,MAAM,OAAO,IAAI,GAAG,OAAO,IAAI,CAAC,WAAW,KAAK;YAChD,WAAW,OAAO,SAAS,IAAI;YAC/B,WAAW,OAAO,SAAS,IAAI;QACjC;QACA,SAAS;IACX;IAEA,kCAAkC;IAClC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,qMAAA,CAAA,UAAK,CAAC,QAAQ,CACxD,eAAe;IAGjB,MAAM,gBAAgB,qMAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QAClC,IAAI,CAAC,WAAW,QAAQ,CAAC,gBAAgB,OAAO,WAAW,QAAQ,EAAE;QACrE,OAAO,UAAU,IAAI,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK;IACzD,GAAG;QAAC,WAAW;QAAM;KAAe;IAEpC,qBACE,8OAAC,8KAAA,CAAA,OAAI;;0BACH,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAM,OAAO;;0CACZ,8OAAC,0NAAA,CAAA,mBAAgB;gCAAC,WAAU;;;;;;4BAC3B,SAAS,WAAW,uBAAuB;;;;;;;kCAE9C,8OAAC;wBAAK,MAAK;kCACR,SAAS,WACN,6CACA;;;;;;;;;;;;0BAKR,8OAAC,8KAAA,CAAA,OAAI;gBACH,MAAM;gBACN,QAAO;gBACP,eAAe;oBACb,GAAG,aAAa;oBAChB,MAAM,eAAe,OAAO,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD,EAAE,cAAc,IAAI,IAAI;gBAC1D;gBACA,UAAU;gBACV,MAAK;;kCAEL,8OAAC,4KAAA,CAAA,MAAG;wBAAC,QAAQ;;0CACX,8OAAC,4KAAA,CAAA,MAAG;gCAAC,IAAI;gCAAI,IAAI;0CACf,cAAA,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCACL;4CAAE,UAAU,SAAS;4CAAU,SAAS;wCAA2B;wCACnE;4CAAE,KAAK;4CAAG,SAAS;wCAA2C;qCAC/D;8CAED,cAAA,8OAAC,gLAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,sBAAQ,8OAAC,sNAAA,CAAA,iBAAc;;;;;wCACvB,UAAU,SAAS;;;;;;;;;;;;;;;;0CAKzB,8OAAC,4KAAA,CAAA,MAAG;gCAAC,IAAI;gCAAI,IAAI;0CACf,cAAA,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCACL;4CAAE,UAAU;4CAAM,SAAS;wCAAuB;qCACnD;8CAED,cAAA,8OAAC,kLAAA,CAAA,SAAM;wCACL,aAAY;wCACZ,SAAS;wCACT,UAAU;wCACV,cAAc,CAAC,OAAO,SACnB,QAAQ,UAAqB,cAAc,SAAS,MAAM,WAAW;wCAExE,UAAU,CAAC;4CACT,kBAAkB;4CAClB,4CAA4C;4CAC5C,KAAK,cAAc,CAAC;gDAAE,YAAY;gDAAW,YAAY;4CAAU;wCACrE;kDAEC,aAAa,MAAM,IAAI,CAAA,uBACtB,8OAAC;gDAAuB,OAAO,OAAO,EAAE;;kEACtC,8OAAC,sNAAA,CAAA,iBAAc;wDAAC,WAAU;;;;;;oDACzB,OAAO,IAAI;oDAAC;oDAAG,OAAO,OAAO;oDAAC;;+CAFpB,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUhC,8OAAC,4KAAA,CAAA,MAAG;wBAAC,QAAQ;;0CACX,8OAAC,4KAAA,CAAA,MAAG;gCAAC,IAAI;gCAAI,IAAI;0CACf,cAAA,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCACL;4CAAE,UAAU;4CAAM,SAAS;wCAA0B;qCACtD;8CAED,cAAA,8OAAC,kLAAA,CAAA,SAAM;wCACL,aAAY;wCACZ,SAAS;wCACT,UAAU;wCACV,cAAc,CAAC,OAAO,SACnB,QAAQ,UAAqB,cAAc,SAAS,MAAM,WAAW;wCAExE,UAAU,CAAC;kDAEV,cAAc,GAAG,CAAC,CAAA,qBACjB,8OAAC;gDAAqB,OAAO,KAAK,EAAE;;kEAClC,8OAAC,kNAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;oDACvB,KAAK,IAAI;;+CAFC,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;0CAS5B,8OAAC,4KAAA,CAAA,MAAG;gCAAC,IAAI;gCAAI,IAAI;0CACf,cAAA,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCACL;4CAAE,UAAU;4CAAM,SAAS;wCAA0B;qCACtD;8CAED,cAAA,8OAAC,kLAAA,CAAA,SAAM;wCACL,aAAY;wCACZ,SAAS;wCACT,UAAU;wCACV,cAAc,CAAC,OAAO,SACnB,QAAQ,UAAqB,cAAc,SAAS,MAAM,WAAW;wCAExE,UAAU,CAAC;kDAEV,cAAc,GAAG,CAAC,CAAA,qBACjB,8OAAC;gDAAqB,OAAO,KAAK,EAAE;;kEAClC,8OAAC,kNAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;oDACvB,KAAK,IAAI;;+CAFC,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;kCAU9B,8OAAC,4KAAA,CAAA,MAAG;wBAAC,QAAQ;;0CACX,8OAAC,4KAAA,CAAA,MAAG;gCAAC,IAAI;gCAAI,IAAI;0CACf,cAAA,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCACL;4CAAE,UAAU;4CAAM,SAAS;wCAAoC;qCAChE;8CAED,cAAA,8OAAC,8LAAA,CAAA,aAAU;wCACT,QAAQ;wCACR,QAAO;wCACP,aAAY;wCACZ,OAAO;4CAAE,OAAO;wCAAO;;;;;;;;;;;;;;;;0CAK7B,8OAAC,4KAAA,CAAA,MAAG;gCAAC,IAAI;gCAAI,IAAI;0CACf,cAAA,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCACL;4CAAE,UAAU;4CAAM,SAAS;wCAAuB;qCACnD;8CAED,cAAA,8OAAC,kLAAA,CAAA,SAAM;wCAAC,aAAY;kDACjB,iBAAiB,GAAG,CAAC,CAAA,uBACpB,8OAAC;gDAA0B,OAAO,OAAO,KAAK;;kEAC5C,8OAAC;wDAAK,OAAO;4DAAE,OAAO,OAAO,KAAK;wDAAC;kEAAG;;;;;;kEACtC,8OAAC;wDAAK,WAAU;kEAAQ,OAAO,KAAK;;;;;;;+CAFzB,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUnC,8OAAC,4KAAA,CAAA,MAAG;wBAAC,QAAQ;;0CACX,8OAAC,4KAAA,CAAA,MAAG;gCAAC,IAAI;gCAAI,IAAI;0CACf,cAAA,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;8CAEN,cAAA,8OAAC,gLAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,sBAAQ,8OAAC,kNAAA,CAAA,eAAY;;;;;;;;;;;;;;;;;;;;0CAK3B,8OAAC,4KAAA,CAAA,MAAG;gCAAC,IAAI;gCAAI,IAAI;0CACf,cAAA,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;8CAEN,cAAA,8OAAC,gLAAA,CAAA,QAAK;wCACJ,aAAY;;;;;;;;;;;;;;;;0CAKlB,8OAAC,4KAAA,CAAA,MAAG;gCAAC,IAAI;gCAAI,IAAI;0CACf,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;4CACR,MAAK;4CACL,OAAM;sDAEN,cAAA,8OAAC,gMAAA,CAAA,cAAW;gDACV,aAAY;gDACZ,KAAK;gDACL,KAAK;gDACL,OAAO;oDAAE,OAAO;gDAAO;;;;;;;;;;;sDAG3B,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;4CACR,MAAK;4CACL,OAAM;sDAEN,cAAA,8OAAC,gMAAA,CAAA,cAAW;gDACV,aAAY;gDACZ,KAAK;gDACL,KAAK;gDACL,OAAO;oDAAE,OAAO;gDAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOjC,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;wBAAC,WAAU;kCACnB,cAAA,8OAAC,gMAAA,CAAA,QAAK;;8CACJ,8OAAC,kMAAA,CAAA,SAAM;oCACL,MAAK;oCACL,UAAS;oCACT,SAAS;oCACT,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;oCACnB,MAAK;8CAEJ,SAAS,WAAW,mBAAmB;;;;;;8CAE1C,8OAAC,kMAAA,CAAA,SAAM;oCACL,SAAS;oCACT,oBAAM,8OAAC,oNAAA,CAAA,gBAAa;;;;;oCACpB,MAAK;8CACN;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb"}}, {"offset": {"line": 1545, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1551, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/football/index.ts"], "sourcesContent": ["/**\n * Football Components\n * Export all football-related components\n */\n\nexport { default as LeagueForm } from './league-form';\nexport { default as TeamForm } from './team-form';\nexport { default as FixtureForm } from './fixture-form';\n"], "names": [], "mappings": "AAAA;;;CAGC"}}, {"offset": {"line": 1558, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1585, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/app/football/fixtures/page.tsx"], "sourcesContent": ["/**\n * Football Fixtures Management Page\n * Comprehensive fixtures management with CRUD operations and sync management\n */\n\n'use client';\n\nimport React, { useState } from 'react';\nimport {\n  Card,\n  Table,\n  Button,\n  Input,\n  Select,\n  Space,\n  Tag,\n  Tooltip,\n  Popconfirm,\n  Row,\n  Col,\n  Statistic,\n  Typography,\n  message,\n  Badge,\n  Avatar,\n  Dropdown,\n  MenuProps,\n  Modal,\n  DatePicker,\n  Progress,\n  Alert,\n  Divider\n} from 'antd';\nimport {\n  PlusOutlined,\n  SearchOutlined,\n  FilterOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  EyeOutlined,\n  CalendarOutlined,\n  GlobalOutlined,\n  MoreOutlined,\n  ReloadOutlined,\n  ExportOutlined,\n  TrophyOutlined,\n  TeamOutlined,\n  SyncOutlined,\n  PlayCircleOutlined,\n  ClockCircleOutlined,\n  CheckCircleOutlined,\n  StopOutlined,\n  WarningOutlined\n} from '@ant-design/icons';\nimport { useRouter } from 'next/navigation';\nimport { ColumnsType } from 'antd/es/table';\nimport { FootballQueries } from '@/lib/query-types';\nimport {\n  useFixtures,\n  useLeagues,\n  useTeams,\n  useCreateFixture,\n  useUpdateFixture,\n  useDeleteFixture,\n  useDailySync,\n  useSyncStatus\n} from '@/hooks/api/football-hooks';\nimport { FixtureForm } from '@/components/football';\nimport dayjs from 'dayjs';\n\nconst { Title, Text } = Typography;\nconst { Option } = Select;\nconst { RangePicker } = DatePicker;\n\n// Mock data for development\nconst MOCK_FIXTURES: FootballQueries.Fixture[] = [\n  {\n    externalId: 'fix_001',\n    homeTeam: { id: '1', name: 'Manchester United', logo: 'https://logos-world.net/wp-content/uploads/2020/06/Manchester-United-Logo.png' },\n    awayTeam: { id: '2', name: 'Liverpool', logo: 'https://logos-world.net/wp-content/uploads/2020/06/Liverpool-Logo.png' },\n    league: { id: '1', name: 'Premier League', country: 'England', season: '2024/25', isActive: true, createdAt: '', updatedAt: '' },\n    date: '2024-05-26T15:00:00Z',\n    status: 'scheduled',\n    venue: 'Old Trafford',\n    round: 'Matchday 38',\n    homeScore: null,\n    awayScore: null,\n    createdAt: '2024-01-15T10:00:00Z',\n    updatedAt: '2024-01-15T10:00:00Z'\n  },\n  {\n    externalId: 'fix_002',\n    homeTeam: { id: '3', name: 'Real Madrid', logo: 'https://logos-world.net/wp-content/uploads/2020/06/Real-Madrid-Logo.png' },\n    awayTeam: { id: '4', name: 'Barcelona', logo: 'https://logos-world.net/wp-content/uploads/2020/06/Barcelona-Logo.png' },\n    league: { id: '2', name: 'La Liga', country: 'Spain', season: '2024/25', isActive: true, createdAt: '', updatedAt: '' },\n    date: '2024-05-25T20:00:00Z',\n    status: 'live',\n    venue: 'Santiago Bernabéu',\n    round: 'El Clásico',\n    homeScore: 2,\n    awayScore: 1,\n    createdAt: '2024-01-15T10:00:00Z',\n    updatedAt: '2024-01-15T10:00:00Z'\n  },\n  {\n    externalId: 'fix_003',\n    homeTeam: { id: '5', name: 'Bayern Munich', logo: 'https://logos-world.net/wp-content/uploads/2020/06/Bayern-Munich-Logo.png' },\n    awayTeam: { id: '6', name: 'Borussia Dortmund', logo: 'https://logos-world.net/wp-content/uploads/2020/06/Borussia-Dortmund-Logo.png' },\n    league: { id: '3', name: 'Bundesliga', country: 'Germany', season: '2024/25', isActive: true, createdAt: '', updatedAt: '' },\n    date: '2024-05-24T18:30:00Z',\n    status: 'finished',\n    venue: 'Allianz Arena',\n    round: 'Der Klassiker',\n    homeScore: 3,\n    awayScore: 1,\n    createdAt: '2024-01-15T10:00:00Z',\n    updatedAt: '2024-01-15T10:00:00Z'\n  },\n  {\n    externalId: 'fix_004',\n    homeTeam: { id: '7', name: 'AC Milan' },\n    awayTeam: { id: '8', name: 'Inter Milan' },\n    league: { id: '4', name: 'Serie A', country: 'Italy', season: '2024/25', isActive: true, createdAt: '', updatedAt: '' },\n    date: '2024-05-27T19:45:00Z',\n    status: 'postponed',\n    venue: 'San Siro',\n    round: 'Derby della Madonnina',\n    homeScore: null,\n    awayScore: null,\n    createdAt: '2024-01-15T10:00:00Z',\n    updatedAt: '2024-01-15T10:00:00Z'\n  },\n  {\n    externalId: 'fix_005',\n    homeTeam: { id: '9', name: 'Paris Saint-Germain' },\n    awayTeam: { id: '10', name: 'Olympique Marseille' },\n    league: { id: '5', name: 'Ligue 1', country: 'France', season: '2023/24', isActive: false, createdAt: '', updatedAt: '' },\n    date: '2024-05-23T21:00:00Z',\n    status: 'cancelled',\n    venue: 'Parc des Princes',\n    round: 'Le Classique',\n    homeScore: null,\n    awayScore: null,\n    createdAt: '2024-01-15T10:00:00Z',\n    updatedAt: '2024-01-15T10:00:00Z'\n  }\n];\n\n// Mock sync status\nconst MOCK_SYNC_STATUS = {\n  isRunning: false,\n  lastSync: '2024-05-25T14:30:00Z',\n  nextSync: '2024-05-26T14:30:00Z',\n  totalFixtures: 1250,\n  syncedToday: 45,\n  errors: 2,\n  status: 'success' as const\n};\n\nexport default function FixturesPage() {\n  const router = useRouter();\n  const [queryParams, setQueryParams] = useState<FootballQueries.FixtureQueryParams>({\n    page: 1,\n    limit: 10,\n    sortBy: 'date',\n    sortOrder: 'desc'\n  });\n  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);\n  const [editingFixture, setEditingFixture] = useState<FootballQueries.Fixture | null>(null);\n\n  // For development, use mock data\n  const fixturesQuery = {\n    data: {\n      data: MOCK_FIXTURES,\n      total: MOCK_FIXTURES.length,\n      page: 1,\n      limit: 10,\n      totalPages: 1\n    },\n    isLoading: false,\n    error: null,\n    refetch: () => Promise.resolve()\n  };\n\n  const syncStatusQuery = {\n    data: MOCK_SYNC_STATUS,\n    isLoading: false,\n    error: null,\n    refetch: () => Promise.resolve()\n  };\n\n  const { data: leaguesData } = useLeagues({ limit: 100 });\n  const { data: teamsData } = useTeams({ limit: 200 });\n  const createFixture = useCreateFixture();\n  const updateFixture = useUpdateFixture();\n  const deleteFixture = useDeleteFixture();\n  const dailySync = useDailySync();\n\n  // Statistics calculation\n  const statistics = React.useMemo(() => {\n    const fixtures = MOCK_FIXTURES;\n    return {\n      total: fixtures.length,\n      scheduled: fixtures.filter(f => f.status === 'scheduled').length,\n      live: fixtures.filter(f => f.status === 'live').length,\n      finished: fixtures.filter(f => f.status === 'finished').length,\n      postponed: fixtures.filter(f => f.status === 'postponed').length,\n      cancelled: fixtures.filter(f => f.status === 'cancelled').length,\n      withScores: fixtures.filter(f => f.homeScore !== null && f.awayScore !== null).length\n    };\n  }, []);\n\n  // Handle search\n  const handleSearch = (value: string) => {\n    setQueryParams(prev => ({ ...prev, query: value, page: 1 }));\n  };\n\n  // Handle filter change\n  const handleFilterChange = (key: keyof FootballQueries.FixtureQueryParams, value: any) => {\n    setQueryParams(prev => ({ ...prev, [key]: value, page: 1 }));\n  };\n\n  // Handle date range change\n  const handleDateRangeChange = (dates: any) => {\n    if (dates && dates.length === 2) {\n      setQueryParams(prev => ({\n        ...prev,\n        dateFrom: dates[0].format('YYYY-MM-DD'),\n        dateTo: dates[1].format('YYYY-MM-DD'),\n        page: 1\n      }));\n    } else {\n      setQueryParams(prev => ({\n        ...prev,\n        dateFrom: undefined,\n        dateTo: undefined,\n        page: 1\n      }));\n    }\n  };\n\n  // Handle create fixture\n  const handleCreateFixture = async (values: FootballQueries.CreateFixtureRequest) => {\n    try {\n      await createFixture.mutateAsync(values);\n      message.success('Fixture created successfully');\n      setIsCreateModalOpen(false);\n    } catch (error) {\n      message.error('Failed to create fixture');\n    }\n  };\n\n  // Handle update fixture\n  const handleUpdateFixture = async (values: FootballQueries.UpdateFixtureRequest) => {\n    if (!editingFixture) return;\n\n    try {\n      await updateFixture.mutateAsync({ externalId: editingFixture.externalId, data: values });\n      message.success('Fixture updated successfully');\n      setEditingFixture(null);\n    } catch (error) {\n      message.error('Failed to update fixture');\n    }\n  };\n\n  // Handle delete\n  const handleDelete = async (externalId: string) => {\n    try {\n      await deleteFixture.mutateAsync(externalId);\n      message.success('Fixture deleted successfully');\n    } catch (error) {\n      message.error('Failed to delete fixture');\n    }\n  };\n\n  // Handle daily sync\n  const handleDailySync = async () => {\n    try {\n      await dailySync.mutateAsync();\n      message.success('Daily sync started successfully');\n      syncStatusQuery.refetch();\n    } catch (error) {\n      message.error('Failed to start daily sync');\n    }\n  };\n\n  // Handle table change\n  const handleTableChange = (pagination: any, filters: any, sorter: any) => {\n    setQueryParams(prev => ({\n      ...prev,\n      page: pagination.current,\n      limit: pagination.pageSize,\n      sortBy: sorter.field || 'date',\n      sortOrder: sorter.order === 'ascend' ? 'asc' : 'desc'\n    }));\n  };\n\n  // Get status color and icon\n  const getStatusDisplay = (status: string) => {\n    const statusConfig = {\n      scheduled: { color: 'blue', icon: <ClockCircleOutlined />, text: 'Scheduled' },\n      live: { color: 'red', icon: <PlayCircleOutlined />, text: 'Live' },\n      finished: { color: 'green', icon: <CheckCircleOutlined />, text: 'Finished' },\n      postponed: { color: 'orange', icon: <WarningOutlined />, text: 'Postponed' },\n      cancelled: { color: 'gray', icon: <StopOutlined />, text: 'Cancelled' },\n      suspended: { color: 'purple', icon: <WarningOutlined />, text: 'Suspended' }\n    };\n\n    return statusConfig[status as keyof typeof statusConfig] || statusConfig.scheduled;\n  };\n\n  // Table columns\n  const columns: ColumnsType<FootballQueries.Fixture> = [\n    {\n      title: 'Match',\n      key: 'match',\n      render: (_, record) => (\n        <div className=\"min-w-[200px]\">\n          <div className=\"flex items-center justify-between mb-2\">\n            <div className=\"flex items-center gap-2\">\n              {record.homeTeam.logo ? (\n                <Avatar src={record.homeTeam.logo} size={24} />\n              ) : (\n                <Avatar size={24} icon={<TeamOutlined />} />\n              )}\n              <Text strong>{record.homeTeam.name}</Text>\n            </div>\n            <div className=\"text-center min-w-[40px]\">\n              {record.homeScore !== null && record.awayScore !== null ? (\n                <Text strong className=\"text-lg\">\n                  {record.homeScore} - {record.awayScore}\n                </Text>\n              ) : (\n                <Text type=\"secondary\">vs</Text>\n              )}\n            </div>\n          </div>\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center gap-2\">\n              {record.awayTeam.logo ? (\n                <Avatar src={record.awayTeam.logo} size={24} />\n              ) : (\n                <Avatar size={24} icon={<TeamOutlined />} />\n              )}\n              <Text strong>{record.awayTeam.name}</Text>\n            </div>\n          </div>\n        </div>\n      ),\n      width: 250,\n    },\n    {\n      title: 'League',\n      key: 'league',\n      render: (_, record) => (\n        <div>\n          <Text strong className=\"block\">{record.league.name}</Text>\n          <Text type=\"secondary\" className=\"text-sm\">\n            {record.league.country}\n          </Text>\n        </div>\n      ),\n      width: 150,\n    },\n    {\n      title: 'Date & Time',\n      dataIndex: 'date',\n      key: 'date',\n      render: (date: string) => (\n        <div>\n          <Text strong className=\"block\">\n            {dayjs(date).format('MMM DD, YYYY')}\n          </Text>\n          <Text type=\"secondary\" className=\"text-sm\">\n            {dayjs(date).format('HH:mm')}\n          </Text>\n        </div>\n      ),\n      width: 120,\n      sorter: true,\n    },\n    {\n      title: 'Status',\n      dataIndex: 'status',\n      key: 'status',\n      render: (status: string) => {\n        const statusDisplay = getStatusDisplay(status);\n        return (\n          <Tag color={statusDisplay.color} icon={statusDisplay.icon}>\n            {statusDisplay.text}\n          </Tag>\n        );\n      },\n      width: 100,\n      filters: [\n        { text: 'Scheduled', value: 'scheduled' },\n        { text: 'Live', value: 'live' },\n        { text: 'Finished', value: 'finished' },\n        { text: 'Postponed', value: 'postponed' },\n        { text: 'Cancelled', value: 'cancelled' },\n      ],\n    },\n    {\n      title: 'Venue & Round',\n      key: 'details',\n      render: (_, record) => (\n        <div>\n          {record.venue && (\n            <Text className=\"block text-sm\">\n              📍 {record.venue}\n            </Text>\n          )}\n          {record.round && (\n            <Text type=\"secondary\" className=\"text-sm\">\n              🏆 {record.round}\n            </Text>\n          )}\n        </div>\n      ),\n      width: 150,\n    },\n    {\n      title: 'Actions',\n      key: 'actions',\n      render: (_, record) => {\n        const menuItems: MenuProps['items'] = [\n          {\n            key: 'view',\n            label: 'View Details',\n            icon: <EyeOutlined />,\n            onClick: () => router.push(`/football/fixtures/${record.externalId}`)\n          },\n          {\n            key: 'edit',\n            label: 'Edit',\n            icon: <EditOutlined />,\n            onClick: () => setEditingFixture(record)\n          },\n          {\n            key: 'broadcast',\n            label: 'Manage Broadcast Links',\n            icon: <GlobalOutlined />,\n            onClick: () => router.push(`/broadcast-links?fixtureId=${record.externalId}`)\n          },\n          {\n            type: 'divider'\n          },\n          {\n            key: 'delete',\n            label: 'Delete',\n            icon: <DeleteOutlined />,\n            danger: true,\n            onClick: () => handleDelete(record.externalId)\n          }\n        ];\n\n        return (\n          <Dropdown menu={{ items: menuItems }} trigger={['click']}>\n            <Button icon={<MoreOutlined />} />\n          </Dropdown>\n        );\n      },\n      width: 80,\n      fixed: 'right',\n    },\n  ];\n\n  return (\n    <div>\n      {/* Page Header */}\n      <div className=\"mb-6\">\n        <Title level={2}>\n          <CalendarOutlined className=\"mr-2\" />\n          Football Fixtures Management\n        </Title>\n        <Text type=\"secondary\">\n          Manage football fixtures with comprehensive CRUD operations and sync management\n        </Text>\n      </div>\n\n      {/* Sync Status Alert */}\n      {syncStatusQuery.data && (\n        <Alert\n          message={\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center gap-2\">\n                <SyncOutlined spin={syncStatusQuery.data.isRunning} />\n                <span>\n                  Sync Status: {syncStatusQuery.data.isRunning ? 'Running' : 'Idle'}\n                </span>\n              </div>\n              <div className=\"flex items-center gap-4 text-sm\">\n                <span>Last Sync: {dayjs(syncStatusQuery.data.lastSync).format('MMM DD, HH:mm')}</span>\n                <span>Synced Today: {syncStatusQuery.data.syncedToday}</span>\n                {syncStatusQuery.data.errors > 0 && (\n                  <span className=\"text-red-500\">Errors: {syncStatusQuery.data.errors}</span>\n                )}\n              </div>\n            </div>\n          }\n          type={syncStatusQuery.data.status === 'success' ? 'success' : 'warning'}\n          showIcon\n          className=\"mb-6\"\n          action={\n            <Button\n              size=\"small\"\n              type=\"primary\"\n              icon={<SyncOutlined />}\n              loading={dailySync.isPending}\n              onClick={handleDailySync}\n              disabled={syncStatusQuery.data.isRunning}\n            >\n              Start Sync\n            </Button>\n          }\n        />\n      )}\n\n      {/* Statistics Cards */}\n      <Row gutter={16} className=\"mb-6\">\n        <Col xs={12} sm={8} md={4}>\n          <Card>\n            <Statistic\n              title=\"Total Fixtures\"\n              value={statistics.total}\n              prefix={<CalendarOutlined />}\n            />\n          </Card>\n        </Col>\n        <Col xs={12} sm={8} md={4}>\n          <Card>\n            <Statistic\n              title=\"Scheduled\"\n              value={statistics.scheduled}\n              prefix={<ClockCircleOutlined />}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={12} sm={8} md={4}>\n          <Card>\n            <Statistic\n              title=\"Live\"\n              value={statistics.live}\n              prefix={<PlayCircleOutlined />}\n              valueStyle={{ color: '#ff4d4f' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={12} sm={8} md={4}>\n          <Card>\n            <Statistic\n              title=\"Finished\"\n              value={statistics.finished}\n              prefix={<CheckCircleOutlined />}\n              valueStyle={{ color: '#52c41a' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={12} sm={8} md={4}>\n          <Card>\n            <Statistic\n              title=\"Postponed\"\n              value={statistics.postponed}\n              prefix={<WarningOutlined />}\n              valueStyle={{ color: '#faad14' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={12} sm={8} md={4}>\n          <Card>\n            <Statistic\n              title=\"With Scores\"\n              value={statistics.withScores}\n              suffix={`/ ${statistics.total}`}\n              prefix={<TrophyOutlined />}\n              valueStyle={{ color: '#722ed1' }}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      {/* Advanced Filters and Actions */}\n      <Card className=\"mb-4\">\n        <Row gutter={16} align=\"middle\">\n          <Col xs={24} sm={8} md={6}>\n            <Input\n              placeholder=\"Search fixtures...\"\n              prefix={<SearchOutlined />}\n              onChange={(e) => handleSearch(e.target.value)}\n              allowClear\n            />\n          </Col>\n          <Col xs={12} sm={6} md={4}>\n            <Select\n              placeholder=\"League\"\n              allowClear\n              onChange={(value) => handleFilterChange('leagueId', value)}\n              className=\"w-full\"\n            >\n              {leaguesData?.data?.map(league => (\n                <Option key={league.id} value={league.id}>\n                  {league.name}\n                </Option>\n              ))}\n            </Select>\n          </Col>\n          <Col xs={12} sm={6} md={4}>\n            <Select\n              placeholder=\"Status\"\n              allowClear\n              onChange={(value) => handleFilterChange('status', value)}\n              className=\"w-full\"\n            >\n              <Option value=\"scheduled\">Scheduled</Option>\n              <Option value=\"live\">Live</Option>\n              <Option value=\"finished\">Finished</Option>\n              <Option value=\"postponed\">Postponed</Option>\n              <Option value=\"cancelled\">Cancelled</Option>\n            </Select>\n          </Col>\n          <Col xs={24} sm={8} md={6}>\n            <RangePicker\n              placeholder={['Start Date', 'End Date']}\n              onChange={handleDateRangeChange}\n              style={{ width: '100%' }}\n            />\n          </Col>\n          <Col xs={24} sm={16} md={4} className=\"text-right\">\n            <Space>\n              <Button\n                icon={<ReloadOutlined />}\n                onClick={() => fixturesQuery.refetch()}\n                loading={fixturesQuery.isLoading}\n              >\n                Refresh\n              </Button>\n              <Button\n                icon={<ExportOutlined />}\n                onClick={() => message.info('Export functionality coming soon')}\n              >\n                Export\n              </Button>\n              <Button\n                type=\"primary\"\n                icon={<PlusOutlined />}\n                onClick={() => setIsCreateModalOpen(true)}\n              >\n                Add Fixture\n              </Button>\n            </Space>\n          </Col>\n        </Row>\n      </Card>\n\n      {/* Fixtures Table */}\n      <Card>\n        <Table\n          columns={columns}\n          dataSource={fixturesQuery.data?.data || []}\n          rowKey=\"externalId\"\n          loading={fixturesQuery.isLoading}\n          pagination={{\n            current: queryParams.page,\n            pageSize: queryParams.limit,\n            total: fixturesQuery.data?.total || 0,\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total, range) =>\n              `${range[0]}-${range[1]} of ${total} fixtures`,\n          }}\n          onChange={handleTableChange}\n          scroll={{ x: 1400 }}\n          rowClassName={(record) => {\n            if (record.status === 'live') return 'bg-red-50';\n            if (record.status === 'finished') return 'bg-green-50';\n            return '';\n          }}\n        />\n      </Card>\n\n      {/* Create Fixture Modal */}\n      <Modal\n        title=\"Create New Fixture\"\n        open={isCreateModalOpen}\n        onCancel={() => setIsCreateModalOpen(false)}\n        footer={null}\n        width={900}\n        destroyOnClose\n      >\n        <FixtureForm\n          mode=\"create\"\n          onSubmit={handleCreateFixture}\n          onCancel={() => setIsCreateModalOpen(false)}\n          loading={createFixture.isPending}\n        />\n      </Modal>\n\n      {/* Edit Fixture Modal */}\n      <Modal\n        title=\"Edit Fixture\"\n        open={!!editingFixture}\n        onCancel={() => setEditingFixture(null)}\n        footer={null}\n        width={900}\n        destroyOnClose\n      >\n        {editingFixture && (\n          <FixtureForm\n            mode=\"edit\"\n            initialValues={editingFixture}\n            onSubmit={handleUpdateFixture}\n            onCancel={() => setEditingFixture(null)}\n            loading={updateFixture.isPending}\n          />\n        )}\n      </Modal>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAID;AA+CA;AAGA;AAUA;AACA;AA5DA;AAAA;AAAA;AAAA;AAyBA;AAAA;AAAA;AAAA;AAAA;AAzBA;AAyBA;AAzBA;AAyBA;AAAA;AAAA;AAAA;AAzBA;AAAA;AAyBA;AAAA;AAzBA;AAyBA;AAzBA;AAAA;AAAA;AAAA;AAyBA;AAzBA;AAyBA;AAzBA;AAyBA;AAAA;AAAA;AAzBA;AAAA;AA2DA;AA9DA;;;;;;;;;AAiEA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,0LAAA,CAAA,aAAU;AAClC,MAAM,EAAE,MAAM,EAAE,GAAG,kLAAA,CAAA,SAAM;AACzB,MAAM,EAAE,WAAW,EAAE,GAAG,8LAAA,CAAA,aAAU;AAElC,4BAA4B;AAC5B,MAAM,gBAA2C;IAC/C;QACE,YAAY;QACZ,UAAU;YAAE,IAAI;YAAK,MAAM;YAAqB,MAAM;QAAgF;QACtI,UAAU;YAAE,IAAI;YAAK,MAAM;YAAa,MAAM;QAAwE;QACtH,QAAQ;YAAE,IAAI;YAAK,MAAM;YAAkB,SAAS;YAAW,QAAQ;YAAW,UAAU;YAAM,WAAW;YAAI,WAAW;QAAG;QAC/H,MAAM;QACN,QAAQ;QACR,OAAO;QACP,OAAO;QACP,WAAW;QACX,WAAW;QACX,WAAW;QACX,WAAW;IACb;IACA;QACE,YAAY;QACZ,UAAU;YAAE,IAAI;YAAK,MAAM;YAAe,MAAM;QAA0E;QAC1H,UAAU;YAAE,IAAI;YAAK,MAAM;YAAa,MAAM;QAAwE;QACtH,QAAQ;YAAE,IAAI;YAAK,MAAM;YAAW,SAAS;YAAS,QAAQ;YAAW,UAAU;YAAM,WAAW;YAAI,WAAW;QAAG;QACtH,MAAM;QACN,QAAQ;QACR,OAAO;QACP,OAAO;QACP,WAAW;QACX,WAAW;QACX,WAAW;QACX,WAAW;IACb;IACA;QACE,YAAY;QACZ,UAAU;YAAE,IAAI;YAAK,MAAM;YAAiB,MAAM;QAA4E;QAC9H,UAAU;YAAE,IAAI;YAAK,MAAM;YAAqB,MAAM;QAAgF;QACtI,QAAQ;YAAE,IAAI;YAAK,MAAM;YAAc,SAAS;YAAW,QAAQ;YAAW,UAAU;YAAM,WAAW;YAAI,WAAW;QAAG;QAC3H,MAAM;QACN,QAAQ;QACR,OAAO;QACP,OAAO;QACP,WAAW;QACX,WAAW;QACX,WAAW;QACX,WAAW;IACb;IACA;QACE,YAAY;QACZ,UAAU;YAAE,IAAI;YAAK,MAAM;QAAW;QACtC,UAAU;YAAE,IAAI;YAAK,MAAM;QAAc;QACzC,QAAQ;YAAE,IAAI;YAAK,MAAM;YAAW,SAAS;YAAS,QAAQ;YAAW,UAAU;YAAM,WAAW;YAAI,WAAW;QAAG;QACtH,MAAM;QACN,QAAQ;QACR,OAAO;QACP,OAAO;QACP,WAAW;QACX,WAAW;QACX,WAAW;QACX,WAAW;IACb;IACA;QACE,YAAY;QACZ,UAAU;YAAE,IAAI;YAAK,MAAM;QAAsB;QACjD,UAAU;YAAE,IAAI;YAAM,MAAM;QAAsB;QAClD,QAAQ;YAAE,IAAI;YAAK,MAAM;YAAW,SAAS;YAAU,QAAQ;YAAW,UAAU;YAAO,WAAW;YAAI,WAAW;QAAG;QACxH,MAAM;QACN,QAAQ;QACR,OAAO;QACP,OAAO;QACP,WAAW;QACX,WAAW;QACX,WAAW;QACX,WAAW;IACb;CACD;AAED,mBAAmB;AACnB,MAAM,mBAAmB;IACvB,WAAW;IACX,UAAU;IACV,UAAU;IACV,eAAe;IACf,aAAa;IACb,QAAQ;IACR,QAAQ;AACV;AAEe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsC;QACjF,MAAM;QACN,OAAO;QACP,QAAQ;QACR,WAAW;IACb;IACA,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkC;IAErF,iCAAiC;IACjC,MAAM,gBAAgB;QACpB,MAAM;YACJ,MAAM;YACN,OAAO,cAAc,MAAM;YAC3B,MAAM;YACN,OAAO;YACP,YAAY;QACd;QACA,WAAW;QACX,OAAO;QACP,SAAS,IAAM,QAAQ,OAAO;IAChC;IAEA,MAAM,kBAAkB;QACtB,MAAM;QACN,WAAW;QACX,OAAO;QACP,SAAS,IAAM,QAAQ,OAAO;IAChC;IAEA,MAAM,EAAE,MAAM,WAAW,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,aAAU,AAAD,EAAE;QAAE,OAAO;IAAI;IACtD,MAAM,EAAE,MAAM,SAAS,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,OAAO;IAAI;IAClD,MAAM,gBAAgB,CAAA,GAAA,wIAAA,CAAA,mBAAgB,AAAD;IACrC,MAAM,gBAAgB,CAAA,GAAA,wIAAA,CAAA,mBAAgB,AAAD;IACrC,MAAM,gBAAgB,CAAA,GAAA,wIAAA,CAAA,mBAAgB,AAAD;IACrC,MAAM,YAAY,CAAA,GAAA,wIAAA,CAAA,eAAY,AAAD;IAE7B,yBAAyB;IACzB,MAAM,aAAa,qMAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QAC/B,MAAM,WAAW;QACjB,OAAO;YACL,OAAO,SAAS,MAAM;YACtB,WAAW,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,MAAM;YAChE,MAAM,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,QAAQ,MAAM;YACtD,UAAU,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,YAAY,MAAM;YAC9D,WAAW,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,MAAM;YAChE,WAAW,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,MAAM;YAChE,YAAY,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,SAAS,KAAK,QAAQ,EAAE,SAAS,KAAK,MAAM,MAAM;QACvF;IACF,GAAG,EAAE;IAEL,gBAAgB;IAChB,MAAM,eAAe,CAAC;QACpB,eAAe,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,OAAO;gBAAO,MAAM;YAAE,CAAC;IAC5D;IAEA,uBAAuB;IACvB,MAAM,qBAAqB,CAAC,KAA+C;QACzE,eAAe,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,IAAI,EAAE;gBAAO,MAAM;YAAE,CAAC;IAC5D;IAEA,2BAA2B;IAC3B,MAAM,wBAAwB,CAAC;QAC7B,IAAI,SAAS,MAAM,MAAM,KAAK,GAAG;YAC/B,eAAe,CAAA,OAAQ,CAAC;oBACtB,GAAG,IAAI;oBACP,UAAU,KAAK,CAAC,EAAE,CAAC,MAAM,CAAC;oBAC1B,QAAQ,KAAK,CAAC,EAAE,CAAC,MAAM,CAAC;oBACxB,MAAM;gBACR,CAAC;QACH,OAAO;YACL,eAAe,CAAA,OAAQ,CAAC;oBACtB,GAAG,IAAI;oBACP,UAAU;oBACV,QAAQ;oBACR,MAAM;gBACR,CAAC;QACH;IACF;IAEA,wBAAwB;IACxB,MAAM,sBAAsB,OAAO;QACjC,IAAI;YACF,MAAM,cAAc,WAAW,CAAC;YAChC,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;YAChB,qBAAqB;QACvB,EAAE,OAAO,OAAO;YACd,oLAAA,CAAA,UAAO,CAAC,KAAK,CAAC;QAChB;IACF;IAEA,wBAAwB;IACxB,MAAM,sBAAsB,OAAO;QACjC,IAAI,CAAC,gBAAgB;QAErB,IAAI;YACF,MAAM,cAAc,WAAW,CAAC;gBAAE,YAAY,eAAe,UAAU;gBAAE,MAAM;YAAO;YACtF,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;YAChB,kBAAkB;QACpB,EAAE,OAAO,OAAO;YACd,oLAAA,CAAA,UAAO,CAAC,KAAK,CAAC;QAChB;IACF;IAEA,gBAAgB;IAChB,MAAM,eAAe,OAAO;QAC1B,IAAI;YACF,MAAM,cAAc,WAAW,CAAC;YAChC,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;QAClB,EAAE,OAAO,OAAO;YACd,oLAAA,CAAA,UAAO,CAAC,KAAK,CAAC;QAChB;IACF;IAEA,oBAAoB;IACpB,MAAM,kBAAkB;QACtB,IAAI;YACF,MAAM,UAAU,WAAW;YAC3B,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;YAChB,gBAAgB,OAAO;QACzB,EAAE,OAAO,OAAO;YACd,oLAAA,CAAA,UAAO,CAAC,KAAK,CAAC;QAChB;IACF;IAEA,sBAAsB;IACtB,MAAM,oBAAoB,CAAC,YAAiB,SAAc;QACxD,eAAe,CAAA,OAAQ,CAAC;gBACtB,GAAG,IAAI;gBACP,MAAM,WAAW,OAAO;gBACxB,OAAO,WAAW,QAAQ;gBAC1B,QAAQ,OAAO,KAAK,IAAI;gBACxB,WAAW,OAAO,KAAK,KAAK,WAAW,QAAQ;YACjD,CAAC;IACH;IAEA,4BAA4B;IAC5B,MAAM,mBAAmB,CAAC;QACxB,MAAM,eAAe;YACnB,WAAW;gBAAE,OAAO;gBAAQ,oBAAM,8OAAC,gOAAA,CAAA,sBAAmB;;;;;gBAAK,MAAM;YAAY;YAC7E,MAAM;gBAAE,OAAO;gBAAO,oBAAM,8OAAC,8NAAA,CAAA,qBAAkB;;;;;gBAAK,MAAM;YAAO;YACjE,UAAU;gBAAE,OAAO;gBAAS,oBAAM,8OAAC,gOAAA,CAAA,sBAAmB;;;;;gBAAK,MAAM;YAAW;YAC5E,WAAW;gBAAE,OAAO;gBAAU,oBAAM,8OAAC,wNAAA,CAAA,kBAAe;;;;;gBAAK,MAAM;YAAY;YAC3E,WAAW;gBAAE,OAAO;gBAAQ,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;gBAAK,MAAM;YAAY;YACtE,WAAW;gBAAE,OAAO;gBAAU,oBAAM,8OAAC,wNAAA,CAAA,kBAAe;;;;;gBAAK,MAAM;YAAY;QAC7E;QAEA,OAAO,YAAY,CAAC,OAAoC,IAAI,aAAa,SAAS;IACpF;IAEA,gBAAgB;IAChB,MAAM,UAAgD;QACpD;YACE,OAAO;YACP,KAAK;YACL,QAAQ,CAAC,GAAG,uBACV,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;wCACZ,OAAO,QAAQ,CAAC,IAAI,iBACnB,8OAAC,kLAAA,CAAA,SAAM;4CAAC,KAAK,OAAO,QAAQ,CAAC,IAAI;4CAAE,MAAM;;;;;iEAEzC,8OAAC,kLAAA,CAAA,SAAM;4CAAC,MAAM;4CAAI,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;;;;;;sDAEvC,8OAAC;4CAAK,MAAM;sDAAE,OAAO,QAAQ,CAAC,IAAI;;;;;;;;;;;;8CAEpC,8OAAC;oCAAI,WAAU;8CACZ,OAAO,SAAS,KAAK,QAAQ,OAAO,SAAS,KAAK,qBACjD,8OAAC;wCAAK,MAAM;wCAAC,WAAU;;4CACpB,OAAO,SAAS;4CAAC;4CAAI,OAAO,SAAS;;;;;;6DAGxC,8OAAC;wCAAK,MAAK;kDAAY;;;;;;;;;;;;;;;;;sCAI7B,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;oCACZ,OAAO,QAAQ,CAAC,IAAI,iBACnB,8OAAC,kLAAA,CAAA,SAAM;wCAAC,KAAK,OAAO,QAAQ,CAAC,IAAI;wCAAE,MAAM;;;;;6DAEzC,8OAAC,kLAAA,CAAA,SAAM;wCAAC,MAAM;wCAAI,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;;;;;;kDAEvC,8OAAC;wCAAK,MAAM;kDAAE,OAAO,QAAQ,CAAC,IAAI;;;;;;;;;;;;;;;;;;;;;;;YAK1C,OAAO;QACT;QACA;YACE,OAAO;YACP,KAAK;YACL,QAAQ,CAAC,GAAG,uBACV,8OAAC;;sCACC,8OAAC;4BAAK,MAAM;4BAAC,WAAU;sCAAS,OAAO,MAAM,CAAC,IAAI;;;;;;sCAClD,8OAAC;4BAAK,MAAK;4BAAY,WAAU;sCAC9B,OAAO,MAAM,CAAC,OAAO;;;;;;;;;;;;YAI5B,OAAO;QACT;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,QAAQ,CAAC,qBACP,8OAAC;;sCACC,8OAAC;4BAAK,MAAM;4BAAC,WAAU;sCACpB,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD,EAAE,MAAM,MAAM,CAAC;;;;;;sCAEtB,8OAAC;4BAAK,MAAK;4BAAY,WAAU;sCAC9B,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD,EAAE,MAAM,MAAM,CAAC;;;;;;;;;;;;YAI1B,OAAO;YACP,QAAQ;QACV;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,QAAQ,CAAC;gBACP,MAAM,gBAAgB,iBAAiB;gBACvC,qBACE,8OAAC,4KAAA,CAAA,MAAG;oBAAC,OAAO,cAAc,KAAK;oBAAE,MAAM,cAAc,IAAI;8BACtD,cAAc,IAAI;;;;;;YAGzB;YACA,OAAO;YACP,SAAS;gBACP;oBAAE,MAAM;oBAAa,OAAO;gBAAY;gBACxC;oBAAE,MAAM;oBAAQ,OAAO;gBAAO;gBAC9B;oBAAE,MAAM;oBAAY,OAAO;gBAAW;gBACtC;oBAAE,MAAM;oBAAa,OAAO;gBAAY;gBACxC;oBAAE,MAAM;oBAAa,OAAO;gBAAY;aACzC;QACH;QACA;YACE,OAAO;YACP,KAAK;YACL,QAAQ,CAAC,GAAG,uBACV,8OAAC;;wBACE,OAAO,KAAK,kBACX,8OAAC;4BAAK,WAAU;;gCAAgB;gCAC1B,OAAO,KAAK;;;;;;;wBAGnB,OAAO,KAAK,kBACX,8OAAC;4BAAK,MAAK;4BAAY,WAAU;;gCAAU;gCACrC,OAAO,KAAK;;;;;;;;;;;;;YAKxB,OAAO;QACT;QACA;YACE,OAAO;YACP,KAAK;YACL,QAAQ,CAAC,GAAG;gBACV,MAAM,YAAgC;oBACpC;wBACE,KAAK;wBACL,OAAO;wBACP,oBAAM,8OAAC,gNAAA,CAAA,cAAW;;;;;wBAClB,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,mBAAmB,EAAE,OAAO,UAAU,EAAE;oBACtE;oBACA;wBACE,KAAK;wBACL,OAAO;wBACP,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;wBACnB,SAAS,IAAM,kBAAkB;oBACnC;oBACA;wBACE,KAAK;wBACL,OAAO;wBACP,oBAAM,8OAAC,sNAAA,CAAA,iBAAc;;;;;wBACrB,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,2BAA2B,EAAE,OAAO,UAAU,EAAE;oBAC9E;oBACA;wBACE,MAAM;oBACR;oBACA;wBACE,KAAK;wBACL,OAAO;wBACP,oBAAM,8OAAC,sNAAA,CAAA,iBAAc;;;;;wBACrB,QAAQ;wBACR,SAAS,IAAM,aAAa,OAAO,UAAU;oBAC/C;iBACD;gBAED,qBACE,8OAAC,sLAAA,CAAA,WAAQ;oBAAC,MAAM;wBAAE,OAAO;oBAAU;oBAAG,SAAS;wBAAC;qBAAQ;8BACtD,cAAA,8OAAC,kMAAA,CAAA,SAAM;wBAAC,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;;;;;;;;;;;YAGjC;YACA,OAAO;YACP,OAAO;QACT;KACD;IAED,qBACE,8OAAC;;0BAEC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAM,OAAO;;0CACZ,8OAAC,0NAAA,CAAA,mBAAgB;gCAAC,WAAU;;;;;;4BAAS;;;;;;;kCAGvC,8OAAC;wBAAK,MAAK;kCAAY;;;;;;;;;;;;YAMxB,gBAAgB,IAAI,kBACnB,8OAAC,gLAAA,CAAA,QAAK;gBACJ,uBACE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kNAAA,CAAA,eAAY;oCAAC,MAAM,gBAAgB,IAAI,CAAC,SAAS;;;;;;8CAClD,8OAAC;;wCAAK;wCACU,gBAAgB,IAAI,CAAC,SAAS,GAAG,YAAY;;;;;;;;;;;;;sCAG/D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;wCAAK;wCAAY,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD,EAAE,gBAAgB,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC;;;;;;;8CAC9D,8OAAC;;wCAAK;wCAAe,gBAAgB,IAAI,CAAC,WAAW;;;;;;;gCACpD,gBAAgB,IAAI,CAAC,MAAM,GAAG,mBAC7B,8OAAC;oCAAK,WAAU;;wCAAe;wCAAS,gBAAgB,IAAI,CAAC,MAAM;;;;;;;;;;;;;;;;;;;gBAK3E,MAAM,gBAAgB,IAAI,CAAC,MAAM,KAAK,YAAY,YAAY;gBAC9D,QAAQ;gBACR,WAAU;gBACV,sBACE,8OAAC,kMAAA,CAAA,SAAM;oBACL,MAAK;oBACL,MAAK;oBACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;oBACnB,SAAS,UAAU,SAAS;oBAC5B,SAAS;oBACT,UAAU,gBAAgB,IAAI,CAAC,SAAS;8BACzC;;;;;;;;;;;0BAQP,8OAAC,4KAAA,CAAA,MAAG;gBAAC,QAAQ;gBAAI,WAAU;;kCACzB,8OAAC,4KAAA,CAAA,MAAG;wBAAC,IAAI;wBAAI,IAAI;wBAAG,IAAI;kCACtB,cAAA,8OAAC,8KAAA,CAAA,OAAI;sCACH,cAAA,8OAAC,wLAAA,CAAA,YAAS;gCACR,OAAM;gCACN,OAAO,WAAW,KAAK;gCACvB,sBAAQ,8OAAC,0NAAA,CAAA,mBAAgB;;;;;;;;;;;;;;;;;;;;kCAI/B,8OAAC,4KAAA,CAAA,MAAG;wBAAC,IAAI;wBAAI,IAAI;wBAAG,IAAI;kCACtB,cAAA,8OAAC,8KAAA,CAAA,OAAI;sCACH,cAAA,8OAAC,wLAAA,CAAA,YAAS;gCACR,OAAM;gCACN,OAAO,WAAW,SAAS;gCAC3B,sBAAQ,8OAAC,gOAAA,CAAA,sBAAmB;;;;;gCAC5B,YAAY;oCAAE,OAAO;gCAAU;;;;;;;;;;;;;;;;kCAIrC,8OAAC,4KAAA,CAAA,MAAG;wBAAC,IAAI;wBAAI,IAAI;wBAAG,IAAI;kCACtB,cAAA,8OAAC,8KAAA,CAAA,OAAI;sCACH,cAAA,8OAAC,wLAAA,CAAA,YAAS;gCACR,OAAM;gCACN,OAAO,WAAW,IAAI;gCACtB,sBAAQ,8OAAC,8NAAA,CAAA,qBAAkB;;;;;gCAC3B,YAAY;oCAAE,OAAO;gCAAU;;;;;;;;;;;;;;;;kCAIrC,8OAAC,4KAAA,CAAA,MAAG;wBAAC,IAAI;wBAAI,IAAI;wBAAG,IAAI;kCACtB,cAAA,8OAAC,8KAAA,CAAA,OAAI;sCACH,cAAA,8OAAC,wLAAA,CAAA,YAAS;gCACR,OAAM;gCACN,OAAO,WAAW,QAAQ;gCAC1B,sBAAQ,8OAAC,gOAAA,CAAA,sBAAmB;;;;;gCAC5B,YAAY;oCAAE,OAAO;gCAAU;;;;;;;;;;;;;;;;kCAIrC,8OAAC,4KAAA,CAAA,MAAG;wBAAC,IAAI;wBAAI,IAAI;wBAAG,IAAI;kCACtB,cAAA,8OAAC,8KAAA,CAAA,OAAI;sCACH,cAAA,8OAAC,wLAAA,CAAA,YAAS;gCACR,OAAM;gCACN,OAAO,WAAW,SAAS;gCAC3B,sBAAQ,8OAAC,wNAAA,CAAA,kBAAe;;;;;gCACxB,YAAY;oCAAE,OAAO;gCAAU;;;;;;;;;;;;;;;;kCAIrC,8OAAC,4KAAA,CAAA,MAAG;wBAAC,IAAI;wBAAI,IAAI;wBAAG,IAAI;kCACtB,cAAA,8OAAC,8KAAA,CAAA,OAAI;sCACH,cAAA,8OAAC,wLAAA,CAAA,YAAS;gCACR,OAAM;gCACN,OAAO,WAAW,UAAU;gCAC5B,QAAQ,CAAC,EAAE,EAAE,WAAW,KAAK,EAAE;gCAC/B,sBAAQ,8OAAC,sNAAA,CAAA,iBAAc;;;;;gCACvB,YAAY;oCAAE,OAAO;gCAAU;;;;;;;;;;;;;;;;;;;;;;0BAOvC,8OAAC,8KAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,MAAG;oBAAC,QAAQ;oBAAI,OAAM;;sCACrB,8OAAC,4KAAA,CAAA,MAAG;4BAAC,IAAI;4BAAI,IAAI;4BAAG,IAAI;sCACtB,cAAA,8OAAC,gLAAA,CAAA,QAAK;gCACJ,aAAY;gCACZ,sBAAQ,8OAAC,sNAAA,CAAA,iBAAc;;;;;gCACvB,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;gCAC5C,UAAU;;;;;;;;;;;sCAGd,8OAAC,4KAAA,CAAA,MAAG;4BAAC,IAAI;4BAAI,IAAI;4BAAG,IAAI;sCACtB,cAAA,8OAAC,kLAAA,CAAA,SAAM;gCACL,aAAY;gCACZ,UAAU;gCACV,UAAU,CAAC,QAAU,mBAAmB,YAAY;gCACpD,WAAU;0CAET,aAAa,MAAM,IAAI,CAAA,uBACtB,8OAAC;wCAAuB,OAAO,OAAO,EAAE;kDACrC,OAAO,IAAI;uCADD,OAAO,EAAE;;;;;;;;;;;;;;;sCAM5B,8OAAC,4KAAA,CAAA,MAAG;4BAAC,IAAI;4BAAI,IAAI;4BAAG,IAAI;sCACtB,cAAA,8OAAC,kLAAA,CAAA,SAAM;gCACL,aAAY;gCACZ,UAAU;gCACV,UAAU,CAAC,QAAU,mBAAmB,UAAU;gCAClD,WAAU;;kDAEV,8OAAC;wCAAO,OAAM;kDAAY;;;;;;kDAC1B,8OAAC;wCAAO,OAAM;kDAAO;;;;;;kDACrB,8OAAC;wCAAO,OAAM;kDAAW;;;;;;kDACzB,8OAAC;wCAAO,OAAM;kDAAY;;;;;;kDAC1B,8OAAC;wCAAO,OAAM;kDAAY;;;;;;;;;;;;;;;;;sCAG9B,8OAAC,4KAAA,CAAA,MAAG;4BAAC,IAAI;4BAAI,IAAI;4BAAG,IAAI;sCACtB,cAAA,8OAAC;gCACC,aAAa;oCAAC;oCAAc;iCAAW;gCACvC,UAAU;gCACV,OAAO;oCAAE,OAAO;gCAAO;;;;;;;;;;;sCAG3B,8OAAC,4KAAA,CAAA,MAAG;4BAAC,IAAI;4BAAI,IAAI;4BAAI,IAAI;4BAAG,WAAU;sCACpC,cAAA,8OAAC,gMAAA,CAAA,QAAK;;kDACJ,8OAAC,kMAAA,CAAA,SAAM;wCACL,oBAAM,8OAAC,sNAAA,CAAA,iBAAc;;;;;wCACrB,SAAS,IAAM,cAAc,OAAO;wCACpC,SAAS,cAAc,SAAS;kDACjC;;;;;;kDAGD,8OAAC,kMAAA,CAAA,SAAM;wCACL,oBAAM,8OAAC,sNAAA,CAAA,iBAAc;;;;;wCACrB,SAAS,IAAM,oLAAA,CAAA,UAAO,CAAC,IAAI,CAAC;kDAC7B;;;;;;kDAGD,8OAAC,kMAAA,CAAA,SAAM;wCACL,MAAK;wCACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;wCACnB,SAAS,IAAM,qBAAqB;kDACrC;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAST,8OAAC,8KAAA,CAAA,OAAI;0BACH,cAAA,8OAAC,gLAAA,CAAA,QAAK;oBACJ,SAAS;oBACT,YAAY,cAAc,IAAI,EAAE,QAAQ,EAAE;oBAC1C,QAAO;oBACP,SAAS,cAAc,SAAS;oBAChC,YAAY;wBACV,SAAS,YAAY,IAAI;wBACzB,UAAU,YAAY,KAAK;wBAC3B,OAAO,cAAc,IAAI,EAAE,SAAS;wBACpC,iBAAiB;wBACjB,iBAAiB;wBACjB,WAAW,CAAC,OAAO,QACjB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC,IAAI,EAAE,MAAM,SAAS,CAAC;oBAClD;oBACA,UAAU;oBACV,QAAQ;wBAAE,GAAG;oBAAK;oBAClB,cAAc,CAAC;wBACb,IAAI,OAAO,MAAM,KAAK,QAAQ,OAAO;wBACrC,IAAI,OAAO,MAAM,KAAK,YAAY,OAAO;wBACzC,OAAO;oBACT;;;;;;;;;;;0BAKJ,8OAAC,gLAAA,CAAA,QAAK;gBACJ,OAAM;gBACN,MAAM;gBACN,UAAU,IAAM,qBAAqB;gBACrC,QAAQ;gBACR,OAAO;gBACP,cAAc;0BAEd,cAAA,8OAAC,2LAAA,CAAA,cAAW;oBACV,MAAK;oBACL,UAAU;oBACV,UAAU,IAAM,qBAAqB;oBACrC,SAAS,cAAc,SAAS;;;;;;;;;;;0BAKpC,8OAAC,gLAAA,CAAA,QAAK;gBACJ,OAAM;gBACN,MAAM,CAAC,CAAC;gBACR,UAAU,IAAM,kBAAkB;gBAClC,QAAQ;gBACR,OAAO;gBACP,cAAc;0BAEb,gCACC,8OAAC,2LAAA,CAAA,cAAW;oBACV,MAAK;oBACL,eAAe;oBACf,UAAU;oBACV,UAAU,IAAM,kBAAkB;oBAClC,SAAS,cAAc,SAAS;;;;;;;;;;;;;;;;;AAM5C"}}, {"offset": {"line": 2978, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}