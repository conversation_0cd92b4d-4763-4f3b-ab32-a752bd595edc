{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/app/dashboard/page.tsx"], "sourcesContent": ["/**\n * Dashboard Page\n * Main dashboard with analytics, quick actions, and system overview\n */\n\n'use client';\n\nimport React from 'react';\nimport {\n  Card,\n  Row,\n  Col,\n  Typography,\n  Alert,\n  Space\n} from 'antd';\nimport {\n  DashboardOutlined\n} from '@ant-design/icons';\nimport { useRouter } from 'next/navigation';\nimport dayjs from 'dayjs';\nimport {\n  AnalyticsCards,\n  QuickActions,\n  RecentActivities,\n  SystemHealthCard,\n  UserStatsCard,\n  type AnalyticsData,\n  type Activity\n} from '@/components/analytics';\n\nconst { Title, Text, Paragraph } = Typography;\n\n// Mock data for dashboard\nconst DASHBOARD_STATS: AnalyticsData = {\n  leagues: { total: 15, active: 12, inactive: 3, growth: 8.5 },\n  teams: { total: 320, active: 298, inactive: 22, growth: 12.3 },\n  fixtures: { total: 1250, scheduled: 45, live: 3, finished: 1202, growth: 15.7 },\n  broadcastLinks: { total: 89, active: 76, inactive: 13, hd: 52, views: 125000, growth: 22.1 },\n  users: { total: 8, admin: 2, editor: 4, moderator: 2 },\n  sync: {\n    lastSync: '2024-05-25T18:30:00Z',\n    nextSync: '2024-05-26T06:00:00Z',\n    status: 'success' as const,\n    successRate: 96.5\n  }\n};\n\nconst RECENT_ACTIVITIES: Activity[] = [\n  {\n    id: 1,\n    type: 'fixture',\n    action: 'created',\n    title: 'Manchester United vs Liverpool',\n    description: 'Premier League fixture added',\n    user: 'admin',\n    time: '2024-05-25T18:45:00Z',\n    status: 'success'\n  },\n  {\n    id: 2,\n    type: 'broadcast',\n    action: 'created',\n    title: 'HD Stream for El Clasico',\n    description: 'Broadcast link added for Real Madrid vs Barcelona',\n    user: 'editor1',\n    time: '2024-05-25T18:30:00Z',\n    status: 'success'\n  },\n  {\n    id: 3,\n    type: 'sync',\n    action: 'completed',\n    title: 'Daily fixtures sync',\n    description: '45 fixtures synchronized successfully',\n    user: 'system',\n    time: '2024-05-25T18:00:00Z',\n    status: 'success'\n  },\n  {\n    id: 4,\n    type: 'team',\n    action: 'updated',\n    title: 'Real Madrid team info',\n    description: 'Team logo and squad updated',\n    user: 'editor2',\n    time: '2024-05-25T17:45:00Z',\n    status: 'success'\n  },\n  {\n    id: 5,\n    type: 'league',\n    action: 'created',\n    title: 'UEFA Champions League',\n    description: 'New league added to system',\n    user: 'admin',\n    time: '2024-05-25T17:30:00Z',\n    status: 'success'\n  }\n];\n\nexport default function DashboardPage() {\n  const router = useRouter();\n\n  const handleQuickAction = (path: string) => {\n    router.push(path);\n  };\n\n  return (\n    <div>\n      {/* Page Header */}\n      <div className=\"mb-6\">\n        <Title level={2}>\n          <DashboardOutlined className=\"mr-2\" />\n          Dashboard\n        </Title>\n        <Text type=\"secondary\">\n          Welcome to APISportsGame CMS - Football Management System\n        </Text>\n      </div>\n\n      {/* System Status Alert */}\n      <Alert\n        message=\"System Status: All Services Operational\"\n        description=\"Last sync completed successfully. All modules are functioning normally.\"\n        type=\"success\"\n        showIcon\n        className=\"mb-6\"\n      />\n\n      {/* Main Statistics */}\n      <Row gutter={16} className=\"mb-6\">\n        <Col xs={12} sm={6}>\n          <Card>\n            <Statistic\n              title=\"Football Leagues\"\n              value={DASHBOARD_STATS.leagues.total}\n              prefix={<TrophyOutlined />}\n              suffix={\n                <div className=\"text-sm\">\n                  <Text type=\"success\">{DASHBOARD_STATS.leagues.active} active</Text>\n                </div>\n              }\n            />\n          </Card>\n        </Col>\n        <Col xs={12} sm={6}>\n          <Card>\n            <Statistic\n              title=\"Teams\"\n              value={DASHBOARD_STATS.teams.total}\n              prefix={<TeamOutlined />}\n              suffix={\n                <div className=\"text-sm\">\n                  <Text type=\"success\">{DASHBOARD_STATS.teams.active} active</Text>\n                </div>\n              }\n            />\n          </Card>\n        </Col>\n        <Col xs={12} sm={6}>\n          <Card>\n            <Statistic\n              title=\"Fixtures\"\n              value={DASHBOARD_STATS.fixtures.total}\n              prefix={<CalendarOutlined />}\n              suffix={\n                <div className=\"text-sm\">\n                  <Text type=\"warning\">{DASHBOARD_STATS.fixtures.scheduled} scheduled</Text>\n                </div>\n              }\n            />\n          </Card>\n        </Col>\n        <Col xs={12} sm={6}>\n          <Card>\n            <Statistic\n              title=\"Broadcast Links\"\n              value={DASHBOARD_STATS.broadcastLinks.total}\n              prefix={<PlayCircleOutlined />}\n              suffix={\n                <div className=\"text-sm\">\n                  <Text type=\"success\">{DASHBOARD_STATS.broadcastLinks.active} active</Text>\n                </div>\n              }\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      <Row gutter={16}>\n        {/* Left Column */}\n        <Col xs={24} lg={16}>\n          {/* Quick Actions */}\n          <Card title=\"Quick Actions\" className=\"mb-6\">\n            <Row gutter={16}>\n              {QUICK_ACTIONS.map((action, index) => (\n                <Col xs={12} md={6} key={index} className=\"mb-4\">\n                  <Card\n                    hoverable\n                    className=\"text-center\"\n                    onClick={() => handleQuickAction(action.path)}\n                    style={{ borderColor: action.color }}\n                  >\n                    <div style={{ color: action.color, fontSize: '24px', marginBottom: '8px' }}>\n                      {action.icon}\n                    </div>\n                    <Title level={5} className=\"mb-1\">{action.title}</Title>\n                    <Text type=\"secondary\" className=\"text-sm\">{action.description}</Text>\n                  </Card>\n                </Col>\n              ))}\n            </Row>\n          </Card>\n\n          {/* System Health */}\n          <Card title=\"System Health\" className=\"mb-6\">\n            <Row gutter={16}>\n              <Col xs={24} md={12}>\n                <div className=\"mb-4\">\n                  <Text strong>Sync Success Rate</Text>\n                  <Progress\n                    percent={DASHBOARD_STATS.sync.successRate}\n                    status=\"active\"\n                    strokeColor=\"#52c41a\"\n                  />\n                </div>\n                <div className=\"mb-4\">\n                  <Text strong>Active Services</Text>\n                  <Progress\n                    percent={100}\n                    status=\"success\"\n                    strokeColor=\"#1890ff\"\n                  />\n                </div>\n              </Col>\n              <Col xs={24} md={12}>\n                <Space direction=\"vertical\" className=\"w-full\">\n                  <div className=\"flex justify-between\">\n                    <Text>Last Sync:</Text>\n                    <Text type=\"secondary\">\n                      {dayjs(DASHBOARD_STATS.sync.lastSync).format('MMM DD, HH:mm')}\n                    </Text>\n                  </div>\n                  <div className=\"flex justify-between\">\n                    <Text>Next Sync:</Text>\n                    <Text type=\"secondary\">\n                      {dayjs(DASHBOARD_STATS.sync.nextSync).format('MMM DD, HH:mm')}\n                    </Text>\n                  </div>\n                  <div className=\"flex justify-between\">\n                    <Text>Status:</Text>\n                    <Tag color=\"success\" icon={<CheckCircleOutlined />}>\n                      Operational\n                    </Tag>\n                  </div>\n                </Space>\n              </Col>\n            </Row>\n          </Card>\n        </Col>\n\n        {/* Right Column */}\n        <Col xs={24} lg={8}>\n          {/* Recent Activities */}\n          <Card title=\"Recent Activities\" className=\"mb-6\">\n            <Timeline\n              items={RECENT_ACTIVITIES.map(activity => ({\n                dot: activity.type === 'sync' ? <SyncOutlined /> :\n                  activity.type === 'fixture' ? <CalendarOutlined /> :\n                    activity.type === 'broadcast' ? <PlayCircleOutlined /> : <TeamOutlined />,\n                children: (\n                  <div>\n                    <Text strong>{activity.title}</Text>\n                    <br />\n                    <Text type=\"secondary\" className=\"text-sm\">\n                      {activity.action} by {activity.user} • {dayjs(activity.time).fromNow()}\n                    </Text>\n                  </div>\n                )\n              }))}\n            />\n          </Card>\n\n          {/* System Users */}\n          <Card title=\"System Users\" className=\"mb-6\">\n            <Row gutter={8} className=\"mb-4\">\n              <Col span={8}>\n                <Statistic\n                  title=\"Admin\"\n                  value={DASHBOARD_STATS.users.admin}\n                  valueStyle={{ fontSize: '18px' }}\n                />\n              </Col>\n              <Col span={8}>\n                <Statistic\n                  title=\"Editor\"\n                  value={DASHBOARD_STATS.users.editor}\n                  valueStyle={{ fontSize: '18px' }}\n                />\n              </Col>\n              <Col span={8}>\n                <Statistic\n                  title=\"Moderator\"\n                  value={DASHBOARD_STATS.users.moderator}\n                  valueStyle={{ fontSize: '18px' }}\n                />\n              </Col>\n            </Row>\n            <Button\n              type=\"dashed\"\n              block\n              icon={<PlusOutlined />}\n              onClick={() => router.push('/users/system')}\n            >\n              Manage Users\n            </Button>\n          </Card>\n        </Col>\n      </Row>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAgBD;AACA;AAZA;AAQA;AARA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;;;AA0BA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,0LAAA,CAAA,aAAU;AAE7C,0BAA0B;AAC1B,MAAM,kBAAiC;IACrC,SAAS;QAAE,OAAO;QAAI,QAAQ;QAAI,UAAU;QAAG,QAAQ;IAAI;IAC3D,OAAO;QAAE,OAAO;QAAK,QAAQ;QAAK,UAAU;QAAI,QAAQ;IAAK;IAC7D,UAAU;QAAE,OAAO;QAAM,WAAW;QAAI,MAAM;QAAG,UAAU;QAAM,QAAQ;IAAK;IAC9E,gBAAgB;QAAE,OAAO;QAAI,QAAQ;QAAI,UAAU;QAAI,IAAI;QAAI,OAAO;QAAQ,QAAQ;IAAK;IAC3F,OAAO;QAAE,OAAO;QAAG,OAAO;QAAG,QAAQ;QAAG,WAAW;IAAE;IACrD,MAAM;QACJ,UAAU;QACV,UAAU;QACV,QAAQ;QACR,aAAa;IACf;AACF;AAEA,MAAM,oBAAgC;IACpC;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,aAAa;QACb,MAAM;QACN,MAAM;QACN,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,aAAa;QACb,MAAM;QACN,MAAM;QACN,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,aAAa;QACb,MAAM;QACN,MAAM;QACN,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,aAAa;QACb,MAAM;QACN,MAAM;QACN,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,aAAa;QACb,MAAM;QACN,MAAM;QACN,QAAQ;IACV;CACD;AAEc,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,oBAAoB,CAAC;QACzB,OAAO,IAAI,CAAC;IACd;IAEA,qBACE,8OAAC;;0BAEC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAM,OAAO;;0CACZ,8OAAC,4NAAA,CAAA,oBAAiB;gCAAC,WAAU;;;;;;4BAAS;;;;;;;kCAGxC,8OAAC;wBAAK,MAAK;kCAAY;;;;;;;;;;;;0BAMzB,8OAAC,gLAAA,CAAA,QAAK;gBACJ,SAAQ;gBACR,aAAY;gBACZ,MAAK;gBACL,QAAQ;gBACR,WAAU;;;;;;0BAIZ,8OAAC,4KAAA,CAAA,MAAG;gBAAC,QAAQ;gBAAI,WAAU;;kCACzB,8OAAC,4KAAA,CAAA,MAAG;wBAAC,IAAI;wBAAI,IAAI;kCACf,cAAA,8OAAC,8KAAA,CAAA,OAAI;sCACH,cAAA,8OAAC;gCACC,OAAM;gCACN,OAAO,gBAAgB,OAAO,CAAC,KAAK;gCACpC,sBAAQ,8OAAC;;;;;gCACT,sBACE,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,MAAK;;4CAAW,gBAAgB,OAAO,CAAC,MAAM;4CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAM/D,8OAAC,4KAAA,CAAA,MAAG;wBAAC,IAAI;wBAAI,IAAI;kCACf,cAAA,8OAAC,8KAAA,CAAA,OAAI;sCACH,cAAA,8OAAC;gCACC,OAAM;gCACN,OAAO,gBAAgB,KAAK,CAAC,KAAK;gCAClC,sBAAQ,8OAAC;;;;;gCACT,sBACE,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,MAAK;;4CAAW,gBAAgB,KAAK,CAAC,MAAM;4CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAM7D,8OAAC,4KAAA,CAAA,MAAG;wBAAC,IAAI;wBAAI,IAAI;kCACf,cAAA,8OAAC,8KAAA,CAAA,OAAI;sCACH,cAAA,8OAAC;gCACC,OAAM;gCACN,OAAO,gBAAgB,QAAQ,CAAC,KAAK;gCACrC,sBAAQ,8OAAC;;;;;gCACT,sBACE,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,MAAK;;4CAAW,gBAAgB,QAAQ,CAAC,SAAS;4CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAMnE,8OAAC,4KAAA,CAAA,MAAG;wBAAC,IAAI;wBAAI,IAAI;kCACf,cAAA,8OAAC,8KAAA,CAAA,OAAI;sCACH,cAAA,8OAAC;gCACC,OAAM;gCACN,OAAO,gBAAgB,cAAc,CAAC,KAAK;gCAC3C,sBAAQ,8OAAC;;;;;gCACT,sBACE,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,MAAK;;4CAAW,gBAAgB,cAAc,CAAC,MAAM;4CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQxE,8OAAC,4KAAA,CAAA,MAAG;gBAAC,QAAQ;;kCAEX,8OAAC,4KAAA,CAAA,MAAG;wBAAC,IAAI;wBAAI,IAAI;;0CAEf,8OAAC,8KAAA,CAAA,OAAI;gCAAC,OAAM;gCAAgB,WAAU;0CACpC,cAAA,8OAAC,4KAAA,CAAA,MAAG;oCAAC,QAAQ;8CACV,cAAc,GAAG,CAAC,CAAC,QAAQ,sBAC1B,8OAAC,4KAAA,CAAA,MAAG;4CAAC,IAAI;4CAAI,IAAI;4CAAe,WAAU;sDACxC,cAAA,8OAAC,8KAAA,CAAA,OAAI;gDACH,SAAS;gDACT,WAAU;gDACV,SAAS,IAAM,kBAAkB,OAAO,IAAI;gDAC5C,OAAO;oDAAE,aAAa,OAAO,KAAK;gDAAC;;kEAEnC,8OAAC;wDAAI,OAAO;4DAAE,OAAO,OAAO,KAAK;4DAAE,UAAU;4DAAQ,cAAc;wDAAM;kEACtE,OAAO,IAAI;;;;;;kEAEd,8OAAC;wDAAM,OAAO;wDAAG,WAAU;kEAAQ,OAAO,KAAK;;;;;;kEAC/C,8OAAC;wDAAK,MAAK;wDAAY,WAAU;kEAAW,OAAO,WAAW;;;;;;;;;;;;2CAXzC;;;;;;;;;;;;;;;0CAmB/B,8OAAC,8KAAA,CAAA,OAAI;gCAAC,OAAM;gCAAgB,WAAU;0CACpC,cAAA,8OAAC,4KAAA,CAAA,MAAG;oCAAC,QAAQ;;sDACX,8OAAC,4KAAA,CAAA,MAAG;4CAAC,IAAI;4CAAI,IAAI;;8DACf,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,MAAM;sEAAC;;;;;;sEACb,8OAAC;4DACC,SAAS,gBAAgB,IAAI,CAAC,WAAW;4DACzC,QAAO;4DACP,aAAY;;;;;;;;;;;;8DAGhB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,MAAM;sEAAC;;;;;;sEACb,8OAAC;4DACC,SAAS;4DACT,QAAO;4DACP,aAAY;;;;;;;;;;;;;;;;;;sDAIlB,8OAAC,4KAAA,CAAA,MAAG;4CAAC,IAAI;4CAAI,IAAI;sDACf,cAAA,8OAAC,gMAAA,CAAA,QAAK;gDAAC,WAAU;gDAAW,WAAU;;kEACpC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;0EAAK;;;;;;0EACN,8OAAC;gEAAK,MAAK;0EACR,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD,EAAE,gBAAgB,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC;;;;;;;;;;;;kEAGjD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;0EAAK;;;;;;0EACN,8OAAC;gEAAK,MAAK;0EACR,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD,EAAE,gBAAgB,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC;;;;;;;;;;;;kEAGjD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;0EAAK;;;;;;0EACN,8OAAC;gEAAI,OAAM;gEAAU,oBAAM,8OAAC;;;;;0EAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAWhE,8OAAC,4KAAA,CAAA,MAAG;wBAAC,IAAI;wBAAI,IAAI;;0CAEf,8OAAC,8KAAA,CAAA,OAAI;gCAAC,OAAM;gCAAoB,WAAU;0CACxC,cAAA,8OAAC;oCACC,OAAO,kBAAkB,GAAG,CAAC,CAAA,WAAY,CAAC;4CACxC,KAAK,SAAS,IAAI,KAAK,uBAAS,8OAAC;;;;yDAC/B,SAAS,IAAI,KAAK,0BAAY,8OAAC;;;;yDAC7B,SAAS,IAAI,KAAK,4BAAc,8OAAC;;;;uEAAwB,8OAAC;;;;;4CAC9D,wBACE,8OAAC;;kEACC,8OAAC;wDAAK,MAAM;kEAAE,SAAS,KAAK;;;;;;kEAC5B,8OAAC;;;;;kEACD,8OAAC;wDAAK,MAAK;wDAAY,WAAU;;4DAC9B,SAAS,MAAM;4DAAC;4DAAK,SAAS,IAAI;4DAAC;4DAAI,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD,EAAE,SAAS,IAAI,EAAE,OAAO;;;;;;;;;;;;;wCAI5E,CAAC;;;;;;;;;;;0CAKL,8OAAC,8KAAA,CAAA,OAAI;gCAAC,OAAM;gCAAe,WAAU;;kDACnC,8OAAC,4KAAA,CAAA,MAAG;wCAAC,QAAQ;wCAAG,WAAU;;0DACxB,8OAAC,4KAAA,CAAA,MAAG;gDAAC,MAAM;0DACT,cAAA,8OAAC;oDACC,OAAM;oDACN,OAAO,gBAAgB,KAAK,CAAC,KAAK;oDAClC,YAAY;wDAAE,UAAU;oDAAO;;;;;;;;;;;0DAGnC,8OAAC,4KAAA,CAAA,MAAG;gDAAC,MAAM;0DACT,cAAA,8OAAC;oDACC,OAAM;oDACN,OAAO,gBAAgB,KAAK,CAAC,MAAM;oDACnC,YAAY;wDAAE,UAAU;oDAAO;;;;;;;;;;;0DAGnC,8OAAC,4KAAA,CAAA,MAAG;gDAAC,MAAM;0DACT,cAAA,8OAAC;oDACC,OAAM;oDACN,OAAO,gBAAgB,KAAK,CAAC,SAAS;oDACtC,YAAY;wDAAE,UAAU;oDAAO;;;;;;;;;;;;;;;;;kDAIrC,8OAAC;wCACC,MAAK;wCACL,KAAK;wCACL,oBAAM,8OAAC;;;;;wCACP,SAAS,IAAM,OAAO,IAAI,CAAC;kDAC5B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb"}}, {"offset": {"line": 826, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}