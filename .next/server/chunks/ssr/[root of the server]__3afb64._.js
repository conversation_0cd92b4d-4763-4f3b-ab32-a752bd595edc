module.exports = {

"[next]/internal/font/google/geist_e531dabc.module.css [app-client] (css module)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_export_value__({
  "className": "geist_e531dabc-module__QGiZLq__className",
  "variable": "geist_e531dabc-module__QGiZLq__variable",
});
}}),
"[next]/internal/font/google/geist_e531dabc.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$geist_e531dabc$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_import__("[next]/internal/font/google/geist_e531dabc.module.css [app-client] (css module)");
;
const fontData = {
    className: __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$geist_e531dabc$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].className,
    style: {
        fontFamily: "'Geist', 'Geist Fallback'",
        fontStyle: "normal"
    }
};
if (__TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$geist_e531dabc$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].variable != null) {
    fontData.variable = __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$geist_e531dabc$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].variable;
}
const __TURBOPACK__default__export__ = fontData;
}}),
"[next]/internal/font/google/geist_mono_68a01160.module.css [app-client] (css module)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_export_value__({
  "className": "geist_mono_68a01160-module__YLcDdW__className",
  "variable": "geist_mono_68a01160-module__YLcDdW__variable",
});
}}),
"[next]/internal/font/google/geist_mono_68a01160.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$geist_mono_68a01160$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_import__("[next]/internal/font/google/geist_mono_68a01160.module.css [app-client] (css module)");
;
const fontData = {
    className: __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$geist_mono_68a01160$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].className,
    style: {
        fontFamily: "'Geist Mono', 'Geist Mono Fallback'",
        fontStyle: "normal"
    }
};
if (__TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$geist_mono_68a01160$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].variable != null) {
    fontData.variable = __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$geist_mono_68a01160$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].variable;
}
const __TURBOPACK__default__export__ = fontData;
}}),
"[project]/src/providers/app-provider.tsx (client proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "AppProvider": (()=>AppProvider),
    "AppProviderErrorBoundary": (()=>AppProviderErrorBoundary),
    "withAppProvider": (()=>withAppProvider)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const AppProvider = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call AppProvider() from the server but AppProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/providers/app-provider.tsx <module evaluation>", "AppProvider");
const AppProviderErrorBoundary = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call AppProviderErrorBoundary() from the server but AppProviderErrorBoundary is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/providers/app-provider.tsx <module evaluation>", "AppProviderErrorBoundary");
const withAppProvider = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call withAppProvider() from the server but withAppProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/providers/app-provider.tsx <module evaluation>", "withAppProvider");
}}),
"[project]/src/providers/app-provider.tsx (client proxy)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "AppProvider": (()=>AppProvider),
    "AppProviderErrorBoundary": (()=>AppProviderErrorBoundary),
    "withAppProvider": (()=>withAppProvider)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const AppProvider = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call AppProvider() from the server but AppProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/providers/app-provider.tsx", "AppProvider");
const AppProviderErrorBoundary = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call AppProviderErrorBoundary() from the server but AppProviderErrorBoundary is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/providers/app-provider.tsx", "AppProviderErrorBoundary");
const withAppProvider = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call withAppProvider() from the server but withAppProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/providers/app-provider.tsx", "withAppProvider");
}}),
"[project]/src/providers/app-provider.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$providers$2f$app$2d$provider$2e$tsx__$28$client__proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_import__("[project]/src/providers/app-provider.tsx (client proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$providers$2f$app$2d$provider$2e$tsx__$28$client__proxy$29$__ = __turbopack_import__("[project]/src/providers/app-provider.tsx (client proxy)");
;
__turbopack_export_namespace__(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$providers$2f$app$2d$provider$2e$tsx__$28$client__proxy$29$__);
}}),
"[project]/src/stores/types.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Store Types and Interfaces
 * Defines TypeScript types for all store modules
 */ // ============================================================================
// Base Store Types
// ============================================================================
/**
 * Base store state interface
 * All stores should extend this interface
 */ __turbopack_esm__({});
;
}}),
"[project]/src/stores/utils.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Store Utilities
 * Helper functions and utilities for store management
 */ __turbopack_esm__({
    "clearStoredTokens": (()=>clearStoredTokens),
    "createBaseStoreActions": (()=>createBaseStoreActions),
    "createBaseStoreState": (()=>createBaseStoreState),
    "createErrorObject": (()=>createErrorObject),
    "createStoreWithMiddleware": (()=>createStoreWithMiddleware),
    "extractErrorMessage": (()=>extractErrorMessage),
    "generateNotificationId": (()=>generateNotificationId),
    "getDefaultNotificationDuration": (()=>getDefaultNotificationDuration),
    "getSessionRemainingTime": (()=>getSessionRemainingTime),
    "getTokenExpiration": (()=>getTokenExpiration),
    "isSessionValid": (()=>isSessionValid),
    "isTokenExpired": (()=>isTokenExpired),
    "logStoreAction": (()=>logStoreAction),
    "storage": (()=>storage),
    "validateStoreState": (()=>validateStoreState)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/zustand/esm/middleware.mjs [app-rsc] (ecmascript)");
;
function createStoreWithMiddleware(storeCreator, config) {
    let store = storeCreator;
    // Apply persistence middleware if configured
    if (config.persist) {
        store = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["persist"])(store, {
            name: config.persist.name,
            version: config.persist.version,
            storage: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createJSONStorage"])(()=>localStorage),
            partialize: config.persist.partialize || ((state)=>state),
            skipHydration: config.persist.skipHydration || false,
            onRehydrateStorage: ()=>(state)=>{
                    if (state) {
                        state.setHasHydrated(true);
                    }
                }
        });
    }
    // Apply devtools middleware if configured
    if (config.devtools) {
        store = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["devtools"])(store, {
            name: config.devtools.name,
            enabled: config.devtools.enabled && ("TURBOPACK compile-time value", "development") === 'development'
        });
    }
    return store;
}
function createBaseStoreState() {
    return {
        _hasHydrated: false,
        setHasHydrated: (hasHydrated)=>{
        // This will be implemented by the actual store
        }
    };
}
function createBaseStoreActions(set) {
    return {
        setHasHydrated: (hasHydrated)=>{
            set({
                _hasHydrated: hasHydrated
            });
        }
    };
}
function isTokenExpired(expiresAt) {
    return Date.now() >= expiresAt;
}
function getTokenExpiration(token, defaultMinutes = 60) {
    try {
        // Try to decode JWT token to get expiration
        const payload = JSON.parse(atob(token.split('.')[1]));
        if (payload.exp) {
            return payload.exp * 1000; // Convert to milliseconds
        }
    } catch (error) {
    // If JWT parsing fails, use default expiration
    }
    // Default expiration: current time + defaultMinutes
    return Date.now() + defaultMinutes * 60 * 1000;
}
function clearStoredTokens() {
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
}
function isSessionValid(lastActivity, sessionTimeout) {
    const now = Date.now();
    const timeoutMs = sessionTimeout * 60 * 1000; // Convert minutes to milliseconds
    return now - lastActivity < timeoutMs;
}
function getSessionRemainingTime(lastActivity, sessionTimeout) {
    const now = Date.now();
    const timeoutMs = sessionTimeout * 60 * 1000;
    const elapsed = now - lastActivity;
    const remaining = timeoutMs - elapsed;
    return Math.max(0, Math.floor(remaining / (60 * 1000)));
}
function extractErrorMessage(error) {
    if (typeof error === 'string') {
        return error;
    }
    if (error?.response?.data?.message) {
        return error.response.data.message;
    }
    if (error?.message) {
        return error.message;
    }
    if (error?.error) {
        return error.error;
    }
    return 'An unexpected error occurred';
}
function createErrorObject(message, details) {
    return {
        message,
        details: details || null
    };
}
function generateNotificationId() {
    return `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}
function getDefaultNotificationDuration(type) {
    switch(type){
        case 'success':
            return 3000; // 3 seconds
        case 'error':
            return 5000; // 5 seconds
        case 'warning':
            return 4000; // 4 seconds
        case 'info':
            return 3000; // 3 seconds
        default:
            return 3000;
    }
}
const storage = {
    get: (key)=>{
        if ("TURBOPACK compile-time truthy", 1) return null;
        "TURBOPACK unreachable";
    },
    set: (key, value)=>{
        if ("TURBOPACK compile-time truthy", 1) return;
        "TURBOPACK unreachable";
    },
    remove: (key)=>{
        if ("TURBOPACK compile-time truthy", 1) return;
        "TURBOPACK unreachable";
    },
    clear: ()=>{
        if ("TURBOPACK compile-time truthy", 1) return;
        "TURBOPACK unreachable";
    }
};
function logStoreAction(storeName, actionName, payload) {
    if ("TURBOPACK compile-time truthy", 1) {
        console.group(`🐻 [${storeName}] ${actionName}`);
        if (payload !== undefined) {
            console.log('Payload:', payload);
        }
        console.log('Timestamp:', new Date().toISOString());
        console.groupEnd();
    }
}
function validateStoreState(state, requiredKeys) {
    for (const key of requiredKeys){
        if (!(key in state)) {
            console.error(`Missing required store state key: ${String(key)}`);
            return false;
        }
    }
    return true;
}
}}),
"[project]/src/stores/constants.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Store Constants
 * Defines constants used across all stores
 */ // ============================================================================
// Store Names
// ============================================================================
__turbopack_esm__({
    "ACTIVITY_TRACKING_INTERVAL": (()=>ACTIVITY_TRACKING_INTERVAL),
    "DEFAULT_APP_SETTINGS": (()=>DEFAULT_APP_SETTINGS),
    "DEFAULT_NAVIGATION": (()=>DEFAULT_NAVIGATION),
    "DEFAULT_THEME": (()=>DEFAULT_THEME),
    "DEFAULT_UI_STATE": (()=>DEFAULT_UI_STATE),
    "DEV_CONFIG": (()=>DEV_CONFIG),
    "ERROR_MESSAGES": (()=>ERROR_MESSAGES),
    "FEATURE_FLAGS": (()=>FEATURE_FLAGS),
    "SESSION_TIMEOUT": (()=>SESSION_TIMEOUT),
    "STORAGE_KEYS": (()=>STORAGE_KEYS),
    "STORE_API_ENDPOINTS": (()=>STORE_API_ENDPOINTS),
    "STORE_NAMES": (()=>STORE_NAMES),
    "STORE_VERSIONS": (()=>STORE_VERSIONS),
    "SUCCESS_MESSAGES": (()=>SUCCESS_MESSAGES),
    "TOKEN_REFRESH_THRESHOLD": (()=>TOKEN_REFRESH_THRESHOLD),
    "VALIDATION_RULES": (()=>VALIDATION_RULES)
});
const STORE_NAMES = {
    AUTH: 'auth-store',
    APP: 'app-store'
};
const STORAGE_KEYS = {
    AUTH: 'auth-storage',
    APP: 'app-storage',
    THEME: 'theme-storage',
    SETTINGS: 'settings-storage'
};
const DEFAULT_THEME = {
    mode: 'light',
    primaryColor: '#1890ff',
    borderRadius: 6,
    compactMode: false
};
const DEFAULT_APP_SETTINGS = {
    language: 'en',
    timezone: 'UTC',
    dateFormat: 'YYYY-MM-DD',
    pageSize: 20,
    autoRefresh: true,
    refreshInterval: 30,
    features: {
        darkMode: true,
        notifications: true,
        autoSave: true,
        advancedFilters: true
    }
};
const DEFAULT_NAVIGATION = {
    currentPath: '/',
    breadcrumbs: [],
    sidebarCollapsed: false,
    activeMenuKey: 'dashboard'
};
const DEFAULT_UI_STATE = {
    globalLoading: false,
    loadingMessage: '',
    globalError: null,
    errorDetails: null,
    notifications: [],
    modals: {}
};
const SESSION_TIMEOUT = 60; // 1 hour
const TOKEN_REFRESH_THRESHOLD = 5; // 5 minutes
const ACTIVITY_TRACKING_INTERVAL = 60000; // 1 minute
const STORE_API_ENDPOINTS = {
    AUTH: {
        LOGIN: '/api/system-auth/login',
        LOGOUT: '/api/system-auth/logout',
        LOGOUT_ALL: '/api/system-auth/logout-all',
        PROFILE: '/api/system-auth/profile',
        REFRESH: '/api/system-auth/refresh'
    }
};
const ERROR_MESSAGES = {
    AUTH: {
        LOGIN_FAILED: 'Login failed. Please check your credentials.',
        LOGOUT_FAILED: 'Logout failed. Please try again.',
        SESSION_EXPIRED: 'Your session has expired. Please log in again.',
        TOKEN_REFRESH_FAILED: 'Failed to refresh authentication token.',
        PROFILE_UPDATE_FAILED: 'Failed to update profile. Please try again.',
        UNAUTHORIZED: 'You are not authorized to perform this action.'
    },
    APP: {
        SETTINGS_SAVE_FAILED: 'Failed to save settings. Please try again.',
        THEME_LOAD_FAILED: 'Failed to load theme configuration.',
        NETWORK_ERROR: 'Network error. Please check your connection.',
        UNKNOWN_ERROR: 'An unexpected error occurred. Please try again.'
    }
};
const SUCCESS_MESSAGES = {
    AUTH: {
        LOGIN_SUCCESS: 'Successfully logged in.',
        LOGOUT_SUCCESS: 'Successfully logged out.',
        PROFILE_UPDATED: 'Profile updated successfully.'
    },
    APP: {
        SETTINGS_SAVED: 'Settings saved successfully.',
        THEME_UPDATED: 'Theme updated successfully.'
    }
};
const STORE_VERSIONS = {
    AUTH: 1,
    APP: 1
};
const DEV_CONFIG = {
    ENABLE_DEVTOOLS: ("TURBOPACK compile-time value", "development") === 'development',
    ENABLE_LOGGING: ("TURBOPACK compile-time value", "development") === 'development',
    MOCK_API_DELAY: 1000
};
const FEATURE_FLAGS = {
    ENABLE_DARK_MODE: true,
    ENABLE_NOTIFICATIONS: true,
    ENABLE_AUTO_SAVE: true,
    ENABLE_ADVANCED_FILTERS: true,
    ENABLE_REAL_TIME_UPDATES: false,
    ENABLE_OFFLINE_MODE: false
};
const VALIDATION_RULES = {
    EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    PASSWORD_MIN_LENGTH: 8,
    NAME_MIN_LENGTH: 2,
    NAME_MAX_LENGTH: 50,
    PAGE_SIZE_MIN: 5,
    PAGE_SIZE_MAX: 100,
    REFRESH_INTERVAL_MIN: 10,
    REFRESH_INTERVAL_MAX: 300
};
}}),
"[project]/src/stores/auth-store.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Authentication Store
 * Manages user authentication state, tokens, and session
 */ __turbopack_esm__({
    "createAuthStore": (()=>createAuthStore),
    "useAuthStore": (()=>useAuthStore)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/utils.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/constants.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/zustand/esm/react.mjs [app-rsc] (ecmascript)");
;
;
;
// ============================================================================
// Initial State
// ============================================================================
const initialAuthState = {
    // Base store state
    _hasHydrated: false,
    // User data
    user: null,
    tokens: null,
    // Authentication status
    isAuthenticated: false,
    isLoading: false,
    // Error handling
    error: null,
    // Session management
    lastActivity: Date.now(),
    sessionTimeout: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SESSION_TIMEOUT"]
};
// ============================================================================
// Store Implementation
// ============================================================================
/**
 * Authentication Store Creator
 */ const createAuthStore = ()=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["create"])()((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createStoreWithMiddleware"])((set, get)=>({
            ...initialAuthState,
            // Base store actions
            ...(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createBaseStoreActions"])(set),
            // ========================================================================
            // Authentication Actions
            // ========================================================================
            /**
         * Login user with email and password
         */ login: async (email, password)=>{
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'login', {
                    email
                });
                set({
                    isLoading: true,
                    error: null
                });
                try {
                    const response = await fetch(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["STORE_API_ENDPOINTS"].AUTH.LOGIN, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            email,
                            password
                        })
                    });
                    if (!response.ok) {
                        const errorData = await response.json().catch(()=>({}));
                        throw new Error(errorData.message || __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ERROR_MESSAGES"].AUTH.LOGIN_FAILED);
                    }
                    const data = await response.json();
                    if (data.success && data.data) {
                        const { user, accessToken, refreshToken } = data.data;
                        // Create tokens object
                        const tokens = {
                            accessToken,
                            refreshToken,
                            expiresAt: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getTokenExpiration"])(accessToken)
                        };
                        // Update store state
                        set({
                            user,
                            tokens,
                            isAuthenticated: true,
                            isLoading: false,
                            error: null,
                            lastActivity: Date.now()
                        });
                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'login_success', {
                            userId: user.id
                        });
                    } else {
                        throw new Error(data.message || __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ERROR_MESSAGES"].AUTH.LOGIN_FAILED);
                    }
                } catch (error) {
                    const errorMessage = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["extractErrorMessage"])(error);
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'login_error', {
                        error: errorMessage
                    });
                    set({
                        isLoading: false,
                        error: errorMessage,
                        isAuthenticated: false,
                        user: null,
                        tokens: null
                    });
                    throw error;
                }
            },
            /**
         * Logout user from current session
         */ logout: async ()=>{
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'logout');
                const { tokens } = get();
                try {
                    // Call logout API if we have tokens
                    if (tokens?.accessToken) {
                        await fetch(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["STORE_API_ENDPOINTS"].AUTH.LOGOUT, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'Authorization': `Bearer ${tokens.accessToken}`
                            }
                        });
                    }
                } catch (error) {
                    // Log error but don't prevent logout
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'logout_api_error', {
                        error: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["extractErrorMessage"])(error)
                    });
                }
                // Clear local state regardless of API call result
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["clearStoredTokens"])();
                set({
                    user: null,
                    tokens: null,
                    isAuthenticated: false,
                    error: null,
                    lastActivity: Date.now()
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'logout_success');
            },
            /**
         * Logout user from all devices
         */ logoutAll: async ()=>{
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'logout_all');
                const { tokens } = get();
                try {
                    if (tokens?.accessToken) {
                        await fetch(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["STORE_API_ENDPOINTS"].AUTH.LOGOUT_ALL, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'Authorization': `Bearer ${tokens.accessToken}`
                            }
                        });
                    }
                } catch (error) {
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'logout_all_api_error', {
                        error: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["extractErrorMessage"])(error)
                    });
                }
                // Clear local state
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["clearStoredTokens"])();
                set({
                    user: null,
                    tokens: null,
                    isAuthenticated: false,
                    error: null,
                    lastActivity: Date.now()
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'logout_all_success');
            },
            // ========================================================================
            // User Profile Actions
            // ========================================================================
            /**
         * Update user profile
         */ updateProfile: async (data)=>{
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'update_profile', data);
                const { tokens, user } = get();
                if (!tokens?.accessToken || !user) {
                    throw new Error(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ERROR_MESSAGES"].AUTH.UNAUTHORIZED);
                }
                set({
                    isLoading: true,
                    error: null
                });
                try {
                    const response = await fetch(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["STORE_API_ENDPOINTS"].AUTH.PROFILE, {
                        method: 'PUT',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${tokens.accessToken}`
                        },
                        body: JSON.stringify(data)
                    });
                    if (!response.ok) {
                        const errorData = await response.json().catch(()=>({}));
                        throw new Error(errorData.message || __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ERROR_MESSAGES"].AUTH.PROFILE_UPDATE_FAILED);
                    }
                    const responseData = await response.json();
                    if (responseData.success && responseData.data) {
                        set({
                            user: {
                                ...user,
                                ...responseData.data
                            },
                            isLoading: false,
                            error: null,
                            lastActivity: Date.now()
                        });
                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'update_profile_success');
                    } else {
                        throw new Error(responseData.message || __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ERROR_MESSAGES"].AUTH.PROFILE_UPDATE_FAILED);
                    }
                } catch (error) {
                    const errorMessage = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["extractErrorMessage"])(error);
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'update_profile_error', {
                        error: errorMessage
                    });
                    set({
                        isLoading: false,
                        error: errorMessage
                    });
                    throw error;
                }
            },
            /**
         * Refresh authentication tokens
         */ refreshTokens: async ()=>{
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'refresh_tokens');
                const { tokens } = get();
                if (!tokens?.refreshToken) {
                    throw new Error(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ERROR_MESSAGES"].AUTH.TOKEN_REFRESH_FAILED);
                }
                try {
                    const response = await fetch(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["STORE_API_ENDPOINTS"].AUTH.REFRESH, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            refreshToken: tokens.refreshToken
                        })
                    });
                    if (!response.ok) {
                        throw new Error(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ERROR_MESSAGES"].AUTH.TOKEN_REFRESH_FAILED);
                    }
                    const data = await response.json();
                    if (data.success && data.data) {
                        const { accessToken, refreshToken } = data.data;
                        const newTokens = {
                            accessToken,
                            refreshToken,
                            expiresAt: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getTokenExpiration"])(accessToken)
                        };
                        set({
                            tokens: newTokens,
                            lastActivity: Date.now()
                        });
                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'refresh_tokens_success');
                    } else {
                        throw new Error(data.message || __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ERROR_MESSAGES"].AUTH.TOKEN_REFRESH_FAILED);
                    }
                } catch (error) {
                    const errorMessage = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["extractErrorMessage"])(error);
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'refresh_tokens_error', {
                        error: errorMessage
                    });
                    // If refresh fails, logout user
                    get().logout();
                    throw error;
                }
            },
            // ========================================================================
            // State Management Actions
            // ========================================================================
            /**
         * Set user data
         */ setUser: (user)=>{
                set({
                    user,
                    isAuthenticated: !!user
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'set_user', {
                    userId: user?.id
                });
            },
            /**
         * Set authentication tokens
         */ setTokens: (tokens)=>{
                set({
                    tokens
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'set_tokens', {
                    hasTokens: !!tokens
                });
            },
            /**
         * Set loading state
         */ setLoading: (loading)=>{
                set({
                    isLoading: loading
                });
            },
            /**
         * Set error message
         */ setError: (error)=>{
                set({
                    error
                });
                if (error) {
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'set_error', {
                        error
                    });
                }
            },
            /**
         * Clear error message
         */ clearError: ()=>{
                set({
                    error: null
                });
            },
            // ========================================================================
            // Session Management Actions
            // ========================================================================
            /**
         * Update last activity timestamp
         */ updateLastActivity: ()=>{
                set({
                    lastActivity: Date.now()
                });
            },
            /**
         * Check if current session is valid
         */ checkSession: ()=>{
                const { lastActivity, sessionTimeout, tokens } = get();
                // Check session timeout
                if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isSessionValid"])(lastActivity, sessionTimeout)) {
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'session_expired');
                    get().logout();
                    return false;
                }
                // Check token expiration
                if (tokens && (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isTokenExpired"])(tokens.expiresAt)) {
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'token_expired');
                    // Try to refresh tokens
                    get().refreshTokens().catch(()=>{
                    // If refresh fails, logout will be called automatically
                    });
                    return false;
                }
                return true;
            },
            /**
         * Hydrate store from persisted state
         */ hydrate: ()=>{
                const state = get();
                // Validate persisted session
                if (state.isAuthenticated && state.user && state.tokens) {
                    const isValid = state.checkSession();
                    if (!isValid) {
                        // Session is invalid, clear state
                        set({
                            user: null,
                            tokens: null,
                            isAuthenticated: false,
                            error: null
                        });
                    }
                }
                set({
                    _hasHydrated: true
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH, 'hydrated');
            }
        }), {
        persist: {
            name: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].AUTH,
            version: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["STORE_VERSIONS"].AUTH,
            partialize: (state)=>({
                    user: state.user,
                    tokens: state.tokens,
                    isAuthenticated: state.isAuthenticated,
                    lastActivity: state.lastActivity,
                    sessionTimeout: state.sessionTimeout
                })
        },
        devtools: {
            name: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["STORE_NAMES"].AUTH,
            enabled: ("TURBOPACK compile-time value", "development") === 'development'
        }
    }));
};
const useAuthStore = createAuthStore();
;
}}),
"[project]/src/stores/auth-hooks.ts (client proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "useAuth": (()=>useAuth),
    "useAuthDebug": (()=>useAuthDebug),
    "useAuthError": (()=>useAuthError),
    "useAuthLoading": (()=>useAuthLoading),
    "useAuthTokens": (()=>useAuthTokens),
    "useAuthWithSession": (()=>useAuthWithSession),
    "useCanAdmin": (()=>useCanAdmin),
    "useCanEdit": (()=>useCanEdit),
    "useCheckSession": (()=>useCheckSession),
    "useClearAuthError": (()=>useClearAuthError),
    "useHasRole": (()=>useHasRole),
    "useIsAdmin": (()=>useIsAdmin),
    "useIsAuthenticated": (()=>useIsAuthenticated),
    "useIsEditor": (()=>useIsEditor),
    "useIsModerator": (()=>useIsModerator),
    "useLogin": (()=>useLogin),
    "useLogout": (()=>useLogout),
    "useLogoutAll": (()=>useLogoutAll),
    "usePermissions": (()=>usePermissions),
    "useRefreshTokens": (()=>useRefreshTokens),
    "useRouteProtection": (()=>useRouteProtection),
    "useUpdateActivity": (()=>useUpdateActivity),
    "useUpdateProfile": (()=>useUpdateProfile),
    "useUser": (()=>useUser)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const useAuth = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/auth-hooks.ts <module evaluation>", "useAuth");
const useAuthDebug = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useAuthDebug() from the server but useAuthDebug is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/auth-hooks.ts <module evaluation>", "useAuthDebug");
const useAuthError = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useAuthError() from the server but useAuthError is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/auth-hooks.ts <module evaluation>", "useAuthError");
const useAuthLoading = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useAuthLoading() from the server but useAuthLoading is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/auth-hooks.ts <module evaluation>", "useAuthLoading");
const useAuthTokens = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useAuthTokens() from the server but useAuthTokens is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/auth-hooks.ts <module evaluation>", "useAuthTokens");
const useAuthWithSession = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useAuthWithSession() from the server but useAuthWithSession is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/auth-hooks.ts <module evaluation>", "useAuthWithSession");
const useCanAdmin = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useCanAdmin() from the server but useCanAdmin is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/auth-hooks.ts <module evaluation>", "useCanAdmin");
const useCanEdit = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useCanEdit() from the server but useCanEdit is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/auth-hooks.ts <module evaluation>", "useCanEdit");
const useCheckSession = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useCheckSession() from the server but useCheckSession is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/auth-hooks.ts <module evaluation>", "useCheckSession");
const useClearAuthError = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useClearAuthError() from the server but useClearAuthError is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/auth-hooks.ts <module evaluation>", "useClearAuthError");
const useHasRole = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useHasRole() from the server but useHasRole is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/auth-hooks.ts <module evaluation>", "useHasRole");
const useIsAdmin = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useIsAdmin() from the server but useIsAdmin is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/auth-hooks.ts <module evaluation>", "useIsAdmin");
const useIsAuthenticated = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useIsAuthenticated() from the server but useIsAuthenticated is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/auth-hooks.ts <module evaluation>", "useIsAuthenticated");
const useIsEditor = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useIsEditor() from the server but useIsEditor is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/auth-hooks.ts <module evaluation>", "useIsEditor");
const useIsModerator = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useIsModerator() from the server but useIsModerator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/auth-hooks.ts <module evaluation>", "useIsModerator");
const useLogin = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useLogin() from the server but useLogin is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/auth-hooks.ts <module evaluation>", "useLogin");
const useLogout = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useLogout() from the server but useLogout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/auth-hooks.ts <module evaluation>", "useLogout");
const useLogoutAll = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useLogoutAll() from the server but useLogoutAll is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/auth-hooks.ts <module evaluation>", "useLogoutAll");
const usePermissions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call usePermissions() from the server but usePermissions is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/auth-hooks.ts <module evaluation>", "usePermissions");
const useRefreshTokens = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useRefreshTokens() from the server but useRefreshTokens is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/auth-hooks.ts <module evaluation>", "useRefreshTokens");
const useRouteProtection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useRouteProtection() from the server but useRouteProtection is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/auth-hooks.ts <module evaluation>", "useRouteProtection");
const useUpdateActivity = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useUpdateActivity() from the server but useUpdateActivity is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/auth-hooks.ts <module evaluation>", "useUpdateActivity");
const useUpdateProfile = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useUpdateProfile() from the server but useUpdateProfile is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/auth-hooks.ts <module evaluation>", "useUpdateProfile");
const useUser = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useUser() from the server but useUser is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/auth-hooks.ts <module evaluation>", "useUser");
}}),
"[project]/src/stores/auth-hooks.ts (client proxy)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "useAuth": (()=>useAuth),
    "useAuthDebug": (()=>useAuthDebug),
    "useAuthError": (()=>useAuthError),
    "useAuthLoading": (()=>useAuthLoading),
    "useAuthTokens": (()=>useAuthTokens),
    "useAuthWithSession": (()=>useAuthWithSession),
    "useCanAdmin": (()=>useCanAdmin),
    "useCanEdit": (()=>useCanEdit),
    "useCheckSession": (()=>useCheckSession),
    "useClearAuthError": (()=>useClearAuthError),
    "useHasRole": (()=>useHasRole),
    "useIsAdmin": (()=>useIsAdmin),
    "useIsAuthenticated": (()=>useIsAuthenticated),
    "useIsEditor": (()=>useIsEditor),
    "useIsModerator": (()=>useIsModerator),
    "useLogin": (()=>useLogin),
    "useLogout": (()=>useLogout),
    "useLogoutAll": (()=>useLogoutAll),
    "usePermissions": (()=>usePermissions),
    "useRefreshTokens": (()=>useRefreshTokens),
    "useRouteProtection": (()=>useRouteProtection),
    "useUpdateActivity": (()=>useUpdateActivity),
    "useUpdateProfile": (()=>useUpdateProfile),
    "useUser": (()=>useUser)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const useAuth = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/auth-hooks.ts", "useAuth");
const useAuthDebug = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useAuthDebug() from the server but useAuthDebug is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/auth-hooks.ts", "useAuthDebug");
const useAuthError = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useAuthError() from the server but useAuthError is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/auth-hooks.ts", "useAuthError");
const useAuthLoading = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useAuthLoading() from the server but useAuthLoading is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/auth-hooks.ts", "useAuthLoading");
const useAuthTokens = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useAuthTokens() from the server but useAuthTokens is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/auth-hooks.ts", "useAuthTokens");
const useAuthWithSession = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useAuthWithSession() from the server but useAuthWithSession is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/auth-hooks.ts", "useAuthWithSession");
const useCanAdmin = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useCanAdmin() from the server but useCanAdmin is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/auth-hooks.ts", "useCanAdmin");
const useCanEdit = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useCanEdit() from the server but useCanEdit is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/auth-hooks.ts", "useCanEdit");
const useCheckSession = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useCheckSession() from the server but useCheckSession is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/auth-hooks.ts", "useCheckSession");
const useClearAuthError = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useClearAuthError() from the server but useClearAuthError is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/auth-hooks.ts", "useClearAuthError");
const useHasRole = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useHasRole() from the server but useHasRole is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/auth-hooks.ts", "useHasRole");
const useIsAdmin = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useIsAdmin() from the server but useIsAdmin is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/auth-hooks.ts", "useIsAdmin");
const useIsAuthenticated = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useIsAuthenticated() from the server but useIsAuthenticated is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/auth-hooks.ts", "useIsAuthenticated");
const useIsEditor = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useIsEditor() from the server but useIsEditor is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/auth-hooks.ts", "useIsEditor");
const useIsModerator = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useIsModerator() from the server but useIsModerator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/auth-hooks.ts", "useIsModerator");
const useLogin = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useLogin() from the server but useLogin is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/auth-hooks.ts", "useLogin");
const useLogout = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useLogout() from the server but useLogout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/auth-hooks.ts", "useLogout");
const useLogoutAll = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useLogoutAll() from the server but useLogoutAll is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/auth-hooks.ts", "useLogoutAll");
const usePermissions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call usePermissions() from the server but usePermissions is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/auth-hooks.ts", "usePermissions");
const useRefreshTokens = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useRefreshTokens() from the server but useRefreshTokens is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/auth-hooks.ts", "useRefreshTokens");
const useRouteProtection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useRouteProtection() from the server but useRouteProtection is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/auth-hooks.ts", "useRouteProtection");
const useUpdateActivity = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useUpdateActivity() from the server but useUpdateActivity is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/auth-hooks.ts", "useUpdateActivity");
const useUpdateProfile = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useUpdateProfile() from the server but useUpdateProfile is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/auth-hooks.ts", "useUpdateProfile");
const useUser = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useUser() from the server but useUser is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/auth-hooks.ts", "useUser");
}}),
"[project]/src/stores/auth-hooks.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$hooks$2e$ts__$28$client__proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_import__("[project]/src/stores/auth-hooks.ts (client proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$hooks$2e$ts__$28$client__proxy$29$__ = __turbopack_import__("[project]/src/stores/auth-hooks.ts (client proxy)");
;
__turbopack_export_namespace__(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$hooks$2e$ts__$28$client__proxy$29$__);
}}),
"[project]/src/stores/auth-utils.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Authentication Utilities
 * Helper functions for authentication operations
 */ __turbopack_esm__({
    "authenticatedApiRequest": (()=>authenticatedApiRequest),
    "authenticatedFetch": (()=>authenticatedFetch),
    "canAccessRoute": (()=>canAccessRoute),
    "canAdmin": (()=>canAdmin),
    "canEdit": (()=>canEdit),
    "canModerate": (()=>canModerate),
    "checkSession": (()=>checkSession),
    "clearAuthError": (()=>clearAuthError),
    "getAccessToken": (()=>getAccessToken),
    "getAuthError": (()=>getAuthError),
    "getAuthHeader": (()=>getAuthHeader),
    "getAuthSessionRemainingTime": (()=>getAuthSessionRemainingTime),
    "getAuthState": (()=>getAuthState),
    "getCurrentUser": (()=>getCurrentUser),
    "getCurrentUserRole": (()=>getCurrentUserRole),
    "getLoginRedirectPath": (()=>getLoginRedirectPath),
    "getPostLoginRedirectPath": (()=>getPostLoginRedirectPath),
    "getRefreshToken": (()=>getRefreshToken),
    "hasAnyRole": (()=>hasAnyRole),
    "hasAuthError": (()=>hasAuthError),
    "hasRole": (()=>hasRole),
    "isAdmin": (()=>isAdmin),
    "isAuthenticated": (()=>isAuthenticated),
    "isEditor": (()=>isEditor),
    "isModerator": (()=>isModerator),
    "isSessionExpiringSoon": (()=>isSessionExpiringSoon),
    "login": (()=>login),
    "logout": (()=>logout),
    "logoutAll": (()=>logoutAll),
    "mockLogin": (()=>mockLogin),
    "refreshTokens": (()=>refreshTokens),
    "resetAuthState": (()=>resetAuthState),
    "updateActivity": (()=>updateActivity),
    "updateProfile": (()=>updateProfile)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/auth-store.ts [app-rsc] (ecmascript)");
;
const getAccessToken = ()=>{
    const tokens = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["useAuthStore"].getState().tokens;
    return tokens?.accessToken || null;
};
const getRefreshToken = ()=>{
    const tokens = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["useAuthStore"].getState().tokens;
    return tokens?.refreshToken || null;
};
const getAuthHeader = ()=>{
    const token = getAccessToken();
    return token ? {
        Authorization: `Bearer ${token}`
    } : {};
};
const isAuthenticated = ()=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["useAuthStore"].getState().isAuthenticated;
};
const getCurrentUser = ()=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["useAuthStore"].getState().user;
};
const getCurrentUserRole = ()=>{
    const user = getCurrentUser();
    return user?.role || null;
};
const hasRole = (role)=>{
    const currentRole = getCurrentUserRole();
    return currentRole === role;
};
const hasAnyRole = (roles)=>{
    const currentRole = getCurrentUserRole();
    return currentRole ? roles.includes(currentRole) : false;
};
const isAdmin = ()=>{
    return hasRole('Admin');
};
const isEditor = ()=>{
    return hasRole('Editor');
};
const isModerator = ()=>{
    return hasRole('Moderator');
};
const canEdit = ()=>{
    return hasAnyRole([
        'Admin',
        'Editor'
    ]);
};
const canAdmin = ()=>{
    return isAdmin();
};
const canModerate = ()=>{
    return hasAnyRole([
        'Admin',
        'Editor',
        'Moderator'
    ]);
};
const checkSession = ()=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["useAuthStore"].getState().checkSession();
};
const updateActivity = ()=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["useAuthStore"].getState().updateLastActivity();
};
const getAuthSessionRemainingTime = ()=>{
    const { lastActivity, sessionTimeout } = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["useAuthStore"].getState();
    const now = Date.now();
    const timeoutMs = sessionTimeout * 60 * 1000;
    const elapsed = now - lastActivity;
    const remaining = timeoutMs - elapsed;
    return Math.max(0, Math.floor(remaining / (60 * 1000)));
};
const isSessionExpiringSoon = ()=>{
    return getAuthSessionRemainingTime() <= 5;
};
const login = async (email, password)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["useAuthStore"].getState().login(email, password);
};
const logout = async ()=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["useAuthStore"].getState().logout();
};
const logoutAll = async ()=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["useAuthStore"].getState().logoutAll();
};
const updateProfile = async (data)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["useAuthStore"].getState().updateProfile(data);
};
const refreshTokens = async ()=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["useAuthStore"].getState().refreshTokens();
};
const getAuthError = ()=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["useAuthStore"].getState().error;
};
const clearAuthError = ()=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["useAuthStore"].getState().clearError();
};
const hasAuthError = ()=>{
    return !!getAuthError();
};
const authenticatedFetch = async (url, options = {})=>{
    const authHeaders = getAuthHeader();
    const config = {
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...authHeaders,
            ...options.headers
        }
    };
    const response = await fetch(url, config);
    // Handle token expiration
    if (response.status === 401) {
        const isAuth = isAuthenticated();
        if (isAuth) {
            // Try to refresh tokens
            try {
                await refreshTokens();
                // Retry the request with new token
                const newAuthHeaders = getAuthHeader();
                const retryConfig = {
                    ...config,
                    headers: {
                        ...config.headers,
                        ...newAuthHeaders
                    }
                };
                return fetch(url, retryConfig);
            } catch (error) {
                // Refresh failed, logout user
                await logout();
                throw new Error('Authentication expired. Please log in again.');
            }
        }
    }
    return response;
};
const authenticatedApiRequest = async (url, options = {})=>{
    const response = await authenticatedFetch(url, options);
    if (!response.ok) {
        const errorData = await response.json().catch(()=>({}));
        throw new Error(errorData.message || `Request failed with status ${response.status}`);
    }
    return response.json();
};
const canAccessRoute = (requiredRoles)=>{
    if (!isAuthenticated()) return false;
    if (!requiredRoles || requiredRoles.length === 0) return true;
    return hasAnyRole(requiredRoles);
};
const getLoginRedirectPath = (currentPath)=>{
    const loginPath = '/login';
    if (currentPath && currentPath !== '/') {
        return `${loginPath}?redirect=${encodeURIComponent(currentPath)}`;
    }
    return loginPath;
};
const getPostLoginRedirectPath = (searchParams)=>{
    const redirectParam = searchParams?.get('redirect');
    return redirectParam || '/dashboard';
};
const getAuthState = ()=>{
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["useAuthStore"].getState();
};
const mockLogin = (user, tokens)=>{
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["useAuthStore"].getState().setUser(user);
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["useAuthStore"].getState().setTokens(tokens);
};
const resetAuthState = ()=>{
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["useAuthStore"].getState().logout();
};
}}),
"[project]/src/stores/app-store.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Application Store
 * Manages application state, theme, settings, navigation, and UI state
 */ __turbopack_esm__({
    "createAppStore": (()=>createAppStore),
    "useAppStore": (()=>useAppStore)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/utils.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/constants.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/zustand/esm/react.mjs [app-rsc] (ecmascript)");
;
;
;
// ============================================================================
// Initial State
// ============================================================================
const initialAppState = {
    // Base store state
    _hasHydrated: false,
    // Configuration
    theme: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["DEFAULT_THEME"],
    settings: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["DEFAULT_APP_SETTINGS"],
    // Navigation
    navigation: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["DEFAULT_NAVIGATION"],
    // UI state
    ui: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["DEFAULT_UI_STATE"],
    // System info
    version: process.env.NEXT_PUBLIC_APP_VERSION || '1.0.0',
    buildTime: process.env.NEXT_PUBLIC_BUILD_TIME || new Date().toISOString(),
    environment: ("TURBOPACK compile-time value", "development") || 'development'
};
// ============================================================================
// Store Implementation
// ============================================================================
/**
 * Application Store Creator
 */ const createAppStore = ()=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["create"])()((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createStoreWithMiddleware"])((set, get)=>({
            ...initialAppState,
            // Base store actions
            ...(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createBaseStoreActions"])(set),
            // ========================================================================
            // Theme Management Actions
            // ========================================================================
            /**
         * Set theme configuration
         */ setTheme: (themeUpdate)=>{
                const currentTheme = get().theme;
                const newTheme = {
                    ...currentTheme,
                    ...themeUpdate
                };
                set({
                    theme: newTheme
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["STORE_NAMES"].APP, 'set_theme', themeUpdate);
            },
            /**
         * Toggle between light and dark mode
         */ toggleTheme: ()=>{
                const currentMode = get().theme.mode;
                const newMode = currentMode === 'light' ? 'dark' : 'light';
                get().setTheme({
                    mode: newMode
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["STORE_NAMES"].APP, 'toggle_theme', {
                    newMode
                });
            },
            // ========================================================================
            // Settings Management Actions
            // ========================================================================
            /**
         * Update application settings
         */ updateSettings: (settingsUpdate)=>{
                const currentSettings = get().settings;
                const newSettings = {
                    ...currentSettings,
                    ...settingsUpdate
                };
                set({
                    settings: newSettings
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["STORE_NAMES"].APP, 'update_settings', settingsUpdate);
            },
            /**
         * Reset settings to default values
         */ resetSettings: ()=>{
                set({
                    settings: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["DEFAULT_APP_SETTINGS"]
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["STORE_NAMES"].APP, 'reset_settings');
            },
            // ========================================================================
            // Navigation Actions
            // ========================================================================
            /**
         * Set current path
         */ setCurrentPath: (path)=>{
                const currentNavigation = get().navigation;
                const newNavigation = {
                    ...currentNavigation,
                    currentPath: path
                };
                set({
                    navigation: newNavigation
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["STORE_NAMES"].APP, 'set_current_path', {
                    path
                });
            },
            /**
         * Set breadcrumbs
         */ setBreadcrumbs: (breadcrumbs)=>{
                const currentNavigation = get().navigation;
                const newNavigation = {
                    ...currentNavigation,
                    breadcrumbs
                };
                set({
                    navigation: newNavigation
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["STORE_NAMES"].APP, 'set_breadcrumbs', {
                    count: breadcrumbs.length
                });
            },
            /**
         * Toggle sidebar collapsed state
         */ toggleSidebar: ()=>{
                const currentNavigation = get().navigation;
                const newCollapsed = !currentNavigation.sidebarCollapsed;
                const newNavigation = {
                    ...currentNavigation,
                    sidebarCollapsed: newCollapsed
                };
                set({
                    navigation: newNavigation
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["STORE_NAMES"].APP, 'toggle_sidebar', {
                    collapsed: newCollapsed
                });
            },
            /**
         * Set active menu key
         */ setActiveMenu: (key)=>{
                const currentNavigation = get().navigation;
                const newNavigation = {
                    ...currentNavigation,
                    activeMenuKey: key
                };
                set({
                    navigation: newNavigation
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["STORE_NAMES"].APP, 'set_active_menu', {
                    key
                });
            },
            // ========================================================================
            // UI State Management Actions
            // ========================================================================
            /**
         * Set global loading state
         */ setGlobalLoading: (loading, message)=>{
                const currentUI = get().ui;
                const newUI = {
                    ...currentUI,
                    globalLoading: loading,
                    loadingMessage: message || ''
                };
                set({
                    ui: newUI
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["STORE_NAMES"].APP, 'set_global_loading', {
                    loading,
                    message
                });
            },
            /**
         * Set global error
         */ setGlobalError: (error, details)=>{
                const currentUI = get().ui;
                const newUI = {
                    ...currentUI,
                    globalError: error,
                    errorDetails: details || null
                };
                set({
                    ui: newUI
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["STORE_NAMES"].APP, 'set_global_error', {
                    error,
                    hasDetails: !!details
                });
            },
            /**
         * Clear global error
         */ clearGlobalError: ()=>{
                const currentUI = get().ui;
                const newUI = {
                    ...currentUI,
                    globalError: null,
                    errorDetails: null
                };
                set({
                    ui: newUI
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["STORE_NAMES"].APP, 'clear_global_error');
            },
            // ========================================================================
            // Notifications Actions
            // ========================================================================
            /**
         * Add notification
         */ addNotification: (notification)=>{
                const id = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["generateNotificationId"])();
                const timestamp = Date.now();
                const duration = notification.duration || (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getDefaultNotificationDuration"])(notification.type);
                const newNotification = {
                    ...notification,
                    id,
                    timestamp,
                    duration
                };
                const currentUI = get().ui;
                const newNotifications = [
                    ...currentUI.notifications,
                    newNotification
                ];
                const newUI = {
                    ...currentUI,
                    notifications: newNotifications
                };
                set({
                    ui: newUI
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["STORE_NAMES"].APP, 'add_notification', {
                    type: notification.type,
                    id
                });
                // Auto-remove notification after duration
                if (duration > 0) {
                    setTimeout(()=>{
                        get().removeNotification(id);
                    }, duration);
                }
            },
            /**
         * Remove notification
         */ removeNotification: (id)=>{
                const currentUI = get().ui;
                const newNotifications = currentUI.notifications.filter((n)=>n.id !== id);
                const newUI = {
                    ...currentUI,
                    notifications: newNotifications
                };
                set({
                    ui: newUI
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["STORE_NAMES"].APP, 'remove_notification', {
                    id
                });
            },
            /**
         * Clear all notifications
         */ clearNotifications: ()=>{
                const currentUI = get().ui;
                const newUI = {
                    ...currentUI,
                    notifications: []
                };
                set({
                    ui: newUI
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["STORE_NAMES"].APP, 'clear_notifications');
            },
            // ========================================================================
            // Modals Actions
            // ========================================================================
            /**
         * Show modal
         */ showModal: (key, data)=>{
                const currentUI = get().ui;
                const newModals = {
                    ...currentUI.modals,
                    [key]: {
                        visible: true,
                        data
                    }
                };
                const newUI = {
                    ...currentUI,
                    modals: newModals
                };
                set({
                    ui: newUI
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["STORE_NAMES"].APP, 'show_modal', {
                    key,
                    hasData: !!data
                });
            },
            /**
         * Hide modal
         */ hideModal: (key)=>{
                const currentUI = get().ui;
                const newModals = {
                    ...currentUI.modals,
                    [key]: {
                        visible: false,
                        data: undefined
                    }
                };
                const newUI = {
                    ...currentUI,
                    modals: newModals
                };
                set({
                    ui: newUI
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["STORE_NAMES"].APP, 'hide_modal', {
                    key
                });
            },
            /**
         * Hide all modals
         */ hideAllModals: ()=>{
                const currentUI = get().ui;
                const newModals = {};
                // Set all modals to hidden
                Object.keys(currentUI.modals).forEach((key)=>{
                    newModals[key] = {
                        visible: false,
                        data: undefined
                    };
                });
                const newUI = {
                    ...currentUI,
                    modals: newModals
                };
                set({
                    ui: newUI
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["logStoreAction"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["STORE_NAMES"].APP, 'hide_all_modals');
            }
        }), {
        persist: {
            name: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].APP,
            version: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["STORE_VERSIONS"].APP,
            partialize: (state)=>({
                    theme: state.theme,
                    settings: state.settings,
                    navigation: {
                        sidebarCollapsed: state.navigation.sidebarCollapsed,
                        activeMenuKey: state.navigation.activeMenuKey
                    }
                })
        },
        devtools: {
            name: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["STORE_NAMES"].APP,
            enabled: ("TURBOPACK compile-time value", "development") === 'development'
        }
    }));
};
const useAppStore = createAppStore();
;
}}),
"[project]/src/stores/app-hooks.ts (client proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "useActiveMenu": (()=>useActiveMenu),
    "useApp": (()=>useApp),
    "useAppSettings": (()=>useAppSettings),
    "useAppVersion": (()=>useAppVersion),
    "useBreadcrumbs": (()=>useBreadcrumbs),
    "useBuildTime": (()=>useBuildTime),
    "useCurrentPath": (()=>useCurrentPath),
    "useEnvironment": (()=>useEnvironment),
    "useGlobalError": (()=>useGlobalError),
    "useGlobalLoading": (()=>useGlobalLoading),
    "useIsDarkMode": (()=>useIsDarkMode),
    "useModal": (()=>useModal),
    "useModalActions": (()=>useModalActions),
    "useModals": (()=>useModals),
    "useNavigation": (()=>useNavigation),
    "useNavigationActions": (()=>useNavigationActions),
    "useNotificationActions": (()=>useNotificationActions),
    "useNotifications": (()=>useNotifications),
    "useNotify": (()=>useNotify),
    "useResponsive": (()=>useResponsive),
    "useSetting": (()=>useSetting),
    "useSettingsActions": (()=>useSettingsActions),
    "useSidebarState": (()=>useSidebarState),
    "useSystemInfo": (()=>useSystemInfo),
    "useTheme": (()=>useTheme),
    "useThemeActions": (()=>useThemeActions),
    "useThemeMode": (()=>useThemeMode),
    "useUIActions": (()=>useUIActions),
    "useUIState": (()=>useUIState)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const useActiveMenu = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useActiveMenu() from the server but useActiveMenu is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/app-hooks.ts <module evaluation>", "useActiveMenu");
const useApp = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useApp() from the server but useApp is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/app-hooks.ts <module evaluation>", "useApp");
const useAppSettings = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useAppSettings() from the server but useAppSettings is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/app-hooks.ts <module evaluation>", "useAppSettings");
const useAppVersion = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useAppVersion() from the server but useAppVersion is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/app-hooks.ts <module evaluation>", "useAppVersion");
const useBreadcrumbs = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useBreadcrumbs() from the server but useBreadcrumbs is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/app-hooks.ts <module evaluation>", "useBreadcrumbs");
const useBuildTime = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useBuildTime() from the server but useBuildTime is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/app-hooks.ts <module evaluation>", "useBuildTime");
const useCurrentPath = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useCurrentPath() from the server but useCurrentPath is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/app-hooks.ts <module evaluation>", "useCurrentPath");
const useEnvironment = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useEnvironment() from the server but useEnvironment is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/app-hooks.ts <module evaluation>", "useEnvironment");
const useGlobalError = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useGlobalError() from the server but useGlobalError is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/app-hooks.ts <module evaluation>", "useGlobalError");
const useGlobalLoading = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useGlobalLoading() from the server but useGlobalLoading is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/app-hooks.ts <module evaluation>", "useGlobalLoading");
const useIsDarkMode = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useIsDarkMode() from the server but useIsDarkMode is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/app-hooks.ts <module evaluation>", "useIsDarkMode");
const useModal = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useModal() from the server but useModal is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/app-hooks.ts <module evaluation>", "useModal");
const useModalActions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useModalActions() from the server but useModalActions is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/app-hooks.ts <module evaluation>", "useModalActions");
const useModals = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useModals() from the server but useModals is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/app-hooks.ts <module evaluation>", "useModals");
const useNavigation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useNavigation() from the server but useNavigation is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/app-hooks.ts <module evaluation>", "useNavigation");
const useNavigationActions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useNavigationActions() from the server but useNavigationActions is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/app-hooks.ts <module evaluation>", "useNavigationActions");
const useNotificationActions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useNotificationActions() from the server but useNotificationActions is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/app-hooks.ts <module evaluation>", "useNotificationActions");
const useNotifications = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useNotifications() from the server but useNotifications is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/app-hooks.ts <module evaluation>", "useNotifications");
const useNotify = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useNotify() from the server but useNotify is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/app-hooks.ts <module evaluation>", "useNotify");
const useResponsive = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useResponsive() from the server but useResponsive is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/app-hooks.ts <module evaluation>", "useResponsive");
const useSetting = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useSetting() from the server but useSetting is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/app-hooks.ts <module evaluation>", "useSetting");
const useSettingsActions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useSettingsActions() from the server but useSettingsActions is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/app-hooks.ts <module evaluation>", "useSettingsActions");
const useSidebarState = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useSidebarState() from the server but useSidebarState is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/app-hooks.ts <module evaluation>", "useSidebarState");
const useSystemInfo = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useSystemInfo() from the server but useSystemInfo is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/app-hooks.ts <module evaluation>", "useSystemInfo");
const useTheme = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/app-hooks.ts <module evaluation>", "useTheme");
const useThemeActions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useThemeActions() from the server but useThemeActions is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/app-hooks.ts <module evaluation>", "useThemeActions");
const useThemeMode = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useThemeMode() from the server but useThemeMode is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/app-hooks.ts <module evaluation>", "useThemeMode");
const useUIActions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useUIActions() from the server but useUIActions is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/app-hooks.ts <module evaluation>", "useUIActions");
const useUIState = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useUIState() from the server but useUIState is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/app-hooks.ts <module evaluation>", "useUIState");
}}),
"[project]/src/stores/app-hooks.ts (client proxy)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "useActiveMenu": (()=>useActiveMenu),
    "useApp": (()=>useApp),
    "useAppSettings": (()=>useAppSettings),
    "useAppVersion": (()=>useAppVersion),
    "useBreadcrumbs": (()=>useBreadcrumbs),
    "useBuildTime": (()=>useBuildTime),
    "useCurrentPath": (()=>useCurrentPath),
    "useEnvironment": (()=>useEnvironment),
    "useGlobalError": (()=>useGlobalError),
    "useGlobalLoading": (()=>useGlobalLoading),
    "useIsDarkMode": (()=>useIsDarkMode),
    "useModal": (()=>useModal),
    "useModalActions": (()=>useModalActions),
    "useModals": (()=>useModals),
    "useNavigation": (()=>useNavigation),
    "useNavigationActions": (()=>useNavigationActions),
    "useNotificationActions": (()=>useNotificationActions),
    "useNotifications": (()=>useNotifications),
    "useNotify": (()=>useNotify),
    "useResponsive": (()=>useResponsive),
    "useSetting": (()=>useSetting),
    "useSettingsActions": (()=>useSettingsActions),
    "useSidebarState": (()=>useSidebarState),
    "useSystemInfo": (()=>useSystemInfo),
    "useTheme": (()=>useTheme),
    "useThemeActions": (()=>useThemeActions),
    "useThemeMode": (()=>useThemeMode),
    "useUIActions": (()=>useUIActions),
    "useUIState": (()=>useUIState)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const useActiveMenu = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useActiveMenu() from the server but useActiveMenu is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/app-hooks.ts", "useActiveMenu");
const useApp = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useApp() from the server but useApp is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/app-hooks.ts", "useApp");
const useAppSettings = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useAppSettings() from the server but useAppSettings is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/app-hooks.ts", "useAppSettings");
const useAppVersion = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useAppVersion() from the server but useAppVersion is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/app-hooks.ts", "useAppVersion");
const useBreadcrumbs = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useBreadcrumbs() from the server but useBreadcrumbs is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/app-hooks.ts", "useBreadcrumbs");
const useBuildTime = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useBuildTime() from the server but useBuildTime is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/app-hooks.ts", "useBuildTime");
const useCurrentPath = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useCurrentPath() from the server but useCurrentPath is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/app-hooks.ts", "useCurrentPath");
const useEnvironment = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useEnvironment() from the server but useEnvironment is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/app-hooks.ts", "useEnvironment");
const useGlobalError = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useGlobalError() from the server but useGlobalError is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/app-hooks.ts", "useGlobalError");
const useGlobalLoading = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useGlobalLoading() from the server but useGlobalLoading is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/app-hooks.ts", "useGlobalLoading");
const useIsDarkMode = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useIsDarkMode() from the server but useIsDarkMode is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/app-hooks.ts", "useIsDarkMode");
const useModal = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useModal() from the server but useModal is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/app-hooks.ts", "useModal");
const useModalActions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useModalActions() from the server but useModalActions is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/app-hooks.ts", "useModalActions");
const useModals = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useModals() from the server but useModals is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/app-hooks.ts", "useModals");
const useNavigation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useNavigation() from the server but useNavigation is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/app-hooks.ts", "useNavigation");
const useNavigationActions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useNavigationActions() from the server but useNavigationActions is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/app-hooks.ts", "useNavigationActions");
const useNotificationActions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useNotificationActions() from the server but useNotificationActions is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/app-hooks.ts", "useNotificationActions");
const useNotifications = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useNotifications() from the server but useNotifications is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/app-hooks.ts", "useNotifications");
const useNotify = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useNotify() from the server but useNotify is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/app-hooks.ts", "useNotify");
const useResponsive = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useResponsive() from the server but useResponsive is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/app-hooks.ts", "useResponsive");
const useSetting = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useSetting() from the server but useSetting is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/app-hooks.ts", "useSetting");
const useSettingsActions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useSettingsActions() from the server but useSettingsActions is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/app-hooks.ts", "useSettingsActions");
const useSidebarState = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useSidebarState() from the server but useSidebarState is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/app-hooks.ts", "useSidebarState");
const useSystemInfo = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useSystemInfo() from the server but useSystemInfo is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/app-hooks.ts", "useSystemInfo");
const useTheme = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/app-hooks.ts", "useTheme");
const useThemeActions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useThemeActions() from the server but useThemeActions is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/app-hooks.ts", "useThemeActions");
const useThemeMode = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useThemeMode() from the server but useThemeMode is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/app-hooks.ts", "useThemeMode");
const useUIActions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useUIActions() from the server but useUIActions is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/app-hooks.ts", "useUIActions");
const useUIState = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useUIState() from the server but useUIState is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/app-hooks.ts", "useUIState");
}}),
"[project]/src/stores/app-hooks.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$hooks$2e$ts__$28$client__proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_import__("[project]/src/stores/app-hooks.ts (client proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$hooks$2e$ts__$28$client__proxy$29$__ = __turbopack_import__("[project]/src/stores/app-hooks.ts (client proxy)");
;
__turbopack_export_namespace__(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$hooks$2e$ts__$28$client__proxy$29$__);
}}),
"[project]/src/stores/app-utils.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Application Utilities
 * Helper functions for application operations
 */ __turbopack_esm__({
    "addNotification": (()=>addNotification),
    "applyThemeToDocument": (()=>applyThemeToDocument),
    "clearGlobalError": (()=>clearGlobalError),
    "clearNotifications": (()=>clearNotifications),
    "getActiveMenuKey": (()=>getActiveMenuKey),
    "getAppState": (()=>getAppState),
    "getAppVersion": (()=>getAppVersion),
    "getBreadcrumbs": (()=>getBreadcrumbs),
    "getBuildTime": (()=>getBuildTime),
    "getCurrentBreakpoint": (()=>getCurrentBreakpoint),
    "getCurrentNavigation": (()=>getCurrentNavigation),
    "getCurrentPath": (()=>getCurrentPath),
    "getCurrentSettings": (()=>getCurrentSettings),
    "getCurrentTheme": (()=>getCurrentTheme),
    "getCurrentThemeMode": (()=>getCurrentThemeMode),
    "getCurrentUIState": (()=>getCurrentUIState),
    "getEnvironment": (()=>getEnvironment),
    "getGlobalError": (()=>getGlobalError),
    "getModalData": (()=>getModalData),
    "getModalsState": (()=>getModalsState),
    "getNotifications": (()=>getNotifications),
    "getSetting": (()=>getSetting),
    "hideAllModals": (()=>hideAllModals),
    "hideModal": (()=>hideModal),
    "isDarkMode": (()=>isDarkMode),
    "isDesktop": (()=>isDesktop),
    "isDevelopment": (()=>isDevelopment),
    "isGlobalLoading": (()=>isGlobalLoading),
    "isMobile": (()=>isMobile),
    "isModalVisible": (()=>isModalVisible),
    "isProduction": (()=>isProduction),
    "isSidebarCollapsed": (()=>isSidebarCollapsed),
    "isTablet": (()=>isTablet),
    "notifyError": (()=>notifyError),
    "notifyInfo": (()=>notifyInfo),
    "notifySuccess": (()=>notifySuccess),
    "notifyWarning": (()=>notifyWarning),
    "removeNotification": (()=>removeNotification),
    "resetAppState": (()=>resetAppState),
    "resetSettings": (()=>resetSettings),
    "setActiveMenu": (()=>setActiveMenu),
    "setBreadcrumbs": (()=>setBreadcrumbs),
    "setCurrentPath": (()=>setCurrentPath),
    "setGlobalError": (()=>setGlobalError),
    "setGlobalLoading": (()=>setGlobalLoading),
    "setThemeMode": (()=>setThemeMode),
    "showModal": (()=>showModal),
    "toggleSidebar": (()=>toggleSidebar),
    "toggleTheme": (()=>toggleTheme),
    "updateSettings": (()=>updateSettings)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/app-store.ts [app-rsc] (ecmascript)");
;
const getCurrentTheme = ()=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["useAppStore"].getState().theme;
};
const getCurrentThemeMode = ()=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["useAppStore"].getState().theme.mode;
};
const isDarkMode = ()=>{
    return getCurrentThemeMode() === 'dark';
};
const toggleTheme = ()=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["useAppStore"].getState().toggleTheme();
};
const setThemeMode = (mode)=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["useAppStore"].getState().setTheme({
        mode
    });
};
const applyThemeToDocument = ()=>{
    if (typeof document === 'undefined') return;
    const theme = getCurrentTheme();
    const { mode, primaryColor, borderRadius } = theme;
    // Apply theme class to body
    document.body.className = document.body.className.replace(/theme-\w+/g, '');
    document.body.classList.add(`theme-${mode}`);
    // Apply CSS custom properties
    const root = document.documentElement;
    root.style.setProperty('--primary-color', primaryColor);
    root.style.setProperty('--border-radius', `${borderRadius}px`);
};
const getCurrentSettings = ()=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["useAppStore"].getState().settings;
};
const getSetting = (key)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["useAppStore"].getState().settings[key];
};
const updateSettings = (settings)=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["useAppStore"].getState().updateSettings(settings);
};
const resetSettings = ()=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["useAppStore"].getState().resetSettings();
};
const getCurrentNavigation = ()=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["useAppStore"].getState().navigation;
};
const getCurrentPath = ()=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["useAppStore"].getState().navigation.currentPath;
};
const setCurrentPath = (path)=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["useAppStore"].getState().setCurrentPath(path);
};
const getBreadcrumbs = ()=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["useAppStore"].getState().navigation.breadcrumbs;
};
const setBreadcrumbs = (breadcrumbs)=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["useAppStore"].getState().setBreadcrumbs(breadcrumbs);
};
const isSidebarCollapsed = ()=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["useAppStore"].getState().navigation.sidebarCollapsed;
};
const toggleSidebar = ()=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["useAppStore"].getState().toggleSidebar();
};
const getActiveMenuKey = ()=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["useAppStore"].getState().navigation.activeMenuKey;
};
const setActiveMenu = (key)=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["useAppStore"].getState().setActiveMenu(key);
};
const getCurrentUIState = ()=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["useAppStore"].getState().ui;
};
const isGlobalLoading = ()=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["useAppStore"].getState().ui.globalLoading;
};
const setGlobalLoading = (loading, message)=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["useAppStore"].getState().setGlobalLoading(loading, message);
};
const getGlobalError = ()=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["useAppStore"].getState().ui.globalError;
};
const setGlobalError = (error, details)=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["useAppStore"].getState().setGlobalError(error, details);
};
const clearGlobalError = ()=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["useAppStore"].getState().clearGlobalError();
};
const getNotifications = ()=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["useAppStore"].getState().ui.notifications;
};
const addNotification = (notification)=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["useAppStore"].getState().addNotification(notification);
};
const removeNotification = (id)=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["useAppStore"].getState().removeNotification(id);
};
const clearNotifications = ()=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["useAppStore"].getState().clearNotifications();
};
const notifySuccess = (message, title)=>{
    addNotification({
        type: 'success',
        title: title || 'Success',
        message
    });
};
const notifyError = (message, title)=>{
    addNotification({
        type: 'error',
        title: title || 'Error',
        message
    });
};
const notifyWarning = (message, title)=>{
    addNotification({
        type: 'warning',
        title: title || 'Warning',
        message
    });
};
const notifyInfo = (message, title)=>{
    addNotification({
        type: 'info',
        title: title || 'Info',
        message
    });
};
const getModalsState = ()=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["useAppStore"].getState().ui.modals;
};
const isModalVisible = (key)=>{
    const modal = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["useAppStore"].getState().ui.modals[key];
    return modal?.visible || false;
};
const getModalData = (key)=>{
    const modal = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["useAppStore"].getState().ui.modals[key];
    return modal?.data;
};
const showModal = (key, data)=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["useAppStore"].getState().showModal(key, data);
};
const hideModal = (key)=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["useAppStore"].getState().hideModal(key);
};
const hideAllModals = ()=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["useAppStore"].getState().hideAllModals();
};
const getAppVersion = ()=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["useAppStore"].getState().version;
};
const getBuildTime = ()=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["useAppStore"].getState().buildTime;
};
const getEnvironment = ()=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["useAppStore"].getState().environment;
};
const isDevelopment = ()=>{
    return getEnvironment() === 'development';
};
const isProduction = ()=>{
    return getEnvironment() === 'production';
};
const isMobile = ()=>{
    if ("TURBOPACK compile-time truthy", 1) return false;
    "TURBOPACK unreachable";
};
const isTablet = ()=>{
    if ("TURBOPACK compile-time truthy", 1) return false;
    "TURBOPACK unreachable";
};
const isDesktop = ()=>{
    if ("TURBOPACK compile-time truthy", 1) return false;
    "TURBOPACK unreachable";
};
const getCurrentBreakpoint = ()=>{
    if (isMobile()) return 'mobile';
    if (isTablet()) return 'tablet';
    return 'desktop';
};
const getAppState = ()=>{
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["useAppStore"].getState();
};
const resetAppState = ()=>{
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    const store = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["useAppStore"].getState();
    store.resetSettings();
    store.clearNotifications();
    store.hideAllModals();
    store.clearGlobalError();
    store.setGlobalLoading(false);
};
}}),
"[project]/src/stores/store-provider.tsx (client proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "StoreProvider": (()=>StoreProvider),
    "StoreProviderUtils": (()=>StoreProviderUtils),
    "withStoreProvider": (()=>withStoreProvider)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const StoreProvider = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call StoreProvider() from the server but StoreProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/store-provider.tsx <module evaluation>", "StoreProvider");
const StoreProviderUtils = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call StoreProviderUtils() from the server but StoreProviderUtils is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/store-provider.tsx <module evaluation>", "StoreProviderUtils");
const withStoreProvider = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call withStoreProvider() from the server but withStoreProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/store-provider.tsx <module evaluation>", "withStoreProvider");
}}),
"[project]/src/stores/store-provider.tsx (client proxy)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "StoreProvider": (()=>StoreProvider),
    "StoreProviderUtils": (()=>StoreProviderUtils),
    "withStoreProvider": (()=>withStoreProvider)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const StoreProvider = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call StoreProvider() from the server but StoreProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/store-provider.tsx", "StoreProvider");
const StoreProviderUtils = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call StoreProviderUtils() from the server but StoreProviderUtils is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/store-provider.tsx", "StoreProviderUtils");
const withStoreProvider = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call withStoreProvider() from the server but withStoreProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/store-provider.tsx", "withStoreProvider");
}}),
"[project]/src/stores/store-provider.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$store$2d$provider$2e$tsx__$28$client__proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_import__("[project]/src/stores/store-provider.tsx (client proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$store$2d$provider$2e$tsx__$28$client__proxy$29$__ = __turbopack_import__("[project]/src/stores/store-provider.tsx (client proxy)");
;
__turbopack_export_namespace__(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$store$2d$provider$2e$tsx__$28$client__proxy$29$__);
}}),
"[project]/src/stores/store-context.tsx (client proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "StoreContextProvider": (()=>StoreContextProvider),
    "useAppStoreContext": (()=>useAppStoreContext),
    "useAuthStoreContext": (()=>useAuthStoreContext),
    "useIsStoreContextAvailable": (()=>useIsStoreContextAvailable),
    "useStoreContext": (()=>useStoreContext)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const StoreContextProvider = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call StoreContextProvider() from the server but StoreContextProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/store-context.tsx <module evaluation>", "StoreContextProvider");
const useAppStoreContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useAppStoreContext() from the server but useAppStoreContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/store-context.tsx <module evaluation>", "useAppStoreContext");
const useAuthStoreContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useAuthStoreContext() from the server but useAuthStoreContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/store-context.tsx <module evaluation>", "useAuthStoreContext");
const useIsStoreContextAvailable = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useIsStoreContextAvailable() from the server but useIsStoreContextAvailable is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/store-context.tsx <module evaluation>", "useIsStoreContextAvailable");
const useStoreContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useStoreContext() from the server but useStoreContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/store-context.tsx <module evaluation>", "useStoreContext");
}}),
"[project]/src/stores/store-context.tsx (client proxy)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "StoreContextProvider": (()=>StoreContextProvider),
    "useAppStoreContext": (()=>useAppStoreContext),
    "useAuthStoreContext": (()=>useAuthStoreContext),
    "useIsStoreContextAvailable": (()=>useIsStoreContextAvailable),
    "useStoreContext": (()=>useStoreContext)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const StoreContextProvider = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call StoreContextProvider() from the server but StoreContextProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/store-context.tsx", "StoreContextProvider");
const useAppStoreContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useAppStoreContext() from the server but useAppStoreContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/store-context.tsx", "useAppStoreContext");
const useAuthStoreContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useAuthStoreContext() from the server but useAuthStoreContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/store-context.tsx", "useAuthStoreContext");
const useIsStoreContextAvailable = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useIsStoreContextAvailable() from the server but useIsStoreContextAvailable is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/store-context.tsx", "useIsStoreContextAvailable");
const useStoreContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useStoreContext() from the server but useStoreContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/store-context.tsx", "useStoreContext");
}}),
"[project]/src/stores/store-context.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$store$2d$context$2e$tsx__$28$client__proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_import__("[project]/src/stores/store-context.tsx (client proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$store$2d$context$2e$tsx__$28$client__proxy$29$__ = __turbopack_import__("[project]/src/stores/store-context.tsx (client proxy)");
;
__turbopack_export_namespace__(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$store$2d$context$2e$tsx__$28$client__proxy$29$__);
}}),
"[project]/src/stores/provider-hooks.ts (client proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "useAppProvider": (()=>useAppProvider),
    "useAuthProvider": (()=>useAuthProvider),
    "useStoreAvailability": (()=>useStoreAvailability),
    "useStoreDebug": (()=>useStoreDebug),
    "useStores": (()=>useStores)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const useAppProvider = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useAppProvider() from the server but useAppProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/provider-hooks.ts <module evaluation>", "useAppProvider");
const useAuthProvider = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useAuthProvider() from the server but useAuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/provider-hooks.ts <module evaluation>", "useAuthProvider");
const useStoreAvailability = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useStoreAvailability() from the server but useStoreAvailability is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/provider-hooks.ts <module evaluation>", "useStoreAvailability");
const useStoreDebug = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useStoreDebug() from the server but useStoreDebug is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/provider-hooks.ts <module evaluation>", "useStoreDebug");
const useStores = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useStores() from the server but useStores is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/provider-hooks.ts <module evaluation>", "useStores");
}}),
"[project]/src/stores/provider-hooks.ts (client proxy)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "useAppProvider": (()=>useAppProvider),
    "useAuthProvider": (()=>useAuthProvider),
    "useStoreAvailability": (()=>useStoreAvailability),
    "useStoreDebug": (()=>useStoreDebug),
    "useStores": (()=>useStores)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const useAppProvider = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useAppProvider() from the server but useAppProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/provider-hooks.ts", "useAppProvider");
const useAuthProvider = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useAuthProvider() from the server but useAuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/provider-hooks.ts", "useAuthProvider");
const useStoreAvailability = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useStoreAvailability() from the server but useStoreAvailability is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/provider-hooks.ts", "useStoreAvailability");
const useStoreDebug = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useStoreDebug() from the server but useStoreDebug is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/provider-hooks.ts", "useStoreDebug");
const useStores = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useStores() from the server but useStores is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/stores/provider-hooks.ts", "useStores");
}}),
"[project]/src/stores/provider-hooks.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$provider$2d$hooks$2e$ts__$28$client__proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_import__("[project]/src/stores/provider-hooks.ts (client proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$provider$2d$hooks$2e$ts__$28$client__proxy$29$__ = __turbopack_import__("[project]/src/stores/provider-hooks.ts (client proxy)");
;
__turbopack_export_namespace__(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$provider$2d$hooks$2e$ts__$28$client__proxy$29$__);
}}),
"[project]/src/stores/index.ts [app-rsc] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Store Index - Central export for all stores
 * Provides barrel exports for all store modules
 */ // Store types and interfaces
__turbopack_esm__({});
;
;
;
;
;
;
;
;
;
;
;
}}),
"[project]/src/stores/index.ts [app-rsc] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_esm__({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$types$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/types.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/utils.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/auth-store.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$hooks$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/auth-hooks.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/auth-utils.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$store$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/app-store.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$hooks$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/app-hooks.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$app$2d$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/app-utils.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$store$2d$provider$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/store-provider.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$store$2d$context$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/store-context.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$provider$2d$hooks$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/provider-hooks.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_import__("[project]/src/stores/index.ts [app-rsc] (ecmascript) <locals>");
}}),
"[project]/src/lib/query-provider.tsx (client proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "QueryProvider": (()=>QueryProvider),
    "QueryProviderUtils": (()=>QueryProviderUtils),
    "QueryProviderWithErrorBoundary": (()=>QueryProviderWithErrorBoundary),
    "withQueryProvider": (()=>withQueryProvider)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const QueryProvider = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call QueryProvider() from the server but QueryProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/lib/query-provider.tsx <module evaluation>", "QueryProvider");
const QueryProviderUtils = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call QueryProviderUtils() from the server but QueryProviderUtils is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/lib/query-provider.tsx <module evaluation>", "QueryProviderUtils");
const QueryProviderWithErrorBoundary = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call QueryProviderWithErrorBoundary() from the server but QueryProviderWithErrorBoundary is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/lib/query-provider.tsx <module evaluation>", "QueryProviderWithErrorBoundary");
const withQueryProvider = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call withQueryProvider() from the server but withQueryProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/lib/query-provider.tsx <module evaluation>", "withQueryProvider");
}}),
"[project]/src/lib/query-provider.tsx (client proxy)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "QueryProvider": (()=>QueryProvider),
    "QueryProviderUtils": (()=>QueryProviderUtils),
    "QueryProviderWithErrorBoundary": (()=>QueryProviderWithErrorBoundary),
    "withQueryProvider": (()=>withQueryProvider)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const QueryProvider = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call QueryProvider() from the server but QueryProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/lib/query-provider.tsx", "QueryProvider");
const QueryProviderUtils = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call QueryProviderUtils() from the server but QueryProviderUtils is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/lib/query-provider.tsx", "QueryProviderUtils");
const QueryProviderWithErrorBoundary = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call QueryProviderWithErrorBoundary() from the server but QueryProviderWithErrorBoundary is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/lib/query-provider.tsx", "QueryProviderWithErrorBoundary");
const withQueryProvider = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call withQueryProvider() from the server but withQueryProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/lib/query-provider.tsx", "withQueryProvider");
}}),
"[project]/src/lib/query-provider.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$provider$2e$tsx__$28$client__proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_import__("[project]/src/lib/query-provider.tsx (client proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$provider$2e$tsx__$28$client__proxy$29$__ = __turbopack_import__("[project]/src/lib/query-provider.tsx (client proxy)");
;
__turbopack_export_namespace__(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$provider$2e$tsx__$28$client__proxy$29$__);
}}),
"[project]/src/providers/index.ts [app-rsc] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Providers Index
 * Central export for all provider components
 */ // App provider (combined)
__turbopack_esm__({
    "ProviderUtils": (()=>ProviderUtils)
});
// Import for internal use
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$provider$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/lib/query-provider.tsx [app-rsc] (ecmascript)");
;
;
;
;
const ProviderUtils = {
    /**
   * Check if all providers are properly initialized
   */ checkProviderStatus: ()=>{
        const storeAvailable = true; // StoreProvider is always available when rendered
        const queryAvailable = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$provider$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["QueryProviderUtils"].isQueryClientAvailable();
        return {
            store: storeAvailable,
            query: queryAvailable,
            all: storeAvailable && queryAvailable
        };
    },
    /**
   * Reset all providers (development only)
   */ resetAllProviders: ()=>{
        if ("TURBOPACK compile-time truthy", 1) {
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$provider$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["QueryProviderUtils"].resetQueryClient();
            console.log('[Dev] All providers reset');
        }
    }
};
}}),
"[project]/src/providers/index.ts [app-rsc] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_esm__({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$providers$2f$app$2d$provider$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/providers/app-provider.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_import__("[project]/src/stores/index.ts [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2d$provider$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/lib/query-provider.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$providers$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_import__("[project]/src/providers/index.ts [app-rsc] (ecmascript) <locals>");
}}),
"[project]/src/components/layout/app-layout.tsx (client proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "AppLayout": (()=>AppLayout),
    "LayoutProvider": (()=>LayoutProvider),
    "SimpleLayout": (()=>SimpleLayout),
    "useLayout": (()=>useLayout)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const AppLayout = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call AppLayout() from the server but AppLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/layout/app-layout.tsx <module evaluation>", "AppLayout");
const LayoutProvider = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call LayoutProvider() from the server but LayoutProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/layout/app-layout.tsx <module evaluation>", "LayoutProvider");
const SimpleLayout = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call SimpleLayout() from the server but SimpleLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/layout/app-layout.tsx <module evaluation>", "SimpleLayout");
const useLayout = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useLayout() from the server but useLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/layout/app-layout.tsx <module evaluation>", "useLayout");
}}),
"[project]/src/components/layout/app-layout.tsx (client proxy)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "AppLayout": (()=>AppLayout),
    "LayoutProvider": (()=>LayoutProvider),
    "SimpleLayout": (()=>SimpleLayout),
    "useLayout": (()=>useLayout)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const AppLayout = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call AppLayout() from the server but AppLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/layout/app-layout.tsx", "AppLayout");
const LayoutProvider = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call LayoutProvider() from the server but LayoutProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/layout/app-layout.tsx", "LayoutProvider");
const SimpleLayout = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call SimpleLayout() from the server but SimpleLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/layout/app-layout.tsx", "SimpleLayout");
const useLayout = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useLayout() from the server but useLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/layout/app-layout.tsx", "useLayout");
}}),
"[project]/src/components/layout/app-layout.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$app$2d$layout$2e$tsx__$28$client__proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_import__("[project]/src/components/layout/app-layout.tsx (client proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$app$2d$layout$2e$tsx__$28$client__proxy$29$__ = __turbopack_import__("[project]/src/components/layout/app-layout.tsx (client proxy)");
;
__turbopack_export_namespace__(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$app$2d$layout$2e$tsx__$28$client__proxy$29$__);
}}),
"[project]/src/components/layout/app-header.tsx (client proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "AppHeader": (()=>AppHeader),
    "HeaderBreadcrumb": (()=>HeaderBreadcrumb)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const AppHeader = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call AppHeader() from the server but AppHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/layout/app-header.tsx <module evaluation>", "AppHeader");
const HeaderBreadcrumb = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call HeaderBreadcrumb() from the server but HeaderBreadcrumb is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/layout/app-header.tsx <module evaluation>", "HeaderBreadcrumb");
}}),
"[project]/src/components/layout/app-header.tsx (client proxy)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "AppHeader": (()=>AppHeader),
    "HeaderBreadcrumb": (()=>HeaderBreadcrumb)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const AppHeader = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call AppHeader() from the server but AppHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/layout/app-header.tsx", "AppHeader");
const HeaderBreadcrumb = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call HeaderBreadcrumb() from the server but HeaderBreadcrumb is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/layout/app-header.tsx", "HeaderBreadcrumb");
}}),
"[project]/src/components/layout/app-header.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$app$2d$header$2e$tsx__$28$client__proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_import__("[project]/src/components/layout/app-header.tsx (client proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$app$2d$header$2e$tsx__$28$client__proxy$29$__ = __turbopack_import__("[project]/src/components/layout/app-header.tsx (client proxy)");
;
__turbopack_export_namespace__(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$app$2d$header$2e$tsx__$28$client__proxy$29$__);
}}),
"[project]/src/components/layout/app-sidebar.tsx (client proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "AppSidebar": (()=>AppSidebar),
    "SidebarMenuItem": (()=>SidebarMenuItem)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const AppSidebar = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call AppSidebar() from the server but AppSidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/layout/app-sidebar.tsx <module evaluation>", "AppSidebar");
const SidebarMenuItem = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call SidebarMenuItem() from the server but SidebarMenuItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/layout/app-sidebar.tsx <module evaluation>", "SidebarMenuItem");
}}),
"[project]/src/components/layout/app-sidebar.tsx (client proxy)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "AppSidebar": (()=>AppSidebar),
    "SidebarMenuItem": (()=>SidebarMenuItem)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const AppSidebar = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call AppSidebar() from the server but AppSidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/layout/app-sidebar.tsx", "AppSidebar");
const SidebarMenuItem = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call SidebarMenuItem() from the server but SidebarMenuItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/layout/app-sidebar.tsx", "SidebarMenuItem");
}}),
"[project]/src/components/layout/app-sidebar.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$app$2d$sidebar$2e$tsx__$28$client__proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_import__("[project]/src/components/layout/app-sidebar.tsx (client proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$app$2d$sidebar$2e$tsx__$28$client__proxy$29$__ = __turbopack_import__("[project]/src/components/layout/app-sidebar.tsx (client proxy)");
;
__turbopack_export_namespace__(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$app$2d$sidebar$2e$tsx__$28$client__proxy$29$__);
}}),
"[project]/src/components/layout/app-footer.tsx (client proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "AppFooter": (()=>AppFooter),
    "SimpleFooter": (()=>SimpleFooter)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const AppFooter = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call AppFooter() from the server but AppFooter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/layout/app-footer.tsx <module evaluation>", "AppFooter");
const SimpleFooter = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call SimpleFooter() from the server but SimpleFooter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/layout/app-footer.tsx <module evaluation>", "SimpleFooter");
}}),
"[project]/src/components/layout/app-footer.tsx (client proxy)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "AppFooter": (()=>AppFooter),
    "SimpleFooter": (()=>SimpleFooter)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const AppFooter = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call AppFooter() from the server but AppFooter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/layout/app-footer.tsx", "AppFooter");
const SimpleFooter = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call SimpleFooter() from the server but SimpleFooter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/layout/app-footer.tsx", "SimpleFooter");
}}),
"[project]/src/components/layout/app-footer.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$app$2d$footer$2e$tsx__$28$client__proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_import__("[project]/src/components/layout/app-footer.tsx (client proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$app$2d$footer$2e$tsx__$28$client__proxy$29$__ = __turbopack_import__("[project]/src/components/layout/app-footer.tsx (client proxy)");
;
__turbopack_export_namespace__(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$app$2d$footer$2e$tsx__$28$client__proxy$29$__);
}}),
"[project]/src/components/layout/auth-layout.tsx (client proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "AuthCard": (()=>AuthCard),
    "AuthDivider": (()=>AuthDivider),
    "AuthForm": (()=>AuthForm),
    "AuthLayout": (()=>AuthLayout)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const AuthCard = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call AuthCard() from the server but AuthCard is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/layout/auth-layout.tsx <module evaluation>", "AuthCard");
const AuthDivider = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call AuthDivider() from the server but AuthDivider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/layout/auth-layout.tsx <module evaluation>", "AuthDivider");
const AuthForm = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call AuthForm() from the server but AuthForm is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/layout/auth-layout.tsx <module evaluation>", "AuthForm");
const AuthLayout = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call AuthLayout() from the server but AuthLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/layout/auth-layout.tsx <module evaluation>", "AuthLayout");
}}),
"[project]/src/components/layout/auth-layout.tsx (client proxy)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "AuthCard": (()=>AuthCard),
    "AuthDivider": (()=>AuthDivider),
    "AuthForm": (()=>AuthForm),
    "AuthLayout": (()=>AuthLayout)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const AuthCard = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call AuthCard() from the server but AuthCard is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/layout/auth-layout.tsx", "AuthCard");
const AuthDivider = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call AuthDivider() from the server but AuthDivider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/layout/auth-layout.tsx", "AuthDivider");
const AuthForm = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call AuthForm() from the server but AuthForm is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/layout/auth-layout.tsx", "AuthForm");
const AuthLayout = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call AuthLayout() from the server but AuthLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/layout/auth-layout.tsx", "AuthLayout");
}}),
"[project]/src/components/layout/auth-layout.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$auth$2d$layout$2e$tsx__$28$client__proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_import__("[project]/src/components/layout/auth-layout.tsx (client proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$auth$2d$layout$2e$tsx__$28$client__proxy$29$__ = __turbopack_import__("[project]/src/components/layout/auth-layout.tsx (client proxy)");
;
__turbopack_export_namespace__(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$auth$2d$layout$2e$tsx__$28$client__proxy$29$__);
}}),
"[project]/src/components/layout/page-header.tsx (client proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "PageHeader": (()=>PageHeader),
    "SectionHeader": (()=>SectionHeader),
    "SimplePageHeader": (()=>SimplePageHeader)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const PageHeader = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call PageHeader() from the server but PageHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/layout/page-header.tsx <module evaluation>", "PageHeader");
const SectionHeader = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call SectionHeader() from the server but SectionHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/layout/page-header.tsx <module evaluation>", "SectionHeader");
const SimplePageHeader = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call SimplePageHeader() from the server but SimplePageHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/layout/page-header.tsx <module evaluation>", "SimplePageHeader");
}}),
"[project]/src/components/layout/page-header.tsx (client proxy)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "PageHeader": (()=>PageHeader),
    "SectionHeader": (()=>SectionHeader),
    "SimplePageHeader": (()=>SimplePageHeader)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const PageHeader = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call PageHeader() from the server but PageHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/layout/page-header.tsx", "PageHeader");
const SectionHeader = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call SectionHeader() from the server but SectionHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/layout/page-header.tsx", "SectionHeader");
const SimplePageHeader = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call SimplePageHeader() from the server but SimplePageHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/layout/page-header.tsx", "SimplePageHeader");
}}),
"[project]/src/components/layout/page-header.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$page$2d$header$2e$tsx__$28$client__proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_import__("[project]/src/components/layout/page-header.tsx (client proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$page$2d$header$2e$tsx__$28$client__proxy$29$__ = __turbopack_import__("[project]/src/components/layout/page-header.tsx (client proxy)");
;
__turbopack_export_namespace__(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$page$2d$header$2e$tsx__$28$client__proxy$29$__);
}}),
"[project]/src/components/layout/content-layout.tsx (client proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "Container": (()=>Container),
    "ContentLayout": (()=>ContentLayout),
    "GridLayout": (()=>GridLayout),
    "SidebarLayout": (()=>SidebarLayout),
    "ThreeColumnLayout": (()=>ThreeColumnLayout),
    "TwoColumnLayout": (()=>TwoColumnLayout)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const Container = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call Container() from the server but Container is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/layout/content-layout.tsx <module evaluation>", "Container");
const ContentLayout = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call ContentLayout() from the server but ContentLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/layout/content-layout.tsx <module evaluation>", "ContentLayout");
const GridLayout = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call GridLayout() from the server but GridLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/layout/content-layout.tsx <module evaluation>", "GridLayout");
const SidebarLayout = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call SidebarLayout() from the server but SidebarLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/layout/content-layout.tsx <module evaluation>", "SidebarLayout");
const ThreeColumnLayout = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call ThreeColumnLayout() from the server but ThreeColumnLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/layout/content-layout.tsx <module evaluation>", "ThreeColumnLayout");
const TwoColumnLayout = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call TwoColumnLayout() from the server but TwoColumnLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/layout/content-layout.tsx <module evaluation>", "TwoColumnLayout");
}}),
"[project]/src/components/layout/content-layout.tsx (client proxy)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "Container": (()=>Container),
    "ContentLayout": (()=>ContentLayout),
    "GridLayout": (()=>GridLayout),
    "SidebarLayout": (()=>SidebarLayout),
    "ThreeColumnLayout": (()=>ThreeColumnLayout),
    "TwoColumnLayout": (()=>TwoColumnLayout)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const Container = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call Container() from the server but Container is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/layout/content-layout.tsx", "Container");
const ContentLayout = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call ContentLayout() from the server but ContentLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/layout/content-layout.tsx", "ContentLayout");
const GridLayout = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call GridLayout() from the server but GridLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/layout/content-layout.tsx", "GridLayout");
const SidebarLayout = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call SidebarLayout() from the server but SidebarLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/layout/content-layout.tsx", "SidebarLayout");
const ThreeColumnLayout = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call ThreeColumnLayout() from the server but ThreeColumnLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/layout/content-layout.tsx", "ThreeColumnLayout");
const TwoColumnLayout = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call TwoColumnLayout() from the server but TwoColumnLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/layout/content-layout.tsx", "TwoColumnLayout");
}}),
"[project]/src/components/layout/content-layout.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$content$2d$layout$2e$tsx__$28$client__proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_import__("[project]/src/components/layout/content-layout.tsx (client proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$content$2d$layout$2e$tsx__$28$client__proxy$29$__ = __turbopack_import__("[project]/src/components/layout/content-layout.tsx (client proxy)");
;
__turbopack_export_namespace__(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$content$2d$layout$2e$tsx__$28$client__proxy$29$__);
}}),
"[project]/src/components/layout/index.ts [app-rsc] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Layout Components Index
 * Export all layout components
 */ // Main app layout components
__turbopack_esm__({
    "Content": (()=>Content),
    "Footer": (()=>Footer),
    "Header": (()=>Header),
    "LAYOUT_COMPONENTS_NAME": (()=>LAYOUT_COMPONENTS_NAME),
    "LAYOUT_COMPONENTS_VERSION": (()=>LAYOUT_COMPONENTS_VERSION),
    "Sider": (()=>Sider),
    "setupLayoutComponents": (()=>setupLayoutComponents)
});
// Export specific layout components
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/antd/es/index.js [app-rsc] (ecmascript)");
;
;
;
;
;
;
;
;
;
const { Header, Footer, Sider, Content } = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Layout"];
;
const LAYOUT_COMPONENTS_VERSION = '1.0.0';
const LAYOUT_COMPONENTS_NAME = 'APISportsGame Layout Components';
function setupLayoutComponents() {
    console.log(`${LAYOUT_COMPONENTS_NAME} v${LAYOUT_COMPONENTS_VERSION} initialized`);
}
}}),
"[project]/src/components/layout/index.ts [app-rsc] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_esm__({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$app$2d$layout$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/layout/app-layout.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$app$2d$header$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/layout/app-header.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$app$2d$sidebar$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/layout/app-sidebar.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$app$2d$footer$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/layout/app-footer.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$auth$2d$layout$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/layout/auth-layout.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$page$2d$header$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/layout/page-header.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$content$2d$layout$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/layout/content-layout.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_import__("[project]/src/components/layout/index.ts [app-rsc] (ecmascript) <locals>");
}}),
"[project]/src/components/layout/index.ts [app-rsc] (ecmascript) <exports>": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_esm__({
    "Content": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["Content"]),
    "Footer": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["Footer"]),
    "Header": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["Header"]),
    "LAYOUT_COMPONENTS_NAME": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["LAYOUT_COMPONENTS_NAME"]),
    "LAYOUT_COMPONENTS_VERSION": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["LAYOUT_COMPONENTS_VERSION"]),
    "Layout": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Layout"]),
    "Sider": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["Sider"]),
    "setupLayoutComponents": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["setupLayoutComponents"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/antd/es/index.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$app$2d$layout$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/layout/app-layout.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$app$2d$header$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/layout/app-header.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$app$2d$sidebar$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/layout/app-sidebar.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$app$2d$footer$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/layout/app-footer.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$auth$2d$layout$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/layout/auth-layout.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$page$2d$header$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/layout/page-header.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$content$2d$layout$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/layout/content-layout.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_import__("[project]/src/components/layout/index.ts [app-rsc] (ecmascript) <locals>");
__turbopack_dynamic__(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$app$2d$layout$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__), __turbopack_dynamic__(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$app$2d$header$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__), __turbopack_dynamic__(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$app$2d$sidebar$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__), __turbopack_dynamic__(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$app$2d$footer$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__), __turbopack_dynamic__(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$auth$2d$layout$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__), __turbopack_dynamic__(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$page$2d$header$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__), __turbopack_dynamic__(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$content$2d$layout$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__);
}}),
"[project]/src/app/layout.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>RootLayout),
    "metadata": (()=>metadata)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$geist_e531dabc$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[next]/internal/font/google/geist_e531dabc.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$geist_mono_68a01160$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[next]/internal/font/google/geist_mono_68a01160.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$providers$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_import__("[project]/src/providers/index.ts [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_import__("[project]/src/components/layout/index.ts [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$providers$2f$app$2d$provider$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/providers/app-provider.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__ = __turbopack_import__("[project]/src/components/layout/index.ts [app-rsc] (ecmascript) <exports>");
;
;
;
;
;
;
const metadata = {
    title: "APISportsGame CMS",
    description: "Frontend CMS for APISportsGame - Manage tournaments, matches, teams, and users"
};
function RootLayout({ children }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("html", {
        lang: "en",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("body", {
            className: `${__TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$geist_e531dabc$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"].variable} ${__TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$geist_mono_68a01160$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"].variable} antialiased`,
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$providers$2f$app$2d$provider$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["AppProvider"], {
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["AppLayout"], {
                    children: children
                }, void 0, false, {
                    fileName: "[project]/src/app/layout.tsx",
                    lineNumber: 33,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/layout.tsx",
                lineNumber: 32,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/app/layout.tsx",
            lineNumber: 29,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/app/layout.tsx",
        lineNumber: 28,
        columnNumber: 5
    }, this);
}
}}),
"[project]/src/app/layout.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_export_namespace__(__turbopack_import__("[project]/src/app/layout.tsx [app-rsc] (ecmascript)"));
}}),

};

//# sourceMappingURL=%5Broot%20of%20the%20server%5D__3afb64._.js.map