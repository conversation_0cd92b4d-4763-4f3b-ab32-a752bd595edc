{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/layout/index.ts"], "sourcesContent": ["/**\n * Layout Components Index\n * Export all layout components\n */\n\n// Main app layout components\nexport * from './app-layout';\nexport * from './app-header';\nexport * from './app-sidebar';\nexport * from './app-footer';\n\n// Authentication layout components\nexport * from './auth-layout';\n\n// Page header components\nexport * from './page-header';\n\n// Content layout components\nexport * from './content-layout';\n\n// Re-export Ant Design layout components for convenience\nexport { Layout } from 'antd';\n\n// Export specific layout components\nimport { Layout } from 'antd';\nconst { Header, Footer, Sider, Content } = Layout;\nexport { Header, Footer, Sider, Content };\n\n/**\n * Layout components metadata\n */\nexport const LAYOUT_COMPONENTS_VERSION = '1.0.0';\nexport const LAYOUT_COMPONENTS_NAME = 'APISportsGame Layout Components';\n\n/**\n * Setup function for layout components\n */\nexport function setupLayoutComponents() {\n  console.log(`${LAYOUT_COMPONENTS_NAME} v${LAYOUT_COMPONENTS_VERSION} initialized`);\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,6BAA6B;;;;;;;;;;AAkB7B,oCAAoC;AACpC;;;;;;;;;;AACA,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,kLAAA,CAAA,SAAM;;AAM1C,MAAM,4BAA4B;AAClC,MAAM,yBAAyB;AAK/B,SAAS;IACd,QAAQ,GAAG,CAAC,GAAG,uBAAuB,EAAE,EAAE,0BAA0B,YAAY,CAAC;AACnF"}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 59, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/app/football/layout.tsx"], "sourcesContent": ["'use client';\n\nimport { AppLayout } from '@/components/layout';\n\nexport default function FootballLayout({\n  children,\n}: {\n  children: React.ReactNode;\n}) {\n  return <AppLayout>{children}</AppLayout>;\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAFA;;;AAIe,SAAS,eAAe,EACrC,QAAQ,EAGT;IACC,qBAAO,8OAAC,6IAAA,CAAA,YAAS;kBAAE;;;;;;AACrB"}}, {"offset": {"line": 77, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}