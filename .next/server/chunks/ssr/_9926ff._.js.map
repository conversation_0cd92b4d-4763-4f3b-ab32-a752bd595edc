{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/app/football/leagues/page.tsx"], "sourcesContent": ["/**\n * Football Leagues Management Page\n * Comprehensive leagues management with CRUD operations\n */\n\n'use client';\n\n'use client';\n\nimport React from 'react';\nimport { Card, Typography, Button, Alert } from 'antd';\nimport { TrophyOutlined, ToolOutlined } from '@ant-design/icons';\nimport { useRouter } from 'next/navigation';\n\nconst { Title, Text } = Typography;\n\nexport default function LeaguesPage() {\n  const router = useRouter();\n\n  return (\n    <div>\n      {/* Page Header */}\n      <div className=\"mb-6\">\n        <Title level={2}>\n          <TrophyOutlined className=\"mr-2\" />\n          Football Leagues (Legacy)\n        </Title>\n        <Text type=\"secondary\">\n          This page is being refactored. Please use the new Football Leagues Management module.\n        </Text>\n      </div>\n\n      {/* Refactor Notice */}\n      <Alert\n        message=\"Page Under Refactoring\"\n        description=\"This legacy football leagues page is being refactored to use modern components and architecture. The new Football Leagues Management module will be available soon with improved functionality.\"\n        type=\"warning\"\n        showIcon\n        className=\"mb-6\"\n      />\n\n      {/* Temporary Actions */}\n      <Card title=\"Available Actions\">\n        <div className=\"space-y-4\">\n          <div>\n            <Title level={4}>\n              <ToolOutlined className=\"mr-2\" />\n              Development Status\n            </Title>\n            <ul className=\"list-disc list-inside text-gray-600\">\n              <li>Legacy page temporarily disabled due to component conflicts</li>\n              <li>New Football Leagues Management module in development</li>\n              <li>Will include modern UI, better performance, and enhanced features</li>\n              <li>Expected completion: Next development cycle</li>\n            </ul>\n          </div>\n\n          <div className=\"pt-4\">\n            <Button\n              type=\"primary\"\n              onClick={() => router.push('/dashboard')}\n            >\n              Return to Dashboard\n            </Button>\n          </div>\n        </div>\n      </Card>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AASD;AAFA;AACA;AADA;AAAA;AACA;AADA;AALA;AAEA;;;;;AAOA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,0LAAA,CAAA,aAAU;AAEnB,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,qBACE,8OAAC;;0BAEC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAM,OAAO;;0CACZ,8OAAC,sNAAA,CAAA,iBAAc;gCAAC,WAAU;;;;;;4BAAS;;;;;;;kCAGrC,8OAAC;wBAAK,MAAK;kCAAY;;;;;;;;;;;;0BAMzB,8OAAC,gLAAA,CAAA,QAAK;gBACJ,SAAQ;gBACR,aAAY;gBACZ,MAAK;gBACL,QAAQ;gBACR,WAAU;;;;;;0BAIZ,8OAAC,8KAAA,CAAA,OAAI;gBAAC,OAAM;0BACV,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAM,OAAO;;sDACZ,8OAAC,kNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;wCAAS;;;;;;;8CAGnC,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;;;;;;;;;;;;;sCAIR,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,kMAAA,CAAA,SAAM;gCACL,MAAK;gCACL,SAAS,IAAM,OAAO,IAAI,CAAC;0CAC5B;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb"}}, {"offset": {"line": 178, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 199, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/node_modules/%40ant-design/icons-svg/es/asn/ToolOutlined.js"], "sourcesContent": ["// This icon file is generated automatically.\nvar ToolOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M876.6 239.5c-.5-.9-1.2-1.8-2-2.5-5-5-13.1-5-18.1 0L684.2 409.3l-67.9-67.9L788.7 169c.8-.8 1.4-1.6 2-2.5 3.6-6.1 1.6-13.9-4.5-17.5-98.2-58-226.8-44.7-311.3 39.7-67 67-89.2 162-66.5 247.4l-293 293c-3 3-2.8 7.9.3 11l169.7 169.7c3.1 3.1 8.1 3.3 11 .3l292.9-292.9c85.5 22.8 180.5.7 247.6-66.4 84.4-84.5 97.7-213.1 39.7-311.3zM786 499.8c-58.1 58.1-145.3 69.3-214.6 33.6l-8.8 8.8-.1-.1-274 274.1-79.2-79.2 230.1-230.1s0 .1.1.1l52.8-52.8c-35.7-69.3-24.5-156.5 33.6-214.6a184.2 184.2 0 01144-53.5L537 318.9a32.05 32.05 0 000 45.3l124.5 124.5a32.05 32.05 0 0045.3 0l132.8-132.8c3.7 51.8-14.4 104.8-53.6 143.9z\" } }] }, \"name\": \"tool\", \"theme\": \"outlined\" };\nexport default ToolOutlined;\n"], "names": [], "mappings": "AAAA,6CAA6C;;;;AAC7C,IAAI,eAAe;IAAE,QAAQ;QAAE,OAAO;QAAO,SAAS;YAAE,WAAW;YAAiB,aAAa;QAAQ;QAAG,YAAY;YAAC;gBAAE,OAAO;gBAAQ,SAAS;oBAAE,KAAK;gBAA2lB;YAAE;SAAE;IAAC;IAAG,QAAQ;IAAQ,SAAS;AAAW;uCAClxB", "ignoreList": [0]}}, {"offset": {"line": 223, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 229, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/node_modules/%40ant-design/icons/es/icons/ToolOutlined.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport ToolOutlinedSvg from \"@ant-design/icons-svg/es/asn/ToolOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar ToolOutlined = function ToolOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: ToolOutlinedSvg\n  }));\n};\n\n/**![tool](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg3Ni42IDIzOS41Yy0uNS0uOS0xLjItMS44LTItMi41LTUtNS0xMy4xLTUtMTguMSAwTDY4NC4yIDQwOS4zbC02Ny45LTY3LjlMNzg4LjcgMTY5Yy44LS44IDEuNC0xLjYgMi0yLjUgMy42LTYuMSAxLjYtMTMuOS00LjUtMTcuNS05OC4yLTU4LTIyNi44LTQ0LjctMzExLjMgMzkuNy02NyA2Ny04OS4yIDE2Mi02Ni41IDI0Ny40bC0yOTMgMjkzYy0zIDMtMi44IDcuOS4zIDExbDE2OS43IDE2OS43YzMuMSAzLjEgOC4xIDMuMyAxMSAuM2wyOTIuOS0yOTIuOWM4NS41IDIyLjggMTgwLjUuNyAyNDcuNi02Ni40IDg0LjQtODQuNSA5Ny43LTIxMy4xIDM5LjctMzExLjN6TTc4NiA0OTkuOGMtNTguMSA1OC4xLTE0NS4zIDY5LjMtMjE0LjYgMzMuNmwtOC44IDguOC0uMS0uMS0yNzQgMjc0LjEtNzkuMi03OS4yIDIzMC4xLTIzMC4xczAgLjEuMS4xbDUyLjgtNTIuOGMtMzUuNy02OS4zLTI0LjUtMTU2LjUgMzMuNi0yMTQuNmExODQuMiAxODQuMiAwIDAxMTQ0LTUzLjVMNTM3IDMxOC45YTMyLjA1IDMyLjA1IDAgMDAwIDQ1LjNsMTI0LjUgMTI0LjVhMzIuMDUgMzIuMDUgMCAwMDQ1LjMgMGwxMzIuOC0xMzIuOGMzLjcgNTEuOC0xNC40IDEwNC44LTUzLjYgMTQzLjl6IiAvPjwvc3ZnPg==) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(ToolOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'ToolOutlined';\n}\nexport default RefIcon;"], "names": [], "mappings": ";;;AAAA;AACA,oCAAoC;AACpC,2BAA2B;AAE3B;AAEA;AADA;;;;;AAEA,IAAI,eAAe,SAAS,aAAa,KAAK,EAAE,GAAG;IACjD,OAAO,WAAW,GAAE,sMAAM,aAAa,CAAC,wKAAA,CAAA,UAAQ,EAAE,CAAA,GAAA,+JAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,OAAO;QACpE,KAAK;QACL,MAAM,4KAAA,CAAA,UAAe;IACvB;AACF;AAEA,mgCAAmgC,GACngC,IAAI,UAAU,WAAW,GAAE,sMAAM,UAAU,CAAC;AAC5C,wCAA2C;IACzC,QAAQ,WAAW,GAAG;AACxB;uCACe", "ignoreList": [0]}}, {"offset": {"line": 253, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}