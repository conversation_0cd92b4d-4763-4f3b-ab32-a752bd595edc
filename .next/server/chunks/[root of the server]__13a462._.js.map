{"version": 3, "sources": [], "sections": [{"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/lib/api-config.ts"], "sourcesContent": ["/**\n * API Configuration for APISportsGame CMS\n * Handles proxy configuration and API utilities\n */\n\n// API Base Configuration\nexport const API_CONFIG = {\n  BASE_URL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000',\n  TIMEOUT: 10000,\n  RETRY_ATTEMPTS: 3,\n  RETRY_DELAY: 1000,\n} as const;\n\n// API Endpoints Configuration\nexport const API_ENDPOINTS = {\n  // System Authentication (requires auth)\n  SYSTEM_AUTH: {\n    LOGIN: '/system-auth/login',\n    PROFILE: '/system-auth/profile',\n    LOGOUT: '/system-auth/logout',\n    LOGOUT_ALL: '/system-auth/logout-all',\n    CREATE_USER: '/system-auth/create-user',\n    CHANGE_PASSWORD: '/system-auth/change-password',\n    REFRESH: '/system-auth/refresh',\n  },\n\n  // Football Data (some endpoints no auth required)\n  FOOTBALL: {\n    LEAGUES: '/football/leagues',\n    TEAMS: '/football/teams',\n    FIXTURES: '/football/fixtures', // No auth required\n    FIXTURES_SYNC: '/football/fixtures/sync',\n    FIXTURES_SYNC_STATUS: '/football/fixtures/sync/status',\n    FIXTURES_SYNC_DAILY: '/football/fixtures/sync/daily',\n  },\n\n  // Broadcast Links (requires auth)\n  BROADCAST_LINKS: {\n    BASE: '/broadcast-links',\n    BY_FIXTURE: '/broadcast-links/fixture',\n  },\n} as const;\n\n// HTTP Methods\nexport const HTTP_METHODS = {\n  GET: 'GET',\n  POST: 'POST',\n  PUT: 'PUT',\n  PATCH: 'PATCH',\n  DELETE: 'DELETE',\n} as const;\n\n// API Response Types\nexport interface ApiResponse<T = any> {\n  success: boolean;\n  data?: T;\n  message?: string;\n  error?: string;\n  statusCode?: number;\n}\n\nexport interface ApiError {\n  message: string;\n  statusCode: number;\n  error?: string;\n  details?: any;\n}\n\n// Request Configuration\nexport interface ProxyRequestConfig {\n  method: string;\n  url: string;\n  headers?: Record<string, string>;\n  body?: any;\n  requiresAuth?: boolean;\n}\n\n// Authentication Configuration\nexport const AUTH_CONFIG = {\n  TOKEN_HEADER: 'Authorization',\n  TOKEN_PREFIX: 'Bearer',\n  REFRESH_TOKEN_HEADER: 'X-Refresh-Token',\n} as const;\n\n// Endpoints that require authentication\nexport const PROTECTED_ENDPOINTS = [\n  '/system-auth/profile',\n  '/system-auth/logout',\n  '/system-auth/logout-all',\n  '/system-auth/create-user',\n  '/system-auth/change-password',\n  '/football/leagues',\n  '/football/teams',\n  '/football/fixtures/sync',\n  '/broadcast-links',\n] as const;\n\n// Endpoints that don't require authentication\nexport const PUBLIC_ENDPOINTS = [\n  '/system-auth/login',\n  '/system-auth/refresh',\n  '/football/fixtures', // Updated: No auth required\n] as const;\n\n/**\n * Check if an endpoint requires authentication\n */\nexport function requiresAuthentication(endpoint: string): boolean {\n  // Check if it's explicitly in public endpoints\n  if (PUBLIC_ENDPOINTS.some(publicEndpoint => endpoint.startsWith(publicEndpoint))) {\n    return false;\n  }\n\n  // Check if it's in protected endpoints\n  if (PROTECTED_ENDPOINTS.some(protectedEndpoint => endpoint.startsWith(protectedEndpoint))) {\n    return true;\n  }\n\n  // Default to requiring authentication for unknown endpoints (security first)\n  return true;\n}\n\n/**\n * Build full API URL\n */\nexport function buildApiUrl(endpoint: string): string {\n  const baseUrl = API_CONFIG.BASE_URL.replace(/\\/$/, '');\n  const cleanEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;\n  return `${baseUrl}${cleanEndpoint}`;\n}\n\n/**\n * Default headers for API requests\n */\nexport function getDefaultHeaders(): Record<string, string> {\n  return {\n    'Content-Type': 'application/json',\n    'Accept': 'application/json',\n  };\n}\n\n/**\n * Get authentication headers if token is available\n */\nexport function getAuthHeaders(token?: string): Record<string, string> {\n  if (!token) return {};\n\n  return {\n    [AUTH_CONFIG.TOKEN_HEADER]: `${AUTH_CONFIG.TOKEN_PREFIX} ${token}`,\n  };\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,yBAAyB;;;;;;;;;;;;;AAClB,MAAM,aAAa;IACxB,UAAU,6DAAmC;IAC7C,SAAS;IACT,gBAAgB;IAChB,aAAa;AACf;AAGO,MAAM,gBAAgB;IAC3B,wCAAwC;IACxC,aAAa;QACX,OAAO;QACP,SAAS;QACT,QAAQ;QACR,YAAY;QACZ,aAAa;QACb,iBAAiB;QACjB,SAAS;IACX;IAEA,kDAAkD;IAClD,UAAU;QACR,SAAS;QACT,OAAO;QACP,UAAU;QACV,eAAe;QACf,sBAAsB;QACtB,qBAAqB;IACvB;IAEA,kCAAkC;IAClC,iBAAiB;QACf,MAAM;QACN,YAAY;IACd;AACF;AAGO,MAAM,eAAe;IAC1B,KAAK;IACL,MAAM;IACN,KAAK;IACL,OAAO;IACP,QAAQ;AACV;AA4BO,MAAM,cAAc;IACzB,cAAc;IACd,cAAc;IACd,sBAAsB;AACxB;AAGO,MAAM,sBAAsB;IACjC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAGM,MAAM,mBAAmB;IAC9B;IACA;IACA;CACD;AAKM,SAAS,uBAAuB,QAAgB;IACrD,+CAA+C;IAC/C,IAAI,iBAAiB,IAAI,CAAC,CAAA,iBAAkB,SAAS,UAAU,CAAC,kBAAkB;QAChF,OAAO;IACT;IAEA,uCAAuC;IACvC,IAAI,oBAAoB,IAAI,CAAC,CAAA,oBAAqB,SAAS,UAAU,CAAC,qBAAqB;QACzF,OAAO;IACT;IAEA,6EAA6E;IAC7E,OAAO;AACT;AAKO,SAAS,YAAY,QAAgB;IAC1C,MAAM,UAAU,WAAW,QAAQ,CAAC,OAAO,CAAC,OAAO;IACnD,MAAM,gBAAgB,SAAS,UAAU,CAAC,OAAO,WAAW,CAAC,CAAC,EAAE,UAAU;IAC1E,OAAO,GAAG,UAAU,eAAe;AACrC;AAKO,SAAS;IACd,OAAO;QACL,gBAAgB;QAChB,UAAU;IACZ;AACF;AAKO,SAAS,eAAe,KAAc;IAC3C,IAAI,CAAC,OAAO,OAAO,CAAC;IAEpB,OAAO;QACL,CAAC,YAAY,YAAY,CAAC,EAAE,GAAG,YAAY,YAAY,CAAC,CAAC,EAAE,OAAO;IACpE;AACF"}}, {"offset": {"line": 160, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 166, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/lib/api-utils.ts"], "sourcesContent": ["/**\n * API Utility Functions for Proxy Operations\n */\n\nimport { NextRequest, NextResponse } from 'next/server';\nimport { \n  API_CONFIG, \n  ApiResponse, \n  ApiError, \n  ProxyRequestConfig,\n  buildApiUrl,\n  getDefaultHeaders,\n  getAuthHeaders,\n  requiresAuthentication \n} from './api-config';\n\n/**\n * Extract authentication token from request headers\n */\nexport function extractAuthToken(request: NextRequest): string | null {\n  const authHeader = request.headers.get('authorization');\n  if (!authHeader || !authHeader.startsWith('Bearer ')) {\n    return null;\n  }\n  return authHeader.substring(7); // Remove 'Bearer ' prefix\n}\n\n/**\n * Create error response\n */\nexport function createErrorResponse(\n  message: string, \n  statusCode: number = 500,\n  error?: string\n): NextResponse<ApiError> {\n  return NextResponse.json(\n    {\n      message,\n      statusCode,\n      error: error || 'Internal Server Error',\n    },\n    { status: statusCode }\n  );\n}\n\n/**\n * Create success response\n */\nexport function createSuccessResponse<T>(\n  data: T,\n  statusCode: number = 200,\n  message?: string\n): NextResponse<ApiResponse<T>> {\n  return NextResponse.json(\n    {\n      success: true,\n      data,\n      message,\n      statusCode,\n    },\n    { status: statusCode }\n  );\n}\n\n/**\n * Validate request method\n */\nexport function validateMethod(\n  request: NextRequest, \n  allowedMethods: string[]\n): boolean {\n  return allowedMethods.includes(request.method);\n}\n\n/**\n * Parse request body safely\n */\nexport async function parseRequestBody(request: NextRequest): Promise<any> {\n  try {\n    const contentType = request.headers.get('content-type');\n    \n    if (contentType?.includes('application/json')) {\n      return await request.json();\n    }\n    \n    if (contentType?.includes('application/x-www-form-urlencoded')) {\n      const formData = await request.formData();\n      const body: Record<string, any> = {};\n      formData.forEach((value, key) => {\n        body[key] = value;\n      });\n      return body;\n    }\n    \n    return null;\n  } catch (error) {\n    console.error('Error parsing request body:', error);\n    return null;\n  }\n}\n\n/**\n * Build proxy request configuration\n */\nexport async function buildProxyConfig(\n  request: NextRequest,\n  targetEndpoint: string\n): Promise<ProxyRequestConfig> {\n  const body = await parseRequestBody(request);\n  const token = extractAuthToken(request);\n  const requiresAuth = requiresAuthentication(targetEndpoint);\n  \n  // Build headers\n  const headers = {\n    ...getDefaultHeaders(),\n    ...(requiresAuth && token ? getAuthHeaders(token) : {}),\n  };\n  \n  // Copy relevant headers from original request\n  const relevantHeaders = ['user-agent', 'accept-language', 'x-forwarded-for'];\n  relevantHeaders.forEach(headerName => {\n    const headerValue = request.headers.get(headerName);\n    if (headerValue) {\n      headers[headerName] = headerValue;\n    }\n  });\n  \n  return {\n    method: request.method,\n    url: buildApiUrl(targetEndpoint),\n    headers,\n    body: body ? JSON.stringify(body) : undefined,\n    requiresAuth,\n  };\n}\n\n/**\n * Execute proxy request to backend API\n */\nexport async function executeProxyRequest(\n  config: ProxyRequestConfig\n): Promise<Response> {\n  const { method, url, headers, body } = config;\n  \n  try {\n    const response = await fetch(url, {\n      method,\n      headers,\n      body,\n      signal: AbortSignal.timeout(API_CONFIG.TIMEOUT),\n    });\n    \n    return response;\n  } catch (error) {\n    console.error('Proxy request failed:', error);\n    throw new Error(`Proxy request failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\n  }\n}\n\n/**\n * Handle proxy response and convert to NextResponse\n */\nexport async function handleProxyResponse(response: Response): Promise<NextResponse> {\n  try {\n    const contentType = response.headers.get('content-type');\n    \n    // Handle JSON responses\n    if (contentType?.includes('application/json')) {\n      const data = await response.json();\n      return NextResponse.json(data, { \n        status: response.status,\n        statusText: response.statusText,\n      });\n    }\n    \n    // Handle text responses\n    if (contentType?.includes('text/')) {\n      const text = await response.text();\n      return new NextResponse(text, {\n        status: response.status,\n        statusText: response.statusText,\n        headers: {\n          'content-type': contentType,\n        },\n      });\n    }\n    \n    // Handle binary responses\n    const buffer = await response.arrayBuffer();\n    return new NextResponse(buffer, {\n      status: response.status,\n      statusText: response.statusText,\n      headers: {\n        'content-type': contentType || 'application/octet-stream',\n      },\n    });\n    \n  } catch (error) {\n    console.error('Error handling proxy response:', error);\n    return createErrorResponse(\n      'Failed to process response from backend API',\n      500\n    );\n  }\n}\n\n/**\n * Main proxy handler function\n */\nexport async function handleProxyRequest(\n  request: NextRequest,\n  targetEndpoint: string,\n  allowedMethods: string[] = ['GET', 'POST', 'PUT', 'PATCH', 'DELETE']\n): Promise<NextResponse> {\n  try {\n    // Validate HTTP method\n    if (!validateMethod(request, allowedMethods)) {\n      return createErrorResponse(\n        `Method ${request.method} not allowed`,\n        405,\n        'Method Not Allowed'\n      );\n    }\n    \n    // Check authentication if required\n    const requiresAuth = requiresAuthentication(targetEndpoint);\n    if (requiresAuth) {\n      const token = extractAuthToken(request);\n      if (!token) {\n        return createErrorResponse(\n          'Authentication required',\n          401,\n          'Unauthorized'\n        );\n      }\n    }\n    \n    // Build proxy configuration\n    const proxyConfig = await buildProxyConfig(request, targetEndpoint);\n    \n    // Execute proxy request\n    const response = await executeProxyRequest(proxyConfig);\n    \n    // Handle and return response\n    return await handleProxyResponse(response);\n    \n  } catch (error) {\n    console.error('Proxy handler error:', error);\n    return createErrorResponse(\n      error instanceof Error ? error.message : 'Internal server error',\n      500\n    );\n  }\n}\n\n/**\n * Log proxy request for debugging\n */\nexport function logProxyRequest(\n  request: NextRequest,\n  targetEndpoint: string,\n  config: ProxyRequestConfig\n): void {\n  if (process.env.NODE_ENV === 'development') {\n    console.log(`[PROXY] ${request.method} ${request.url} -> ${config.url}`);\n    console.log(`[PROXY] Auth required: ${config.requiresAuth}`);\n    console.log(`[PROXY] Headers:`, Object.keys(config.headers || {}));\n  }\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;;;;;;;;AAED;AACA;;;AAcO,SAAS,iBAAiB,OAAoB;IACnD,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;IACvC,IAAI,CAAC,cAAc,CAAC,WAAW,UAAU,CAAC,YAAY;QACpD,OAAO;IACT;IACA,OAAO,WAAW,SAAS,CAAC,IAAI,0BAA0B;AAC5D;AAKO,SAAS,oBACd,OAAe,EACf,aAAqB,GAAG,EACxB,KAAc;IAEd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;QACE;QACA;QACA,OAAO,SAAS;IAClB,GACA;QAAE,QAAQ;IAAW;AAEzB;AAKO,SAAS,sBACd,IAAO,EACP,aAAqB,GAAG,EACxB,OAAgB;IAEhB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;QACE,SAAS;QACT;QACA;QACA;IACF,GACA;QAAE,QAAQ;IAAW;AAEzB;AAKO,SAAS,eACd,OAAoB,EACpB,cAAwB;IAExB,OAAO,eAAe,QAAQ,CAAC,QAAQ,MAAM;AAC/C;AAKO,eAAe,iBAAiB,OAAoB;IACzD,IAAI;QACF,MAAM,cAAc,QAAQ,OAAO,CAAC,GAAG,CAAC;QAExC,IAAI,aAAa,SAAS,qBAAqB;YAC7C,OAAO,MAAM,QAAQ,IAAI;QAC3B;QAEA,IAAI,aAAa,SAAS,sCAAsC;YAC9D,MAAM,WAAW,MAAM,QAAQ,QAAQ;YACvC,MAAM,OAA4B,CAAC;YACnC,SAAS,OAAO,CAAC,CAAC,OAAO;gBACvB,IAAI,CAAC,IAAI,GAAG;YACd;YACA,OAAO;QACT;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO;IACT;AACF;AAKO,eAAe,iBACpB,OAAoB,EACpB,cAAsB;IAEtB,MAAM,OAAO,MAAM,iBAAiB;IACpC,MAAM,QAAQ,iBAAiB;IAC/B,MAAM,eAAe,CAAA,GAAA,6HAAA,CAAA,yBAAsB,AAAD,EAAE;IAE5C,gBAAgB;IAChB,MAAM,UAAU;QACd,GAAG,CAAA,GAAA,6HAAA,CAAA,oBAAiB,AAAD,GAAG;QACtB,GAAI,gBAAgB,QAAQ,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,SAAS,CAAC,CAAC;IACxD;IAEA,8CAA8C;IAC9C,MAAM,kBAAkB;QAAC;QAAc;QAAmB;KAAkB;IAC5E,gBAAgB,OAAO,CAAC,CAAA;QACtB,MAAM,cAAc,QAAQ,OAAO,CAAC,GAAG,CAAC;QACxC,IAAI,aAAa;YACf,OAAO,CAAC,WAAW,GAAG;QACxB;IACF;IAEA,OAAO;QACL,QAAQ,QAAQ,MAAM;QACtB,KAAK,CAAA,GAAA,6HAAA,CAAA,cAAW,AAAD,EAAE;QACjB;QACA,MAAM,OAAO,KAAK,SAAS,CAAC,QAAQ;QACpC;IACF;AACF;AAKO,eAAe,oBACpB,MAA0B;IAE1B,MAAM,EAAE,MAAM,EAAE,GAAG,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG;IAEvC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,KAAK;YAChC;YACA;YACA;YACA,QAAQ,YAAY,OAAO,CAAC,6HAAA,CAAA,aAAU,CAAC,OAAO;QAChD;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,MAAM,IAAI,MAAM,CAAC,sBAAsB,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;IACrG;AACF;AAKO,eAAe,oBAAoB,QAAkB;IAC1D,IAAI;QACF,MAAM,cAAc,SAAS,OAAO,CAAC,GAAG,CAAC;QAEzC,wBAAwB;QACxB,IAAI,aAAa,SAAS,qBAAqB;YAC7C,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,MAAM;gBAC7B,QAAQ,SAAS,MAAM;gBACvB,YAAY,SAAS,UAAU;YACjC;QACF;QAEA,wBAAwB;QACxB,IAAI,aAAa,SAAS,UAAU;YAClC,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,MAAM;gBAC5B,QAAQ,SAAS,MAAM;gBACvB,YAAY,SAAS,UAAU;gBAC/B,SAAS;oBACP,gBAAgB;gBAClB;YACF;QACF;QAEA,0BAA0B;QAC1B,MAAM,SAAS,MAAM,SAAS,WAAW;QACzC,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,QAAQ;YAC9B,QAAQ,SAAS,MAAM;YACvB,YAAY,SAAS,UAAU;YAC/B,SAAS;gBACP,gBAAgB,eAAe;YACjC;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,OAAO,oBACL,+CACA;IAEJ;AACF;AAKO,eAAe,mBACpB,OAAoB,EACpB,cAAsB,EACtB,iBAA2B;IAAC;IAAO;IAAQ;IAAO;IAAS;CAAS;IAEpE,IAAI;QACF,uBAAuB;QACvB,IAAI,CAAC,eAAe,SAAS,iBAAiB;YAC5C,OAAO,oBACL,CAAC,OAAO,EAAE,QAAQ,MAAM,CAAC,YAAY,CAAC,EACtC,KACA;QAEJ;QAEA,mCAAmC;QACnC,MAAM,eAAe,CAAA,GAAA,6HAAA,CAAA,yBAAsB,AAAD,EAAE;QAC5C,IAAI,cAAc;YAChB,MAAM,QAAQ,iBAAiB;YAC/B,IAAI,CAAC,OAAO;gBACV,OAAO,oBACL,2BACA,KACA;YAEJ;QACF;QAEA,4BAA4B;QAC5B,MAAM,cAAc,MAAM,iBAAiB,SAAS;QAEpD,wBAAwB;QACxB,MAAM,WAAW,MAAM,oBAAoB;QAE3C,6BAA6B;QAC7B,OAAO,MAAM,oBAAoB;IAEnC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO,oBACL,iBAAiB,QAAQ,MAAM,OAAO,GAAG,yBACzC;IAEJ;AACF;AAKO,SAAS,gBACd,OAAoB,EACpB,cAAsB,EACtB,MAA0B;IAE1B,wCAA4C;QAC1C,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,QAAQ,MAAM,CAAC,CAAC,EAAE,QAAQ,GAAG,CAAC,IAAI,EAAE,OAAO,GAAG,EAAE;QACvE,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,OAAO,YAAY,EAAE;QAC3D,QAAQ,GAAG,CAAC,CAAC,gBAAgB,CAAC,EAAE,OAAO,IAAI,CAAC,OAAO,OAAO,IAAI,CAAC;IACjE;AACF"}}, {"offset": {"line": 351, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 357, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/app/api/football/fixtures/route.ts"], "sourcesContent": ["/**\n * Football Fixtures Route\n * Handles football fixtures data operations\n */\n\nimport { NextRequest } from 'next/server';\nimport { handleProxyRequest } from '@/lib/api-utils';\nimport { API_ENDPOINTS, HTTP_METHODS } from '@/lib/api-config';\n\n/**\n * GET /api/football/fixtures\n * Get football fixtures with advanced filtering\n * Authentication: NOT REQUIRED (Public endpoint)\n * \n * Query Parameters:\n * - league?: string - Filter by league ID\n * - team?: string - Filter by team ID (home or away)\n * - date?: string - Filter by date (YYYY-MM-DD)\n * - dateFrom?: string - Filter from date\n * - dateTo?: string - Filter to date\n * - status?: string - Filter by status (live, finished, upcoming)\n * - limit?: number - Limit results (default: 20, max: 100)\n * - offset?: number - Pagination offset\n * \n * Response:\n * {\n *   \"success\": true,\n *   \"data\": [\n *     {\n *       \"id\": \"fixture-id\",\n *       \"externalId\": \"external-api-id\",\n *       \"date\": \"2024-05-24T15:00:00Z\",\n *       \"status\": \"upcoming\",\n *       \"homeTeam\": {\n *         \"id\": \"team-id\",\n *         \"name\": \"Manchester United\",\n *         \"logo\": \"https://...\"\n *       },\n *       \"awayTeam\": {\n *         \"id\": \"team-id\",\n *         \"name\": \"Liverpool\",\n *         \"logo\": \"https://...\"\n *       },\n *       \"league\": {\n *         \"id\": \"league-id\",\n *         \"name\": \"Premier League\"\n *       },\n *       \"score\": {\n *         \"home\": null,\n *         \"away\": null\n *       },\n *       \"venue\": \"Old Trafford\"\n *     }\n *   ],\n *   \"pagination\": {\n *     \"total\": 1000,\n *     \"limit\": 20,\n *     \"offset\": 0\n *   }\n * }\n */\nexport async function GET(request: NextRequest) {\n  return handleProxyRequest(\n    request,\n    API_ENDPOINTS.FOOTBALL.FIXTURES,\n    [HTTP_METHODS.GET]\n  );\n}\n\n/**\n * POST /api/football/fixtures\n * Create new football fixture\n * Requires: Authorization header with Bearer token (Admin role)\n * \n * Request Body:\n * {\n *   \"externalId\": \"external-api-id\",\n *   \"date\": \"2024-05-24T15:00:00Z\",\n *   \"homeTeamId\": \"team-id\",\n *   \"awayTeamId\": \"team-id\",\n *   \"leagueId\": \"league-id\",\n *   \"venue\": \"Stadium Name\"\n * }\n * \n * Response:\n * {\n *   \"success\": true,\n *   \"data\": {\n *     \"id\": \"new-fixture-id\",\n *     \"externalId\": \"external-api-id\",\n *     \"date\": \"2024-05-24T15:00:00Z\",\n *     \"status\": \"upcoming\",\n *     \"homeTeam\": { ... },\n *     \"awayTeam\": { ... },\n *     \"league\": { ... },\n *     \"venue\": \"Stadium Name\",\n *     \"createdAt\": \"2024-05-24T...\"\n *   },\n *   \"message\": \"Fixture created successfully\"\n * }\n */\nexport async function POST(request: NextRequest) {\n  return handleProxyRequest(\n    request,\n    API_ENDPOINTS.FOOTBALL.FIXTURES,\n    [HTTP_METHODS.POST]\n  );\n}\n\n/**\n * PATCH /api/football/fixtures\n * Update existing football fixture\n * Requires: Authorization header with Bearer token (Admin role)\n * \n * Request Body:\n * {\n *   \"externalId\": \"external-api-id\",\n *   \"status\": \"finished\",\n *   \"score\": {\n *     \"home\": 2,\n *     \"away\": 1\n *   }\n * }\n * \n * Response:\n * {\n *   \"success\": true,\n *   \"data\": {\n *     \"id\": \"fixture-id\",\n *     \"status\": \"finished\",\n *     \"score\": {\n *       \"home\": 2,\n *       \"away\": 1\n *     },\n *     \"updatedAt\": \"2024-05-24T...\"\n *   },\n *   \"message\": \"Fixture updated successfully\"\n * }\n */\nexport async function PATCH(request: NextRequest) {\n  return handleProxyRequest(\n    request,\n    API_ENDPOINTS.FOOTBALL.FIXTURES,\n    [HTTP_METHODS.PATCH]\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AAGD;AACA;;;AAsDO,eAAe,IAAI,OAAoB;IAC5C,OAAO,CAAA,GAAA,4HAAA,CAAA,qBAAkB,AAAD,EACtB,SACA,6HAAA,CAAA,gBAAa,CAAC,QAAQ,CAAC,QAAQ,EAC/B;QAAC,6HAAA,CAAA,eAAY,CAAC,GAAG;KAAC;AAEtB;AAkCO,eAAe,KAAK,OAAoB;IAC7C,OAAO,CAAA,GAAA,4HAAA,CAAA,qBAAkB,AAAD,EACtB,SACA,6HAAA,CAAA,gBAAa,CAAC,QAAQ,CAAC,QAAQ,EAC/B;QAAC,6HAAA,CAAA,eAAY,CAAC,IAAI;KAAC;AAEvB;AAgCO,eAAe,MAAM,OAAoB;IAC9C,OAAO,CAAA,GAAA,4HAAA,CAAA,qBAAkB,AAAD,EACtB,SACA,6HAAA,CAAA,gBAAa,CAAC,QAAQ,CAAC,QAAQ,EAC/B;QAAC,6HAAA,CAAA,eAAY,CAAC,KAAK;KAAC;AAExB"}}, {"offset": {"line": 384, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}