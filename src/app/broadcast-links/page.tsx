'use client';

import React, { useState } from 'react';
import {
  Card,
  Table,
  Button,
  Input,
  Select,
  Space,
  Tag,
  Tooltip,
  Popconfirm,
  Row,
  Col,
  Statistic,
  Typography,
  message,
  Badge,
  Avatar,
  Dropdown,
  MenuProps
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  FilterOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  <PERSON>Outlined,
  PlayCircleOutlined,
  GlobalOutlined,
  MoreOutlined,
  ReloadOutlined,
  ExportOutlined
} from '@ant-design/icons';
import { useRouter } from 'next/navigation';
import { ColumnsType } from 'antd/es/table';
import {
  BroadcastLink,
  BroadcastLinkQueryParams,
  BROADCAST_QUALITIES,
  BROADCAST_LANGUAGES,
  BroadcastHelpers,
  MOCK_BROADCAST_LINKS
} from '@/types/broadcast';
import { useBroadcastLinks, useDeleteBroadcastLink, useAvailableFixtures } from '@/hooks/api/broadcast-hooks';
import { FootballQueries } from '@/lib/query-types';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { Option } = Select;

export default function BroadcastLinksPage() {
  const router = useRouter();
  const [queryParams, setQueryParams] = useState<BroadcastLinkQueryParams>({
    page: 1,
    limit: 10,
    sortBy: 'createdAt',
    sortOrder: 'desc'
  });

  // Get fixture ID from URL params if coming from fixtures page
  const [preSelectedFixtureId, setPreSelectedFixtureId] = React.useState<string | null>(null);

  React.useEffect(() => {
    if (typeof window !== 'undefined') {
      const urlParams = new URLSearchParams(window.location.search);
      setPreSelectedFixtureId(urlParams.get('fixtureId'));
    }
  }, []);

  // For development, use mock data
  const broadcastLinksQuery = {
    data: {
      data: MOCK_BROADCAST_LINKS,
      total: MOCK_BROADCAST_LINKS.length,
      page: 1,
      limit: 10,
      totalPages: 1
    },
    isLoading: false,
    error: null,
    refetch: () => Promise.resolve()
  };

  // Fetch available fixtures for filtering
  // Temporarily disabled for development - use mock data
  // const { data: fixturesData, isLoading: fixturesLoading } = useAvailableFixtures();
  const fixturesData = []; // Mock empty data for now
  const fixturesLoading = false;
  const deleteLink = useDeleteBroadcastLink();

  // Statistics calculation
  const statistics = React.useMemo(() => {
    const links = MOCK_BROADCAST_LINKS;
    return {
      total: links.length,
      active: links.filter(l => l.isActive).length,
      inactive: links.filter(l => !l.isActive).length,
      hd: links.filter(l => l.quality === 'HD').length,
      totalViews: links.reduce((sum, l) => sum + (l.viewCount || 0), 0)
    };
  }, []);

  // Handle search
  const handleSearch = (value: string) => {
    setQueryParams(prev => ({ ...prev, search: value, page: 1 }));
  };

  // Handle filter change
  const handleFilterChange = (key: keyof BroadcastLinkQueryParams, value: any) => {
    setQueryParams(prev => ({ ...prev, [key]: value, page: 1 }));
  };

  // Handle delete
  const handleDelete = async (id: string) => {
    try {
      await deleteLink.mutateAsync(id);
      message.success('Broadcast link deleted successfully');
    } catch (error) {
      message.error('Failed to delete broadcast link');
    }
  };

  // Handle table change
  const handleTableChange = (pagination: any, filters: any, sorter: any) => {
    setQueryParams(prev => ({
      ...prev,
      page: pagination.current,
      limit: pagination.pageSize,
      sortBy: sorter.field || 'createdAt',
      sortOrder: sorter.order === 'ascend' ? 'asc' : 'desc'
    }));
  };

  // Table columns
  const columns: ColumnsType<BroadcastLink> = [
    {
      title: 'Fixture',
      key: 'fixture',
      render: (_, record) => {
        // Use fixture data from mock broadcast links
        const fixture = record.fixture;

        if (!fixture) {
          return (
            <div>
              <Text type="secondary">Fixture not found</Text>
              <br />
              <Text type="secondary" className="text-xs">ID: {record.fixtureId}</Text>
            </div>
          );
        }

        return (
          <div>
            <Text strong className="block">
              {fixture.homeTeam} vs {fixture.awayTeam}
            </Text>
            <div className="flex items-center gap-2 text-sm">
              <Text type="secondary">{fixture.league}</Text>
              {fixture.status === 'LIVE' && (
                <Tag color="red" size="small">LIVE</Tag>
              )}
              {fixture.status === 'SCHEDULED' && (
                <Tag color="blue" size="small">
                  {dayjs(fixture.date).format('MMM DD, HH:mm')}
                </Tag>
              )}
            </div>
          </div>
        );
      },
      width: 250,
    },
    {
      title: 'Stream Info',
      key: 'streamInfo',
      render: (_, record) => (
        <div>
          <div className="flex items-center gap-2 mb-1">
            <LinkOutlined />
            <Text strong>{record.title || 'Untitled Stream'}</Text>
          </div>
          <div className="flex items-center gap-2">
            <Tag color={BroadcastHelpers.getQualityColor(record.quality)}>
              {record.quality}
            </Tag>
            <Tag icon={<GlobalOutlined />}>
              {record.language}
            </Tag>
          </div>
        </div>
      ),
      width: 200,
    },
    {
      title: 'Status',
      dataIndex: 'isActive',
      key: 'status',
      render: (isActive: boolean, record) => (
        <div>
          <Badge
            status={isActive ? 'success' : 'default'}
            text={isActive ? 'Active' : 'Inactive'}
          />
          <br />
          <Tag color={BroadcastHelpers.getStatusColor(record.status)} className="mt-1">
            {record.status.toUpperCase()}
          </Tag>
        </div>
      ),
      width: 100,
    },
    {
      title: 'Performance',
      key: 'performance',
      render: (_, record) => (
        <div>
          <div className="flex items-center gap-2">
            <EyeOutlined />
            <Text>{BroadcastHelpers.formatViewCount(record.viewCount || 0)} views</Text>
          </div>
          {record.rating && (
            <div className="flex items-center gap-2 mt-1">
              <Text type="secondary">★ {record.rating.toFixed(1)}</Text>
            </div>
          )}
        </div>
      ),
      width: 120,
    },
    {
      title: 'Created',
      key: 'created',
      render: (_, record) => (
        <div>
          <Text className="block">{new Date(record.createdAt).toLocaleDateString()}</Text>
          <Text type="secondary" className="text-sm">by {record.createdBy}</Text>
        </div>
      ),
      width: 120,
      sorter: true,
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => {
        const menuItems: MenuProps['items'] = [
          {
            key: 'view',
            label: 'View Details',
            icon: <EyeOutlined />,
            onClick: () => router.push(`/broadcast-links/${record.id}`)
          },
          {
            key: 'edit',
            label: 'Edit',
            icon: <EditOutlined />,
            onClick: () => router.push(`/broadcast-links/${record.id}/edit`)
          },
          {
            type: 'divider'
          },
          {
            key: 'delete',
            label: 'Delete',
            icon: <DeleteOutlined />,
            danger: true,
            onClick: () => handleDelete(record.id)
          }
        ];

        return (
          <Dropdown menu={{ items: menuItems }} trigger={['click']}>
            <Button icon={<MoreOutlined />} />
          </Dropdown>
        );
      },
      width: 80,
      fixed: 'right',
    },
  ];

  return (
    <div>
      {/* Page Header */}
      <div className="mb-6">
        <Title level={2}>
          <PlayCircleOutlined className="mr-2" />
          Broadcast Links Management
        </Title>
        <Text type="secondary">
          Manage broadcast links for football fixtures with quality control and language support
        </Text>
      </div>

      {/* Statistics Cards */}
      <Row gutter={16} className="mb-6">
        <Col xs={12} sm={6}>
          <Card>
            <Statistic
              title="Total Links"
              value={statistics.total}
              prefix={<LinkOutlined />}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card>
            <Statistic
              title="Active Links"
              value={statistics.active}
              prefix={<PlayCircleOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card>
            <Statistic
              title="HD Quality"
              value={statistics.hd}
              suffix={`/ ${statistics.total}`}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card>
            <Statistic
              title="Total Views"
              value={BroadcastHelpers.formatViewCount(statistics.totalViews)}
              prefix={<EyeOutlined />}
            />
          </Card>
        </Col>
      </Row>

      {/* Filters and Actions */}
      <Card className="mb-4">
        <Row gutter={16} align="middle">
          <Col xs={24} sm={8} md={6}>
            <Input
              placeholder="Search broadcast links..."
              prefix={<SearchOutlined />}
              onChange={(e) => handleSearch(e.target.value)}
              allowClear
            />
          </Col>
          <Col xs={12} sm={4} md={3}>
            <Select
              placeholder="Quality"
              allowClear
              onChange={(value) => handleFilterChange('quality', value)}
              className="w-full"
            >
              {BROADCAST_QUALITIES.map(quality => (
                <Option key={quality} value={quality}>
                  <Tag color={BroadcastHelpers.getQualityColor(quality)}>
                    {quality}
                  </Tag>
                </Option>
              ))}
            </Select>
          </Col>
          <Col xs={12} sm={4} md={3}>
            <Select
              placeholder="Language"
              allowClear
              onChange={(value) => handleFilterChange('language', value)}
              className="w-full"
            >
              {BROADCAST_LANGUAGES.slice(0, 8).map(language => (
                <Option key={language} value={language}>
                  {language}
                </Option>
              ))}
            </Select>
          </Col>
          <Col xs={12} sm={4} md={3}>
            <Select
              placeholder="Fixture"
              allowClear
              loading={fixturesLoading}
              onChange={(value) => handleFilterChange('fixtureId', value)}
              className="w-full"
              showSearch
              filterOption={(input, option) =>
                option?.children?.toString().toLowerCase().includes(input.toLowerCase()) ?? false
              }
              defaultValue={preSelectedFixtureId}
            >
              {MOCK_BROADCAST_LINKS.map(link => (
                link.fixture && (
                  <Option key={link.fixtureId} value={link.fixtureId}>
                    {link.fixture.homeTeam} vs {link.fixture.awayTeam}
                  </Option>
                )
              ))}
            </Select>
          </Col>
          <Col xs={12} sm={4} md={3}>
            <Select
              placeholder="Status"
              allowClear
              onChange={(value) => handleFilterChange('isActive', value)}
              className="w-full"
            >
              <Option value={true}>Active</Option>
              <Option value={false}>Inactive</Option>
            </Select>
          </Col>
          <Col xs={12} sm={4} md={6} className="text-right">
            <Space>
              <Button
                icon={<ReloadOutlined />}
                onClick={() => broadcastLinksQuery.refetch()}
                loading={broadcastLinksQuery.isLoading}
              >
                Refresh
              </Button>
              <Button
                icon={<ExportOutlined />}
                onClick={() => message.info('Export functionality coming soon')}
              >
                Export
              </Button>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => router.push('/broadcast-links/create')}
              >
                Add Broadcast Link
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* Broadcast Links Table */}
      <Card>
        <Table
          columns={columns}
          dataSource={broadcastLinksQuery.data?.data || []}
          rowKey="id"
          loading={broadcastLinksQuery.isLoading}
          pagination={{
            current: queryParams.page,
            pageSize: queryParams.limit,
            total: broadcastLinksQuery.data?.total || 0,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} of ${total} broadcast links`,
          }}
          onChange={handleTableChange}
          scroll={{ x: 1200 }}
        />
      </Card>
    </div>
  );
}
