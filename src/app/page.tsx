'use client';

import React from 'react';
import {
  Card,
  Button,
  Space,
  Typography,
  Alert,
  Row,
  Col,
  Statistic,
} from 'antd';
import {
  DashboardOutlined,
  UserOutlined,
  TrophyOutlined,
  CalendarOutlined,
  LinkOutlined,
  Bar<PERSON><PERSON>Outlined,
  ApiOutlined,
  SettingOutlined,
  ArrowUpOutlined,
} from '@ant-design/icons';
import { AppLayout } from '@/components/layout';

const { Title, Text, Paragraph } = Typography;

export default function Home() {
  return (
    <div>
      {/* Page Header */}
      <div className="mb-6 flex justify-between items-start">
        <div>
          <Title level={2}>
            <DashboardOutlined className="mr-2" />
            Dashboard
          </Title>
          <Text type="secondary">
            Welcome to APISportsGame CMS - Your central hub for managing football data and broadcast links
          </Text>
        </div>
        <Space>
          <Button icon={<SettingOutlined />}>
            Settings
          </Button>
        </Space>
      </div>
      {/* Welcome Alert */}
      <Alert
        message="Welcome to APISportsGame CMS!"
        description="This is your central dashboard for managing football leagues, teams, fixtures, broadcast links, and system users. Navigate using the sidebar menu to access different sections."
        type="success"
        showIcon
        className="mb-6"
      />

      {/* Stats Overview */}
      <Row gutter={16} className="mb-6">
        <Col xs={12} sm={6}>
          <Card>
            <Statistic
              title="Total Leagues"
              value={25}
              prefix={<TrophyOutlined />}
              suffix={<ArrowUpOutlined style={{ color: '#3f8600' }} />}
              valueStyle={{ color: '#3f8600' }}
            />
            <div style={{ marginTop: '8px', fontSize: '12px', color: '#666' }}>
              Active football leagues
            </div>
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card>
            <Statistic
              title="Teams"
              value="500+"
              prefix={<UserOutlined />}
              suffix={<ArrowUpOutlined style={{ color: '#3f8600' }} />}
              valueStyle={{ color: '#3f8600' }}
            />
            <div style={{ marginTop: '8px', fontSize: '12px', color: '#666' }}>
              Registered teams
            </div>
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card>
            <Statistic
              title="Fixtures"
              value={1250}
              prefix={<CalendarOutlined />}
              suffix={<ArrowUpOutlined style={{ color: '#3f8600' }} />}
              valueStyle={{ color: '#3f8600' }}
            />
            <div style={{ marginTop: '8px', fontSize: '12px', color: '#666' }}>
              Total fixtures
            </div>
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card>
            <Statistic
              title="Broadcast Links"
              value={850}
              prefix={<LinkOutlined />}
              suffix={<ArrowUpOutlined style={{ color: '#3f8600' }} />}
              valueStyle={{ color: '#3f8600' }}
            />
            <div style={{ marginTop: '8px', fontSize: '12px', color: '#666' }}>
              Active links
            </div>
          </Card>
        </Col>
      </Row>

      {/* Main Content */}
      <Row gutter={16} className="mb-6">
        <Col xs={24} md={12}>
          <Space direction="vertical" style={{ width: '100%' }} size="large">
            <Card title="Quick Actions">
              <Space direction="vertical" style={{ width: '100%' }}>
                <Button type="primary" icon={<CalendarOutlined />} block>
                  Sync Latest Fixtures
                </Button>
                <Button icon={<LinkOutlined />} block>
                  Add Broadcast Link
                </Button>
                <Button icon={<UserOutlined />} block>
                  Create System User
                </Button>
                <Button icon={<BarChartOutlined />} block>
                  View Reports
                </Button>
              </Space>
            </Card>

            <Card title="System Overview">
              <Space direction="vertical" style={{ width: '100%' }}>
                <div>
                  <Text strong>API Status:</Text>
                  <Text style={{ color: '#52c41a', marginLeft: '8px' }}>● Online</Text>
                </div>
                <div>
                  <Text strong>Database:</Text>
                  <Text style={{ color: '#52c41a', marginLeft: '8px' }}>● Connected</Text>
                </div>
                <div>
                  <Text strong>External API:</Text>
                  <Text style={{ color: '#52c41a', marginLeft: '8px' }}>● Syncing</Text>
                </div>
                <div>
                  <Text strong>Last Sync:</Text>
                  <Text style={{ marginLeft: '8px' }}>2 minutes ago</Text>
                </div>
              </Space>
            </Card>
          </Space>
        </Col>
        <Col xs={24} md={12}>
          <Space direction="vertical" style={{ width: '100%' }} size="large">
            <Card title="Recent Activity">
              <Space direction="vertical" style={{ width: '100%' }}>
                <div>
                  <Text strong>Fixture sync completed</Text>
                  <br />
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    2 minutes ago
                  </Text>
                </div>
                <div>
                  <Text strong>New broadcast link added</Text>
                  <br />
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    5 minutes ago
                  </Text>
                </div>
                <div>
                  <Text strong>User John Doe logged in</Text>
                  <br />
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    10 minutes ago
                  </Text>
                </div>
                <div>
                  <Text strong>System backup completed</Text>
                  <br />
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    1 hour ago
                  </Text>
                </div>
              </Space>
            </Card>

            <Card title="Quick Links">
              <Space direction="vertical" style={{ width: '100%' }}>
                <Button type="link" href="/broadcast-links" icon={<LinkOutlined />}>
                  Broadcast Management
                </Button>
                <Button type="link" href="/users/system" icon={<UserOutlined />}>
                  User Management
                </Button>
                <Button type="link" href="/football/leagues" icon={<TrophyOutlined />}>
                  Football Leagues
                </Button>
                <Button type="link" href="/football/fixtures" icon={<CalendarOutlined />}>
                  Fixtures Management
                </Button>
              </Space>
            </Card>
          </Space>
        </Col>
      </Row>

      {/* Getting Started */}
      <Card title="Getting Started" style={{ marginTop: '24px' }}>
        <Paragraph>
          Welcome to the APISportsGame CMS! This dashboard provides you with a comprehensive overview
          of your football data management system. Here's what you can do:
        </Paragraph>

        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '16px' }}>
          <div>
            <Title level={5}>
              <TrophyOutlined /> Football Data Management
            </Title>
            <Text>
              Manage leagues, teams, and fixtures. Sync data from external APIs and
              keep your football database up to date.
            </Text>
          </div>

          <div>
            <Title level={5}>
              <LinkOutlined /> Broadcast Links
            </Title>
            <Text>
              Add and manage broadcast links for fixtures. Control quality settings
              and ensure reliable streaming sources.
            </Text>
          </div>

          <div>
            <Title level={5}>
              <UserOutlined /> User System
            </Title>
            <Text>
              Manage system users, roles, and permissions. Control access to different
              parts of the CMS based on user roles.
            </Text>
          </div>

          <div>
            <Title level={5}>
              <BarChartOutlined /> System Monitoring
            </Title>
            <Text>
              Monitor API health, view system logs, and track performance metrics
              to ensure optimal system operation.
            </Text>
          </div>
        </div>
      </Card>
    </div>
  );
}
