/**
 * Football Teams Management Page
 * Comprehensive teams management with CRUD operations
 */

'use client';

import React, { useState } from 'react';
import {
  Card,
  Table,
  Button,
  Input,
  Select,
  Space,
  Tag,
  Tooltip,
  Popconfirm,
  Row,
  Col,
  Statistic,
  Typography,
  message,
  Badge,
  Avatar,
  Dropdown,
  MenuProps,
  Modal,
  Image,
  Progress
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  FilterOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  TeamOutlined,
  GlobalOutlined,
  MoreOutlined,
  ReloadOutlined,
  ExportOutlined,
  TrophyOutlined,
  HomeOutlined,
  CalendarOutlined
} from '@ant-design/icons';
import { useRouter } from 'next/navigation';
import { ColumnsType } from 'antd/es/table';
import { FootballQueries } from '@/lib/query-types';
import {
  useTeams,
  useLeagues,
  useCreateTeam,
  useUpdateTeam,
  useDeleteTeam
} from '@/hooks/api/football-hooks';
import { TeamForm } from '@/components/football';

const { Title, Text } = Typography;
const { Option } = Select;

// Mock data for development
const MOCK_TEAMS: FootballQueries.Team[] = [
  {
    id: '1',
    name: 'Manchester United',
    logo: 'https://logos-world.net/wp-content/uploads/2020/06/Manchester-United-Logo.png',
    country: 'England',
    leagueId: '1',
    league: { id: '1', name: 'Premier League', country: 'England', season: '2024/25', isActive: true, createdAt: '', updatedAt: '' },
    statistics: {
      played: 20,
      wins: 12,
      draws: 4,
      losses: 4,
      goalsFor: 35,
      goalsAgainst: 22,
      points: 40
    },
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z'
  },
  {
    id: '2',
    name: 'Real Madrid',
    logo: 'https://logos-world.net/wp-content/uploads/2020/06/Real-Madrid-Logo.png',
    country: 'Spain',
    leagueId: '2',
    league: { id: '2', name: 'La Liga', country: 'Spain', season: '2024/25', isActive: true, createdAt: '', updatedAt: '' },
    statistics: {
      played: 18,
      wins: 14,
      draws: 3,
      losses: 1,
      goalsFor: 42,
      goalsAgainst: 15,
      points: 45
    },
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z'
  },
  {
    id: '3',
    name: 'Bayern Munich',
    logo: 'https://logos-world.net/wp-content/uploads/2020/06/Bayern-Munich-Logo.png',
    country: 'Germany',
    leagueId: '3',
    league: { id: '3', name: 'Bundesliga', country: 'Germany', season: '2024/25', isActive: true, createdAt: '', updatedAt: '' },
    statistics: {
      played: 17,
      wins: 13,
      draws: 2,
      losses: 2,
      goalsFor: 48,
      goalsAgainst: 18,
      points: 41
    },
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z'
  },
  {
    id: '4',
    name: 'AC Milan',
    country: 'Italy',
    leagueId: '4',
    league: { id: '4', name: 'Serie A', country: 'Italy', season: '2024/25', isActive: true, createdAt: '', updatedAt: '' },
    statistics: {
      played: 19,
      wins: 10,
      draws: 6,
      losses: 3,
      goalsFor: 28,
      goalsAgainst: 20,
      points: 36
    },
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z'
  },
  {
    id: '5',
    name: 'Paris Saint-Germain',
    country: 'France',
    leagueId: '5',
    league: { id: '5', name: 'Ligue 1', country: 'France', season: '2023/24', isActive: false, createdAt: '', updatedAt: '' },
    statistics: {
      played: 22,
      wins: 16,
      draws: 4,
      losses: 2,
      goalsFor: 55,
      goalsAgainst: 25,
      points: 52
    },
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z'
  }
];

export default function TeamsPage() {
  const router = useRouter();
  const [queryParams, setQueryParams] = useState<FootballQueries.TeamQueryParams>({
    page: 1,
    limit: 10,
    sortBy: 'name',
    sortOrder: 'asc'
  });
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [editingTeam, setEditingTeam] = useState<FootballQueries.Team | null>(null);

  // For development, use mock data
  const teamsQuery = {
    data: {
      data: MOCK_TEAMS,
      total: MOCK_TEAMS.length,
      page: 1,
      limit: 10,
      totalPages: 1
    },
    isLoading: false,
    error: null,
    refetch: () => Promise.resolve()
  };

  const { data: leaguesData } = useLeagues({ limit: 100 });
  const createTeam = useCreateTeam();
  const updateTeam = useUpdateTeam();
  const deleteTeam = useDeleteTeam();
