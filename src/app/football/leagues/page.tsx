/**
 * Football Leagues Management Page
 * Comprehensive leagues management with CRUD operations
 */

'use client';

import React, { useState } from 'react';
import {
  Card,
  Table,
  Button,
  Input,
  Select,
  Space,
  Tag,
  Tooltip,
  Popconfirm,
  Row,
  Col,
  Statistic,
  Typography,
  message,
  Badge,
  Avatar,
  Dropdown,
  MenuProps,
  Modal,
  Image
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  FilterOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  TrophyOutlined,
  GlobalOutlined,
  MoreOutlined,
  ReloadOutlined,
  ExportOutlined,
  CalendarOutlined
} from '@ant-design/icons';
import { useRouter } from 'next/navigation';
import { ColumnsType } from 'antd/es/table';
import { FootballQueries } from '@/lib/query-types';
import {
  useLeagues,
  useCreateLeague,
  useUpdateLeague,
  useDeleteLeague
} from '@/hooks/api/football-hooks';
import { LeagueForm } from '@/components/football';

const { Title, Text } = Typography;
const { Option } = Select;

// Mock data for development
const MOCK_LEAGUES: FootballQueries.League[] = [
  {
    id: '1',
    name: 'Premier League',
    country: 'England',
    logo: 'https://logos-world.net/wp-content/uploads/2020/06/Premier-League-Logo.png',
    season: '2024/25',
    isActive: true,
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z'
  },
  {
    id: '2',
    name: 'La Liga',
    country: 'Spain',
    logo: 'https://logos-world.net/wp-content/uploads/2020/06/La-Liga-Logo.png',
    season: '2024/25',
    isActive: true,
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z'
  },
  {
    id: '3',
    name: 'Bundesliga',
    country: 'Germany',
    logo: 'https://logos-world.net/wp-content/uploads/2020/06/Bundesliga-Logo.png',
    season: '2024/25',
    isActive: true,
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z'
  },
  {
    id: '4',
    name: 'Serie A',
    country: 'Italy',
    season: '2024/25',
    isActive: true,
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z'
  },
  {
    id: '5',
    name: 'Ligue 1',
    country: 'France',
    season: '2023/24',
    isActive: false,
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z'
  }
];

export default function LeaguesPage() {
  const router = useRouter();
  const [queryParams, setQueryParams] = useState<FootballQueries.LeagueQueryParams>({
    page: 1,
    limit: 10,
    sortBy: 'name',
    sortOrder: 'asc'
  });
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [editingLeague, setEditingLeague] = useState<FootballQueries.League | null>(null);

  // For development, use mock data
  const leaguesQuery = {
    data: {
      data: MOCK_LEAGUES,
      total: MOCK_LEAGUES.length,
      page: 1,
      limit: 10,
      totalPages: 1
    },
    isLoading: false,
    error: null,
    refetch: () => Promise.resolve()
  };

  const createLeague = useCreateLeague();
  const updateLeague = useUpdateLeague();
  const deleteLeague = useDeleteLeague();

  // Statistics calculation
  const statistics = React.useMemo(() => {
    const leagues = MOCK_LEAGUES;
    return {
      total: leagues.length,
      active: leagues.filter(l => l.isActive).length,
      inactive: leagues.filter(l => !l.isActive).length,
      countries: new Set(leagues.map(l => l.country)).size
    };
  }, []);

  // Handle search
  const handleSearch = (value: string) => {
    setQueryParams(prev => ({ ...prev, query: value, page: 1 }));
  };

  // Handle filter change
  const handleFilterChange = (key: keyof FootballQueries.LeagueQueryParams, value: any) => {
    setQueryParams(prev => ({ ...prev, [key]: value, page: 1 }));
  };

  // Handle create league
  const handleCreateLeague = async (values: FootballQueries.CreateLeagueRequest) => {
    try {
      await createLeague.mutateAsync(values);
      message.success('League created successfully');
      setIsCreateModalOpen(false);
    } catch (error) {
      message.error('Failed to create league');
    }
  };

  // Handle update league
  const handleUpdateLeague = async (values: FootballQueries.UpdateLeagueRequest) => {
    if (!editingLeague) return;

    try {
      await updateLeague.mutateAsync({ id: editingLeague.id, data: values });
      message.success('League updated successfully');
      setEditingLeague(null);
    } catch (error) {
      message.error('Failed to update league');
    }
  };

  // Handle delete
  const handleDelete = async (id: string) => {
    try {
      await deleteLeague.mutateAsync(id);
      message.success('League deleted successfully');
    } catch (error) {
      message.error('Failed to delete league');
    }
  };

  // Handle table change
  const handleTableChange = (pagination: any, filters: any, sorter: any) => {
    setQueryParams(prev => ({
      ...prev,
      page: pagination.current,
      limit: pagination.pageSize,
      sortBy: sorter.field || 'name',
      sortOrder: sorter.order === 'ascend' ? 'asc' : 'desc'
    }));
  };
