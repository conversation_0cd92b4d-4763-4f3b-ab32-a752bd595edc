/**
 * Football Fixtures Management Page
 * Comprehensive fixtures management with CRUD operations and sync management
 */

'use client';

import React, { useState } from 'react';
import {
  Card,
  Table,
  Button,
  Input,
  Select,
  Space,
  Tag,
  Tooltip,
  Popconfirm,
  Row,
  Col,
  Statistic,
  Typography,
  message,
  Badge,
  Avatar,
  Dropdown,
  MenuProps,
  Modal,
  DatePicker,
  Progress,
  Alert,
  Divider
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  FilterOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  CalendarOutlined,
  GlobalOutlined,
  MoreOutlined,
  ReloadOutlined,
  ExportOutlined,
  TrophyOutlined,
  TeamOutlined,
  SyncOutlined,
  PlayCircleOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  StopOutlined,
  WarningOutlined
} from '@ant-design/icons';
import { useRouter } from 'next/navigation';
import { ColumnsType } from 'antd/es/table';
import { FootballQueries } from '@/lib/query-types';
import {
  useFixtures,
  useLeagues,
  useTeams,
  useCreateFixture,
  useUpdateFixture,
  useDeleteFixture,
  useDailySync,
  useSyncStatus
} from '@/hooks/api/football-hooks';
import { FixtureForm } from '@/components/football';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { Option } = Select;
const { RangePicker } = DatePicker;
