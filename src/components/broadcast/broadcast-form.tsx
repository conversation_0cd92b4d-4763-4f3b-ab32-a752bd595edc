'use client';

import React, { useState, useEffect } from 'react';
import {
  Form,
  Input,
  Select,
  Button,
  Card,
  Space,
  Alert,
  Tag,
  Divider,
  Row,
  Col,
  Typography,
  Switch,
  Rate,
  message
} from 'antd';
import {
  LinkOutlined,
  PlayCircleOutlined,
  GlobalOutlined,
  TagsOutlined,
  SaveOutlined,
  ReloadOutlined,
  CalendarOutlined,
  TeamOutlined,
  TrophyOutlined
} from '@ant-design/icons';
import {
  BroadcastLink,
  CreateBroadcastLinkRequest,
  UpdateBroadcastLinkRequest,
  BroadcastLinkFormData,
  BROADCAST_QUALITIES,
  BROADCAST_LANGUAGES,
  BROADCAST_VALIDATION,
  BroadcastHelpers
} from '@/types/broadcast';
import { FootballQueries } from '@/lib/query-types';
import { useAvailableFixtures } from '@/hooks/api/broadcast-hooks';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { TextArea } = Input;
const { Option } = Select;

interface BroadcastFormProps {
  initialData?: Partial<BroadcastLink>;
  onSubmit: (data: CreateBroadcastLinkRequest | UpdateBroadcastLinkRequest) => Promise<void>;
  onCancel?: () => void;
  loading?: boolean;
  mode?: 'create' | 'edit';
  fixtureId?: string; // Pre-selected fixture ID
}

export function BroadcastForm({
  initialData,
  onSubmit,
  onCancel,
  loading = false,
  mode = 'create',
  fixtureId
}: BroadcastFormProps) {
  const [form] = Form.useForm();
  const [urlValid, setUrlValid] = useState<boolean | null>(null);
  const [selectedFixture, setSelectedFixture] = useState<string | undefined>(
    initialData?.fixtureId || fixtureId
  );

  // Fetch available fixtures for selection
  // Temporarily disabled for development - use mock data
  // const { data: fixturesData, isLoading: fixturesLoading } = useAvailableFixtures();
  const fixturesData = []; // Mock empty data for now
  const fixturesLoading = false;
  const fixtures = fixturesData || [];

  // Initialize form with data
  useEffect(() => {
    if (initialData) {
      form.setFieldsValue({
        fixtureId: initialData.fixtureId,
        url: initialData.url,
        title: initialData.title || '',
        description: initialData.description || '',
        quality: initialData.quality || 'HD',
        language: initialData.language || 'English',
        isActive: initialData.isActive ?? true,
        tags: initialData.tags || []
      });
      setSelectedFixture(initialData.fixtureId);
    }
  }, [initialData, form]);

  // Validate URL
  const validateUrl = (url: string) => {
    const isValid = BroadcastHelpers.isValidUrl(url);
    setUrlValid(isValid);
    return isValid;
  };

  // Handle form submission
  const handleSubmit = async (values: BroadcastLinkFormData) => {
    try {
      const submitData = mode === 'create'
        ? {
          fixtureId: values.fixtureId,
          url: values.url,
          title: values.title,
          description: values.description,
          quality: values.quality,
          language: values.language,
          tags: values.tags
        } as CreateBroadcastLinkRequest
        : {
          url: values.url,
          title: values.title,
          description: values.description,
          quality: values.quality,
          language: values.language,
          isActive: values.isActive,
          tags: values.tags
        } as UpdateBroadcastLinkRequest;

      await onSubmit(submitData);
      message.success(`Broadcast link ${mode === 'create' ? 'created' : 'updated'} successfully`);

      if (mode === 'create') {
        form.resetFields();
        setUrlValid(null);
        setSelectedFixture(undefined);
      }
    } catch (error) {
      message.error(`Failed to ${mode} broadcast link`);
    }
  };

  // Auto-generate title when fixture or quality changes
  const handleFixtureOrQualityChange = () => {
    const fixtureId = form.getFieldValue('fixtureId');
    const quality = form.getFieldValue('quality');
    const currentTitle = form.getFieldValue('title');

    if (fixtureId && quality && !currentTitle) {
      const fixture = fixtures.find(f => f.externalId === fixtureId);
      if (fixture) {
        const generatedTitle = `${fixture.homeTeam.name} vs ${fixture.awayTeam.name} - ${quality}`;
        form.setFieldValue('title', generatedTitle);
      }
    }
  };

  const selectedFixtureData = fixtures.find(f => f.externalId === selectedFixture);

  return (
    <Card>
      <Title level={4}>
        <PlayCircleOutlined className="mr-2" />
        {mode === 'create' ? 'Create Broadcast Link' : 'Edit Broadcast Link'}
      </Title>

      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        initialValues={{
          quality: 'HD',
          language: 'English',
          isActive: true,
          tags: []
        }}
      >
        {/* Fixture Selection */}
        {mode === 'create' && (
          <Form.Item
            name="fixtureId"
            label="Fixture"
            rules={[{ required: true, message: BROADCAST_VALIDATION.fixtureId.message }]}
          >
            <Select
              placeholder="Select a fixture"
              showSearch
              loading={fixturesLoading}
              filterOption={(input, option) =>
                option?.children?.toString().toLowerCase().includes(input.toLowerCase()) ?? false
              }
              onChange={(value) => {
                setSelectedFixture(value);
                handleFixtureOrQualityChange();
              }}
              optionLabelProp="label"
            >
              {fixtures.map(fixture => (
                <Option
                  key={fixture.externalId}
                  value={fixture.externalId}
                  label={`${fixture.homeTeam.name} vs ${fixture.awayTeam.name}`}
                >
                  <div className="py-2">
                    <div className="flex items-center justify-between mb-1">
                      <div className="flex items-center gap-2">
                        <TeamOutlined />
                        <Text strong>
                          {fixture.homeTeam.name} vs {fixture.awayTeam.name}
                        </Text>
                      </div>
                      <div className="flex items-center gap-2">
                        {fixture.status === 'live' && (
                          <Tag color="red" icon={<PlayCircleOutlined />}>LIVE</Tag>
                        )}
                        {fixture.status === 'scheduled' && (
                          <Tag color="blue" icon={<CalendarOutlined />}>
                            {dayjs(fixture.date).format('MMM DD, HH:mm')}
                          </Tag>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center gap-2 text-sm">
                      <TrophyOutlined />
                      <Text type="secondary">{fixture.league.name}</Text>
                      <Text type="secondary">•</Text>
                      <Text type="secondary">{fixture.league.country}</Text>
                      {fixture.venue && (
                        <>
                          <Text type="secondary">•</Text>
                          <Text type="secondary">{fixture.venue}</Text>
                        </>
                      )}
                    </div>
                  </div>
                </Option>
              ))}
            </Select>
          </Form.Item>
        )}

        {/* Selected Fixture Info */}
        {selectedFixtureData && (
          <Alert
            message={
              <div className="flex items-center justify-between">
                <div>
                  <Text strong>
                    {selectedFixtureData.homeTeam.name} vs {selectedFixtureData.awayTeam.name}
                  </Text>
                  <br />
                  <Text type="secondary">
                    {selectedFixtureData.league.name} • {selectedFixtureData.league.country}
                  </Text>
                  {selectedFixtureData.venue && (
                    <>
                      <Text type="secondary"> • {selectedFixtureData.venue}</Text>
                    </>
                  )}
                </div>
                <div className="flex items-center gap-2">
                  {selectedFixtureData.status === 'live' && (
                    <Tag color="red" icon={<PlayCircleOutlined />}>LIVE</Tag>
                  )}
                  {selectedFixtureData.status === 'scheduled' && (
                    <Tag color="blue" icon={<CalendarOutlined />}>
                      {dayjs(selectedFixtureData.date).format('MMM DD, HH:mm')}
                    </Tag>
                  )}
                </div>
              </div>
            }
            type="info"
            showIcon
            className="mb-4"
          />
        )}

        <Row gutter={16}>
          <Col xs={24} md={12}>
            {/* URL */}
            <Form.Item
              name="url"
              label="Stream URL"
              rules={[
                { required: true, message: BROADCAST_VALIDATION.url.message },
                {
                  validator: (_, value) => {
                    if (!value || validateUrl(value)) {
                      return Promise.resolve();
                    }
                    return Promise.reject(new Error(BROADCAST_VALIDATION.url.message));
                  }
                }
              ]}
            >
              <Input
                prefix={<LinkOutlined />}
                placeholder="https://stream.example.com/match"
                onChange={(e) => validateUrl(e.target.value)}
                status={urlValid === false ? 'error' : urlValid === true ? 'success' : undefined}
              />
            </Form.Item>
          </Col>

          <Col xs={24} md={12}>
            {/* Quality */}
            <Form.Item
              name="quality"
              label="Quality"
              rules={[{ required: true, message: BROADCAST_VALIDATION.quality.message }]}
            >
              <Select onChange={handleFixtureOrQualityChange}>
                {BROADCAST_QUALITIES.map(quality => (
                  <Option key={quality} value={quality}>
                    <Tag color={BroadcastHelpers.getQualityColor(quality)}>
                      {quality}
                    </Tag>
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col xs={24} md={12}>
            {/* Title */}
            <Form.Item
              name="title"
              label="Title (Optional)"
              rules={[
                { min: BROADCAST_VALIDATION.title.minLength, message: BROADCAST_VALIDATION.title.message },
                { max: BROADCAST_VALIDATION.title.maxLength, message: BROADCAST_VALIDATION.title.message }
              ]}
            >
              <Input
                placeholder="Auto-generated from fixture and quality"
                suffix={
                  <Button
                    type="text"
                    size="small"
                    icon={<ReloadOutlined />}
                    onClick={handleFixtureOrQualityChange}
                    title="Auto-generate title"
                  />
                }
              />
            </Form.Item>
          </Col>

          <Col xs={24} md={12}>
            {/* Language */}
            <Form.Item
              name="language"
              label="Language"
              rules={[{ required: true, message: BROADCAST_VALIDATION.language.message }]}
            >
              <Select
                showSearch
                placeholder="Select language"
                filterOption={(input, option) =>
                  option?.children?.toString().toLowerCase().includes(input.toLowerCase()) ?? false
                }
              >
                {BROADCAST_LANGUAGES.map(language => (
                  <Option key={language} value={language}>
                    <GlobalOutlined className="mr-2" />
                    {language}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>

        {/* Description */}
        <Form.Item
          name="description"
          label="Description (Optional)"
          rules={[
            { max: BROADCAST_VALIDATION.description.maxLength, message: BROADCAST_VALIDATION.description.message }
          ]}
        >
          <TextArea
            rows={3}
            placeholder="Additional information about the stream..."
            showCount
            maxLength={BROADCAST_VALIDATION.description.maxLength}
          />
        </Form.Item>

        {/* Tags */}
        <Form.Item
          name="tags"
          label="Tags (Optional)"
        >
          <Select
            mode="tags"
            placeholder="Add tags (press Enter to add)"
            tokenSeparators={[',']}
            suffixIcon={<TagsOutlined />}
          >
            <Option value="hd">HD</Option>
            <Option value="mobile">Mobile</Option>
            <Option value="live">Live</Option>
            <Option value="free">Free</Option>
            <Option value="premium">Premium</Option>
          </Select>
        </Form.Item>

        {/* Active Status (Edit mode only) */}
        {mode === 'edit' && (
          <Form.Item
            name="isActive"
            label="Status"
            valuePropName="checked"
          >
            <Switch
              checkedChildren="Active"
              unCheckedChildren="Inactive"
            />
          </Form.Item>
        )}

        <Divider />

        {/* Form Actions */}
        <Form.Item>
          <Space>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              icon={<SaveOutlined />}
            >
              {mode === 'create' ? 'Create Broadcast Link' : 'Update Broadcast Link'}
            </Button>
            {onCancel && (
              <Button onClick={onCancel}>
                Cancel
              </Button>
            )}
          </Space>
        </Form.Item>
      </Form>
    </Card>
  );
}

export default BroadcastForm;
