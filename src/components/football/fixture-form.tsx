/**
 * Fixture Form Component
 * Form for creating and editing football fixtures
 */

'use client';

import React from 'react';
import {
  Form,
  Input,
  Select,
  Button,
  Card,
  Row,
  Col,
  Typography,
  Space,
  DatePicker,
  InputNumber,
  message
} from 'antd';
import {
  CalendarOutlined,
  SaveOutlined,
  CloseOutlined,
  TeamOutlined,
  TrophyOutlined,
  HomeOutlined,
  NumberOutlined
} from '@ant-design/icons';
import { FootballQueries } from '@/lib/query-types';
import { useLeagues, useTeams } from '@/hooks/api/football-hooks';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { Option } = Select;

interface FixtureFormProps {
  initialValues?: Partial<FootballQueries.Fixture>;
  onSubmit: (values: FootballQueries.CreateFixtureRequest | FootballQueries.UpdateFixtureRequest) => void;
  onCancel: () => void;
  loading?: boolean;
  mode: 'create' | 'edit';
}

// Fixture status options
const FIXTURE_STATUSES = [
  { value: 'scheduled', label: 'Scheduled', color: 'blue' },
  { value: 'live', label: 'Live', color: 'red' },
  { value: 'finished', label: 'Finished', color: 'green' },
  { value: 'postponed', label: 'Postponed', color: 'orange' },
  { value: 'cancelled', label: 'Cancelled', color: 'gray' },
  { value: 'suspended', label: 'Suspended', color: 'purple' }
];

export default function FixtureForm({
  initialValues,
  onSubmit,
  onCancel,
  loading = false,
  mode
}: FixtureFormProps) {
  const [form] = Form.useForm();
  
  // Fetch leagues and teams for dropdowns
  const { data: leaguesData, isLoading: leaguesLoading } = useLeagues({ limit: 100 });
  const { data: teamsData, isLoading: teamsLoading } = useTeams({ limit: 200 });

  const handleSubmit = (values: any) => {
    const formData = {
      ...values,
      date: values.date ? values.date.toISOString() : undefined,
      homeScore: values.homeScore || undefined,
      awayScore: values.awayScore || undefined
    };
    onSubmit(formData);
  };

  // Filter teams by selected league
  const [selectedLeague, setSelectedLeague] = React.useState<string | undefined>(
    initialValues?.leagueId
  );

  const filteredTeams = React.useMemo(() => {
    if (!teamsData?.data || !selectedLeague) return teamsData?.data || [];
    return teamsData.data.filter(team => team.leagueId === selectedLeague);
  }, [teamsData?.data, selectedLeague]);

  return (
    <Card>
      <div className="mb-6">
        <Title level={3}>
          <CalendarOutlined className="mr-2" />
          {mode === 'create' ? 'Create New Fixture' : 'Edit Fixture'}
        </Title>
        <Text type="secondary">
          {mode === 'create' 
            ? 'Add a new football fixture to the system'
            : 'Update fixture information and results'
          }
        </Text>
      </div>

      <Form
        form={form}
        layout="vertical"
        initialValues={{
          ...initialValues,
          date: initialValues?.date ? dayjs(initialValues.date) : undefined
        }}
        onFinish={handleSubmit}
        size="large"
      >
        <Row gutter={24}>
          <Col xs={24} md={12}>
            <Form.Item
              name="externalId"
              label="External ID"
              rules={[
                { required: mode === 'create', message: 'Please enter external ID' },
                { min: 1, message: 'External ID must be at least 1 character' }
              ]}
            >
              <Input
                placeholder="e.g., 12345, ext_001"
                prefix={<NumberOutlined />}
                disabled={mode === 'edit'}
              />
            </Form.Item>
          </Col>

          <Col xs={24} md={12}>
            <Form.Item
              name="leagueId"
              label="League"
              rules={[
                { required: true, message: 'Please select league' }
              ]}
            >
              <Select
                placeholder="Select league"
                loading={leaguesLoading}
                showSearch
                filterOption={(input, option) =>
                  (option?.children as string)?.toLowerCase().includes(input.toLowerCase())
                }
                onChange={(value) => {
                  setSelectedLeague(value);
                  // Clear team selections when league changes
                  form.setFieldsValue({ homeTeamId: undefined, awayTeamId: undefined });
                }}
              >
                {leaguesData?.data?.map(league => (
                  <Option key={league.id} value={league.id}>
                    <TrophyOutlined className="mr-2" />
                    {league.name} ({league.country})
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={24}>
          <Col xs={24} md={12}>
            <Form.Item
              name="homeTeamId"
              label="Home Team"
              rules={[
                { required: true, message: 'Please select home team' }
              ]}
            >
              <Select
                placeholder="Select home team"
                loading={teamsLoading}
                showSearch
                filterOption={(input, option) =>
                  (option?.children as string)?.toLowerCase().includes(input.toLowerCase())
                }
                disabled={!selectedLeague}
              >
                {filteredTeams.map(team => (
                  <Option key={team.id} value={team.id}>
                    <TeamOutlined className="mr-2" />
                    {team.name}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>

          <Col xs={24} md={12}>
            <Form.Item
              name="awayTeamId"
              label="Away Team"
              rules={[
                { required: true, message: 'Please select away team' }
              ]}
            >
              <Select
                placeholder="Select away team"
                loading={teamsLoading}
                showSearch
                filterOption={(input, option) =>
                  (option?.children as string)?.toLowerCase().includes(input.toLowerCase())
                }
                disabled={!selectedLeague}
              >
                {filteredTeams.map(team => (
                  <Option key={team.id} value={team.id}>
                    <TeamOutlined className="mr-2" />
                    {team.name}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={24}>
          <Col xs={24} md={12}>
            <Form.Item
              name="date"
              label="Match Date & Time"
              rules={[
                { required: true, message: 'Please select match date and time' }
              ]}
            >
              <DatePicker
                showTime
                format="YYYY-MM-DD HH:mm"
                placeholder="Select date and time"
                style={{ width: '100%' }}
              />
            </Form.Item>
          </Col>

          <Col xs={24} md={12}>
            <Form.Item
              name="status"
              label="Status"
              rules={[
                { required: true, message: 'Please select status' }
              ]}
            >
              <Select placeholder="Select status">
                {FIXTURE_STATUSES.map(status => (
                  <Option key={status.value} value={status.value}>
                    <span style={{ color: status.color }}>●</span>
                    <span className="ml-2">{status.label}</span>
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={24}>
          <Col xs={24} md={8}>
            <Form.Item
              name="venue"
              label="Venue"
            >
              <Input
                placeholder="e.g., Old Trafford, Wembley Stadium"
                prefix={<HomeOutlined />}
              />
            </Form.Item>
          </Col>

          <Col xs={24} md={8}>
            <Form.Item
              name="round"
              label="Round/Matchday"
            >
              <Input
                placeholder="e.g., Matchday 15, Round 16"
              />
            </Form.Item>
          </Col>

          <Col xs={24} md={8}>
            <div className="grid grid-cols-2 gap-2">
              <Form.Item
                name="homeScore"
                label="Home Score"
              >
                <InputNumber
                  placeholder="0"
                  min={0}
                  max={20}
                  style={{ width: '100%' }}
                />
              </Form.Item>
              <Form.Item
                name="awayScore"
                label="Away Score"
              >
                <InputNumber
                  placeholder="0"
                  min={0}
                  max={20}
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </div>
          </Col>
        </Row>

        <Form.Item className="mb-0">
          <Space>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              icon={<SaveOutlined />}
              size="large"
            >
              {mode === 'create' ? 'Create Fixture' : 'Update Fixture'}
            </Button>
            <Button
              onClick={onCancel}
              icon={<CloseOutlined />}
              size="large"
            >
              Cancel
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </Card>
  );
}
