/**
 * Team Form Component
 * Form for creating and editing football teams
 */

'use client';

import React from 'react';
import {
  Form,
  Input,
  Select,
  Switch,
  Button,
  Card,
  Row,
  Col,
  Typography,
  Space,
  InputNumber,
  message
} from 'antd';
import {
  TeamOutlined,
  SaveOutlined,
  CloseOutlined,
  UploadOutlined,
  GlobalOutlined,
  TrophyOutlined,
  HomeOutlined,
  CalendarOutlined
} from '@ant-design/icons';
import { FootballQueries } from '@/lib/query-types';
import { useLeagues } from '@/hooks/api/football-hooks';

const { Title, Text } = Typography;
const { Option } = Select;

interface TeamFormProps {
  initialValues?: Partial<FootballQueries.Team>;
  onSubmit: (values: FootballQueries.CreateTeamRequest | FootballQueries.UpdateTeamRequest) => void;
  onCancel: () => void;
  loading?: boolean;
  mode: 'create' | 'edit';
}

// Common countries for football teams
const COUNTRIES = [
  'England', 'Spain', 'Germany', 'Italy', 'France', 'Netherlands', 'Portugal',
  'Brazil', 'Argentina', 'Mexico', 'United States', 'Turkey', 'Russia',
  'Belgium', 'Scotland', 'Austria', 'Switzerland', 'Greece', 'Ukraine',
  'Poland', 'Czech Republic', 'Croatia', 'Serbia', 'Denmark', 'Sweden',
  'Norway', 'Romania', 'Bulgaria', 'Hungary', 'Slovakia', 'Slovenia'
];

export default function TeamForm({
  initialValues,
  onSubmit,
  onCancel,
  loading = false,
  mode
}: TeamFormProps) {
  const [form] = Form.useForm();
  
  // Fetch leagues for dropdown
  const { data: leaguesData, isLoading: leaguesLoading } = useLeagues({ limit: 100 });

  const handleSubmit = (values: any) => {
    const formData = {
      ...values,
      isActive: values.isActive ?? true,
      founded: values.founded ? parseInt(values.founded) : undefined
    };
    onSubmit(formData);
  };

  return (
    <Card>
      <div className="mb-6">
        <Title level={3}>
          <TeamOutlined className="mr-2" />
          {mode === 'create' ? 'Create New Team' : 'Edit Team'}
        </Title>
        <Text type="secondary">
          {mode === 'create' 
            ? 'Add a new football team to the system'
            : 'Update team information and settings'
          }
        </Text>
      </div>

      <Form
        form={form}
        layout="vertical"
        initialValues={initialValues}
        onFinish={handleSubmit}
        size="large"
      >
        <Row gutter={24}>
          <Col xs={24} md={12}>
            <Form.Item
              name="name"
              label="Team Name"
              rules={[
                { required: true, message: 'Please enter team name' },
                { min: 2, message: 'Team name must be at least 2 characters' },
                { max: 100, message: 'Team name must not exceed 100 characters' }
              ]}
            >
              <Input
                placeholder="e.g., Manchester United, Real Madrid, Bayern Munich"
                prefix={<TeamOutlined />}
              />
            </Form.Item>
          </Col>

          <Col xs={24} md={12}>
            <Form.Item
              name="country"
              label="Country"
              rules={[
                { required: true, message: 'Please select country' }
              ]}
            >
              <Select
                placeholder="Select country"
                showSearch
                filterOption={(input, option) =>
                  (option?.children as string)?.toLowerCase().includes(input.toLowerCase())
                }
              >
                {COUNTRIES.map(country => (
                  <Option key={country} value={country}>
                    <GlobalOutlined className="mr-2" />
                    {country}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={24}>
          <Col xs={24} md={12}>
            <Form.Item
              name="leagueId"
              label="League"
              rules={[
                { required: true, message: 'Please select league' }
              ]}
            >
              <Select
                placeholder="Select league"
                loading={leaguesLoading}
                showSearch
                filterOption={(input, option) =>
                  (option?.children as string)?.toLowerCase().includes(input.toLowerCase())
                }
              >
                {leaguesData?.data?.map(league => (
                  <Option key={league.id} value={league.id}>
                    <TrophyOutlined className="mr-2" />
                    {league.name} ({league.country})
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>

          <Col xs={24} md={12}>
            <Form.Item
              name="founded"
              label="Founded Year"
              rules={[
                { type: 'number', min: 1800, max: new Date().getFullYear(), message: 'Please enter a valid year' }
              ]}
            >
              <InputNumber
                placeholder="e.g., 1878, 1902, 1900"
                prefix={<CalendarOutlined />}
                style={{ width: '100%' }}
                min={1800}
                max={new Date().getFullYear()}
              />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={24}>
          <Col xs={24} md={12}>
            <Form.Item
              name="venue"
              label="Home Venue"
              rules={[
                { max: 200, message: 'Venue name must not exceed 200 characters' }
              ]}
            >
              <Input
                placeholder="e.g., Old Trafford, Santiago Bernabéu, Allianz Arena"
                prefix={<HomeOutlined />}
              />
            </Form.Item>
          </Col>

          <Col xs={24} md={12}>
            <Form.Item
              name="isActive"
              label="Status"
              valuePropName="checked"
            >
              <Switch
                checkedChildren="Active"
                unCheckedChildren="Inactive"
                defaultChecked
              />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={24}>
          <Col xs={24}>
            <Form.Item
              name="logo"
              label="Team Logo URL"
              rules={[
                { type: 'url', message: 'Please enter a valid URL' }
              ]}
            >
              <Input
                placeholder="https://example.com/team-logo.png"
                prefix={<UploadOutlined />}
              />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item className="mb-0">
          <Space>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              icon={<SaveOutlined />}
              size="large"
            >
              {mode === 'create' ? 'Create Team' : 'Update Team'}
            </Button>
            <Button
              onClick={onCancel}
              icon={<CloseOutlined />}
              size="large"
            >
              Cancel
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </Card>
  );
}
