/**
 * League Form Component
 * Form for creating and editing football leagues
 */

'use client';

import React from 'react';
import {
  Form,
  Input,
  Select,
  Switch,
  Button,
  Card,
  Row,
  Col,
  Typography,
  Space,
  Upload,
  message
} from 'antd';
import {
  TrophyOutlined,
  SaveOutlined,
  CloseOutlined,
  UploadOutlined,
  GlobalOutlined
} from '@ant-design/icons';
import { FootballQueries } from '@/lib/query-types';

const { Title, Text } = Typography;
const { Option } = Select;

interface LeagueFormProps {
  initialValues?: Partial<FootballQueries.League>;
  onSubmit: (values: FootballQueries.CreateLeagueRequest | FootballQueries.UpdateLeagueRequest) => void;
  onCancel: () => void;
  loading?: boolean;
  mode: 'create' | 'edit';
}

// Common countries for football leagues
const COUNTRIES = [
  'England', 'Spain', 'Germany', 'Italy', 'France', 'Netherlands', 'Portugal',
  'Brazil', 'Argentina', 'Mexico', 'United States', 'Turkey', 'Russia',
  'Belgium', 'Scotland', 'Austria', 'Switzerland', 'Greece', 'Ukraine',
  'Poland', 'Czech Republic', 'Croatia', 'Serbia', 'Denmark', 'Sweden',
  'Norway', 'Romania', 'Bulgaria', 'Hungary', 'Slovakia', 'Slovenia'
];

// Current and recent seasons
const SEASONS = [
  '2024/25', '2023/24', '2022/23', '2021/22', '2020/21', '2019/20'
];

export default function LeagueForm({
  initialValues,
  onSubmit,
  onCancel,
  loading = false,
  mode
}: LeagueFormProps) {
  const [form] = Form.useForm();

  const handleSubmit = (values: any) => {
    const formData = {
      ...values,
      isActive: values.isActive ?? true
    };
    onSubmit(formData);
  };

  const handleLogoUpload = (info: any) => {
    if (info.file.status === 'done') {
      message.success(`${info.file.name} file uploaded successfully`);
      form.setFieldsValue({ logo: info.file.response?.url });
    } else if (info.file.status === 'error') {
      message.error(`${info.file.name} file upload failed.`);
    }
  };

  return (
    <Card>
      <div className="mb-6">
        <Title level={3}>
          <TrophyOutlined className="mr-2" />
          {mode === 'create' ? 'Create New League' : 'Edit League'}
        </Title>
        <Text type="secondary">
          {mode === 'create' 
            ? 'Add a new football league to the system'
            : 'Update league information and settings'
          }
        </Text>
      </div>

      <Form
        form={form}
        layout="vertical"
        initialValues={initialValues}
        onFinish={handleSubmit}
        size="large"
      >
        <Row gutter={24}>
          <Col xs={24} md={12}>
            <Form.Item
              name="name"
              label="League Name"
              rules={[
                { required: true, message: 'Please enter league name' },
                { min: 2, message: 'League name must be at least 2 characters' },
                { max: 100, message: 'League name must not exceed 100 characters' }
              ]}
            >
              <Input
                placeholder="e.g., Premier League, La Liga, Bundesliga"
                prefix={<TrophyOutlined />}
              />
            </Form.Item>
          </Col>

          <Col xs={24} md={12}>
            <Form.Item
              name="country"
              label="Country"
              rules={[
                { required: true, message: 'Please select country' }
              ]}
            >
              <Select
                placeholder="Select country"
                showSearch
                filterOption={(input, option) =>
                  (option?.children as string)?.toLowerCase().includes(input.toLowerCase())
                }
                prefix={<GlobalOutlined />}
              >
                {COUNTRIES.map(country => (
                  <Option key={country} value={country}>
                    {country}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={24}>
          <Col xs={24} md={12}>
            <Form.Item
              name="season"
              label="Season"
              rules={[
                { required: true, message: 'Please select season' }
              ]}
            >
              <Select placeholder="Select season">
                {SEASONS.map(season => (
                  <Option key={season} value={season}>
                    {season}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>

          <Col xs={24} md={12}>
            <Form.Item
              name="isActive"
              label="Status"
              valuePropName="checked"
            >
              <Switch
                checkedChildren="Active"
                unCheckedChildren="Inactive"
                defaultChecked
              />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={24}>
          <Col xs={24}>
            <Form.Item
              name="logo"
              label="League Logo URL"
              rules={[
                { type: 'url', message: 'Please enter a valid URL' }
              ]}
            >
              <Input
                placeholder="https://example.com/logo.png"
                prefix={<UploadOutlined />}
              />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item className="mb-0">
          <Space>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              icon={<SaveOutlined />}
              size="large"
            >
              {mode === 'create' ? 'Create League' : 'Update League'}
            </Button>
            <Button
              onClick={onCancel}
              icon={<CloseOutlined />}
              size="large"
            >
              Cancel
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </Card>
  );
}
