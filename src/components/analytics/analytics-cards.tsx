/**
 * Analytics Cards Component
 * Reusable analytics cards for dashboard and other pages
 */

'use client';

import React from 'react';
import {
  Card,
  Statistic,
  Row,
  Col,
  Progress,
  Typography,
  Tag,
  Space,
  Tooltip
} from 'antd';
import {
  TrophyOutlined,
  TeamOutlined,
  CalendarOutlined,
  PlayCircleOutlined,
  UserOutlined,
  SyncOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  ClockCircleOutlined,
  EyeOutlined,
  GlobalOutlined,
  LinkOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined
} from '@ant-design/icons';

const { Text } = Typography;

export interface AnalyticsData {
  leagues: {
    total: number;
    active: number;
    inactive: number;
    growth?: number;
  };
  teams: {
    total: number;
    active: number;
    inactive: number;
    growth?: number;
  };
  fixtures: {
    total: number;
    scheduled: number;
    live: number;
    finished: number;
    growth?: number;
  };
  broadcastLinks: {
    total: number;
    active: number;
    inactive: number;
    hd: number;
    views?: number;
    growth?: number;
  };
  users: {
    total: number;
    admin: number;
    editor: number;
    moderator: number;
  };
  sync: {
    lastSync: string;
    nextSync: string;
    status: 'success' | 'error' | 'warning';
    successRate: number;
  };
}

export interface AnalyticsCardsProps {
  data: AnalyticsData;
  loading?: boolean;
  showGrowth?: boolean;
}

export function AnalyticsCards({ data, loading = false, showGrowth = true }: AnalyticsCardsProps) {
  const formatGrowth = (growth?: number) => {
    if (!growth || !showGrowth) return null;
    
    const isPositive = growth > 0;
    const icon = isPositive ? <ArrowUpOutlined /> : <ArrowDownOutlined />;
    const color = isPositive ? '#52c41a' : '#ff4d4f';
    
    return (
      <Text style={{ color, fontSize: '12px' }}>
        {icon} {Math.abs(growth)}%
      </Text>
    );
  };

  return (
    <Row gutter={16}>
      {/* Football Leagues */}
      <Col xs={12} sm={6}>
        <Card loading={loading}>
          <Statistic
            title="Football Leagues"
            value={data.leagues.total}
            prefix={<TrophyOutlined />}
            suffix={
              <div className="text-sm">
                <div>
                  <Text type="success">{data.leagues.active} active</Text>
                </div>
                {formatGrowth(data.leagues.growth)}
              </div>
            }
          />
          <div className="mt-2">
            <Progress
              percent={(data.leagues.active / data.leagues.total) * 100}
              size="small"
              strokeColor="#52c41a"
              showInfo={false}
            />
            <Text type="secondary" className="text-xs">
              {data.leagues.inactive} inactive
            </Text>
          </div>
        </Card>
      </Col>

      {/* Teams */}
      <Col xs={12} sm={6}>
        <Card loading={loading}>
          <Statistic
            title="Teams"
            value={data.teams.total}
            prefix={<TeamOutlined />}
            suffix={
              <div className="text-sm">
                <div>
                  <Text type="success">{data.teams.active} active</Text>
                </div>
                {formatGrowth(data.teams.growth)}
              </div>
            }
          />
          <div className="mt-2">
            <Progress
              percent={(data.teams.active / data.teams.total) * 100}
              size="small"
              strokeColor="#1890ff"
              showInfo={false}
            />
            <Text type="secondary" className="text-xs">
              {data.teams.inactive} inactive
            </Text>
          </div>
        </Card>
      </Col>

      {/* Fixtures */}
      <Col xs={12} sm={6}>
        <Card loading={loading}>
          <Statistic
            title="Fixtures"
            value={data.fixtures.total}
            prefix={<CalendarOutlined />}
            suffix={
              <div className="text-sm">
                <div className="flex items-center gap-2">
                  <Text type="warning">{data.fixtures.scheduled} scheduled</Text>
                  {data.fixtures.live > 0 && (
                    <Tag color="red" size="small">
                      {data.fixtures.live} LIVE
                    </Tag>
                  )}
                </div>
                {formatGrowth(data.fixtures.growth)}
              </div>
            }
          />
          <div className="mt-2">
            <Space size="small">
              <Text type="secondary" className="text-xs">
                {data.fixtures.finished} finished
              </Text>
            </Space>
          </div>
        </Card>
      </Col>

      {/* Broadcast Links */}
      <Col xs={12} sm={6}>
        <Card loading={loading}>
          <Statistic
            title="Broadcast Links"
            value={data.broadcastLinks.total}
            prefix={<PlayCircleOutlined />}
            suffix={
              <div className="text-sm">
                <div className="flex items-center gap-2">
                  <Text type="success">{data.broadcastLinks.active} active</Text>
                  <Tag color="gold" size="small">
                    {data.broadcastLinks.hd} HD
                  </Tag>
                </div>
                {formatGrowth(data.broadcastLinks.growth)}
              </div>
            }
          />
          <div className="mt-2">
            {data.broadcastLinks.views && (
              <div className="flex items-center justify-between">
                <Text type="secondary" className="text-xs">
                  <EyeOutlined /> {data.broadcastLinks.views.toLocaleString()} views
                </Text>
                <Text type="secondary" className="text-xs">
                  {data.broadcastLinks.inactive} inactive
                </Text>
              </div>
            )}
          </div>
        </Card>
      </Col>
    </Row>
  );
}

export interface SystemHealthCardProps {
  data: AnalyticsData['sync'];
  loading?: boolean;
}

export function SystemHealthCard({ data, loading = false }: SystemHealthCardProps) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': return '#52c41a';
      case 'error': return '#ff4d4f';
      case 'warning': return '#faad14';
      default: return '#d9d9d9';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success': return <CheckCircleOutlined />;
      case 'error': return <ExclamationCircleOutlined />;
      case 'warning': return <ClockCircleOutlined />;
      default: return <SyncOutlined />;
    }
  };

  return (
    <Card title="System Health" loading={loading}>
      <Row gutter={16}>
        <Col xs={24} md={12}>
          <div className="mb-4">
            <Text strong>Sync Success Rate</Text>
            <Progress 
              percent={data.successRate} 
              status={data.successRate > 95 ? "success" : data.successRate > 80 ? "normal" : "exception"}
              strokeColor={getStatusColor(data.status)}
            />
          </div>
          <div className="mb-4">
            <Text strong>System Status</Text>
            <div className="flex items-center justify-between mt-2">
              <Tag color={data.status === 'success' ? 'success' : data.status === 'error' ? 'error' : 'warning'} 
                   icon={getStatusIcon(data.status)}>
                {data.status.toUpperCase()}
              </Tag>
              <Text type="secondary" className="text-sm">
                All services operational
              </Text>
            </div>
          </div>
        </Col>
        <Col xs={24} md={12}>
          <Space direction="vertical" className="w-full">
            <div className="flex justify-between">
              <Text>Last Sync:</Text>
              <Text type="secondary">
                {new Date(data.lastSync).toLocaleString()}
              </Text>
            </div>
            <div className="flex justify-between">
              <Text>Next Sync:</Text>
              <Text type="secondary">
                {new Date(data.nextSync).toLocaleString()}
              </Text>
            </div>
            <div className="flex justify-between">
              <Text>Success Rate:</Text>
              <Text type={data.successRate > 95 ? "success" : "warning"}>
                {data.successRate}%
              </Text>
            </div>
          </Space>
        </Col>
      </Row>
    </Card>
  );
}

export interface UserStatsCardProps {
  data: AnalyticsData['users'];
  loading?: boolean;
}

export function UserStatsCard({ data, loading = false }: UserStatsCardProps) {
  return (
    <Card title="System Users" loading={loading}>
      <Row gutter={8} className="mb-4">
        <Col span={8}>
          <Statistic
            title="Admin"
            value={data.admin}
            valueStyle={{ fontSize: '18px', color: '#722ed1' }}
            prefix={<UserOutlined />}
          />
        </Col>
        <Col span={8}>
          <Statistic
            title="Editor"
            value={data.editor}
            valueStyle={{ fontSize: '18px', color: '#1890ff' }}
            prefix={<UserOutlined />}
          />
        </Col>
        <Col span={8}>
          <Statistic
            title="Moderator"
            value={data.moderator}
            valueStyle={{ fontSize: '18px', color: '#52c41a' }}
            prefix={<UserOutlined />}
          />
        </Col>
      </Row>
      <div className="text-center">
        <Text type="secondary">
          Total: {data.total} active users
        </Text>
      </div>
    </Card>
  );
}
