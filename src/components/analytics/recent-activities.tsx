/**
 * Recent Activities Component
 * Display recent system activities and changes
 */

'use client';

import React from 'react';
import {
  Card,
  Timeline,
  Typography,
  Tag,
  Avatar,
  Space,
  Button,
  Empty,
  Tooltip
} from 'antd';
import {
  CalendarOutlined,
  PlayCircleOutlined,
  SyncOutlined,
  UserOutlined,
  TrophyOutlined,
  TeamOutlined,
  EditOutlined,
  DeleteOutlined,
  PlusOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  ClockCircleOutlined,
  EyeOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';

dayjs.extend(relativeTime);

const { Text, Title } = Typography;

export interface Activity {
  id: string | number;
  type: 'fixture' | 'broadcast' | 'sync' | 'team' | 'league' | 'user' | 'system';
  action: 'created' | 'updated' | 'deleted' | 'completed' | 'failed' | 'started' | 'viewed';
  title: string;
  description?: string;
  user: string;
  time: string;
  status?: 'success' | 'error' | 'warning' | 'info';
  metadata?: Record<string, any>;
}

export interface RecentActivitiesProps {
  activities: Activity[];
  title?: string;
  maxItems?: number;
  showRefresh?: boolean;
  loading?: boolean;
  onRefresh?: () => void;
  onViewAll?: () => void;
}

const getActivityIcon = (type: string, action: string) => {
  switch (type) {
    case 'fixture':
      return <CalendarOutlined />;
    case 'broadcast':
      return <PlayCircleOutlined />;
    case 'sync':
      return <SyncOutlined />;
    case 'team':
      return <TeamOutlined />;
    case 'league':
      return <TrophyOutlined />;
    case 'user':
      return <UserOutlined />;
    case 'system':
      return action === 'completed' ? <CheckCircleOutlined /> : 
             action === 'failed' ? <ExclamationCircleOutlined /> : 
             <ClockCircleOutlined />;
    default:
      return <ClockCircleOutlined />;
  }
};

const getActivityColor = (type: string, action: string, status?: string) => {
  if (status) {
    switch (status) {
      case 'success': return '#52c41a';
      case 'error': return '#ff4d4f';
      case 'warning': return '#faad14';
      case 'info': return '#1890ff';
    }
  }

  switch (action) {
    case 'created': return '#52c41a';
    case 'updated': return '#1890ff';
    case 'deleted': return '#ff4d4f';
    case 'completed': return '#52c41a';
    case 'failed': return '#ff4d4f';
    case 'started': return '#faad14';
    default: return '#d9d9d9';
  }
};

const getActionText = (action: string) => {
  switch (action) {
    case 'created': return 'Created';
    case 'updated': return 'Updated';
    case 'deleted': return 'Deleted';
    case 'completed': return 'Completed';
    case 'failed': return 'Failed';
    case 'started': return 'Started';
    case 'viewed': return 'Viewed';
    default: return action;
  }
};

const getUserAvatar = (user: string) => {
  if (user === 'system') {
    return <Avatar size="small" icon={<SyncOutlined />} style={{ backgroundColor: '#722ed1' }} />;
  }
  
  return (
    <Avatar size="small" style={{ backgroundColor: '#1890ff' }}>
      {user.charAt(0).toUpperCase()}
    </Avatar>
  );
};

export function RecentActivities({
  activities,
  title = "Recent Activities",
  maxItems = 10,
  showRefresh = true,
  loading = false,
  onRefresh,
  onViewAll
}: RecentActivitiesProps) {
  const displayActivities = activities.slice(0, maxItems);

  const timelineItems = displayActivities.map(activity => ({
    dot: (
      <div style={{ color: getActivityColor(activity.type, activity.action, activity.status) }}>
        {getActivityIcon(activity.type, activity.action)}
      </div>
    ),
    children: (
      <div>
        <div className="flex items-start justify-between mb-1">
          <div className="flex-1">
            <Text strong className="block">
              {activity.title}
            </Text>
            {activity.description && (
              <Text type="secondary" className="text-sm block">
                {activity.description}
              </Text>
            )}
          </div>
          {activity.status && (
            <Tag 
              color={activity.status === 'success' ? 'success' : 
                     activity.status === 'error' ? 'error' : 
                     activity.status === 'warning' ? 'warning' : 'default'}
              size="small"
            >
              {activity.status.toUpperCase()}
            </Tag>
          )}
        </div>
        <div className="flex items-center justify-between">
          <Space size="small">
            {getUserAvatar(activity.user)}
            <Text type="secondary" className="text-sm">
              {getActionText(activity.action)} by {activity.user}
            </Text>
          </Space>
          <Tooltip title={dayjs(activity.time).format('YYYY-MM-DD HH:mm:ss')}>
            <Text type="secondary" className="text-xs">
              {dayjs(activity.time).fromNow()}
            </Text>
          </Tooltip>
        </div>
      </div>
    )
  }));

  return (
    <Card 
      title={title}
      loading={loading}
      extra={
        <Space>
          {showRefresh && onRefresh && (
            <Button 
              type="text" 
              size="small" 
              icon={<ReloadOutlined />}
              onClick={onRefresh}
            >
              Refresh
            </Button>
          )}
          {onViewAll && (
            <Button 
              type="text" 
              size="small" 
              icon={<EyeOutlined />}
              onClick={onViewAll}
            >
              View All
            </Button>
          )}
        </Space>
      }
    >
      {displayActivities.length > 0 ? (
        <Timeline items={timelineItems} />
      ) : (
        <Empty 
          description="No recent activities"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      )}
    </Card>
  );
}

export interface ActivitySummaryProps {
  activities: Activity[];
  timeRange?: 'today' | 'week' | 'month';
}

export function ActivitySummary({ activities, timeRange = 'today' }: ActivitySummaryProps) {
  const now = dayjs();
  const startTime = timeRange === 'today' ? now.startOf('day') :
                   timeRange === 'week' ? now.startOf('week') :
                   now.startOf('month');

  const filteredActivities = activities.filter(activity => 
    dayjs(activity.time).isAfter(startTime)
  );

  const summary = filteredActivities.reduce((acc, activity) => {
    const key = `${activity.type}_${activity.action}`;
    acc[key] = (acc[key] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const summaryItems = Object.entries(summary).map(([key, count]) => {
    const [type, action] = key.split('_');
    return {
      type,
      action,
      count,
      icon: getActivityIcon(type, action),
      color: getActivityColor(type, action)
    };
  });

  return (
    <Card title={`Activity Summary (${timeRange})`} size="small">
      <Space wrap>
        {summaryItems.map((item, index) => (
          <Tag 
            key={index}
            icon={item.icon}
            color={item.color}
          >
            {item.count} {item.type} {item.action}
          </Tag>
        ))}
      </Space>
      {summaryItems.length === 0 && (
        <Text type="secondary">No activities in this time range</Text>
      )}
    </Card>
  );
}
