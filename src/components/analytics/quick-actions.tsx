/**
 * Quick Actions Component
 * Reusable quick actions for dashboard and other pages
 */

'use client';

import React from 'react';
import {
  Card,
  Row,
  Col,
  Button,
  Typography,
  Space,
  Tooltip,
  Badge
} from 'antd';
import {
  CalendarOutlined,
  PlayCircleOutlined,
  SyncOutlined,
  UserOutlined,
  TrophyOutlined,
  TeamOutlined,
  PlusOutlined,
  SettingOutlined,
  FileTextOutlined,
  DatabaseOutlined,
  LinkOutlined,
  BarChartOutlined
} from '@ant-design/icons';
import { useRouter } from 'next/navigation';

const { Title, Text } = Typography;

export interface QuickAction {
  title: string;
  description: string;
  icon: React.ReactNode;
  color: string;
  path: string;
  badge?: number | string;
  disabled?: boolean;
  tooltip?: string;
}

export interface QuickActionsProps {
  actions?: QuickAction[];
  title?: string;
  columns?: number;
  loading?: boolean;
}

const DEFAULT_ACTIONS: QuickAction[] = [
  {
    title: 'Add New Fixture',
    description: 'Create a new football fixture',
    icon: <CalendarOutlined />,
    color: '#1890ff',
    path: '/football/fixtures/create',
    tooltip: 'Create a new football match fixture'
  },
  {
    title: 'Add Broadcast Link',
    description: 'Add broadcast link for fixture',
    icon: <PlayCircleOutlined />,
    color: '#52c41a',
    path: '/broadcast-links/create',
    tooltip: 'Add streaming link for matches'
  },
  {
    title: 'Sync Fixtures',
    description: 'Trigger manual sync',
    icon: <SyncOutlined />,
    color: '#faad14',
    path: '/football/sync',
    tooltip: 'Manually sync fixture data from API'
  },
  {
    title: 'System Users',
    description: 'Manage system users',
    icon: <UserOutlined />,
    color: '#722ed1',
    path: '/users/system',
    tooltip: 'Manage admin, editor, and moderator accounts'
  },
  {
    title: 'Add League',
    description: 'Create new league',
    icon: <TrophyOutlined />,
    color: '#eb2f96',
    path: '/football/leagues/create',
    tooltip: 'Add a new football league'
  },
  {
    title: 'Add Team',
    description: 'Create new team',
    icon: <TeamOutlined />,
    color: '#13c2c2',
    path: '/football/teams/create',
    tooltip: 'Add a new football team'
  },
  {
    title: 'View Analytics',
    description: 'System analytics',
    icon: <BarChartOutlined />,
    color: '#f5222d',
    path: '/analytics',
    tooltip: 'View detailed system analytics'
  },
  {
    title: 'System Settings',
    description: 'Configure system',
    icon: <SettingOutlined />,
    color: '#595959',
    path: '/system/settings',
    tooltip: 'Configure system settings'
  }
];

export function QuickActions({ 
  actions = DEFAULT_ACTIONS, 
  title = "Quick Actions",
  columns = 4,
  loading = false 
}: QuickActionsProps) {
  const router = useRouter();

  const handleAction = (path: string) => {
    router.push(path);
  };

  const getColSpan = () => {
    switch (columns) {
      case 2: return { xs: 24, sm: 12 };
      case 3: return { xs: 12, sm: 8 };
      case 4: return { xs: 12, md: 6 };
      case 6: return { xs: 8, sm: 4 };
      default: return { xs: 12, md: 6 };
    }
  };

  return (
    <Card title={title} loading={loading}>
      <Row gutter={16}>
        {actions.map((action, index) => (
          <Col {...getColSpan()} key={index} className="mb-4">
            <Tooltip title={action.tooltip || action.description}>
              <Card
                hoverable
                className="text-center h-full"
                onClick={() => !action.disabled && handleAction(action.path)}
                style={{ 
                  borderColor: action.color,
                  opacity: action.disabled ? 0.5 : 1,
                  cursor: action.disabled ? 'not-allowed' : 'pointer'
                }}
                bodyStyle={{ padding: '16px 12px' }}
              >
                <div className="flex flex-col items-center justify-center h-full">
                  <div 
                    style={{ 
                      color: action.color, 
                      fontSize: '24px', 
                      marginBottom: '8px' 
                    }}
                  >
                    {action.badge ? (
                      <Badge count={action.badge} size="small">
                        {action.icon}
                      </Badge>
                    ) : (
                      action.icon
                    )}
                  </div>
                  <Title level={5} className="mb-1" style={{ fontSize: '14px' }}>
                    {action.title}
                  </Title>
                  <Text type="secondary" className="text-xs text-center">
                    {action.description}
                  </Text>
                </div>
              </Card>
            </Tooltip>
          </Col>
        ))}
      </Row>
    </Card>
  );
}

export interface QuickActionButtonsProps {
  actions?: QuickAction[];
  size?: 'small' | 'middle' | 'large';
  type?: 'default' | 'primary' | 'dashed' | 'link' | 'text';
  loading?: boolean;
}

export function QuickActionButtons({ 
  actions = DEFAULT_ACTIONS.slice(0, 4), 
  size = 'middle',
  type = 'default',
  loading = false 
}: QuickActionButtonsProps) {
  const router = useRouter();

  const handleAction = (path: string) => {
    router.push(path);
  };

  return (
    <Space wrap>
      {actions.map((action, index) => (
        <Tooltip key={index} title={action.tooltip || action.description}>
          <Button
            type={type}
            size={size}
            icon={action.icon}
            loading={loading}
            disabled={action.disabled}
            onClick={() => handleAction(action.path)}
            style={{ borderColor: action.color }}
          >
            {action.title}
          </Button>
        </Tooltip>
      ))}
    </Space>
  );
}

export interface FloatingQuickActionsProps {
  actions?: QuickAction[];
  position?: 'bottomRight' | 'bottomLeft' | 'topRight' | 'topLeft';
}

export function FloatingQuickActions({ 
  actions = DEFAULT_ACTIONS.slice(0, 3),
  position = 'bottomRight'
}: FloatingQuickActionsProps) {
  const router = useRouter();

  const getPositionStyle = () => {
    const base = {
      position: 'fixed' as const,
      zIndex: 1000,
      display: 'flex',
      flexDirection: 'column' as const,
      gap: '8px'
    };

    switch (position) {
      case 'bottomRight':
        return { ...base, bottom: '24px', right: '24px' };
      case 'bottomLeft':
        return { ...base, bottom: '24px', left: '24px' };
      case 'topRight':
        return { ...base, top: '24px', right: '24px' };
      case 'topLeft':
        return { ...base, top: '24px', left: '24px' };
      default:
        return { ...base, bottom: '24px', right: '24px' };
    }
  };

  return (
    <div style={getPositionStyle()}>
      {actions.map((action, index) => (
        <Tooltip key={index} title={action.title} placement="left">
          <Button
            type="primary"
            shape="circle"
            size="large"
            icon={action.icon}
            style={{ backgroundColor: action.color, borderColor: action.color }}
            onClick={() => router.push(action.path)}
          />
        </Tooltip>
      ))}
    </div>
  );
}
