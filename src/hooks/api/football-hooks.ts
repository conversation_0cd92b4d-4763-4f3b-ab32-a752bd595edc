/**
 * Football Data API Hooks
 * Hooks for football leagues, teams, and fixtures operations
 */

'use client';

import { queryKeys } from '@/lib/query-client';
import { FootballQueries } from '@/lib/query-types';
import { PaginatedResponse } from '@/lib/query-utils';
import {
  useBaseQuery,
  usePaginatedQuery,
  useBackgroundSyncQuery,
  useBaseMutation,
  useApiHookUtils
} from './base-hooks';

/**
 * Hook for getting football leagues
 */
export function useLeagues(params?: FootballQueries.LeagueQueryParams) {
  const queryParams = new URLSearchParams();
  if (params?.page) queryParams.set('page', params.page.toString());
  if (params?.limit) queryParams.set('limit', params.limit.toString());
  if (params?.country) queryParams.set('country', params.country);
  if (params?.isActive !== undefined) queryParams.set('isActive', params.isActive.toString());
  if (params?.query) queryParams.set('query', params.query);

  return usePaginatedQuery(
    [...queryKeys.football.leagues(), params],
    async (): Promise<PaginatedResponse<FootballQueries.League>> => {
      const response = await fetch(`/api/football/leagues?${queryParams.toString()}`);

      if (!response.ok) {
        throw new Error(`Failed to fetch leagues: ${response.statusText}`);
      }

      return response.json();
    },
    {
      staleTime: 10 * 60 * 1000, // 10 minutes - leagues don't change often
    }
  );
}

/**
 * Hook for getting specific league
 */
export function useLeague(leagueId: string) {
  return useBaseQuery(
    queryKeys.football.league(leagueId),
    async (): Promise<FootballQueries.League> => {
      const response = await fetch(`/api/football/leagues/${leagueId}`);

      if (!response.ok) {
        throw new Error(`Failed to fetch league: ${response.statusText}`);
      }

      return response.json();
    },
    {
      enabled: !!leagueId,
      staleTime: 10 * 60 * 1000, // 10 minutes
    }
  );
}

/**
 * Hook for getting football teams
 */
export function useTeams(params?: FootballQueries.TeamQueryParams) {
  const queryParams = new URLSearchParams();
  if (params?.page) queryParams.set('page', params.page.toString());
  if (params?.limit) queryParams.set('limit', params.limit.toString());
  if (params?.leagueId) queryParams.set('leagueId', params.leagueId);
  if (params?.country) queryParams.set('country', params.country);
  if (params?.query) queryParams.set('query', params.query);

  return usePaginatedQuery(
    [...queryKeys.football.teams(), params],
    async (): Promise<PaginatedResponse<FootballQueries.Team>> => {
      const response = await fetch(`/api/football/teams?${queryParams.toString()}`);

      if (!response.ok) {
        throw new Error(`Failed to fetch teams: ${response.statusText}`);
      }

      return response.json();
    },
    {
      staleTime: 5 * 60 * 1000, // 5 minutes
    }
  );
}

/**
 * Hook for getting specific team
 */
export function useTeam(teamId: string) {
  return useBaseQuery(
    queryKeys.football.team(teamId),
    async (): Promise<FootballQueries.Team> => {
      const response = await fetch(`/api/football/teams/${teamId}`);

      if (!response.ok) {
        throw new Error(`Failed to fetch team: ${response.statusText}`);
      }

      return response.json();
    },
    {
      enabled: !!teamId,
      staleTime: 5 * 60 * 1000, // 5 minutes
    }
  );
}

/**
 * Hook for getting football fixtures
 */
export function useFixtures(params?: FootballQueries.FixtureQueryParams) {
  const queryParams = new URLSearchParams();
  if (params?.page) queryParams.set('page', params.page.toString());
  if (params?.limit) queryParams.set('limit', params.limit.toString());
  if (params?.leagueId) queryParams.set('leagueId', params.leagueId);
  if (params?.teamId) queryParams.set('teamId', params.teamId);
  if (params?.status) queryParams.set('status', params.status);
  if (params?.dateFrom) queryParams.set('dateFrom', params.dateFrom);
  if (params?.dateTo) queryParams.set('dateTo', params.dateTo);
  if (params?.query) queryParams.set('query', params.query);

  return usePaginatedQuery(
    [...queryKeys.football.fixtures(), params],
    async (): Promise<PaginatedResponse<FootballQueries.Fixture>> => {
      const response = await fetch(`/api/football/fixtures?${queryParams.toString()}`);

      if (!response.ok) {
        throw new Error(`Failed to fetch fixtures: ${response.statusText}`);
      }

      return response.json();
    },
    {
      staleTime: 1 * 60 * 1000, // 1 minute - fixtures change frequently
    }
  );
}

/**
 * Hook for getting specific fixture
 */
export function useFixture(fixtureId: string) {
  return useBaseQuery(
    queryKeys.football.fixture(fixtureId),
    async (): Promise<FootballQueries.Fixture> => {
      const response = await fetch(`/api/football/fixtures/${fixtureId}`);

      if (!response.ok) {
        throw new Error(`Failed to fetch fixture: ${response.statusText}`);
      }

      return response.json();
    },
    {
      enabled: !!fixtureId,
      staleTime: 30 * 1000, // 30 seconds - live fixtures need frequent updates
    }
  );
}

/**
 * Hook for getting sync status
 */
export function useSyncStatus() {
  return useBackgroundSyncQuery(
    queryKeys.football.syncStatus(),
    async (): Promise<FootballQueries.SyncStatus> => {
      const response = await fetch('/api/football/fixtures/sync/status');

      if (!response.ok) {
        throw new Error(`Failed to fetch sync status: ${response.statusText}`);
      }

      return response.json();
    },
    {
      staleTime: 30 * 1000, // 30 seconds
      refetchInterval: 60 * 1000, // Refetch every minute
    }
  );
}

/**
 * Hook for triggering fixtures sync
 */
export function useSyncFixtures() {
  const { invalidateQueries } = useApiHookUtils();

  return useBaseMutation<{ message: string; syncId: string }, void>(
    async () => {
      const response = await fetch('/api/football/fixtures/sync', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to start sync: ${response.statusText}`);
      }

      return response.json();
    },
    {
      onSuccess: () => {
        // Invalidate sync status and fixtures to show updated data
        invalidateQueries(queryKeys.football.syncStatus());
        invalidateQueries(queryKeys.football.fixtures());
        console.log('✅ Fixtures sync started');
      },
      onError: (error) => {
        console.error('❌ Fixtures sync failed:', error);
      },
    }
  );
}

/**
 * Hook for triggering daily sync
 */
export function useDailySync() {
  const { invalidateQueries } = useApiHookUtils();

  return useBaseMutation<{ message: string; syncId: string }, void>(
    async () => {
      const response = await fetch('/api/football/fixtures/sync/daily', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to start daily sync: ${response.statusText}`);
      }

      return response.json();
    },
    {
      onSuccess: () => {
        // Invalidate sync status and fixtures
        invalidateQueries(queryKeys.football.syncStatus());
        invalidateQueries(queryKeys.football.fixtures());
        console.log('✅ Daily sync started');
      },
      onError: (error) => {
        console.error('❌ Daily sync failed:', error);
      },
    }
  );
}

/**
 * Hook for creating a new league
 */
export function useCreateLeague() {
  const { invalidateQueries } = useApiHookUtils();

  return useBaseMutation<FootballQueries.League, FootballQueries.CreateLeagueRequest>(
    async (data) => {
      const response = await fetch('/api/football/leagues', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error(`Failed to create league: ${response.statusText}`);
      }

      return response.json();
    },
    {
      onSuccess: () => {
        // Invalidate leagues queries to show updated data
        invalidateQueries(queryKeys.football.leagues());
        console.log('✅ League created successfully');
      },
      onError: (error) => {
        console.error('❌ League creation failed:', error);
      },
    }
  );
}

/**
 * Hook for updating a league
 */
export function useUpdateLeague() {
  const { invalidateQueries } = useApiHookUtils();

  return useBaseMutation<FootballQueries.League, { id: string; data: FootballQueries.UpdateLeagueRequest }>(
    async ({ id, data }) => {
      const response = await fetch(`/api/football/leagues/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error(`Failed to update league: ${response.statusText}`);
      }

      return response.json();
    },
    {
      onSuccess: (data) => {
        // Invalidate leagues queries and specific league
        invalidateQueries(queryKeys.football.leagues());
        invalidateQueries(queryKeys.football.league(data.id));
        console.log('✅ League updated successfully');
      },
      onError: (error) => {
        console.error('❌ League update failed:', error);
      },
    }
  );
}

/**
 * Hook for deleting a league
 */
export function useDeleteLeague() {
  const { invalidateQueries } = useApiHookUtils();

  return useBaseMutation<{ message: string }, string>(
    async (leagueId) => {
      const response = await fetch(`/api/football/leagues/${leagueId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error(`Failed to delete league: ${response.statusText}`);
      }

      return response.json();
    },
    {
      onSuccess: () => {
        // Invalidate leagues queries
        invalidateQueries(queryKeys.football.leagues());
        console.log('✅ League deleted successfully');
      },
      onError: (error) => {
        console.error('❌ League deletion failed:', error);
      },
    }
  );
}

/**
 * Composite hook for football data operations
 */
export function useFootball() {
  const syncFixtures = useSyncFixtures();
  const dailySync = useDailySync();
  const syncStatus = useSyncStatus();

  return {
    // Sync operations
    syncFixtures,
    dailySync,
    syncStatus,

    // Sync actions
    startSync: syncFixtures.mutate,
    startDailySync: dailySync.mutate,

    // Sync state
    isSyncing: syncFixtures.isPending || dailySync.isPending,
    syncError: syncFixtures.error || dailySync.error,
    lastSyncStatus: syncStatus.data,
  };
}

/**
 * Hook for live fixtures (real-time updates)
 */
export function useLiveFixtures() {
  return useFixtures({
    status: 'live',
    limit: 50,
  });
}

/**
 * Hook for today's fixtures
 */
export function useTodayFixtures() {
  const today = new Date().toISOString().split('T')[0];

  return useFixtures({
    dateFrom: today,
    dateTo: today,
    limit: 100,
  });
}

/**
 * Hook for upcoming fixtures
 */
export function useUpcomingFixtures(days: number = 7) {
  const today = new Date();
  const futureDate = new Date(today.getTime() + days * 24 * 60 * 60 * 1000);

  return useFixtures({
    dateFrom: today.toISOString().split('T')[0],
    dateTo: futureDate.toISOString().split('T')[0],
    status: 'scheduled',
    limit: 100,
  });
}
