/**
 * Query Types
 * TypeScript types and interfaces for TanStack Query
 */

import {
  UseQueryOptions,
  UseMutationOptions,
  QueryKey,
  MutationKey
} from '@tanstack/react-query';
import { ApiError } from './query-error-handler';
import { ApiResponse, PaginatedResponse } from './query-utils';

/**
 * Base query options with error type
 */
export type BaseQueryOptions<T> = Omit<
  UseQueryOptions<T, ApiError, T, QueryKey>,
  'queryKey' | 'queryFn'
>;

/**
 * Base mutation options with error type
 */
export type BaseMutationOptions<TData, TVariables> = Omit<
  UseMutationOptions<TData, ApiError, TVariables, unknown>,
  'mutationFn'
>;

/**
 * Query function type
 */
export type QueryFunction<T> = () => Promise<T>;

/**
 * Mutation function type
 */
export type MutationFunction<TData, TVariables> = (variables: TVariables) => Promise<TData>;

/**
 * Query hook return type
 */
export interface QueryHookResult<T> {
  data: T | undefined;
  isLoading: boolean;
  isError: boolean;
  error: ApiError | null;
  isSuccess: boolean;
  isFetching: boolean;
  isStale: boolean;
  refetch: () => void;
}

/**
 * Mutation hook return type
 */
export interface MutationHookResult<TData, TVariables> {
  mutate: (variables: TVariables) => void;
  mutateAsync: (variables: TVariables) => Promise<TData>;
  data: TData | undefined;
  isLoading: boolean;
  isError: boolean;
  error: ApiError | null;
  isSuccess: boolean;
  reset: () => void;
}

/**
 * Paginated query parameters
 */
export interface PaginationParams {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

/**
 * Search parameters
 */
export interface SearchParams {
  query?: string;
  filters?: Record<string, any>;
}

/**
 * Combined query parameters
 */
export interface QueryParams extends PaginationParams, SearchParams {
  [key: string]: any;
}

/**
 * System authentication types
 */
export namespace AuthQueries {
  export interface LoginRequest {
    username: string;
    password: string;
  }

  export interface LoginResponse {
    user: {
      id: string;
      username: string;
      email: string;
      role: 'Admin' | 'Editor' | 'Moderator';
      createdAt: string;
      updatedAt: string;
    };
    tokens: {
      accessToken: string;
      refreshToken: string;
      expiresAt: string;
    };
  }

  export interface ProfileResponse {
    id: string;
    username: string;
    email: string;
    role: 'Admin' | 'Editor' | 'Moderator';
    createdAt: string;
    updatedAt: string;
  }

  export interface UpdateProfileRequest {
    username?: string;
    email?: string;
  }

  export interface ChangePasswordRequest {
    currentPassword: string;
    newPassword: string;
  }

  export interface CreateUserRequest {
    username: string;
    email: string;
    password: string;
    role: 'Admin' | 'Editor' | 'Moderator';
  }
}

/**
 * Football data types
 */
export namespace FootballQueries {
  export interface League {
    id: string;
    name: string;
    country: string;
    logo?: string;
    season: string;
    isActive: boolean;
    createdAt: string;
    updatedAt: string;
  }

  export interface Team {
    id: string;
    name: string;
    logo?: string;
    country: string;
    leagueId: string;
    league?: League;
    statistics?: TeamStatistics;
    createdAt: string;
    updatedAt: string;
  }

  export interface TeamStatistics {
    played: number;
    wins: number;
    draws: number;
    losses: number;
    goalsFor: number;
    goalsAgainst: number;
    points: number;
  }

  export interface Fixture {
    id: string;
    externalId: string;
    homeTeamId: string;
    awayTeamId: string;
    homeTeam?: Team;
    awayTeam?: Team;
    leagueId: string;
    league?: League;
    date: string;
    status: 'scheduled' | 'live' | 'finished' | 'postponed' | 'cancelled';
    homeScore?: number;
    awayScore?: number;
    venue?: string;
    round?: string;
    createdAt: string;
    updatedAt: string;
  }

  export interface SyncStatus {
    lastSync: string;
    status: 'idle' | 'syncing' | 'error';
    totalFixtures: number;
    syncedFixtures: number;
    errors: string[];
  }

  export interface LeagueQueryParams extends QueryParams {
    country?: string;
    isActive?: boolean;
  }

  export interface TeamQueryParams extends QueryParams {
    leagueId?: string;
    country?: string;
  }

  export interface FixtureQueryParams extends QueryParams {
    leagueId?: string;
    teamId?: string;
    status?: string;
    dateFrom?: string;
    dateTo?: string;
  }

  export interface CreateLeagueRequest {
    name: string;
    country: string;
    logo?: string;
    season: string;
    isActive?: boolean;
  }

  export interface UpdateLeagueRequest {
    name?: string;
    country?: string;
    logo?: string;
    season?: string;
    isActive?: boolean;
  }

  export interface CreateTeamRequest {
    name: string;
    country: string;
    leagueId: string;
    logo?: string;
    founded?: number;
    venue?: string;
    isActive?: boolean;
  }

  export interface UpdateTeamRequest {
    name?: string;
    country?: string;
    leagueId?: string;
    logo?: string;
    founded?: number;
    venue?: string;
    isActive?: boolean;
  }

  export interface CreateFixtureRequest {
    externalId: string;
    homeTeamId: string;
    awayTeamId: string;
    leagueId: string;
    date: string;
    status: string;
    venue?: string;
    round?: string;
    homeScore?: number;
    awayScore?: number;
  }

  export interface UpdateFixtureRequest {
    homeTeamId?: string;
    awayTeamId?: string;
    leagueId?: string;
    date?: string;
    status?: string;
    venue?: string;
    round?: string;
    homeScore?: number;
    awayScore?: number;
  }
}

/**
 * Broadcast links types
 */
export namespace BroadcastQueries {
  export interface BroadcastLink {
    id: string;
    fixtureId: string;
    fixture?: FootballQueries.Fixture;
    url: string;
    quality: 'HD' | 'SD' | 'Mobile';
    language: string;
    isActive: boolean;
    createdBy: string;
    createdAt: string;
    updatedAt: string;
  }

  export interface CreateBroadcastLinkRequest {
    fixtureId: string;
    url: string;
    quality: 'HD' | 'SD' | 'Mobile';
    language: string;
  }

  export interface UpdateBroadcastLinkRequest {
    url?: string;
    quality?: 'HD' | 'SD' | 'Mobile';
    language?: string;
    isActive?: boolean;
  }

  export interface BroadcastLinkQueryParams extends QueryParams {
    fixtureId?: string;
    quality?: string;
    language?: string;
    isActive?: boolean;
  }
}

/**
 * Health check types
 */
export namespace HealthQueries {
  export interface HealthResponse {
    status: 'healthy' | 'unhealthy';
    timestamp: string;
    services: {
      database: 'up' | 'down';
      api: 'up' | 'down';
      cache: 'up' | 'down';
    };
    version: string;
    uptime: number;
  }
}

/**
 * Generic API response wrappers
 */
export type AuthApiResponse<T> = ApiResponse<T>;
export type FootballApiResponse<T> = ApiResponse<T>;
export type BroadcastApiResponse<T> = ApiResponse<T>;
export type HealthApiResponse<T> = ApiResponse<T>;

export type AuthPaginatedResponse<T> = PaginatedResponse<T>;
export type FootballPaginatedResponse<T> = PaginatedResponse<T>;
export type BroadcastPaginatedResponse<T> = PaginatedResponse<T>;

/**
 * Query key type definitions
 */
export type AuthQueryKey = readonly ['auth', ...string[]];
export type FootballQueryKey = readonly ['football', ...string[]];
export type BroadcastQueryKey = readonly ['broadcast', ...string[]];
export type HealthQueryKey = readonly ['health', ...string[]];

/**
 * Mutation key type definitions
 */
export type AuthMutationKey = readonly ['auth', string];
export type FootballMutationKey = readonly ['football', string];
export type BroadcastMutationKey = readonly ['broadcast', string];

/**
 * Combined query and mutation types
 */
export type AppQueryKey = AuthQueryKey | FootballQueryKey | BroadcastQueryKey | HealthQueryKey;
export type AppMutationKey = AuthMutationKey | FootballMutationKey | BroadcastMutationKey;
